name: Update Storybook from dev

on:
  push:
    branches:
      - dev

jobs:
  chromatic-web:
    name: Capture Nordvik Megler Chromatic Snapshots
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v4
        with:
          version: 10.17.1
          run_install: false

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Capture Nordvik Megler Chromatic Snapshots
        run: |
          cd ./apps/web
          pnpm storybook:snapshot:accept

  chromatic-nordvik-ui:
    name: Capture Nordvik UI Chromatic Snapshots
    runs-on: ubuntu-latest
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v3
        with:
          version: 8

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Cache pnpm store
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-
      - uses: pnpm/action-setup@v3
        with:
          version: 8.6.10
          run_install: true
      - name: Capture Nordvik UI Chromatic Snapshots
        run: |
          cd ./packages/nordvik-ui
          pnpm storybook:snapshot:accept
