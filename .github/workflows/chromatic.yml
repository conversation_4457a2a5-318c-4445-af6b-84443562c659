name: Run Chromatic

on:
  pull_request:
    branches:
      # Swap to main when main is the default branch
      - dev

jobs:
  check-chromatic:
    name: ${{ matrix.app-name }}
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        include:
          - app: web
            path: 'apps/web'
            app-name: 'Nord<PERSON> Megler'
          - app: '@nordvik/ui'
            path: 'packages/nordvik-ui'
            app-name: 'Nordvik UI'

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            web:
              - 'apps/web/**'
              - 'packages/nordvik-ui/**'
            '@nordvik/ui':
              - 'packages/nordvik-ui/**'
            shared-ui:
              - 'packages/nordvik-ui/**'
              - 'packages/nordvik-theme/**'
              - 'packages/nordvik-utils/**'

      - name: Determine if Chromatic should run
        run: |
          echo "RUN_CHROMATIC=true" >> $GITHUB_ENV
        if: |
          steps.changes.outputs[matrix.app] == 'true' ||
          steps.changes.outputs.shared-ui == 'true' ||
          (matrix.app == 'web' && contains(github.event.pull_request.labels.*.name, 'run storybook'))

      - uses: pnpm/action-setup@v4
        if: env.RUN_CHROMATIC == 'true'
        with:
          version: 10.13.1
          run_install: false

      - uses: actions/setup-node@v4
        if: env.RUN_CHROMATIC == 'true'
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        if: env.RUN_CHROMATIC == 'true'
        run: pnpm install --frozen-lockfile

      - name: Capture Chromatic Snapshots
        if: env.RUN_CHROMATIC == 'true'
        run: |
          cd ${{ matrix.path }}
          pnpm storybook:snapshot
