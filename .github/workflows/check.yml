name: Check Code Quality

on:
  pull_request:
    branches:
      # Swap to main when main is the default branch
      - dev

jobs:
  check:
    name: 'Lint, typecheck & check format'
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            web:
              - 'apps/web/**'
            '@nordvik/ui':
              - 'packages/nordvik-ui/**'
            '@nordvik/theme':
              - 'packages/nordvik-theme/**'

      - uses: pnpm/action-setup@v4
        if: contains(join(steps.changes.outputs.*), 'true')
        with:
          version: 10.17.1
          run_install: false

      - uses: actions/setup-node@v4
        if: contains(join(steps.changes.outputs.*), 'true')
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        if: contains(join(steps.changes.outputs.*), 'true')
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma Client
        if: contains(join(steps.changes.outputs.*), 'true')
        run: pnpm --filter @nordvik/database run prisma:generate

      - name: Run Checks if Relevant Files Changed
        if: contains(join(steps.changes.outputs.*), 'true')
        run: |
          FILTERS=$(jq -r 'to_entries | map(select(.value == "true") | "--filter " + .key) | join(" ")' <<< '${{ toJSON(steps.changes.outputs) }}')
          pnpm exec turbo run check $FILTERS
