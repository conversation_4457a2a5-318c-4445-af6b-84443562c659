name: Setup Node.js and pnpm

on:
  workflow_call:
    inputs:
      node-version:
        description: 'Node.js version to use'
        required: false
        default: '22'
        type: string
      pnpm-version:
        description: 'pnpm version to use'
        required: false
        default: '10.13.1'
        type: string
      install-dependencies:
        description: 'Whether to install dependencies'
        required: false
        default: true
        type: boolean

jobs:
  setup:
    runs-on: ubuntu-latest
    steps:
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ inputs.pnpm-version }}
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          cache: 'pnpm'
          cache-dependency-path: 'pnpm-lock.yaml'

      - name: Get pnpm store directory
        if: ${{ inputs.install-dependencies }}
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        if: ${{ inputs.install-dependencies }}
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        if: ${{ inputs.install-dependencies }}
        run: |
          pnpm install --frozen-lockfile --prefer-offline
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
          NPM_CONFIG_LEGACY_PEER_DEPS: 'true'
