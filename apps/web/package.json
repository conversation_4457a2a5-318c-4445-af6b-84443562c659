{"name": "web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "build:analyze": "ANALYZE=true npm run build", "bundle:analyze": "npx @next/bundle-analyzer", "bundle:size": "npx bundlesize", "bundle:report": "node scripts/bundle-analysis.js", "check:format": "prettier --check src", "dev": "PORT=${PORT:=8040} next dev", "check:type": "tsc --noEmit", "format": "prettier --write src", "build-storybook": "STORYBOOK_CI=true storybook build", "storybook": "storybook dev -p 8060 --no-version-updates --no-open --disable-telemetry", "storybook:snapshot": "npx chromatic --project-token=chpt_17fcfd5c3570ccb -b build-storybook --only-changed --exit-zero-on-changes --exit-once-uploaded", "storybook:snapshot:accept": "npx chromatic --project-token=chpt_17fcfd5c3570ccb -b build-storybook --only-changed --exit-zero-on-changes --exit-once-uploaded --auto-accept-changes", "storybook:skip": "npx chromatic --project-token=chpt_17fcfd5c3570ccb --skip", "lint": "eslint src --ext .ts,.tsx --max-warnings 0", "start": "next start"}, "dependencies": {"@ai-sdk/openai": "0.0.72", "@apollo/server": "5.0.0", "@as-integrations/next": "4.0.0", "@aws-sdk/client-lambda": "3.614.0", "@aws-sdk/client-s3": "3.614.0", "@aws-sdk/s3-request-presigner": "3.685.0", "@hookform/resolvers": "3.9.0", "@mailchimp/mailchimp_transactional": "1.0.59", "@nordvik/eslint-config": "workspace:*", "@nordvik/signicat-express-sdk": "workspace:*", "@nordvik/theme": "workspace:*", "@nordvik/ui": "workspace:*", "@nordvik/utils": "workspace:*", "@number-flow/react": "0.5.10", "@radix-ui/react-collapsible": "1.1.11", "@react-email/components": "0.5.1", "@sentry/nextjs": "8.55.0", "@sentry/node": "8.55.0", "@simplewebauthn/browser": "9.0.1", "@simplewebauthn/server": "9.0.3", "@tailwindcss/container-queries": "0.1.1", "@tanstack/react-query": "5.90.2", "@tsparticles/confetti": "3.8.1", "@vercel/analytics": "1.5.0", "@vercel/functions": "3.1.0", "@vercel/kv": "2.0.0", "ai": "3.4.33", "babel-plugin-react-compiler": "19.1.0-rc.3", "calendar-utils": "0.11.0", "cheerio": "1.0.0", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "dataloader": "2.2.3", "date-fns": "4.1.0", "date-fns-tz": "3.2.0", "dotenv-cli": "7.4.2", "emoji-regex": "10.4.0", "framer-motion": "12.4.7", "fuse.js": "7.1.0", "graphql": "16.11.0", "graphql-request": "7.2.0", "graphql-tag": "2.12.6", "input-otp": "1.4.2", "jsonwebtoken": "9.0.2", "lodash": "4.17.21", "lucide-react": "0.510.0", "marked": "15.0.7", "mongodb": "6.18.0", "next": "15.5.4", "next-auth": "5.0.0-beta.29", "nodemailer": "6.10.1", "norwegian-utils": "0.4.1", "nuqs": "2.7.0", "pdf-parse": "1.1.1", "posthog-js": "1.270.0", "posthog-node": "4.18.0", "quill": "2.0.3", "react": "19.1.1", "react-dom": "19.1.1", "react-email": "4.2.8", "react-hook-form": "7.53.0", "react-use-intercom": "5.5.0", "react-virtuoso": "4.12.3", "recharts": "2.15.3", "reflect-metadata": "0.2.2", "server-only": "0.0.1", "tailwind-merge": "2.6.0", "tailwindcss-animate": "1.0.7", "@microsoft/fetch-event-source": "2.0.1", "yup": "1.4.0", "zod": "3.23.8"}, "devDependencies": {"@nordvik/database": "workspace:*", "@next/bundle-analyzer": "15.4.2", "@playwright/test": "1.51.1", "@savvywombat/tailwindcss-grid-areas": "4.0.0", "@storybook/addon-styling-webpack": "2.0.0", "@storybook/nextjs": "9.1.3", "@storybook/react-webpack5": "9.1.3", "@storybook/test-runner": "0.23.0", "@storybook/testing-library": "0.2.2", "@tanstack/react-query-devtools": "5.90.2", "@types/lodash": "4.17.7", "@types/mailchimp__mailchimp_transactional": "1.0.10", "@types/node": "22.17.0", "@types/nodemailer": "6.4.15", "@types/pdf-parse": "1.1.5", "@types/react": "19.1.12", "@types/react-dom": "19.1.9", "autoprefixer": "10.4.21", "bundlesize": "0.18.2", "eslint": "8.57.0", "eslint-config-next": "15.5.2", "eslint-plugin-storybook": "9.1.3", "mockdate": "3.0.5", "postcss": "8.4.16", "storybook": "9.1.3", "tailwindcss": "3.4.4", "tsconfig-paths-webpack-plugin": "4.1.0", "typescript": "5.9.3", "webpack-bundle-analyzer": "4.10.2"}, "bundlesize": [{"path": ".next/static/js/**/*.js", "maxSize": "300kb", "compression": "gzip"}, {"path": ".next/static/css/**/*.css", "maxSize": "50kb", "compression": "gzip"}], "pnpm": {"overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}}