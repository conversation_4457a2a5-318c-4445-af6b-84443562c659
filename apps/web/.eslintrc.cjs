/** @type {import("eslint").Linter.Config} */
module.exports = {
  root: true,
  extends: ['@nordvik/eslint-config', 'next/core-web-vitals'],
  parserOptions: {
    project: true,
  },
  ignorePatterns: [
    // Ignore dotfiles
    '.*.js',
    'tailwind.config.ts',
    'postcss.config.js',
    'nordvik-client.ts',
    'next.config.js',
    'scripts/**',
    // ignore if generated in the name
    '*generated*.*',
  ],
  globals: {
    JSX: true,
    React: true,
    NodeJS: true,
  },
  overrides: [
    {
      files: ['./turbo/generators/**'],
    },
    {
      files: ['*.stories.tsx'],
      rules: {
        // We want to allow Storybook stories to export default story configs
        'import/no-anonymous-default-export': 'off',
      },
    },
  ],
  rules: {
    // We don't always want next/image
    '@next/next/no-img-element': 'off',
  },
  overrides: [
    {
      files: ['*.stories.tsx'],
      rules: {
        // We don't always want next/image
        'import/no-anonymous-default-export': 'off',
      },
    },
  ],
}
