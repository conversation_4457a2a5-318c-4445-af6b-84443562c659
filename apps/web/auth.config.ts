import { gql } from 'graphql-request'
import type { NextAuthConfig, Session } from 'next-auth'
import type { AdapterUser } from 'next-auth/adapters'
import { cookies } from 'next/headers'

import { posthog } from '@/lib/analytics/posthog-node-client'
import { executeRawQuery } from '@/server/nordvik-client-adaptor'

export const authConfig = {
  trustHost: true,
  pages: {
    signIn: '/login',
    verifyRequest: '/login',
  },
  callbacks: {
    async jwt({ token, account }) {
      if (account?.provider === 'credentials') {
        token.credentials = true
      }
      return token
    },

    async signIn({ user, account }) {
      let isValid = false
      const distinctId = user.id ?? account?.userId

      if (!user.email) return false

      const isNordvikEmail = Boolean(user.email?.endsWith('@nordvikbolig.no'))
      const isUnfoldEmail = Boolean(user.email?.endsWith('@unfold.no'))
      const isCorpcomEmail = Boolean(user.email?.endsWith('@corpcom.no'))
      const isTechEmail = user.email === '<EMAIL>'

      const cookieStore = await cookies()

      if (isTechEmail) {
        cookieStore.set('is_tech', 'true')
      } else {
        cookieStore.delete('is_tech')
      }

      if (isNordvikEmail) {
        const { employee } = await executeRawQuery<{
          employee: {
            id: string
          }
        }>(
          gql`
            query Employee($email: String) {
              employee(email: $email, all: true) {
                id
              }
            }
          `,
          {
            email: user.email,
          },
        )

        isValid = Boolean(employee)
      } else {
        isValid = isUnfoldEmail || isCorpcomEmail
      }

      if (!isValid) {
        return false
      }

      if (distinctId) {
        posthog?.capture({
          event: 'user_login',
          distinctId,
          properties: {
            login_method: account?.provider, // email | passkey
          },
        })
      }

      return true
    },
    authorized({ auth, request }) {
      const isLoggedIn = Boolean(auth?.user)
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- We need to check if nextUrl is defined
      const isNotOnLogin = !request.nextUrl.pathname?.startsWith('/login')
      if (isNotOnLogin) {
        if (isLoggedIn) return true
        return false
      } else if (isLoggedIn) {
        return Response.redirect(new URL('/nyheter', request.nextUrl))
      }
      return true
    },
    session(params) {
      const { session, user } = params as {
        session: Session
        user: AdapterUser
      }
      return {
        ...session,
        user: {
          ...session.user,
          ...user,
        },
      }
    },
  },
  providers: [],
} satisfies NextAuthConfig
