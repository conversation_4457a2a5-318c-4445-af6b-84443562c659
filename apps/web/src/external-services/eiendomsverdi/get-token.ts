import { get, set } from '@/db/kv'
import { HOUR } from '@/db/util'

export async function getOAuthToken(clientId, clientSecret) {
  const cachedToken = await get<string>('ev_token')

  if (cachedToken) {
    return cachedToken
  }

  console.info('EV token expired, fetching new OAuth token')

  const tokenUrl = process.env.EV_TOKEN_URL

  if (!tokenUrl) {
    console.error('Missing EV_TOKEN_URL')
    throw new Error('Missing EV_TOKEN_URL')
  }

  const params = new URLSearchParams()
  params.append('grant_type', 'client_credentials')
  params.append('client_id', clientId)
  params.append('client_secret', clientSecret)

  const response = await fetch(tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params,
    next: {
      revalidate: HOUR * 20,
    },
  })

  if (!response.ok) {
    console.error('Error fetching OAuth token', await response.text())
    return null
  }

  const data = await response.json()
  const token = data.access_token

  await set('ev_token', token, HOUR * 24 - HOUR / 2)

  return token
}
