import React from 'react'

import OldOARevisitEmail from '../old-oa-revisit'

import ReactEmailPreviewRender from './ReactEmailPreviewRender'

export default {
  title: 'Email',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender height="500px">
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const OldOARevisit = () => {
  return (
    <OldOARevisitEmail
      brokerFirstName={'Nora'}
      contactName={'Unfoldsen'}
      address={'Testveien 1'}
      daysSinceLastVisit={2}
      estateId={'0A8D9ADB-72BD-4E61-8737-FFDFD51D522F'}
    />
  )
}
