import React from 'react'

import SendEtakstRemindersEmail from '../send-etakst-reminders'

import ReactEmailPreviewRender from './ReactEmailPreviewRender'

export default {
  title: 'Email',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const SendEtakstReminders = () => {
  return (
    <SendEtakstRemindersEmail
      address={'Testveien 1'}
      name="<PERSON> Unfoldsen"
      vitecLink="/"
    />
  )
}
