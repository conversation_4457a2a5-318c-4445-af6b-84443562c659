import { DetailedComponent } from '@nordvik/ui/story-blocks'

type Props = {
  html: string
}

const EmailStoryPreview = ({ html }: Props) => {
  const parser = new DOMParser()
  const emailDom = parser.parseFromString(html, 'text/html')
  const title = emailDom.querySelector('title')?.textContent

  return (
    <div className="flex flex-col gap-5 p-4">
      <div className="flex justify-between items-end">
        <p className="typo-body-md font-medium pl-3">
          Emne: <span className="font-normal">{title}</span>
        </p>
      </div>
      <DetailedComponent name="Email Preview Light">
        <div
          dangerouslySetInnerHTML={{ __html: html }}
          className="rounded-sm border border-muted mt-1"
          style={{ background: 'white', color: 'black' }}
        />
      </DetailedComponent>
      <DetailedComponent name="Email Preview Dark">
        <div
          dangerouslySetInnerHTML={{ __html: html }}
          className="rounded-sm border border-muted mt-1"
          style={{ background: 'black', color: 'white' }}
        />
      </DetailedComponent>
    </div>
  )
}

export default EmailStoryPreview
