import React from 'react'

import EmailSignature from '../components/signature-layout'
import SignedOfferEmail from '../signed_offer_mail_content'

import ReactEmailPreviewRender from './ReactEmailPreviewRender'

export default {
  title: 'Email',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender height="1200px">
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const SignedOffer = () => {
  return (
    <SignedOfferEmail
      fornavn="Nora"
      isCompany={false}
      signature={
        <EmailSignature
          slug={'slug'}
          image={
            'https://d1j4wdkidt72cf.cloudfront.net/employees/nora-unfoldsen-657130402191-small.jpg'
          }
          name={'Nora Unfoldsen'}
          title={'Eiendomsmeglerfullmektig'}
          mobilePhone={'12345678'}
          email={'<EMAIL>'}
        />
      }
    />
  )
}
