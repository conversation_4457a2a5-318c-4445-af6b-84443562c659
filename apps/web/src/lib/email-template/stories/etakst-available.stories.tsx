import React from 'react'

import EtakstAvailableEmail from '../etakst-available.tsx'

import ReactEmailPreviewRender from './ReactEmailPreviewRender'

export default {
  title: 'Email / Etakst available',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const Default = () => {
  return <EtakstAvailableEmail authenticatedLink={''} withStorebrand />
}

export const WithContentOverride = () => {
  return (
    <EtakstAvailableEmail
      authenticatedLink={''}
      withStorebrand={true}
      customerName={'Nora Unfoldsen'}
      estateName={'Testveien 1'}
      title={'My custom title'}
      templateOverrideHtml="<p>Custom text goes here...</p><p>Second paragraph</p>"
    />
  )
}
