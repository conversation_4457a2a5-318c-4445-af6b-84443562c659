import React from 'react'

import NotifyBrokerOfLeadsEmail from '../notify-broker-of-leads'

import ReactEmailPreviewRender from './ReactEmailPreviewRender'

export default {
  title: 'Email',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const NotifyBrokerOfLeads = () => {
  return (
    <NotifyBrokerOfLeadsEmail
      isValuation={true}
      address={'Testveien 1'}
      brokerName="<PERSON> Unfoldsen"
    />
  )
}
