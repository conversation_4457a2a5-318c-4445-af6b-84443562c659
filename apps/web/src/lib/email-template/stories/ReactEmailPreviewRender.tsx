import { CSSProperties, ReactNode, useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'

import { DetailedComponent } from '@nordvik/ui/story-blocks'

type Props = {
  children: ReactNode
  height?: CSSProperties['height']
}

const ReactEmailPreviewRender = ({ children, height = '40vh' }: Props) => {
  /** Render logic */
  const [rerender, forceRerender] = useState<HTMLIFrameElement | null>(null)
  const contentRefLight = useRef<HTMLIFrameElement>(null)
  const contentRefDark = useRef<HTMLIFrameElement>(null)

  useEffect(() => {
    forceRerender(contentRefLight.current)
  }, [])

  /** Theme logic */
  useEffect(() => {
    const htmlElementDark =
      contentRefDark.current?.contentDocument?.querySelector('html')
    if (!htmlElementDark) return
    {
      htmlElementDark.style.color = 'white'
      htmlElementDark.style.background = 'black'
    }
  }, [rerender])

  return (
    <div className="flex flex-col gap-5">
      <DetailedComponent name="Email Preview Light">
        <iframe
          ref={contentRefLight}
          className="w-full rounded-sm border border-muted mt-1"
          style={{
            height,
            boxSizing: 'content-box',
          }}
        >
          {contentRefLight.current?.contentWindow?.document?.body &&
            createPortal(
              children,
              contentRefLight.current?.contentWindow?.document?.body,
            )}
        </iframe>
      </DetailedComponent>

      <DetailedComponent name="Email Preview Dark">
        <iframe
          ref={contentRefDark}
          className="w-full rounded-sm border border-muted mt-1"
          style={{
            height,
            boxSizing: 'content-box',
          }}
        >
          {contentRefDark.current?.contentWindow?.document?.body &&
            createPortal(
              children,
              contentRefDark.current?.contentWindow?.document?.body,
            )}
        </iframe>
      </DetailedComponent>
    </div>
  )
}

export default ReactEmailPreviewRender
