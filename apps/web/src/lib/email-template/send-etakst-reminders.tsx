import { Text, render } from '@react-email/components'

import Button from './components/button'
import { emailTypo } from './components/email-typo'
import ReactEmailWrapper from './components/react-email-wrapper'

type Props = {
  name: string
  address: string | undefined
  vitecLink?: string
}

export default function SendEtakstRemindersEmail({
  name,
  address,
  vitecLink,
}: Props) {
  return (
    <ReactEmailWrapper>
      <Text style={emailTypo.bodyMd}>Hei, {name}</Text>
      <Text style={emailTypo.bodyMd}>
        E-taksten for {address} er ikke sendt til kunden. Du må ha opprettet
        e-takst hos Eiendomsverdi og huket av sjekklistepunktet &quot;Risiko for
        hvitvasking vurdert&quot; i Next for at e-taksten skal bli sendt.
      </Text>
      {vitecLink && <Button url={vitecLink}>Gå til Next</Button>}
    </ReactEmailWrapper>
  )
}

export const renderSendEtakstRemindersEmail = (props: Props) =>
  render(<SendEtakstRemindersEmail {...props} />)
