import { Container, Row, Text, render } from '@react-email/components'

import { emailTypo } from './components/email-typo'
import Footer from './components/footer'
import ReactEmailWrapper from './components/react-email-wrapper'

type Props = { token: string }

export default function VerificationCodeTemplate({ token }: Props) {
  return (
    <ReactEmailWrapper centeredHeader withBackground withHeader>
      <Container>
        <Row>
          <Text style={{ ...emailTypo.bodyXs, textAlign: 'center' }}>
            DIN ENGANGSKODE
          </Text>
          <Text
            style={{
              width: 'fit-content',
              margin: '12px auto 16px',
              background: '#f1f6fd',
              color: '#000',
              fontFamily: 'Basel Grotesk, sans-serif',
              fontSize: '32px',
              fontWeight: '535',
              padding: '12px 16px',
              borderRadius: '4px',
            }}
          >
            {token || 6052}
          </Text>
          <Text
            style={{
              ...emailTypo.bodyMd,
              textAlign: 'center',
            }}
          >
            Kopier engangskoden inn i feltet presentert i Nordvik Megler.
          </Text>
        </Row>
        <Footer
          centered
          text="Denne e-posten ble automatisk generert etter forespørsel: Nordvik Megler"
        />
      </Container>
    </ReactEmailWrapper>
  )
}

export const renderVerificationCodeEmail = ({ token }: Props) =>
  render(<VerificationCodeTemplate token={token} />)
