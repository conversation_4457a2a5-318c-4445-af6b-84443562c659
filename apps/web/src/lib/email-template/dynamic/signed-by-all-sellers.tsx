import { Text, render } from '@react-email/components'

import { DATE_TIME_FORMAT } from '@/lib/constants'
import { formatDate } from '@/lib/dates'
import { getBaseUrl } from '@/lib/getBaseUrl'

import Button from '../components/button'
import { emailTypo } from '../components/email-typo'
import ReactEmailWrapper from '../components/react-email-wrapper'

type Person = {
  first_name: string | null
  last_name: string | null
  signed_at: Date | null
}
type Props = {
  sellers: Person[]
  brokers: Person[]
  address?: string | null
  estateId?: string | null
}

export default function SignedByAllSellersEmail({
  sellers,
  brokers,
  address,
  estateId,
}: Props) {
  return (
    <ReactEmailWrapper>
      <Text style={{ ...emailTypo.displaySm }}>
        {address ? address : 'Ukjent adresse'}
      </Text>
      <Text
        style={{
          ...emailTypo.bodyMdBold,
        }}
      >
        Signeringsstatus:
      </Text>
      <table align="center" width="100%">
        <tr>
          <td
            colSpan={3}
            style={{
              ...emailTypo.bodyMdBold,
              padding: '8px',
              textAlign: 'left',
              backgroundColor: '#f0f0f0',
              color: '#000',
              borderRadius: '4px',
              margin: 0,
            }}
          >
            Selger{sellers.length > 1 ? 'e' : ''}
          </td>
        </tr>
        {sellers.map((seller) => (
          <tr key={'seller' + seller.first_name}>
            <td
              style={{
                ...emailTypo.bodyMd,
                padding: '8px',
                textAlign: 'left',
              }}
            >
              {seller.first_name} {seller.last_name}
            </td>
            <td
              style={{
                ...emailTypo.bodyMd,
                padding: '8px',
                textAlign: 'left',
              }}
            >
              {seller.signed_at ? 'Signert' : 'Ikke signert'}
            </td>
            <td
              style={{
                ...emailTypo.bodyMd,
                padding: '8px',
                textAlign: 'right',
              }}
            >
              {seller.signed_at &&
                formatDate(seller.signed_at, DATE_TIME_FORMAT)}
            </td>
          </tr>
        ))}
      </table>

      <table align="center" width="100%" style={{ marginTop: '16px' }}>
        <tr>
          <td
            colSpan={3}
            style={{
              ...emailTypo.bodyMdBold,
              padding: '8px',
              textAlign: 'left',
              backgroundColor: '#f0f0f0',
              color: '#000',
              borderRadius: '4px',
              margin: 0,
            }}
          >
            Megler{brokers.length > 1 ? 'e' : ''}
          </td>
        </tr>
        {brokers.map((Broker) => (
          <tr key={'Broker' + Broker.first_name}>
            <td
              style={{
                ...emailTypo.bodyMd,
                padding: '8px',
                textAlign: 'left',
              }}
            >
              {Broker.first_name} {Broker.last_name}
            </td>
            <td
              style={{
                ...emailTypo.bodyMd,
                padding: '8px',
                textAlign: 'left',
              }}
            >
              {Broker.signed_at ? 'Signert' : 'Ikke signert'}
            </td>
            <td
              style={{
                ...emailTypo.bodyMd,
                padding: '8px',
                textAlign: 'right',
              }}
            >
              {Broker.signed_at &&
                formatDate(Broker.signed_at, DATE_TIME_FORMAT)}
            </td>
          </tr>
        ))}
      </table>
      <Button url={`${getBaseUrl()}/oppdrag/detaljer/${estateId}`}>
        Se oppdraget i Nordvik Megler
      </Button>
    </ReactEmailWrapper>
  )
}

export const renderSignedByAllSellersEmail = (props: Props) =>
  render(<SignedByAllSellersEmail {...props} />)
