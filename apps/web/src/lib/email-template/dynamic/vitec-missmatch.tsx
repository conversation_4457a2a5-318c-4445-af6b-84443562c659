import { ValidateVitecDataSync } from '@befaring/lib/validate-vitec-data-sync'
import { Text, render } from '@react-email/components'

import Button from '../components/button'
import { emailTypo } from '../components/email-typo'
import ReactEmailWrapper from '../components/react-email-wrapper'

type Props = {
  validation: ValidateVitecDataSync
  address?: string | null
  linkToNext?: string | null
  marketingPackage?: string
}

export default function VitecMissmatchEmail({
  validation: { isSynced, ...validatedFields },
  address,
  linkToNext,
  marketingPackage,
}: Props) {
  // Check for any field mismatches
  const invalidSuggestedPrice =
    validatedFields.suggestedPriceVitec !==
    validatedFields.suggestedPriceDatabase

  const invalidFeePercentage =
    validatedFields.feePercentageVitec !== validatedFields.feePercentageDatabase

  const invalidCommission =
    validatedFields.commissionVitec !== validatedFields.commissionDatabase

  // For conditional checks
  const { type } = validatedFields

  return (
    <ReactEmailWrapper>
      <div>
        <Text style={{ ...emailTypo.displaySm }}>
          {address ? address : 'Ukjent adresse'}
        </Text>
        {!isSynced && (
          <>
            <Text style={{ ...emailTypo.bodyMdBold }}>
              Du må oppdatere Next med tallene fra den signerte oppdragsavtalen:
            </Text>

            {invalidSuggestedPrice && (
              <Text style={{ ...emailTypo.bodyMd, margin: '4px 0' }}>
                Prisantydning: Endre fra{' '}
                {validatedFields.suggestedPriceVitec?.toLocaleString('no-NO')}{' '}
                kr til{' '}
                {validatedFields.suggestedPriceDatabase?.toLocaleString(
                  'no-NO',
                )}{' '}
                kr
              </Text>
            )}

            {type === 1 && invalidFeePercentage && (
              <Text style={{ ...emailTypo.bodyMd, margin: '4px 0' }}>
                Provisjon: Endre fra{' '}
                {validatedFields.feePercentageVitec?.toLocaleString('no-NO')} %
                til{' '}
                {validatedFields.feePercentageDatabase?.toLocaleString('no-NO')}{' '}
                %
              </Text>
            )}

            {type !== 1 && invalidCommission && (
              <Text style={{ ...emailTypo.bodyMd, margin: '4px 0' }}>
                Budsjett: Endre fra{' '}
                {validatedFields.commissionVitec?.toLocaleString('no-NO')} kr
                til{' '}
                {validatedFields.commissionDatabase?.toLocaleString('no-NO')} kr
              </Text>
            )}

            <Text style={{ ...emailTypo.bodyMd }}>
              Hvis du ikke oppdaterer tallene i Next vil det bli feil i både
              salgsoppgaven og faktureringen. Andre endringer du potensielt
              gjorde i oppdragsavtalen har blitt oppdatert automatisk.
            </Text>

            {linkToNext && <Button url={linkToNext}>Gå til Next</Button>}
          </>
        )}
        {marketingPackage && (
          <>
            <Text style={emailTypo.bodyMdBold}>
              Det ble også valgt ekstra markedsføring på oppdraget. Legg det inn
              på de nødvendige plattformene.
            </Text>
            <Text style={emailTypo.bodyMd}>
              Markedsføringsprodukt: {marketingPackage}
            </Text>
          </>
        )}
      </div>
    </ReactEmailWrapper>
  )
}

export const renderVitecMissmatchEmail = (props: Props) =>
  render(<VitecMissmatchEmail {...props} />)
