import { render } from '@react-email/components'

import ReactEmailWrapper from '../components/react-email-wrapper'

type Props = {
  html: string
}

export default function SendToClientEmail({ html }: Props) {
  return (
    <ReactEmailWrapper>
      <div dangerouslySetInnerHTML={{ __html: html }} />
    </ReactEmailWrapper>
  )
}

export const renderSendToClientEmail = (props: Props) =>
  render(<SendToClientEmail {...props} />)
