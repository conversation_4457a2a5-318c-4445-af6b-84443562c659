import React from 'react'

import ReactEmailPreviewRender from '../../stories/ReactEmailPreviewRender'
import VitecMissmatchEmail from '../vitec-missmatch'

export default {
  title: 'Email / Dynamic',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const VitecMissmatch = () => {
  return (
    <VitecMissmatchEmail
      validation={{
        type: 1,
        suggestedPriceVitec: 1,
        suggestedPriceDatabase: 10,
        feePercentageVitec: 110,
        feePercentageDatabase: 100,
        commissionVitec: 120,
        commissionDatabase: 1200,
        isSynced: false,
      }}
      address="Testveien 1, 0000 Oslo"
      linkToNext="http://google.com"
      marketingPackage="Nordvik Ekstra"
    />
  )
}
