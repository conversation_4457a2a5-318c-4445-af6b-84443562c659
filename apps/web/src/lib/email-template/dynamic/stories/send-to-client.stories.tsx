import { eastwoodEmail } from '@/components/message-editors/stories/utils'
import { useQuill } from '@/components/rich-text-editor/use-quill.hook'
import { getEmailContent } from '@/components/send-offer-dialog/utils'

import { sendToClientContent } from '../../send_to_client_content'
import ReactEmailPreviewRender from '../../stories/ReactEmailPreviewRender'
import SendToClientEmail from '../send-to-client'

export default {
  title: 'Email / Dynamic',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const SendToClient = () => {
  const { Quill } = useQuill()
  const emailContent = JSON.parse(eastwoodEmail.emailContent).map((content) =>
    getEmailContent(content, Quill),
  )

  return (
    <SendToClientEmail
      html={sendToClientContent({
        url: '/',
        content: emailContent,
        linkToOfferText: 'Gå til tilbud',
      })}
    />
  )
}
