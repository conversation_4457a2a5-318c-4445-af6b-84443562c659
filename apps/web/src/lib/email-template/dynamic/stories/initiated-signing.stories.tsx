import React from 'react'

import ReactEmailPreviewRender from '../../stories/ReactEmailPreviewRender'
import InitiatedSigningEmail from '../initiated-signing'

export default {
  title: 'Email / Dynamic',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const InitiatedSigning = () => {
  return (
    <InitiatedSigningEmail
      initiatedBy="Nora Unfoldsen"
      estateId="0A8D9ADB-72BD-4E61-8737-FFDFD51D522F"
    />
  )
}
