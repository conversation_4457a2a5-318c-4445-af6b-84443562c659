import React from 'react'

import ReactEmailPreviewRender from '../../stories/ReactEmailPreviewRender'
import SellerFormsReminderEmail from '../seller-forms-reminder'

export default {
  title: 'Email / Dynamic',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender height="700px">
          <Story />
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const SellerFormsReminder = () => {
  return (
    <SellerFormsReminderEmail.email
      html={SellerFormsReminderEmail.getTemplateText('<PERSON> Unfoldsen')}
      tasks={[
        { title: 'Oppgave 1', status: 'COMPLETE' },
        { title: 'Oppgave 2', status: 'STARTED' },
        { title: 'Oppgave 3', status: 'NONE' },
      ]}
    />
  )
}
