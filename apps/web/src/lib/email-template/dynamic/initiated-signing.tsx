import { Container, Text, render } from '@react-email/components'

import { getBaseUrl } from '@/lib/getBaseUrl'

import Button from '../components/button'
import { emailTypo } from '../components/email-typo'
import ReactEmailWrapper from '../components/react-email-wrapper'

type Props = { estateId: string; initiatedBy: string }

export default function InitiateSigningEmail({ estateId, initiatedBy }: Props) {
  return (
    <ReactEmailWrapper>
      <Container>
        <Text style={emailTypo.bodyMd}>Hei,</Text>
        <Text style={emailTypo.bodyMd}>
          {initiatedBy} har nå begynt signeringen av oppdragsavtalen. Du får
          beskjed når alle parter har signert avtalen.
        </Text>
        <Button url={`${getBaseUrl()}/oppdrag/detaljer/${estateId}`}>
          Se oppdraget i Nordvik Megler
        </Button>
      </Container>
    </ReactEmailWrapper>
  )
}

export const renderInitiateSigningEmail = (props: Props) =>
  render(<InitiateSigningEmail {...props} />)
