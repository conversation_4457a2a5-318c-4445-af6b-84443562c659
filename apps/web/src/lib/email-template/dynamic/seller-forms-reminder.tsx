import { render } from '@react-email/components'
import { ReactNode } from 'react'

import { cn } from '@nordvik/theme/cn'

import { GQLBroker } from '@/api/generated-client'
import { EstateSellerTasks } from '@/app/(protected)/(sidebar)/oppdrag/ny/[view]/[status]/components/use-estates-document-statuses'

import Button from '../components/button'
import EmailLink from '../components/email-link'
import { emailTypo } from '../components/email-typo'
import ReactEmailWrapper from '../components/react-email-wrapper'
import EmailSignature from '../components/signature-layout'

type Props = {
  html: string
  tasks: EstateSellerTasks
  broker?: Pick<GQLBroker, 'name' | 'email' | 'title' | 'employeeId' | 'image'>
}

const editableStyle = cn(
  'bg-background-root focus-within:bg-gray-muted hover:bg-gray-muted rounded-sm border-none ring-transparent outline-none [&>p]:my-4',
)

function SellerFormsReminderContent({
  html,
  onChange,
  editable = false,
  tasks,
  broker,
}: Props &
  (
    | { onChange: (html: string) => void; editable: true }
    | { onChange?: never; editable?: false }
  )) {
  return (
    <div>
      <div
        className={editable ? editableStyle : undefined}
        contentEditable={editable}
        dangerouslySetInnerHTML={{ __html: html }}
        onInput={
          editable && onChange
            ? (val) => onChange(val.currentTarget.innerHTML)
            : undefined
        }
      />
      <EmailLink url={''}>Følg status i appen</EmailLink>
      <div
        style={{
          borderRadius: '4px',
          border: '1px solid #E1E6E6',
          marginTop: '24px',
          marginBottom: '24px',
          padding: '16px',
        }}
      >
        <table
          style={{
            width: '100%',
          }}
        >
          <tbody>
            {tasks.map((task, idx) => (
              <tr
                key={task.title}
                style={{
                  borderBottom:
                    idx === tasks.length - 1 ? 'none' : '1px solid #E1E6E6',
                }}
              >
                <td
                  style={{
                    paddingRight: '8px',
                    verticalAlign: 'top',
                    paddingTop: '8px',
                    paddingBottom: '8px',
                    width: '32px',
                  }}
                >
                  <img
                    src={getStatusImage(task.status)}
                    alt={task.status}
                    style={{ width: '24px', height: '24px' }}
                  />
                </td>
                <td
                  style={{
                    width: 'auto',
                    paddingTop: '8px',
                    paddingBottom: '8px',
                    verticalAlign: 'top',
                  }}
                >
                  <span style={emailTypo.bodySmBold}>{task.title}</span>
                  <div>
                    <span
                      style={{
                        ...emailTypo.bodyXs,
                        color: '#9298A0',
                      }}
                    >
                      {task.status === 'COMPLETE' ? 'Utført' : 'Ikke utført'}
                    </span>
                  </div>
                  <div>
                    <span style={{ ...emailTypo.bodySm }}>
                      {getTaskDescription(task)}
                    </span>
                  </div>
                </td>
                {task.title === 'Selgers intervju' && (
                  <td
                    style={{
                      width: 'fit-content',
                      verticalAlign: 'top',
                      textAlign: 'right',
                    }}
                  >
                    <Button url={''} size="xs" style={{ margin: 0 }}>
                      Fyll ut skjema
                    </Button>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {EmailSignature({ ...broker, image: broker?.image?.small })}
    </div>
  )
}

const getTaskDescription = (
  task: EstateSellerTasks[number],
): ReactNode | undefined => {
  if (task.status === 'COMPLETE') return

  switch (task.title) {
    case 'Selgers intervju':
      return ''
    case 'Energiattest':
      return <p>Fyll ut informasjon fra Enova</p>
    case 'Egenerklæring':
      return (
        <p>
          Du har mottatt en e-post fra Storebrand.
          <br /> Emne: Egenerklæringsskjema er klart til utfylling
          <br />
          Avsender: <EMAIL>
        </p>
      )
    case 'Sikringsobligasjon':
      return (
        <p>
          Du har mottatt en e-post fra Ambita.
          <br /> Emne: Du har et nytt signeringsoppdrag
          <br />
          Avsender: <EMAIL>
        </p>
      )
    case 'Takstrapport':
      return ''
    case 'Foto':
      return ''
    default:
      ''
  }
}

const getStatusImage = (
  status: EstateSellerTasks[number]['status'],
): string => {
  if (status === 'COMPLETE')
    return 'https://nordvik-omega.s3.eu-north-1.amazonaws.com/assets/done.png'
  if (status === 'STARTED')
    return 'https://nordvik-omega.s3.eu-north-1.amazonaws.com/assets/started.png'
  return 'https://nordvik-omega.s3.eu-north-1.amazonaws.com/assets/none.png'
}

export const getTemplateText = (
  sellerName: string,
) => `<p>Hei, ${sellerName}</p>
<p> Vi er godt i gang med å gjøre klar salgsoppgaven, men vi mangler noen ting fra dere for å komme videre. Her er en oversikt over hva dere har gjort og hva som mangler!</p>
<p>Har dere spørsmål angående utfylling er det bare å ta kontakt!</p>
`

export function SellerFormsReminder({ html, tasks }: Props) {
  return (
    <ReactEmailWrapper>
      <SellerFormsReminderContent html={html} tasks={tasks} />
    </ReactEmailWrapper>
  )
}

function EditablePreview(props: Props & { onChange: (html: string) => void }) {
  return <SellerFormsReminderContent {...props} editable />
}

export const renderSellerFormsReminder = (props: Props) =>
  render(<SellerFormsReminder {...props} />)

const SellerFormsReminderEmail = {
  getTemplateText: getTemplateText,
  EditablePreview,
  render: renderSellerFormsReminder,
  email: SellerFormsReminder,
}

export default SellerFormsReminderEmail
