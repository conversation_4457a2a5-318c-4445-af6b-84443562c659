import { Container, Heading, Text, render } from '@react-email/components'

import Button from './components/button'
import { emailTypo } from './components/email-typo'
import Footer from './components/footer'
import ReactEmailWrapper from './components/react-email-wrapper'

type Props = {
  brokerFirstName?: string
  contactName?: string
  address: string
  daysSinceLastVisit: number
  title?: string
  estateId?: string
}

export default function OldOARevisitEmail({
  brokerFirstName,
  contactName,
  address,
  daysSinceLastVisit,
  title = 'Oppdragsavtale åpnet igjen',
  estateId,
}: Props) {
  const inactivityText =
    daysSinceLastVisit >= 14 ? 'to uker' : `${daysSinceLastVisit} dager`

  const baseUrl =
    process.env.NEXT_PUBLIC_URL || process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL
      ? process.env.NEXT_PUBLIC_URL ||
        `https://${process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL}`
      : undefined
  const detailUrl =
    estateId && baseUrl ? `${baseUrl}/oppdrag/detaljer/${estateId}` : undefined

  return (
    <ReactEmailWrapper>
      <Container>
        <Heading as="h1" style={emailTypo.displaySm}>
          {title}
        </Heading>
        <Text style={emailTypo.bodyMd}>Hei {brokerFirstName ?? ''},</Text>
        <Text style={emailTypo.bodyMd}>
          For en time siden var {contactName || 'selgeren'} på {address} inne og
          åpnet oppdragsavtalen på nytt.
        </Text>
        <Text style={emailTypo.bodyMd}>
          Det er nå {inactivityText} siden de sist var inne.
        </Text>
        <Text style={emailTypo.bodyMd}>
          Det kan være et godt tidspunkt å ta kontakt. Kanskje de er klar for å
          selge?
        </Text>

        {detailUrl && <Button url={detailUrl}>Åpne oppdragssiden</Button>}

        <Footer text="Automatisk varsling ved 14 dagers inaktivitet på oppdragssiden" />
      </Container>
    </ReactEmailWrapper>
  )
}

export const renderOldOARevisitEmail = (props: Props) =>
  render(<OldOARevisitEmail {...props} />)
