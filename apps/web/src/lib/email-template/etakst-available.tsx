import { Container, Heading, Text, render } from '@react-email/components'

import Button from './components/button'
import { emailTypo } from './components/email-typo'
import Footer from './components/footer'
import ReactEmailWrapper from './components/react-email-wrapper'

type Props = {
  withStorebrand?: boolean
  customerName?: string
  estateName?: string
  authenticatedLink: string
  title?: string
  templateOverrideHtml?: string
}

export default function EtakstAvailableEmail({
  withStorebrand = false,
  customerName,
  estateName,
  authenticatedLink,
  title = 'E-takst er klar',
  templateOverrideHtml,
}: Props) {
  const propertyText = estateName ? `for ${estateName}` : ''

  if (templateOverrideHtml) {
    return (
      <ReactEmailWrapper>
        <Container>
          <Heading as="h1" style={emailTypo.displaySm}>
            {title}
          </Heading>
          <div dangerouslySetInnerHTML={{ __html: templateOverrideHtml }} />

          {withStorebrand && (
            <Text style={emailTypo.bodyMd}>
              Du har også takket ja til å bli kontaktet av Storebrand om
              finansiering. De vil snart kontakte deg på telefon med et
              uforpliktende tilbud.
            </Text>
          )}
          <div style={{ textAlign: 'center' }}>
            <Button url={authenticatedLink}>Gå til e-takst</Button>
          </div>
          <Footer text="Denne e-posten ble automatisk generert på vegne av Nordvik Megler" />
        </Container>
      </ReactEmailWrapper>
    )
  }

  return (
    <ReactEmailWrapper>
      <Container>
        <Heading as="h1" style={emailTypo.displaySm}>
          {title}
        </Heading>
        <Text style={emailTypo.bodyMd}>Hei, {customerName}</Text>
        <Text style={emailTypo.bodyMd}>
          Nå er e-taksten {propertyText} klar. Har du noen spørsmål er det bare
          å ringe eller sende e-post.
        </Text>
        <Text style={emailTypo.bodyMd}>
          Hvis du vurderer å selge i tiden fremover så håper jeg du har fått et
          godt inntrykk av meg, og at du tar kontakt.
        </Text>

        {withStorebrand && (
          <Text style={emailTypo.bodyMd}>
            Du har også takket ja til å bli kontaktet av Storebrand om
            finansiering. De vil snart kontakte deg på telefon med et
            uforpliktende tilbud.
          </Text>
        )}
        <div style={{ textAlign: 'center' }}>
          <Button url={authenticatedLink}>Gå til e-takst</Button>
        </div>
        <Footer text="Denne e-posten ble automatisk generert på vegne av Nordvik Megler" />
      </Container>
    </ReactEmailWrapper>
  )
}

export const renderEtakstAvailableEmail = (props: Props) =>
  render(<EtakstAvailableEmail {...props} />)
