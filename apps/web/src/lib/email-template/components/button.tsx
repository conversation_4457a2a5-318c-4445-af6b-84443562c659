import { Link } from '@react-email/components'
import { CSSProperties, ReactNode } from 'react'

import { primitives } from '@nordvik/theme/variables'

import { emailTypo } from './email-typo'

// import { primitives } from '@nordvik/theme/variables'

type Props = {
  children: ReactNode
  url: string
  size?: 'xs' | 'sm' | 'md'
  style?: CSSProperties | undefined
}

const Button = ({ children, url, size = 'md', style }: Props) => {
  const sizes = {
    xs: {
      fontSize: '12px',
      padding: '2px 12px',
    },
    sm: {
      fontSize: '14px',
      padding: '2px 16px',
    },
    md: {
      fontSize: '16px',
      padding: '2px 24px',
    },
  }

  return (
    <Link
      href={url}
      target="_blank"
      {...{ 'mc:disable-tracking': '' }}
      style={{
        display: 'inline-block',
        color: '#fff',
        textDecoration: 'none',
        borderRadius: '50px',
        ...sizes[size],
        border: '0px',
        fontWeight: 'medium',
        width: 'max-content',
        margin: '16px 0',
        backgroundColor: primitives.teal[700],
        ...style,
      }}
    >
      <span
        style={{
          ...emailTypo.bodyMdBold,
          color: '#FFFFFF',
          display: 'inline-block',
          verticalAlign: 'middle',
          margin: size === 'xs' ? '2px 0' : size === 'sm' ? '4px 0' : '8px 0',
          fontSize: sizes[size].fontSize,
        }}
      >
        {children}
      </span>
    </Link>
  )
}

export default Button
