import { Link } from '@react-email/components'
import { ReactNode } from 'react'

import { primitives } from '@nordvik/theme/variables'

import { emailTypo } from './email-typo'

// import { primitives } from '@nordvik/theme/variables'

type Props = {
  children: ReactNode
  url: string
}

const Button = ({ children, url }: Props) => {
  return (
    <Link
      href={url}
      target="_blank"
      {...{ 'mc:disable-tracking': '' }}
      style={{
        display: 'block',
        color: '#fff',
        textDecoration: 'none',
        borderRadius: '50px',
        padding: '2px 24px',
        border: '0px',
        fontWeight: 'medium',
        width: 'max-content',
        margin: '16px 0',
        backgroundColor: primitives.teal[700],
      }}
    >
      <span
        style={{
          ...emailTypo.bodyMdBold,
          color: '#FFFFFF',
          display: 'inline-block',
          verticalAlign: 'middle',
          margin: '8px 0',
        }}
      >
        {children}
      </span>
    </Link>
  )
}

export default But<PERSON>
