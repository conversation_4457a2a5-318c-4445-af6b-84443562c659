import nordvikApi from '@/server/nordvik-client-adaptor'

import EmailSignatureLayout from './signature-layout'

type Props = {
  employeeId?: string
  from: { email: string; name: string }
}

const getEmailSignature = async ({ employeeId, from }: Props) => {
  if (!employeeId) return
  const apiResponse = await nordvikApi.employeeSessionInfo({
    employeeId,
  })

  const employee = apiResponse.employee

  return EmailSignatureLayout({
    ...from,
    ...employee,
    image: employee?.image?.small,
  })
}

export default getEmailSignature
