import React from 'react'

import ReactEmailPreviewRender from '../../stories/ReactEmailPreviewRender'
import { emailTypo } from '../email-typo'
import { ReactEmailBaseWrapper } from '../react-email-wrapper'

export default {
  title: 'Email / Components',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <ReactEmailBaseWrapper>
            <Story />
          </ReactEmailBaseWrapper>
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const Typography = () => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {Object.keys(emailTypo).map((typo: keyof typeof emailTypo) => (
        <div key={typo}>
          <p style={emailTypo.bodySmBold}>{typo}</p>
          <p style={emailTypo[typo]}>
            The quick brown fox jumps over the lazy dog
          </p>
        </div>
      ))}
    </div>
  )
}
