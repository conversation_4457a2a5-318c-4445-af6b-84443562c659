import ReactEmailPreviewRender from '../../stories/ReactEmailPreviewRender'
import EmailLink from '../email-link'
import { ReactEmailBaseWrapper } from '../react-email-wrapper'

export default {
  title: 'Email / Components',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <ReactEmailBaseWrapper>
            <Story />
          </ReactEmailBaseWrapper>
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const Link = () => {
  return <EmailLink url={''}>Click me</EmailLink>
}
