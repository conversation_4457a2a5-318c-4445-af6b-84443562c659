import React from 'react'

import ReactEmailPreviewRender from '../../stories/ReactEmailPreviewRender'
import { ReactEmailBaseWrapper } from '../react-email-wrapper'
import EmailSignature from '../signature-layout'

export default {
  title: 'Email / Components / Signature',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <ReactEmailBaseWrapper>
            <Story />
          </ReactEmailBaseWrapper>
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const Simple = () => {
  return (
    <EmailSignature
      name={'Nora Unfoldsen'}
      title={'Eiendomsmeglerfullmektig'}
      mobilePhone={'12345678'}
      email={'<EMAIL>'}
    />
  )
}

export const Full = () => {
  return (
    <EmailSignature
      slug={'slug'}
      image={
        'https://d1j4wdkidt72cf.cloudfront.net/employees/nora-unfoldsen-657130402191-small.jpg'
      }
      name={'<PERSON> Unfoldsen'}
      title={'Eiendomsmeglerfullmektig'}
      mobilePhone={'12345678'}
      email={'<EMAIL>'}
    />
  )
}
