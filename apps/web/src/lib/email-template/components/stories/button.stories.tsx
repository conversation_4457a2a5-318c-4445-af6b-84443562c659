import ReactEmailPreviewRender from '../../stories/ReactEmailPreviewRender'
import ButtonComponent from '../button'
import { ReactEmailBaseWrapper } from '../react-email-wrapper'

export default {
  title: 'Email / Components',
  parameters: {
    theme: 'light',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <ReactEmailPreviewRender>
          <ReactEmailBaseWrapper>
            <Story />
          </ReactEmailBaseWrapper>
        </ReactEmailPreviewRender>
      </div>
    ),
  ],
}

export const Button = () => {
  return <ButtonComponent url={''}>Click me</ButtonComponent>
}
