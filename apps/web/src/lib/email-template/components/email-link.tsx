import { Link } from '@react-email/components'
import { ReactNode } from 'react'

import { emailTypo } from './email-typo'

type Props = {
  children: ReactNode
  url: string
}

const EmailLink = ({ children, url }: Props) => {
  return (
    <Link
      href={url}
      target="_blank"
      {...{ 'mc:disable-tracking': '' }}
      style={{
        display: 'block',
        textDecoration: 'none',
        fontWeight: 'medium',
        width: 'max-content',
        borderBottom: '1px solid #CCD5D6',
      }}
    >
      <span
        style={{
          ...emailTypo.bodyMd,
          color: '#155356',
          display: 'inline-block',
          verticalAlign: 'middle',
          margin: 0,
        }}
      >
        {children}
      </span>
    </Link>
  )
}

export default EmailLink
