import { Column, Row, Text } from '@react-email/components'

import { formatPhoneNumber } from '@/lib/format-phone-number'

import { emailTypo } from './email-typo'

type Props = {
  slug?: string | null
  image?: string | null
  name?: string | null
  title?: string | null
  mobilePhone?: string | null
  email?: string | null
}
const EmailSignatureLayout = ({
  slug,
  image,
  name,
  title,
  mobilePhone,
  email,
}: Props) => {
  if (!name || !title || !mobilePhone || !email || !image) {
    return (
      <div>
        <Text style={{ ...emailTypo.bodyMd, marginBottom: '8px' }}>
          Med vennlig hilsen
        </Text>
        {name && (
          <Text style={{ ...emailTypo.bodySmBold, margin: 0 }}>
            <strong>{name}</strong>
          </Text>
        )}
        {title && (
          <Text style={{ ...emailTypo.bodyMd, margin: 0 }}>
            <span>{title}</span>
          </Text>
        )}
        <Text style={{ ...emailTypo.bodyMd, margin: 0 }}>
          <a href={`mailto:${email}`}>{email}</a>
        </Text>{' '}
        <Text style={{ ...emailTypo.bodyMd, margin: 0 }}>
          {mobilePhone && formatPhoneNumber(mobilePhone)}
        </Text>
      </div>
    )
  }

  return (
    <div style={{ marginTop: '24px' }}>
      <Text style={emailTypo.bodyMd}>Med vennlig hilsen</Text>
      <Row>
        <Column align="left" valign="middle" width="140">
          <a
            href={`https://www.nordvikbolig.no/megler/${slug}`}
            style={{
              textDecoration: 'none',
              display: 'block',
              minWidth: '140px',
            }}
          >
            {image && (
              <img
                src={image}
                height="110"
                width="110"
                style={{ borderRadius: 0, width: '112px', height: '112px' }}
                alt=""
              />
            )}
          </a>
        </Column>
        <Column
          align="left"
          valign="middle"
          style={{ textDecoration: 'none', border: 'none' }}
        >
          <Text
            style={{
              ...emailTypo.displayXs,
              margin: 0,
            }}
          >
            {name}
          </Text>
          <Text
            style={{
              ...emailTypo.bodyXs,
              marginTop: 0,
              marginBottom: 0,
            }}
          >
            {title}
          </Text>
          <Text style={{ ...emailTypo.bodyXs, marginBottom: 0 }}>
            <span
              style={{
                marginTop: '2px',
                textDecoration: 'none',
                display: 'block',
              }}
            >
              Nordvik
              <br />
            </span>
            <a
              href={`tel:${mobilePhone}`}
              style={{
                marginTop: '2px',
                color: 'inherit',
                textDecoration: 'none',
                display: 'block',
              }}
            >
              {formatPhoneNumber(mobilePhone)}
              <br />
            </a>
            <a
              href={`mailto:${email}`}
              style={{
                marginTop: '2px',
                color: 'inherit',
                textDecoration: 'none',
                display: 'block',
              }}
            >
              {email}
            </a>
          </Text>
        </Column>
      </Row>
      <Row style={{ textDecoration: 'none', marginTop: '16px' }}>
        <Column
          align="left"
          valign="middle"
          style={{ border: 'none' }}
          width={140}
        >
          <Text
            style={{
              margin: 0,
            }}
          >
            <a
              style={{
                textDecoration: 'none',
                display: 'inline-block',
              }}
              href="https://www.nordvikbolig.no"
            >
              <img
                src="https://nordvikbolig-static.s3.eu-north-1.amazonaws.com/email-signature/nordvik-logo-green-2022.png?v=1"
                alt="Nordvik"
                width="110"
              />
            </a>
          </Text>
        </Column>
        <Column
          align="left"
          valign="middle"
          style={{ border: 'none', textAlign: 'left' }}
        >
          <Text
            style={{
              margin: 0,
            }}
          >
            <span style={{ display: 'inline-block' }}>
              <a
                style={{
                  textDecoration: 'none',
                  display: 'inline-block',
                }}
                href="https://www.instagram.com/nordvikbolig/"
              >
                <img
                  src="https://nordvikbolig-static.s3.eu-north-1.amazonaws.com/email-signature/ig-2022.png"
                  alt="Nordvik på Instagram"
                  height="16"
                  width="16"
                />
              </a>
            </span>
            <span
              style={{
                display: 'inline-block',
                width: '12px',
              }}
            >
              <img
                src="https://nordvikbolig-static.s3.eu-north-1.amazonaws.com/email-signature/12x17-spacer.png"
                alt=""
                height="17"
                width="12"
              />
            </span>
            <span style={{ display: 'inline-block', margin: '0 4px' }}>
              <a
                style={{
                  textDecoration: 'none',
                  display: 'inline-block',
                }}
                href="https://www.facebook.com/nordvikbolig/"
              >
                <img
                  src="https://nordvikbolig-static.s3.eu-north-1.amazonaws.com/email-signature/fb-2022.png"
                  alt="Nordvik på Facebook"
                  height="17"
                  width="17"
                />
              </a>
            </span>
            <span style={{ display: 'inline-block', width: '12px' }}>
              <img
                src="https://nordvikbolig-static.s3.eu-north-1.amazonaws.com/email-signature/12x17-spacer.png"
                alt=""
                height="17"
                width="12"
              />
            </span>
            <span style={{ display: 'inline-block' }}>
              <a
                style={{
                  textDecoration: 'none',
                  display: 'inline-block',
                }}
                href="https://www.nordvik.app/?utm_source=office&utm_medium=email&utm_campaign=2020_10_launch&utm_content=signature"
              >
                <img
                  src="https://nordvikbolig-static.s3.eu-north-1.amazonaws.com/email-signature/nordvik-appen-icon-2022.png"
                  alt="Nordvik.app"
                  height="17"
                  width="86"
                />
              </a>
            </span>
          </Text>
        </Column>
      </Row>
    </div>
  )
}

export default EmailSignatureLayout
