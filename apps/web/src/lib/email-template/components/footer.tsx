import { Row } from '@react-email/components'

import { emailTypo } from './email-typo'

const Footer = ({ text, centered }: { text: string; centered?: boolean }) => {
  return (
    <Row
      style={{
        marginTop: '16px',
      }}
    >
      <div
        style={{ height: '1px', width: '100%', background: '#E1E6E6' }}
      ></div>
      <p
        style={{
          ...emailTypo.bodySm,
          color: '#68707C',
          textAlign: centered ? 'center' : 'left',
        }}
      >
        {text}
      </p>
    </Row>
  )
}

export default Footer
