import { Con<PERSON><PERSON>, <PERSON><PERSON>, Head, Html, Section } from '@react-email/components'
import { PropsWithChildren } from 'react'

import Header from './header'

type Props = PropsWithChildren<{
  centeredHeader?: boolean
  withHeader?: boolean
  withBackground?: boolean
}>

export const ReactEmailBaseWrapper = (props: Props) => {
  return (
    <Html>
      <Head>
        <style>
          {`
            p, li, h1, h2, h3, h4, a {
            font-family: 'Basel Grotesk', Arial, sans-serif;
            color: #141E29
          }          
        `}
        </style>
        <Font
          fontFamily="Basel Classic"
          webFont={{
            url: 'https://static.nordvikbolig.no/fonts/basel-classic/basel-classic-regular-webfont.woff2',
            format: 'woff2',
          }}
          fontStyle="regular"
          fontWeight={400}
          fallbackFontFamily={['Arial', 'sans-serif']}
        />

        <Font
          fontFamily="Basel Grotesk"
          webFont={{
            url: 'https://static.nordvikbolig.no/fonts/basel-grotesk/basel-grotesk-regular-webfont.woff2',
            format: 'woff2',
          }}
          fontStyle="regular"
          fontWeight={400}
          fallbackFontFamily={['Helvetica', 'Arial', 'sans-serif']}
        />
        <Font
          fontFamily="Basel Grotesk"
          webFont={{
            url: 'https://static.nordvikbolig.no/fonts/basel-grotesk/basel-grotesk-medium-webfont.woff2',
            format: 'woff2',
          }}
          fontStyle="medium"
          fontWeight={500}
          fallbackFontFamily={['Helvetica', 'Arial', 'sans-serif']}
        />
      </Head>
      <div
        style={{
          background: props.withBackground ? '#EEF1F2' : 'white',
          padding: '16px',
        }}
      >
        <Container
          style={{
            background: 'white',
            color: '#141E29',
            borderRadius: '4px',
            maxWidth: '600px',
          }}
        >
          {props.children}
        </Container>
      </div>
    </Html>
  )
}

const ReactEmailWrapper = (props: Props) => {
  return (
    <ReactEmailBaseWrapper {...props}>
      {props.withHeader && <Header centered={props.centeredHeader} />}

      <Section style={{ padding: '16px 32px' }}>{props.children}</Section>
    </ReactEmailBaseWrapper>
  )
}

export default ReactEmailWrapper
