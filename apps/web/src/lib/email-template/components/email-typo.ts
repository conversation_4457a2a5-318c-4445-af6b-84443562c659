// Display
import { CSSProperties } from 'react'

const displayXs: CSSProperties = {
  fontSize: '20px',
  lineHeight: '28px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Classic, Arial, sans-serif',
}

const displaySm: CSSProperties = {
  fontSize: '24px',
  lineHeight: '32px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Classic, Arial, sans-serif',
}

const displayMd: CSSProperties = {
  fontSize: '32px',
  lineHeight: '40px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Classic, Arial, sans-serif',
}

const displayLg: CSSProperties = {
  fontSize: '40px',
  lineHeight: '48px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Classic, Arial, sans-serif',
}

const displayXl: CSSProperties = {
  fontSize: '48px',
  lineHeight: '58px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Classic, Arial, sans-serif',
}

// Title

const titleXs: CSSProperties = {
  fontSize: '16px',
  lineHeight: '22px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
  fontWeight: 435,
}

const titleSm: CSSProperties = {
  fontSize: '18px',
  lineHeight: '24px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
  fontWeight: 435,
}

const titleMd: CSSProperties = {
  fontSize: '24px',
  lineHeight: '30px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
  fontWeight: 435,
}

const titleLg: CSSProperties = {
  fontSize: '32px',
  lineHeight: '38px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
  fontWeight: 435,
}

// Body

const bodyXs: CSSProperties = {
  fontSize: '12px',
  lineHeight: '14px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

const bodySm: CSSProperties = {
  fontSize: '14px',
  lineHeight: '20px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

const bodyMd: CSSProperties = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

const bodyLg: CSSProperties = {
  fontSize: '18px',
  lineHeight: '26px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

const bodyXl: CSSProperties = {
  fontSize: '22px',
  lineHeight: '30px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

// Body bold

const bodyXsBold: CSSProperties = {
  fontSize: '12px',
  lineHeight: '14px',
  color: '#141E29',
  margin: '16px 0',
  fontWeight: 535,
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

const bodySmBold: CSSProperties = {
  fontSize: '14px',
  lineHeight: '20px',
  color: '#141E29',
  margin: '16px 0',
  fontWeight: 535,
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

const bodyMdBold: CSSProperties = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#141E29',
  margin: '16px 0',
  fontWeight: 535,
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

const bodyLgBold: CSSProperties = {
  fontSize: '18px',
  lineHeight: '26px',
  color: '#141E29',
  margin: '16px 0',
  fontWeight: 535,
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

const bodyXlBold: CSSProperties = {
  fontSize: '22px',
  lineHeight: '30px',
  color: '#141E29',
  margin: '16px 0',
  fontWeight: 535,
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
}

// Label

const labelMd: CSSProperties = {
  fontSize: '14px',
  lineHeight: '20px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
  fontWeight: 435,
}

const labelLg: CSSProperties = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#141E29',
  margin: '16px 0',
  fontFamily: 'Basel Grotesk, Arial, sans-serif',
  fontWeight: 435,
}

export const emailTypo = {
  displayXs,
  displaySm,
  displayMd,
  displayLg,
  displayXl,
  titleXs,
  titleSm,
  titleMd,
  titleLg,
  bodyXs,
  bodySm,
  bodyMd,
  bodyLg,
  bodyXl,
  bodyXsBold,
  bodySmBold,
  bodyMdBold,
  bodyLgBold,
  bodyXlBold,
  labelMd,
  labelLg,
}
