import { Text, render } from '@react-email/components'
import { ReactNode } from 'react'

import { emailTypo } from './components/email-typo'
import Footer from './components/footer'
import ReactEmailWrapper from './components/react-email-wrapper'

type Props = {
  fornavn: string
  isCompany?: boolean
  signature: ReactNode
}

export default function SignedOfferEmail({
  fornavn,
  isCompany,
  signature,
}: Props) {
  return (
    <ReactEmailWrapper>
      <Text style={emailTypo.bodyMd}>Hei {fornavn},</Text>

      <Text style={emailTypo.bodyMd}>
        Det første steget på veien mot
        {isCompany ? ' salg' : ' å selge boligen din'} er nå tatt! Vi ser frem
        til å sørge for en god salgsprosess som ender med
        {isCompany ? ' et godt salg.' : ' at du får den beste prisen.'}
      </Text>

      <Text style={emailTypo.bodyMd}>
        Vedlagt finner du den signerte oppdragsavtalen. Dokumentet inneholder
        personopplysninger som krever passordbeskyttelse og du mottar derfor
        passordet på sms.
      </Text>

      <Text style={{ ...emailTypo.bodyMd, margin: '16px 0 0 0' }}>
        For å få en smidig salgsprosess anbefaler vi at du:
      </Text>

      <ul style={{ margin: 0 }}>
        <li style={{ ...emailTypo.bodyMd, margin: '4px 0 0 0' }}>
          Tenker gjennom og skriver ned alt av eventuelle feil og/eller mangler
          ved eiendommen.
        </li>
        <li style={{ ...emailTypo.bodyMd, margin: '4px 0 0 0' }}>
          Finner frem dokumenter om eiendommen før bygningssakkyndig kommer på
          besøk.
        </li>
        <li style={{ ...emailTypo.bodyMd, margin: '4px 0 0 0' }}>
          Samler dokumentasjon om eventuell oppussing og vedlikehold.
        </li>
        <li style={{ ...emailTypo.bodyMd, margin: '4px 0 0 0' }}>
          Fyller ut skjema for energimerking av eiendommen på{' '}
          <a href="https://www.enova.no/energimerking">
            https://www.enova.no/energimerking
          </a>{' '}
          og sender over rapporten.
        </li>
      </ul>

      <Text style={{ ...emailTypo.bodyMd, margin: '16px 0 0 0' }}>
        Samtidig begynner vi nå arbeidet med å gjøre boligen klar for salg. I
        dagene som kommer vil vi:
      </Text>

      <ul style={{ margin: 0 }}>
        <li style={{ ...emailTypo.bodyMd, margin: '4px 0 0 0' }}>
          Hente offentlige reguleringer og bestemmelser fra kommunen.
        </li>
        <li style={{ ...emailTypo.bodyMd, margin: '4px 0 0 0' }}>
          Undersøke grunnboken for å se hva som er tinglyst av hjemler,
          heftelser, servitutter (tinglyste avtaler på eiendommen) og grunndata.
        </li>
        <li style={{ ...emailTypo.bodyMd, margin: '4px 0 0 0' }}>
          Kontakte bygningssakkyndig og fotograf.
        </li>
      </ul>

      <Text style={emailTypo.bodyMd}>
        Vi gjør deg også oppmerksom på at gevinst ved salg av bolig er
        skattefritt når selger har eid eiendommen og selv bebodd denne i minst
        12 av de siste 24 månedene. Botid regnes fra den dagen man registrerer
        adressen i Folkeregisteret til bud er akseptert. Du oppfordres selv til
        å sjekke andre skattemessige konsekvenser.
      </Text>

      <Text style={emailTypo.bodyMd}>
        Hvis det er noe du lurer på, er det bare å kontakte meg.
      </Text>

      {signature}
      <Footer text="Denne e-posten ble automatisk generert etter forespørsel: Nordvik Megler" />
    </ReactEmailWrapper>
  )
}

export const renderSignedOfferEmail = (props: Props) =>
  render(<SignedOfferEmail {...props} />)
