import { Text, render } from '@react-email/components'

import { emailTypo } from './components/email-typo'
import ReactEmailWrapper from './components/react-email-wrapper'

type Props = {
  isValuation: boolean
  brokerName?: string
  address: string | undefined
}

export default function NotifyBrokerOfLeadsEmail({
  isValuation,
  brokerName,
  address,
}: Props) {
  const leadSource = isValuation ? 'via verdivurderingen' : ''

  return (
    <ReactEmailWrapper>
      <Text style={emailTypo.bodyMd}>Hei, {brokerName?.split(' ')[0]}</Text>
      <Text style={emailTypo.bodyMd}>
        Eieren av {address} har bedt om å bli kontaktet av Storebrand{' '}
        {leadSource}.
      </Text>
      <Text style={emailTypo.bodyMd}>
        Hvis du har lagt inn din Storebrand-kontakt under Din profil i Nordvik
        Megler så sendes leadet direkte til kontakten. Ellers følg opp med din
        kontakt hos Storebrand ved behov.
      </Text>
    </ReactEmailWrapper>
  )
}

export const renderNotifyBrokerOfLeadsEmail = (props: Props) =>
  render(<NotifyBrokerOfLeadsEmail {...props} />)
