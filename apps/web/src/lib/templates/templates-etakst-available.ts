import { TemplateReplacements } from '@/components/send-offer-dialog/types'
import {
  Template,
  TemplateContentType,
  TemplateIds,
} from '@/components/template-renderer/types'

export const ETAKST_AVAILABLE_TEMPLATE: Template<TemplateReplacements> = {
  id: TemplateIds.ETAKST_AVAILABLE_TEMPLATE,
  name: 'E-takst er klar',
  subject: 'E-takst for {estateAddress} er klar',
  emailContent: [
    {
      id: 'text-1',
      type: TemplateContentType.Delta,
      value: {
        ops: [
          { insert: `Hei, {sellersFirstName}` },
          { insert: '\n\n' },
          {
            insert: `Nå er e-taksten for {estateAddress} klar. Har {pronoun} noen spørsmål er det bare å ringe eller sende e-post.`,
          },
          { insert: '\n\n' },
          {
            insert: `Hvis {pronoun} vurderer å selge i tiden fremover så håper jeg {pronoun} har fått et godt inntrykk av meg, og at {pronoun} tar kontakt.`,
          },
        ],
      },
    },
    {
      id: 'link-1',
      type: TemplateContentType.Link,
      value: {
        text: 'Gå til e-takst',
      },
    },
  ],
  smsContent: [],
} as const
