import 'server-only'

export interface ServiceHealthCheck {
  name: string
  url: string
  method?: 'GET' | 'POST' | 'HEAD'
  timeout?: number
  expectedStatus?: number[]
  headers?: Record<string, string>
  body?: Record<string, unknown>
  customCheck?: () => Promise<ServiceHealthStatus>
}

export interface ServiceHealthStatus {
  service: string
  status: 'healthy' | 'degraded' | 'down'
  responseTime: number
  timestamp: string
  error?: string
  metadata?: Record<string, unknown>
}

export interface AlertChannel {
  type: 'email' | 'slack' | 'webhook'
  config: {
    recipients?: string[]
    webhookUrl?: string
    slackChannel?: string
  }
}

export interface MonitoringConfig {
  services: ServiceHealthCheck[]
  alertChannels: AlertChannel[]
  checkInterval: number // in minutes
  retryAttempts: number
  alertThreshold: number // consecutive failures before alerting
  degradedThreshold: number // response time in ms to consider degraded
}

export const DEFAULT_MONITORING_CONFIG: MonitoringConfig = {
  services: [],
  alertChannels: [],
  checkInterval: 5,
  retryAttempts: 3,
  alertThreshold: 3,
  degradedThreshold: 5000,
}
