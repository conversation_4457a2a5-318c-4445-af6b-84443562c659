import 'server-only'

import { getVitecHubApiAuth } from '@/api/vitec-hub-api'

import { ServiceHealthCheck, ServiceHealthStatus } from './types'

export async function checkSignicatHealth(): Promise<ServiceHealthStatus> {
  const startTime = Date.now()
  const url = `https://status.signicat.com/api/v2/status.json`

  try {
    // Use Signicat ping endpoint
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const responseTime = Date.now() - startTime

    const body = await response.json()

    const ok = body.status?.indicator === 'none'

    return {
      service: 'Signicat',
      status: ok ? 'healthy' : 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      metadata: {
        httpStatus: response.status,
        url,
      },
      error: !response.ok ? `HTTP ${response.status}` : undefined,
    }
  } catch (error) {
    const responseTime = Date.now() - startTime

    return {
      service: 'Signicat',
      status: 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        url,
      },
    }
  }
}

/**
 * Check Vitec API health
 */
export async function checkVitecHealth(): Promise<ServiceHealthStatus> {
  const startTime = Date.now()
  const url = `${process.env.VITEC_URL}/Account/Installations`

  try {
    // Use Vitec base URL with authentication
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000)

    const auth = getVitecHubApiAuth()
    const response = await fetch(url, {
      headers: {
        Authorization: auth,
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const responseTime = Date.now() - startTime

    return {
      service: 'Vitec',
      status: response.ok ? 'healthy' : 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      metadata: {
        httpStatus: response.status,
        url,
      },
      error: !response.ok ? `HTTP ${response.status}` : undefined,
    }
  } catch (error) {
    const responseTime = Date.now() - startTime

    return {
      service: 'Vitec',
      status: 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        url,
      },
    }
  }
}

/**
 * Check AWS S3 health
 */
export async function checkAWSS3Health(): Promise<ServiceHealthStatus> {
  const startTime = Date.now()

  try {
    // Simple head request to S3 bucket using known bucket from config
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000)

    const response = await fetch(
      `https://nordvik-prod.s3.${process.env.AWS_REGION}.amazonaws.com`,
      {
        method: 'HEAD',
        signal: controller.signal,
      },
    )

    clearTimeout(timeoutId)
    const responseTime = Date.now() - startTime

    return {
      service: 'AWS S3',
      status: response.ok || response.status === 403 ? 'healthy' : 'down', // 403 is expected for private buckets
      responseTime,
      timestamp: new Date().toISOString(),
      metadata: {
        httpStatus: response.status,
        bucket: 'nordvik-prod',
        region: process.env.AWS_REGION,
      },
      error:
        !response.ok && response.status !== 403
          ? `HTTP ${response.status}`
          : undefined,
    }
  } catch (error) {
    const responseTime = Date.now() - startTime

    return {
      service: 'AWS S3',
      status: 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        bucket: 'nordvik-prod',
        region: process.env.AWS_REGION,
      },
    }
  }
}

/**
 * Check Mailchimp health
 */
export async function checkMailchimpHealth(): Promise<ServiceHealthStatus> {
  const startTime = Date.now()

  try {
    // Mailchimp ping endpoint
    const apiKey = process.env.MAILCHIMP_API_KEY
    if (!apiKey) {
      throw new Error('MAILCHIMP_API_KEY not configured')
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000)

    const dc = apiKey.split('-')[1]
    const response = await fetch(`https://${dc}.api.mailchimp.com/3.0/ping`, {
      method: 'GET',
      headers: {
        Authorization: `apikey ${apiKey}`,
      },
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const responseTime = Date.now() - startTime

    return {
      service: 'Mailchimp',
      status: response.ok ? 'healthy' : 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      metadata: {
        httpStatus: response.status,
        datacenter: dc,
        url: `https://${dc}.api.mailchimp.com/3.0/ping`,
      },
      error: !response.ok ? `HTTP ${response.status}` : undefined,
    }
  } catch (error) {
    const responseTime = Date.now() - startTime

    return {
      service: 'Mailchimp',
      status: 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        configured: !!process.env.MAILCHIMP_API_KEY,
      },
    }
  }
}

/**
 * Check Nordvik API health
 */
export async function checkNordvikAPIHealth(): Promise<ServiceHealthStatus> {
  const startTime = Date.now()

  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000)

    const response = await fetch(`${process.env.NORDVIK_NO_API_URL}/status`, {
      method: 'GET',
      headers: {
        'x-api-key': process.env.NORDVIK_NO_API_KEY!,
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const responseTime = Date.now() - startTime

    return {
      service: 'Nordvik API',
      status: response.ok ? 'healthy' : 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      metadata: {
        httpStatus: response.status,
        url: `${process.env.NORDVIK_NO_API_URL}/status`,
      },
      error: !response.ok ? `HTTP ${response.status}` : undefined,
    }
  } catch (error) {
    const responseTime = Date.now() - startTime

    return {
      service: 'Nordvik API',
      status: 'down',
      responseTime,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        url: `${process.env.NORDVIK_NO_API_URL}/status`,
      },
    }
  }
}

/**
 * Default service configurations for monitoring
 */
export const DEFAULT_SERVICE_CHECKS: ServiceHealthCheck[] = [
  {
    name: 'Signicat',
    url: 'placeholder', // Will be handled by custom check
    customCheck: checkSignicatHealth,
  },
  {
    name: 'Vitec',
    url: 'placeholder', // Will be handled by custom check
    customCheck: checkVitecHealth,
  },
  // {
  //   name: 'AWS S3',
  //   url: 'placeholder', // Will be handled by custom check
  //   customCheck: checkAWSS3Health,
  // },
  // {
  //   name: 'Mailchimp',
  //   url: 'placeholder', // Will be handled by custom check
  //   customCheck: checkMailchimpHealth,
  // },
  {
    name: 'Nordvik API',
    url: 'placeholder', // Will be handled by custom check
    customCheck: checkNordvikAPIHealth,
  },
]
