import 'server-only'

import {
  DEFAULT_MONITORING_CONFIG,
  MonitoringConfig,
  ServiceHealthCheck,
  ServiceHealthStatus,
} from './types'

export class HealthChecker {
  private config: MonitoringConfig

  constructor(config: Partial<MonitoringConfig> = {}) {
    this.config = { ...DEFAULT_MONITORING_CONFIG, ...config }
  }

  /**
   * Check the health of a single service
   */
  async checkService(
    service: ServiceHealthCheck,
  ): Promise<ServiceHealthStatus> {
    const startTime = Date.now()
    const timestamp = new Date().toISOString()

    try {
      // Use custom check if provided
      if (service.customCheck) {
        return await service.customCheck()
      }

      // Standard HTTP health check
      const controller = new AbortController()
      const timeoutId = setTimeout(
        () => controller.abort(),
        service.timeout || 10000,
      )

      const response = await fetch(service.url, {
        method: service.method || 'GET',
        headers: service.headers || {},
        body: service.body ? JSON.stringify(service.body) : undefined,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime
      const expectedStatus = service.expectedStatus || [200, 201, 204]

      let status: 'healthy' | 'degraded' | 'down' = 'healthy'

      if (!expectedStatus.includes(response.status)) {
        status = 'down'
      } else if (responseTime > this.config.degradedThreshold) {
        status = 'degraded'
      }

      return {
        service: service.name,
        status,
        responseTime,
        timestamp,
        metadata: {
          httpStatus: response.status,
          url: service.url,
        },
      }
    } catch (error) {
      const responseTime = Date.now() - startTime

      return {
        service: service.name,
        status: 'down',
        responseTime,
        timestamp,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          url: service.url,
        },
      }
    }
  }

  /**
   * Check health of all configured services
   */
  async checkAllServices(): Promise<ServiceHealthStatus[]> {
    const promises = this.config.services.map((service) =>
      this.checkService(service),
    )

    return await Promise.all(promises)
  }

  /**
   * Check health with retry logic
   */
  async checkServiceWithRetry(
    service: ServiceHealthCheck,
  ): Promise<ServiceHealthStatus> {
    let lastResult: ServiceHealthStatus | null = null

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      lastResult = await this.checkService(service)

      if (lastResult.status === 'healthy') {
        return lastResult
      }

      // Wait before retry (exponential backoff)
      if (attempt < this.config.retryAttempts) {
        await new Promise((resolve) =>
          setTimeout(resolve, Math.pow(2, attempt) * 1000),
        )
      }
    }

    return lastResult!
  }

  /**
   * Get service configuration
   */
  getConfig(): MonitoringConfig {
    return { ...this.config }
  }

  /**
   * Update service configuration
   */
  updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}
