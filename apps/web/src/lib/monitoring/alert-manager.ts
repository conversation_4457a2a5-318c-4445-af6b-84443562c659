import 'server-only'

import { AlertChannel, ServiceHealthStatus } from './types'

export interface AlertMetrics {
  consecutiveFailures: number
  lastAlertSent: string | null
  totalFailures: number
  firstFailureTime: string | null
}

export class AlertManager {
  private serviceMetrics: Map<string, AlertMetrics> = new Map()
  private alertChannels: AlertChannel[]
  private alertThreshold: number
  private cooldownPeriod: number // minutes

  constructor(
    alertChannels: AlertChannel[] = [],
    alertThreshold: number = 3,
    cooldownPeriod: number = 30,
  ) {
    this.alertChannels = alertChannels
    this.alertThreshold = alertThreshold
    this.cooldownPeriod = cooldownPeriod
  }

  /**
   * Process health check results and trigger alerts if needed
   */
  async processHealthResults(results: ServiceHealthStatus[]): Promise<void> {
    for (const result of results) {
      await this.processServiceResult(result)
    }
  }

  /**
   * Process a single service health result
   */
  private async processServiceResult(
    result: ServiceHealthStatus,
  ): Promise<void> {
    const serviceName = result.service
    let metrics = this.serviceMetrics.get(serviceName) || {
      consecutiveFailures: 0,
      lastAlertSent: null,
      totalFailures: 0,
      firstFailureTime: null,
    }

    if (result.status === 'healthy') {
      // Reset metrics on recovery
      if (metrics.consecutiveFailures > 0) {
        await this.sendRecoveryAlert(result, metrics)
      }
      metrics = {
        consecutiveFailures: 0,
        lastAlertSent: null,
        totalFailures: metrics.totalFailures,
        firstFailureTime: null,
      }
    } else {
      // Track failure
      metrics.consecutiveFailures++
      metrics.totalFailures++

      if (metrics.firstFailureTime === null) {
        metrics.firstFailureTime = result.timestamp
      }

      // Check if we should send an alert
      if (this.shouldSendAlert(metrics)) {
        await this.sendDownAlert(result, metrics)
        metrics.lastAlertSent = result.timestamp
      }
    }

    this.serviceMetrics.set(serviceName, metrics)
  }

  /**
   * Determine if an alert should be sent
   */
  private shouldSendAlert(metrics: AlertMetrics): boolean {
    // Don't alert if we haven't reached the threshold
    if (metrics.consecutiveFailures < this.alertThreshold) {
      return false
    }

    // Don't alert if we're in cooldown period
    if (metrics.lastAlertSent) {
      const lastAlertTime = new Date(metrics.lastAlertSent).getTime()
      const now = Date.now()
      const cooldownMs = this.cooldownPeriod * 60 * 1000

      if (now - lastAlertTime < cooldownMs) {
        return false
      }
    }

    return true
  }

  /**
   * Send alert for service down
   */
  private async sendDownAlert(
    result: ServiceHealthStatus,
    metrics: AlertMetrics,
  ): Promise<void> {
    const message = this.formatDownMessage(result, metrics)

    await Promise.all(
      this.alertChannels.map((channel) =>
        this.sendAlert(channel, message, 'error'),
      ),
    )
  }

  /**
   * Send alert for service recovery
   */
  private async sendRecoveryAlert(
    result: ServiceHealthStatus,
    metrics: AlertMetrics,
  ): Promise<void> {
    const message = this.formatRecoveryMessage(result, metrics)

    await Promise.all(
      this.alertChannels.map((channel) =>
        this.sendAlert(channel, message, 'success'),
      ),
    )
  }

  /**
   * Send alert through specific channel
   */
  private async sendAlert(
    channel: AlertChannel,
    message: string,
    severity: 'error' | 'warning' | 'success',
  ): Promise<void> {
    try {
      switch (channel.type) {
        case 'email':
          await this.sendEmailAlert(channel.config, message, severity)
          break
        case 'slack':
          await this.sendSlackAlert(channel.config, message, severity)
          break
        case 'webhook':
          await this.sendWebhookAlert(channel.config, message, severity)
          break
      }
    } catch (error) {
      console.error(`Failed to send alert via ${channel.type}:`, error)
    }
  }

  /**
   * Send email alert
   */
  private async sendEmailAlert(
    config: AlertChannel['config'],
    message: string,
    severity: 'error' | 'warning' | 'success',
  ): Promise<void> {
    if (!config.recipients?.length) return

    // This would integrate with your email service (Mailchimp/Nodemailer)
    const subject = `[${severity.toUpperCase()}] Service Health Alert - Nordvik Megler`

    // Implementation would depend on your email service setup
    // TODO: Integrate with actual email service
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log(`Email alert: ${subject}\n${message}`)
    }
  }

  /**
   * Send Slack alert
   */
  private async sendSlackAlert(
    config: AlertChannel['config'],
    message: string,
    severity: 'error' | 'warning' | 'success',
  ): Promise<void> {
    if (!config.webhookUrl) return

    const color =
      severity === 'error'
        ? 'danger'
        : severity === 'warning'
          ? 'warning'
          : 'good'

    const payload = {
      channel: config.slackChannel || '#alerts',
      username: 'Health Monitor',
      icon_emoji: ':warning:',
      attachments: [
        {
          color,
          title: `Service Health Alert`,
          text: message,
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    }

    await fetch(config.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    })
  }

  /**
   * Send webhook alert
   */
  private async sendWebhookAlert(
    config: AlertChannel['config'],
    message: string,
    severity: 'error' | 'warning' | 'success',
  ): Promise<void> {
    if (!config.webhookUrl) return

    const payload = {
      timestamp: new Date().toISOString(),
      severity,
      message,
      source: 'nordvik-health-monitor',
    }

    try {
      await fetch(config.webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })
    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('Failed to send webhook alert:', error)
      }
    }
  }

  /**
   * Format down alert message
   */
  private formatDownMessage(
    result: ServiceHealthStatus,
    metrics: AlertMetrics,
  ): string {
    const duration = metrics.firstFailureTime
      ? this.formatDuration(
          new Date(metrics.firstFailureTime),
          new Date(result.timestamp),
        )
      : 'Unknown'

    return `🚨 Service Alert: ${result.service}

Status: ${result.status.toUpperCase()}
Error: ${result.error || 'Service check failed'}
Response Time: ${result.responseTime}ms
Consecutive Failures: ${metrics.consecutiveFailures}
Duration: ${duration}
URL: ${result.metadata?.url || 'N/A'}

Timestamp: ${result.timestamp}`
  }

  /**
   * Format recovery alert message
   */
  private formatRecoveryMessage(
    result: ServiceHealthStatus,
    metrics: AlertMetrics,
  ): string {
    const downtime = metrics.firstFailureTime
      ? this.formatDuration(
          new Date(metrics.firstFailureTime),
          new Date(result.timestamp),
        )
      : 'Unknown'

    return `✅ Service Recovered: ${result.service}

Status: HEALTHY
Response Time: ${result.responseTime}ms
Total Downtime: ${downtime}
Total Failures: ${metrics.totalFailures}

Timestamp: ${result.timestamp}`
  }

  /**
   * Format duration between two dates
   */
  private formatDuration(start: Date, end: Date): string {
    const ms = end.getTime() - start.getTime()
    const minutes = Math.floor(ms / (1000 * 60))
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  /**
   * Get current metrics for all services
   */
  getMetrics(): Map<string, AlertMetrics> {
    return new Map(this.serviceMetrics)
  }

  /**
   * Reset metrics for a specific service
   */
  resetServiceMetrics(serviceName: string): void {
    this.serviceMetrics.delete(serviceName)
  }

  /**
   * Reset all metrics
   */
  resetAllMetrics(): void {
    this.serviceMetrics.clear()
  }
}
