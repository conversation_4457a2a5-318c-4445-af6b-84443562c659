import 'server-only'

import { initializeMonitoring } from '@/lib/monitoring'

/**
 * Initialize monitoring system
 * This should be called during app startup
 */
export function setupMonitoring() {
  try {
    const monitoring = initializeMonitoring({
      checkInterval: 5, // Check every 5 minutes
      retryAttempts: 3,
      alertThreshold: 3, // Alert after 3 consecutive failures
      degradedThreshold: 5000, // Consider degraded if response time > 5s
    })

    // Start monitoring in production
    if (process.env.NODE_ENV === 'production') {
      monitoring.start()
    }

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('✅ Monitoring system initialized')
    }

    return monitoring
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('❌ Failed to initialize monitoring system:', error)
    }
    return null
  }
}

/**
 * Add example alert channels
 * Call this function to configure alert channels programmatically
 */
export async function configureAlertChannels() {
  try {
    // Import here to avoid issues with module loading
    const { getMonitoringService } = await import('@/lib/monitoring')
    const monitoring = getMonitoringService()

    // Example: Add Slack webhook alert
    // monitoring.addAlertChannel({
    //   type: 'slack',
    //   config: {
    //     webhookUrl: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
    //     slackChannel: '#alerts',
    //   },
    // })

    // Example: Add email alerts
    // monitoring.addAlertChannel({
    //   type: 'email',
    //   config: {
    //     recipients: ['<EMAIL>', '<EMAIL>'],
    //   },
    // })

    // Example: Add webhook alert
    // monitoring.addAlertChannel({
    //   type: 'webhook',
    //   config: {
    //     webhookUrl: 'https://your-monitoring-system.com/webhook',
    //   },
    // })

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('📧 Alert channels configured (examples commented out)')
    }

    return monitoring
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('❌ Failed to configure alert channels:', error)
    }
    return null
  }
}
