import 'server-only'

import { get, set } from '@/db/kv'

import { AlertManager } from './alert-manager'
import { DEFAULT_SERVICE_CHECKS } from './custom-checks'
import { HealthChecker } from './health-checker'
import { AlertChannel, MonitoringConfig, ServiceHealthStatus } from './types'

export class MonitoringService {
  private healthChecker: HealthChecker
  private alertManager: AlertManager
  private intervalId: NodeJS.Timeout | null = null
  private isRunning = false
  private lastResults: ServiceHealthStatus[] = []

  constructor(config?: Partial<MonitoringConfig>) {
    const defaultConfig = {
      services: DEFAULT_SERVICE_CHECKS,
      alertChannels: this.getDefaultAlertChannels(),
      checkInterval: 5, // minutes
      retryAttempts: 3,
      alertThreshold: 3,
      degradedThreshold: 5000, // 5 seconds
    }

    const finalConfig = { ...defaultConfig, ...config }
    this.healthChecker = new HealthChecker(finalConfig)
    this.alertManager = new AlertManager(
      finalConfig.alertChannels,
      finalConfig.alertThreshold,
      30, // cooldown period in minutes
    )
  }

  start(): void {
    if (this.isRunning) {
      return
    }

    const config = this.healthChecker.getConfig()
    const intervalMs = config.checkInterval * 60 * 1000 // Convert minutes to milliseconds

    this.intervalId = setInterval(async () => {
      await this.performHealthCheck()
    }, intervalMs)

    this.isRunning = true

    this.performHealthCheck().catch((error) => {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Health check error:', error)
      }
    })

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log(
        `Monitoring service started with ${config.checkInterval} minute intervals`,
      )
    }
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    this.isRunning = false
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('Monitoring service stopped')
    }
  }

  /**
   * Perform a single health check cycle
   */
  async performHealthCheck(): Promise<ServiceHealthStatus[]> {
    try {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('🔍 Starting health check cycle...')
      }

      const results = await this.healthChecker.checkAllServices()
      this.lastResults = results

      await this.storeResults(results)

      await this.alertManager.processHealthResults(results)

      this.logHealthSummary(results)

      return results
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Error during health check:', error)
      }
      return []
    }
  }

  getLastResults(): ServiceHealthStatus[] {
    return [...this.lastResults]
  }

  async getCachedResults(): Promise<ServiceHealthStatus[] | null> {
    try {
      const cached = await get<ServiceHealthStatus[]>('monitoring:last-results')
      return cached
    } catch {
      return null
    }
  }

  /**
   * Force a health check for a specific service
   */
  async checkService(serviceName: string): Promise<ServiceHealthStatus | null> {
    const config = this.healthChecker.getConfig()
    const service = config.services.find((s) => s.name === serviceName)

    if (!service) {
      return null
    }

    return await this.healthChecker.checkServiceWithRetry(service)
  }

  getServiceMetrics() {
    return this.alertManager.getMetrics()
  }

  /**
   * Get overall system health status
   */
  getSystemHealth(): {
    status: 'healthy' | 'degraded' | 'down'
    healthyServices: number
    degradedServices: number
    downServices: number
    totalServices: number
    lastCheck: string | null
  } {
    if (this.lastResults.length === 0) {
      return {
        status: 'down',
        healthyServices: 0,
        degradedServices: 0,
        downServices: 0,
        totalServices: 0,
        lastCheck: null,
      }
    }

    const healthyServices = this.lastResults.filter(
      (r) => r.status === 'healthy',
    ).length
    const degradedServices = this.lastResults.filter(
      (r) => r.status === 'degraded',
    ).length
    const downServices = this.lastResults.filter(
      (r) => r.status === 'down',
    ).length
    const totalServices = this.lastResults.length

    let overallStatus: 'healthy' | 'degraded' | 'down' = 'healthy'

    if (downServices > 0) {
      overallStatus = 'down'
    } else if (degradedServices > 0) {
      overallStatus = 'degraded'
    }

    return {
      status: overallStatus,
      healthyServices,
      degradedServices,
      downServices,
      totalServices,
      lastCheck: this.lastResults[0]?.timestamp || null,
    }
  }

  addAlertChannel(channel: AlertChannel): void {
    const config = this.healthChecker.getConfig()
    const existingIndex = config.alertChannels.findIndex(
      (c) =>
        c.type === channel.type &&
        JSON.stringify(c.config) === JSON.stringify(channel.config),
    )

    if (existingIndex >= 0) {
      config.alertChannels[existingIndex] = channel
    } else {
      config.alertChannels.push(channel)
    }

    this.healthChecker.updateConfig(config)
  }

  removeAlertChannel(
    type: string,
    configMatch: Partial<AlertChannel['config']>,
  ): void {
    const config = this.healthChecker.getConfig()
    config.alertChannels = config.alertChannels.filter((channel) => {
      if (channel.type !== type) return true

      for (const [key, value] of Object.entries(configMatch)) {
        if (channel.config[key as keyof typeof channel.config] !== value) {
          return true
        }
      }
      return false
    })

    this.healthChecker.updateConfig(config)
  }

  resetMetrics(): void {
    this.alertManager.resetAllMetrics()
  }

  /**
   * Check if monitoring is running
   */
  isActive(): boolean {
    return this.isRunning
  }

  /**
   * Get the health checker instance
   */
  getHealthChecker(): HealthChecker {
    return this.healthChecker
  }

  private async storeResults(results: ServiceHealthStatus[]): Promise<void> {
    try {
      await set('monitoring:last-results', results, 60 * 60) // Cache for 1 hour
      await set('monitoring:last-check', new Date().toISOString(), 60 * 60)
    } catch (error) {
      console.error('Failed to store monitoring results:', error)
    }
  }

  private logHealthSummary(results: ServiceHealthStatus[]): void {
    const healthy = results.filter((r) => r.status === 'healthy').length
    const degraded = results.filter((r) => r.status === 'degraded').length
    const down = results.filter((r) => r.status === 'down').length

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log(
        `📊 Health Check Summary: ✅ ${healthy} healthy, ⚠️ ${degraded} degraded, ❌ ${down} down`,
      )

      if (down > 0 || degraded > 0) {
        const issues = results.filter((r) => r.status !== 'healthy')
        issues.forEach((result) => {
          const emoji = result.status === 'down' ? '❌' : '⚠️'
          // eslint-disable-next-line no-console
          console.log(
            `${emoji} ${result.service}: ${result.error || result.status} (${result.responseTime}ms)`,
          )
        })
      }
    }
  }

  private getDefaultAlertChannels(): AlertChannel[] {
    const channels: AlertChannel[] = []

    return channels
  }
}

let monitoringInstance: MonitoringService | null = null

/**
 * Get the global monitoring service instance
 */
export function getMonitoringService(): MonitoringService {
  if (!monitoringInstance) {
    monitoringInstance = new MonitoringService()
  }
  return monitoringInstance
}

export function initializeMonitoring(
  config?: Partial<MonitoringConfig>,
): MonitoringService {
  if (monitoringInstance) {
    monitoringInstance.stop()
  }

  monitoringInstance = new MonitoringService(config)

  if (process.env.NODE_ENV === 'production') {
    monitoringInstance.start()
  }

  return monitoringInstance
}

export function cleanupMonitoring(): void {
  if (monitoringInstance) {
    monitoringInstance.stop()
    monitoringInstance = null
  }
}
