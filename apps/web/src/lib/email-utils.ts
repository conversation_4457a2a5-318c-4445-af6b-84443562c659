/**
 * Generates a random string using base36 encoding.
 *
 * @param length - The desired length of the random string (default: 7)
 * @returns A random string
 */
function generateRandomString(length: number = 7): string {
  return Math.random()
    .toString(36)
    .substring(2, 2 + length)
}

/**
 * Generates a unique message ID for failed email deliveries.
 *
 * The format is: failed_{timestamp}_{randomString}
 * - timestamp: Current time in milliseconds for chronological ordering
 * - randomString: 7-character random string for uniqueness
 *
 * @returns A unique message ID string
 */
export function generateFailedMessageId(): string {
  const timestamp = Date.now()
  const randomString = generateRandomString()
  return `failed_${timestamp}_${randomString}`
}

/**
 * Generates a unique message ID for any custom email scenario.
 *
 * @param prefix - The prefix to use for the message ID (default: 'custom')
 * @returns A unique message ID string
 */
export function generateCustomMessageId(prefix: string = 'custom'): string {
  const timestamp = Date.now()
  const randomString = generateRandomString()
  return `${prefix}_${timestamp}_${randomString}`
}

/**
 * Generates a simple random ID (useful for UI components, temporary IDs, etc.)
 *
 * @param length - The desired length of the ID (default: 7)
 * @returns A random string ID
 */
export function generateRandomId(length: number = 7): string {
  return generateRandomString(length)
}

/**
 * Email status utility functions
 */

export type EmailStatus = 'opened' | 'bounced' | 'rejected' | 'failed' | 'sent'

/**
 * Checks if the email status represents a failure state
 */
export function isEmailFailureStatus(status: string | undefined): boolean {
  if (!status) return false
  return status === 'bounced' || status === 'rejected' || status === 'failed'
}

/**
 * Checks if the email status represents a bounce
 */
export function isEmailBounced(status: string | undefined): boolean {
  if (!status) return false
  return status === 'bounced'
}

/**
 * Checks if the email status represents a rejection or failure
 */
export function isEmailRejectedOrFailed(status: string | undefined): boolean {
  if (!status) return false
  return status === 'rejected' || status === 'failed' || status === 'invalid'
}

/**
 * Checks if an email status indicates it was sent successfully
 */
export function isEmailSent(status: string): boolean {
  return status === 'sent'
}

/**
 * Checks if an email status indicates it was opened
 */
export function isEmailOpened(status: string): boolean {
  return status === 'opened'
}
