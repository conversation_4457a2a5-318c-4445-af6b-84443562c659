/**
 * Centralized cache keys management
 * All cache keys used throughout the application should be defined here
 * to avoid duplication and make cache management easier.
 *
 * Usage:
 * - Import CACHE_KEYS for predefined cache key generators
 * - Import CacheKeyUtils for utility functions
 * - Import CACHE_PATTERNS for bulk operations
 *
 * Example:
 * ```typescript
 * import { CACHE_KEYS, CacheKeyUtils } from '@/lib/cache-keys'
 *
 * // Use predefined keys
 * const key = CACHE_KEYS.ESTATE.FOR_BROKER_COUNT(brokerId)
 *
 * // Use utility functions
 * const estateKey = CacheKeyUtils.generateEstateKey(brokerId, tabs, search, args)
 * ```
 */

// Cache key generators
export const CACHE_KEYS = {
  // Dashboard related keys
  DASHBOARD: {
    CACHE_FOR_EMPLOYEE: (employeeId: string) =>
      `dashboardCacheForEmployee:${employeeId}`,
    KEY_FIGURES: (
      prefix: string,
      departmentId: string,
      period: string,
      employeeId?: string,
    ) => {
      const base = `${prefix}:${departmentId}:${period}`
      return employeeId ? `${base}:${employeeId}` : base
    },
    COMPOSE_KEY_FIGURES: (
      departmentId: string | number,
      dateParams: string,
      employeeId?: string,
    ) => {
      const base = `composeKeyFigures:v1:${departmentId}:${dateParams}`
      return employeeId ? `${base}:${employeeId}` : base
    },
    KTI: (employeeId: string) => `dashboardKti:${employeeId}`,
    STOREBRAND_LEADS: (
      prefix: string,
      departmentId: string,
      employeeId?: string,
    ) => {
      const base = `${prefix}:${departmentId}`
      return employeeId ? `${base}:${employeeId}` : base
    },
  },

  // Estate related keys
  ESTATE: {
    FOR_BROKER: (
      brokerId: string,
      tabs: string,
      search?: string,
      args?: string,
    ) => {
      const searchPart = search ? `search:${search}:` : ''
      return `estatesForBroker:${brokerId}:${tabs}:${searchPart}${args || ''}`
    },
    FOR_BROKER_COUNT: (brokerId?: string) =>
      `estatesForBrokerIdCount:v2:${brokerId}`,
    BY_ID: (estateId: string) => `getEstateById:${estateId}`,
    BY_ID_PATTERN: (estateId: string) => `getEstateById:${estateId}:*`,
    FOR_BROKER_PATTERN: (employeeId: string) =>
      `estatesForBroker:${employeeId}:*`,
    LOCK: (estateId: string) => `lock:${estateId}`,
  },

  // Assignment document status keys
  ASSIGNMENT_DOCUMENT_STATUS: {
    BY_ESTATE_AND_TYPE: (estateId: string, type: string) =>
      `assignment-doc-status:${estateId}:${type}`,
    BY_ESTATE_PATTERN: (estateId: string) =>
      `assignment-doc-status:${estateId}:*`,
  },

  // User and session related keys
  USER: {
    IS_EMPLOYEE_ACTIVE: (employeeId: string) =>
      `isEmployeeActive:${employeeId}`,
    PINNED_BROKERS: (userId: string) => `pinnedBrokers:${userId}`,
  },

  // Article and news related keys
  ARTICLE: {
    NEWS_READERS: (userId: string, articleId: string) =>
      `news_readers:${userId}:${articleId}`,
    INTERCOM_CMS: 'intercom-cms',
  },

  // Price statistics
  PRICE_STATISTICS: (postalCode?: string, years?: number | null) =>
    `priceStatistics:${postalCode}:${years}`,

  // Storebrand related keys
  STOREBRAND: {
    DUPLICATE_CHECK: (estateId: string) =>
      `storebrand-duplicate-check:${estateId}`,
  },

  // External service keys
  SIGNICAT: {
    OAUTH_TOKEN: 'signicat-oauth-token',
  },

  // Toplist related keys
  TOPLIST: {
    BASE: (
      section: string,
      role: string,
      departmentId: string,
      period: string,
    ) => `toplist:${section}:${role}:${departmentId}:${period}`,
  },

  // Budget post related keys
  BUDGET_POST: {
    BY_ID: (budgetPostId: string) => `getBudgetPostById:${budgetPostId}`,
    BUDGET_POST_DESCRIPTIONS: 'getBudgetPostDescriptions',
  },
} as const

// Cache key utility functions
export const CacheKeyUtils = {
  /**
   * Generate a cache key with optional personal and extra parameters
   */
  generateCacheKey: (
    prefix: string,
    isPersonal: boolean,
    ids: { id: string; departmentId: string | number },
    extras?: string[],
  ): string => {
    let key = `${prefix}:${ids.departmentId}`

    if (isPersonal) {
      key = `${key}:${ids.id}`
    }

    if (extras?.length) {
      key = `${key}:${extras.join(':')}`
    }

    return key
  },

  /**
   * Sanitize search value for cache keys
   */
  sanitizeSearchValue: (search?: string | null): string => {
    return search?.trim().replace(/\s/g, '').toLowerCase() || ''
  },

  /**
   * Generate estate cache key with search and filters
   */
  generateEstateKey: (
    brokerId: string,
    tabs: string[],
    search?: string | null,
    args: Record<string, unknown> = {},
  ): string => {
    const searchValue = CacheKeyUtils.sanitizeSearchValue(search)
    const argsString = JSON.stringify(args, Object.keys(args).sort())

    return CACHE_KEYS.ESTATE.FOR_BROKER(
      brokerId,
      tabs.join(','),
      searchValue,
      argsString,
    )
  },
} as const

// Cache patterns for bulk operations
export const CACHE_PATTERNS = {
  ESTATE: {
    FOR_BROKER_ALL: (employeeId: string) => `estatesForBroker:${employeeId}:*`,
    BY_ID_ALL: (estateId: string) => `getEstateById:${estateId}:*`,
  },
} as const
