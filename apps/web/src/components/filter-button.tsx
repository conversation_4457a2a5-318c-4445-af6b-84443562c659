import { LucideProps, Settings2 } from 'lucide-react'
import { ForwardRefExoticComponent } from 'react'

import { cn } from '@nordvik/theme/cn'

export type FilterButtonProps = React.ComponentPropsWithoutRef<'button'> & {
  text?: string
  ref?: React.Ref<HTMLButtonElement>
  onClick?: () => void
  badgeText?: string
  disabled?: boolean
  iconOnly?: boolean
  Icon?: ForwardRefExoticComponent<Omit<LucideProps, 'ref'>>
}

const FilterButton = ({
  text = 'Filter',
  ref,
  badgeText,
  onClick,
  disabled,
  iconOnly,
  Icon = Settings2,
  ...rest
}: FilterButtonProps) => {
  return (
    <button
      {...rest}
      ref={ref}
      onClick={onClick}
      className={cn(
        'flex gap-2 rounded-md py-1 px-2 items-center w-fit leading-none',
        'ring-offset-background-root transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-focus focus-visible:ring-offset-2',
        'border border-emphasis bg-transparent text-button-secondary-ink hover:border-button-secondary-border-hover hover:bg-transparent',
        'disabled:cursor-default disabled:border-button-secondary-border-disabled disabled:text-button-secondary-ink-disabled disabled:hover:border-button-secondary-border-hover',
      )}
      disabled={disabled}
    >
      <span className="inline-block py-[3px]">
        <Icon className="size-4" />
      </span>
      {
        <span className={cn('typo-body-sm', iconOnly && 'sr-only')}>
          {text}
        </span>
      }
      {!!badgeText && (
        <>
          <span className="w-[1px] h-4 bg-stroke-muted inline-block" />
          <span className="inline-block bg-fill-interactive-muted rounded px-1.5 py-0.5 typo-body-xs">
            {badgeText}
          </span>
        </>
      )}
    </button>
  )
}

export default FilterButton
