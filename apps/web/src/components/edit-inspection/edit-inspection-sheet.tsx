import { useState } from 'react'

import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from '@nordvik/ui/sheet'

import { EditableContent } from './edit-inspection'

export function EditInspectionSheet({
  trigger,
  estateId,
  isValuation,
  modal = true,
}: {
  trigger: React.ReactNode
  estateId: string
  isValuation: boolean
  modal?: boolean
}) {
  const [openDrawer, setOpenDrawer] = useState(false)
  return (
    <Sheet open={openDrawer} onOpenChange={setOpenDrawer} modal={modal}>
      <SheetTrigger asChild>{trigger}</SheetTrigger>
      <SheetContent className="max-w-lg p-0 flex flex-col gap-0 w-full">
        <SheetHeader>
          <SheetTitle>Tilpass innhold i befaringen</SheetTitle>
        </SheetHeader>
        <div className="p-6 flex flex-col grow">
          <EditableContent
            estateId={estateId}
            isValuation={isValuation}
            isExpandable={false}
            forceOpen
            hideIcon
            closeModal={() => setOpenDrawer(false)}
          />
        </div>
      </SheetContent>
    </Sheet>
  )
}
