'use client'

import { getBrokerByRole } from '@befaring/lib/format-brokers'
import { PropsWithChildren } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Checkbox } from '@nordvik/ui/checkbox'
import { Input } from '@nordvik/ui/input'
import { Separator } from '@nordvik/ui/separator'

import {
  GQLAgreementAndInspectionQuery,
  GQLGetBrokerEstateQuery,
} from '@/api/generated-client'
import {
  RichTextValue,
  TemplateContent,
} from '@/components/template-renderer/types'

import { useOfferContext } from '../send-offer-dialog/offer-context'
import { TemplateTabs } from '../send-offer-dialog/types'
import { TemplateRenderer } from '../template-renderer/template-renderer'

import { EmailSignature } from './email-signature'
import { SMSPreview } from './sms-preview'

export type EditorProps = {
  draftContent?: TemplateContent[]
  linkToOffer?: { url?: string | null; text?: string | null }
  estate?:
    | NonNullable<GQLAgreementAndInspectionQuery['estate']>
    | NonNullable<GQLGetBrokerEstateQuery['estate']>
    | null
  onChange: (
    value: RichTextValue,
    id: string,
    type: 'email' | 'sms' | 'emailSubject',
  ) => void
  options?: {
    richText?: boolean
  }
  broker?: ReturnType<typeof getBrokerByRole>
}

type EmailEditorProps = EditorProps & {
  signature?: string | null
  subject: string
  className?: string
}

export function EmailEditor({
  draftContent,
  linkToOffer,
  subject,
  signature,
  onChange,
  options,
  className,
}: EmailEditorProps) {
  if (!draftContent) return null

  return (
    <div className={cn('border border-muted rounded-lg pb-4 mt-2', className)}>
      <div className="flex gap-1 items-baseline p-2">
        <p className="typo-body-md font-medium pl-3">Emne:</p>
        <Input
          value={subject}
          onChange={(e) => {
            onChange({ text: e.target.value }, 'emailSubject', 'emailSubject')
          }}
          placeholder="Skriv emne på e-posten"
          wrapperClassName="focus-within:ring-0 ring-0 rounded-sm"
          className={cn(
            'grow bg-root focus-within:bg-gray-muted rounded-sm p-2',
            'hover:bg-gray-muted break-words max-w-full',
          )}
        />
      </div>

      <Separator />
      <div className="p-2 max-w-full">
        <TemplateRenderer
          onChange={(value, id) => onChange(value, id, 'email')}
          draftContent={draftContent}
          link={linkToOffer}
          options={options}
          tab={TemplateTabs.EMAIL}
        />

        {signature && (
          <div className="px-3 overflow-x-scroll hide-scrollbar w-full">
            <EmailSignature signature={signature} />
          </div>
        )}
      </div>
    </div>
  )
}

type EmailEditorWrapperProps = PropsWithChildren<{
  subject: string
  changeSubject: (s: string) => void
  className?: string
  recipient?: string
}>

export function EmailEditorWrapper({
  subject,
  recipient,
  changeSubject,
  className,
  children,
}: EmailEditorWrapperProps) {
  return (
    <div className={cn('border border-muted rounded-lg pb-4', className)}>
      <div className="flex gap-1 items-baseline p-2">
        <p className="typo-body-md font-medium pl-3">Emne:</p>
        <Input
          value={subject}
          onChange={(e) => {
            changeSubject(e.target.value)
          }}
          placeholder="Skriv emne på e-posten"
          wrapperClassName="focus-within:ring-0 ring-0 rounded-sm"
          className={cn(
            'grow bg-root focus-within:bg-gray-muted rounded-sm p-2',
            'hover:bg-gray-muted break-words max-w-full',
          )}
        />
      </div>
      {recipient && (
        <>
          <Separator />

          <div className="flex gap-1 items-baseline p-2">
            <p className="typo-body-md font-medium pl-3">Mottaker:</p>
            <Input
              value={recipient}
              disabled
              placeholder="Skriv emne på e-posten"
              wrapperClassName="focus-within:ring-0 ring-0 rounded-sm"
              className={cn(
                'grow bg-root focus-within:bg-gray-muted rounded-sm p-2',
                'hover:bg-gray-muted break-words max-w-full',
              )}
            />
          </div>
        </>
      )}
      <Separator />
      <div className="p-4 max-w-full">{children}</div>
    </div>
  )
}

export function SmsEditor({
  draftContent,
  linkToOffer,
  estate,
  onChange,
  options,
}: EditorProps) {
  const { selectedChannels } = useOfferContext()
  if (!draftContent) return null
  const totalUsedCharacters = draftContent.reduce((acc, block) => {
    if (block.type === 'link') {
      return acc + (linkToOffer?.url?.length ?? 0)
    }
    return acc + (block.value.text?.length ?? 0)
  }, 0)

  const isDisabled = !selectedChannels.sms

  return (
    <div className="flex flex-col h-full">
      <ChannelSelector />
      <div
        className={cn(
          'border border-muted rounded-lg p-2 transition-colors duration-200',
          isDisabled && 'bg-inputs-fill-disabled border-muted',
        )}
      >
        <TemplateRenderer
          onChange={(draftContent, id) => onChange(draftContent, id, 'sms')}
          draftContent={draftContent}
          link={linkToOffer}
          options={options}
          tab={TemplateTabs.SMS}
          isDisabled={isDisabled}
        />
      </div>
      <span className={cn('typo-body-sm ink-muted mt-2')}>
        {`${totalUsedCharacters} tegn brukt`}
      </span>
      <div className="mt-6">
        <SMSPreview
          draftContent={draftContent}
          link={linkToOffer}
          estate={estate}
          isDisabled={isDisabled}
        />
      </div>
    </div>
  )
}

function ChannelSelector() {
  const { selectedChannels, handleSelectChannel } = useOfferContext()
  return (
    <div className="flex gap-2 mb-6">
      <label className="mt-0.5">
        <Checkbox
          id="select-sms-channel"
          checked={selectedChannels.sms}
          onCheckedChange={(checked) =>
            handleSelectChannel('sms', Boolean(checked))
          }
        />
      </label>
      <div className="space-y-1">
        <label htmlFor="select-sms-channel" className="cursor-pointer">
          Send sms i tillegg til e-post
        </label>
        <p className="ink-muted">
          Hvis du kun sender e-post er det lavere sjanse for at den blir åpnet,
          og e-post kan også havne i spam.
        </p>
      </div>
    </div>
  )
}
