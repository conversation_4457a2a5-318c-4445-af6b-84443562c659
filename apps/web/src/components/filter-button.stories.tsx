import { DetailedComponent, StoryPartHeading } from '@nordvik/ui/story-blocks'

import FilterButton from './filter-button'

export default {
  title: 'Components / Filter Button',
  parameters: {
    theme: 'both',
  },
}

export function FilterButtonStory() {
  return (
    <div className="flex flex-col gap-12">
      <div>
        <StoryPartHeading>Default</StoryPartHeading>
        <div className="flex gap-2">
          <DetailedComponent name="Default">
            <FilterButton onClick={() => null} />
          </DetailedComponent>
          <DetailedComponent name="Filtered">
            <FilterButton badgeText="3 valgt" onClick={() => null} />
          </DetailedComponent>
          <DetailedComponent name="Disabled">
            <FilterButton badgeText="3 valgt" onClick={() => null} disabled />
          </DetailedComponent>
        </div>
      </div>
      <div className="flex gap-2">
        <FilterButton onClick={() => null} iconOnly />
        <FilterButton badgeText="3 valgt" onClick={() => null} iconOnly />
        <FilterButton
          badgeText="3 valgt"
          onClick={() => null}
          disabled
          iconOnly
        />
      </div>
    </div>
  )
}
FilterButtonStory.storyName = 'FilterButton'
