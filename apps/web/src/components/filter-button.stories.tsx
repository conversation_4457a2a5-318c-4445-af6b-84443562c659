import { DetailedComponent, StoryPartHeading } from '@nordvik/ui/story-blocks'

import FilterButton from './filter-button'

export default {
  title: 'Components / Filter Button',
  parameters: {
    theme: 'both',
  },
}

export function FilterButtonStory() {
  return (
    <div className="flex flex-col gap-12">
      <div>
        <StoryPartHeading>Default</StoryPartHeading>
        <div className="flex gap-2">
          <DetailedComponent name="Default">
            <FilterButton selectedCount={0} onClick={() => null} />
          </DetailedComponent>
          <DetailedComponent name="Filtered">
            <FilterButton selectedCount={3} onClick={() => null} />
          </DetailedComponent>
          <DetailedComponent name="Disabled">
            <FilterButton selectedCount={3} onClick={() => null} disabled />
          </DetailedComponent>
        </div>
      </div>
      <div className="flex gap-2">
        <FilterButton selectedCount={0} onClick={() => null} iconOnly />
        <FilterButton selectedCount={3} onClick={() => null} iconOnly />
        <FilterButton
          selectedCount={3}
          onClick={() => null}
          disabled
          iconOnly
        />
      </div>
    </div>
  )
}
FilterButtonStory.storyName = 'FilterButton'
