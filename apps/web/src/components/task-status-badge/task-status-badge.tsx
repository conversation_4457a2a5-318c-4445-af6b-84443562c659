import { Check, Circle, X } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'

import { Icons } from '@/components/icons'

import PendingSpinner from './pending-spinner'

export const taskBadgeState = {
  COMPLETE: 'COMPLETE',
  ERROR: 'ERROR',
  STARTED: 'STARTED',
  NONE: 'NONE',
  PENDING: 'PENDING',
}

export type TaskBadgeState = keyof typeof taskBadgeState

type Props = {
  status: TaskBadgeState
  text?: string
}

type BadgeStyles = {
  badgeStyle: string
  textStyle: string
}

const getStyles = (status: string): BadgeStyles => {
  switch (status) {
    case taskBadgeState.COMPLETE:
      return {
        badgeStyle: cn(
          'border-success-muted bg-fill-success-subtle text-ink-on-success-emphasis',
        ),
        textStyle: cn('typo-body-xs text-ink-on-success'),
      }
    case taskBadgeState.STARTED:
      return {
        badgeStyle: cn(
          'border-stroke-yellow-muted bg-fill-yellow-subtle fill-interactive-emphasis text-fill-interactive-emphasis',
        ),
        textStyle: cn('typo-body-xs text-ink-on-yellow'),
      }
    case taskBadgeState.ERROR:
      return {
        badgeStyle: cn('border-danger-muted bg-fill-danger-subtle ink-danger'),
        textStyle: cn('typo-body-xs text-ink-danger'),
      }
    case taskBadgeState.PENDING:
      return {
        badgeStyle: cn(
          'border-stroke-muted bg-root text-fill-interactive-emphasis',
        ),
        textStyle: cn('typo-body-xs text-ink-on-gray-subtle'),
      }
    default:
      return {
        badgeStyle: cn(
          'border-stroke-muted bg-fill-gray-muted text-fill-interactive-emphasis',
        ),
        textStyle: cn('typo-body-xs text-ink-on-gray-subtle'),
      }
  }
}

const StatusIcon = ({ status }: { status: string }) => {
  switch (status) {
    case taskBadgeState.COMPLETE:
      return <Check className="size-4" />
    case taskBadgeState.STARTED:
      return <Icons.standby className="size-4 text-ink-on-yellow-subtle" />
    case taskBadgeState.ERROR:
      return <X className="size-4" />
    case taskBadgeState.PENDING:
      return <PendingSpinner />
    default:
      return <Circle className="size-4 p-0.5" />
  }
}

const TaskStatusBadge = ({ status, text }: Props) => {
  const styles = getStyles(status)

  return (
    <div
      className={cn(
        'flex gap-0.5 items-center rounded-sm border p-1 w-fit h-fit',
        text && 'px-1.5',
        styles.badgeStyle,
      )}
    >
      <StatusIcon status={status} />
      {text && <span className={styles.textStyle}>{text}</span>}
    </div>
  )
}

export default TaskStatusBadge
