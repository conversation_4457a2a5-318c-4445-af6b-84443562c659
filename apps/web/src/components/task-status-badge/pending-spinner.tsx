export function PendingSpinner({ progress = 0.75 }: { progress?: number }) {
  const radius = 6
  const stroke = 2
  const circumference = 2 * Math.PI * radius
  const offset = circumference * (1 - progress)
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      className="shrink-0 animate-spin"
    >
      <circle
        cx="8"
        cy="8"
        r={radius}
        strokeWidth={stroke}
        fill="none"
        className="stroke-muted"
      />
      <circle
        cx="8"
        cy="8"
        r={radius}
        strokeWidth={stroke}
        fill="none"
        className="stroke-emphasis"
        strokeDasharray={circumference}
        strokeDashoffset={offset}
        transform="rotate(-90 8 8)"
        strokeLinecap="round"
      />
    </svg>
  )
}

export default PendingSpinner
