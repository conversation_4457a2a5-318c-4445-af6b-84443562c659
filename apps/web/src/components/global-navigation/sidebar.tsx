import Image from 'next/image'
import { notFound } from 'next/navigation'

import { Link } from '@nordvik/ui/global-navigation-progress/link'

import { auth } from '@/auth'

import logo from '../../../public/logo-full-trimmed.svg'

import NavMenuContent from './nav-menu-content'

export default async function SideBar() {
  const session = await auth()
  if (!session) notFound()

  return (
    <nav
      className="group sticky top-0 flex h-screen w-[280px] shrink-0 flex-col bg-root-muted max-lg:hidden overflow-y-auto"
      data-theme="dark"
    >
      <div className="p-4 w-full">
        <Link href="/dashboard">
          <Image
            alt="Nordvik logo"
            className="my-[-1px] max-w-[126px]"
            src={logo}
            width={186}
            priority
          />
        </Link>
      </div>

      <NavMenuContent user={session.user} />
    </nav>
  )
}
