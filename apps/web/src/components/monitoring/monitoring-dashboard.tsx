'use client'

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  RefreshCw,
  Shield,
  XCircle,
} from 'lucide-react'
import { useEffect, useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@nordvik/ui/card'

interface ServiceHealth {
  service: string
  status: 'healthy' | 'degraded' | 'down'
  responseTime: number
  timestamp: string
  error?: string
  metadata?: Record<string, unknown>
}

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'down'
  healthyServices: number
  degradedServices: number
  downServices: number
  totalServices: number
  lastCheck: string | null
}

interface ServiceMetric {
  service: string
  consecutiveFailures: number
  lastAlertSent: string | null
  totalFailures: number
  firstFailureTime: string | null
}

interface MonitoringData {
  system: SystemHealth
  services: ServiceHealth[]
  metrics: ServiceMetric[]
  monitoring: {
    isActive: boolean
    lastCheck: string | null
  }
}

function getStatusIcon(
  status: 'healthy' | 'degraded' | 'down',
  size: 'sm' | 'md' = 'sm',
) {
  const iconProps = {
    className: cn(
      size === 'sm' ? 'h-4 w-4' : 'h-5 w-5',
      status === 'healthy' && 'ink-success',
      status === 'degraded' && 'ink-gold',
      status === 'down' && 'ink-danger',
    ),
  }

  switch (status) {
    case 'healthy':
      return <CheckCircle {...iconProps} />
    case 'degraded':
      return <AlertTriangle {...iconProps} />
    case 'down':
      return <XCircle {...iconProps} />
  }
}

function getSystemStatusIcon(status: 'healthy' | 'degraded' | 'down') {
  const iconProps = {
    className: cn(
      'h-6 w-6',
      status === 'healthy' && 'ink-success',
      status === 'degraded' && 'ink-gold',
      status === 'down' && 'ink-danger',
    ),
  }

  switch (status) {
    case 'healthy':
      return <Shield {...iconProps} />
    case 'degraded':
      return <AlertTriangle {...iconProps} />
    case 'down':
      return <XCircle {...iconProps} />
  }
}

function MetricCard({
  title,
  value,
  status,
}: {
  title: string
  value: string | number
  status?: 'healthy' | 'degraded' | 'down'
}) {
  return (
    <div className="space-y-1">
      <div className="flex items-center gap-2">
        <div className="typo-body-lg font-medium">{value}</div>
        {status && getStatusIcon(status, 'sm')}
      </div>
      <div className="typo-body-sm ink-muted">{title}</div>
    </div>
  )
}

export default function MonitoringDashboard() {
  const [data, setData] = useState<MonitoringData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/monitoring/health')
      if (!response.ok) {
        throw new Error('Failed to fetch monitoring data')
      }
      const result = await response.json()
      setData(result)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const forceHealthCheck = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/monitoring/health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
      if (!response.ok) {
        throw new Error('Failed to force health check')
      }
      // Refresh data after forcing check
      await fetchData()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const toggleMonitoring = async () => {
    try {
      const action = data?.monitoring.isActive ? 'stop' : 'start'
      const response = await fetch('/api/monitoring/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      })
      if (!response.ok) {
        throw new Error(`Failed to ${action} monitoring`)
      }
      await fetchData()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }

  useEffect(() => {
    fetchData()

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchData, 30000)
    return () => clearInterval(interval)
  }, [])

  if (loading && !data) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-lg">Loading monitoring data...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="ink-danger">
            <div className="typo-body-md font-medium">
              Error loading monitoring data
            </div>
            <div className="typo-body-sm">{error}</div>
            <Button
              size="lg"
              onClick={fetchData}
              className="mt-4"
              variant="outline"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data) {
    return null
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="typo-display-sm">Service Health Monitoring</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={forceHealthCheck}
            disabled={loading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={cn('h-3 w-3', loading && 'animate-spin')} />
          </Button>
          <Button
            onClick={toggleMonitoring}
            variant={data.monitoring.isActive ? 'tertiary' : 'default'}
            size="sm"
          >
            {data.monitoring.isActive ? 'Stop' : 'Start'}
          </Button>
        </div>
      </div>

      {/* System Overview Metrics */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="System Status"
          value={
            data.system.status.charAt(0).toUpperCase() +
            data.system.status.slice(1)
          }
          status={data.system.status}
        />
        <MetricCard
          title="Healthy Services"
          value={data.system.healthyServices}
        />
        <MetricCard
          title="Issues"
          value={data.system.degradedServices + data.system.downServices}
        />
        <MetricCard
          title="Monitoring"
          value={data.monitoring.isActive ? 'Active' : 'Inactive'}
        />
      </div>

      {/* System Health Overview */}
      <Card className="bg-root">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getSystemStatusIcon(data.system.status)}
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="w-full bg-fill-gray-subtle rounded-full h-2">
              <div
                className={cn(
                  'h-2 rounded-full transition-all duration-300',
                  data.system.status === 'healthy' && 'bg-fill-success-bold',
                  data.system.status === 'degraded' && 'bg-fill-gold-emphasis',
                  data.system.status === 'down' && 'bg-fill-danger-bold',
                )}
                style={{
                  width: `${(data.system.healthyServices / data.system.totalServices) * 100}%`,
                }}
              />
            </div>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="typo-body-md font-medium ink-success">
                  {data.system.healthyServices}
                </div>
                <div className="typo-body-xs ink-muted">Healthy</div>
              </div>
              <div>
                <div className="typo-body-md font-medium ink-gold">
                  {data.system.degradedServices}
                </div>
                <div className="typo-body-xs ink-muted">Degraded</div>
              </div>
              <div>
                <div className="typo-body-md font-medium ink-danger">
                  {data.system.downServices}
                </div>
                <div className="typo-body-xs ink-muted">Down</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Services List */}
      <Card className="bg-root">
        <CardHeader>
          <CardTitle>Services</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {data.services?.map((service) => (
              <div
                key={service.service}
                className="flex items-center justify-between py-3 px-4 rounded-lg bg-fill-gray-muted/20"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(service.status)}
                  <div>
                    <div className="typo-body-md font-medium">
                      {service.service}
                    </div>
                    {service.error && (
                      <div className="typo-body-sm ink-danger">
                        {service.error}
                      </div>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="typo-body-sm ink-muted">
                    {service.responseTime}ms
                  </div>
                </div>
              </div>
            ))}
            {(!data.services || data.services.length === 0) && (
              <div className="text-center ink-muted py-8">
                <div className="typo-body-sm">No services configured</div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Metrics */}
      {data.metrics && data.metrics.length > 0 && (
        <Card className="bg-root">
          <CardHeader>
            <CardTitle>Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.metrics.map((metric) => (
                <div
                  key={metric.service}
                  className="p-3 rounded-lg bg-fill-gray-muted/20"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="typo-body-md font-medium">
                      {metric.service}
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div>
                      <div className="typo-body-md font-medium ink-danger">
                        {metric.consecutiveFailures}
                      </div>
                      <div className="typo-body-xs ink-muted">Consecutive</div>
                    </div>
                    <div>
                      <div className="typo-body-md font-medium ink-default">
                        {metric.totalFailures}
                      </div>
                      <div className="typo-body-xs ink-muted">Total</div>
                    </div>
                    <div>
                      <div className="typo-body-md font-medium ink-muted">
                        {metric.lastAlertSent ? 'Sent' : 'Never'}
                      </div>
                      <div className="typo-body-xs ink-muted">Alert</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
