'use client'

import React from 'react'

import { customTemplates } from '@/lib/templates/templates'

import { RichTextValue, Template } from '../template-renderer/types'

import { TemplateReplacements } from './types'
import {
  createTemplateWithSimpleReplacements,
  isDraftDirty,
  updateDraftContent,
} from './utils'

export const useSimpleTemplates = (
  templateId?: string,
  replacements?: Partial<Record<TemplateReplacements[number], string>>,
  forceRegenerate?: unknown,
) => {
  const [draft, setDraft] = React.useState<Template<TemplateReplacements>>()
  const [initialVersion, setInitialVersion] =
    React.useState<Template<TemplateReplacements>>()
  const [dirty, setDirty] = React.useState(false)

  // Memoize replacements to prevent infinite re-renders
  const replacementsKey = JSON.stringify(replacements)
  const memoizedReplacements = React.useMemo(() => {
    return replacements || {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [replacementsKey])

  // Initialize template when templateId changes
  React.useEffect(() => {
    if (!templateId) return

    // Find template from both inspection and valuation templates
    const template = customTemplates.find((t) => t.id === templateId)

    if (template) {
      const processedTemplate = createTemplateWithSimpleReplacements(
        template,
        memoizedReplacements,
      )
      setDraft(processedTemplate)
      setInitialVersion(processedTemplate)
      setDirty(false)
    }
  }, [templateId, memoizedReplacements, forceRegenerate])

  const handleOnChangeTemplateContent = React.useCallback(
    (
      value: RichTextValue,
      id: string,
      type: 'emailSubject' | 'email' | 'sms',
    ) => {
      if (!draft) return

      const updatedDraft = updateDraftContent(draft, value, type, id, setDraft)
      if (!updatedDraft) return

      const isDirty = isDraftDirty(updatedDraft, initialVersion)
      setDirty(isDirty)
    },
    [draft, initialVersion],
  )

  const resetDraft = React.useCallback(() => {
    if (initialVersion) {
      setDraft(initialVersion)
      setDirty(false)
    }
  }, [initialVersion])

  return {
    draft,
    handleOnChangeTemplateContent,
    dirty,
    resetDraft,
    setDraft,
  }
}
