'use client'

import { PropsWithChildren, useCallback, useEffect, useRef } from 'react'

type Props = PropsWithChildren<{
  fetchNextPage: () => void
  isLoading: boolean
  lastPage?: boolean
}>

const InfinityScroll = ({
  children,
  fetchNextPage,
  isLoading,
  lastPage,
}: Props) => {
  const bottomRef = useRef<HTMLDivElement>(null)

  const handleInstersection = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const isIntersecting = entries[0]?.isIntersecting

      if (isIntersecting && !lastPage && !isLoading) fetchNextPage()
    },
    [fetchNextPage, isLoading, lastPage],
  )

  useEffect(() => {
    if (!bottomRef?.current) return
    const observer = new IntersectionObserver(handleInstersection, {
      threshold: 1.0,
    })
    observer.observe(bottomRef.current)
    return () => {
      observer.disconnect()
    }
  }, [handleInstersection])

  return (
    <>
      {children}
      <div ref={bottomRef} />
    </>
  )
}

export default InfinityScroll
