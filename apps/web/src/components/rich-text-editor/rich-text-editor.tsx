'use client'

import { marked } from 'marked'
import type QuillType from 'quill'
import 'quill/dist/quill.bubble.css'
import React, { useEffect, useRef } from 'react'

import { cn } from '@nordvik/theme/cn'

import { QUILL_FORMATS_EMAILS_SAFE, QUILL_MODULES, QUILL_THEME } from './config'
import { RichTextEditorProps } from './types'
import { useQuill } from './use-quill.hook'
import { convertHtmlToDelta, convertMarkdownToDelta, isMarkdown } from './utils'

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Start typing...',
  className,
  isDisabled = false,
  autoFocus = false,
  formats = QUILL_FORMATS_EMAILS_SAFE,
}) => {
  const editorRef = useRef<HTMLDivElement | null>(null)
  const { Quill, Delta, quillInstance } = useQuill()
  const quillRef = useRef<QuillType | null>(null)

  useEffect(() => {
    if (
      !Quill ||
      !editorRef.current ||
      quillInstance ||
      typeof document === 'undefined'
    )
      return

    // Initialize Quill with configs
    quillRef.current = new Quill(editorRef.current, {
      theme: QUILL_THEME,
      modules: QUILL_MODULES,
      formats,
      placeholder,
      readOnly: isDisabled,
      bounds: editorRef.current,
    })

    if (!quillRef.current) return
    // Set initial content

    if (value?.ops) {
      quillRef.current.setContents(value.ops)
    } else if (value?.text) {
      const delta = convertHtmlToDelta(value.text, Quill)
      if (delta) {
        quillRef.current.setContents(delta)
      }
    }

    // Handle paste events
    quillRef.current.clipboard.onPaste = async (range, { text, html }) => {
      if (!quillRef.current) return
      const selection = quillRef.current.getSelection()
      if (!selection) return
      if (isMarkdown(text ?? '')) {
        const markdown = await marked(text ?? '')
        const pastedDelta = await convertMarkdownToDelta(markdown, Quill)

        if (pastedDelta && Delta) {
          const newDelta = new Delta()
            .retain(range.index)
            .delete(range.length)
            .concat(pastedDelta)
          quillRef.current.updateContents(newDelta, 'user')
        }
      } else if (html) {
        const pastedDelta = convertHtmlToDelta(html, Quill)
        if (pastedDelta && Delta) {
          const newDelta = new Delta()
            .retain(range.index)
            .delete(range.length)
            .concat(pastedDelta)
          quillRef.current.updateContents(newDelta, 'user')
        }
      } else if (text && Delta) {
        const textDelta = new Delta().insert(text)
        const newDelta = new Delta()
          .retain(range.index)
          .delete(range.length)
          .concat(textDelta)
        quillRef.current.updateContents(newDelta, 'user')
      }
    }

    // Handle text changes
    quillRef.current.on('text-change', () => {
      if (!quillRef.current) return
      const delta = quillRef.current.getContents()
      // Pass only ops to avoid breaking the delta structure
      onChange({
        ops: delta.ops,
      })
    })
  }, [
    value?.ops,
    value?.text,
    onChange,
    placeholder,
    isDisabled,
    Quill,
    Delta,
    editorRef,
    quillInstance,
    formats,
  ])

  useEffect(() => {
    if (autoFocus && quillRef.current) {
      quillRef.current.focus()
      const length = quillRef.current.getLength()
      quillRef.current.setSelection(length - 1, 0)
    }
  }, [autoFocus, Quill])

  return (
    <div
      className={cn(
        'w-full',
        '[&_#editor]:text-[1rem] [&_*]:leading-6 [&_#editor]:font-grotesk [&_strong]:font-medium [&_ol]:!pl-0 [&_ul]:!pl-0',
        '[&_h1]:typo-display-md [&_h2]:typo-display-sm [&_h3]:typo-display-xs',
        '[&_.ql-editor.ql-blank::before]:not-italic',
        className,
      )}
    >
      <div
        className="flex flex-col *:grow"
        id="editor"
        role="textbox"
        ref={editorRef}
      />
    </div>
  )
}
