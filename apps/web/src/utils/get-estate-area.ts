import { ReactNode } from 'react'

import { GQLEstateByIdQuery } from '@/api/generated-client'

function getEstateArea(
  area: NonNullable<GQLEstateByIdQuery['estate']>['sumArea'],
): {
  label: string
  value: ReactNode
} {
  if (area?.braI) {
    return { label: 'BRA-i', value: area?.braI }
  }
  if (area?.pRom) {
    return { label: 'P-rom', value: area?.pRom }
  }
  return { label: 'BRA-i', value: 'Ukjent' }
}

export default getEstateArea
