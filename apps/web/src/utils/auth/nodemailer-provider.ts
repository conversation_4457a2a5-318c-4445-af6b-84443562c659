import Nodemailer, { NodemailerConfig } from 'next-auth/providers/nodemailer'
import { cookies } from 'next/headers'
import { createTransport } from 'nodemailer'

import { renderVerificationCodeEmail } from '@/lib/email-template/verification-code'

const sendVerificationRequest: NodemailerConfig['sendVerificationRequest'] =
  async (params) => {
    const { identifier, provider, token } = params
    const transport = createTransport(provider.server, {
      tls: { rejectUnauthorized: false },
    })
    const html = await renderVerificationCodeEmail({ token })
    const result = await transport.sendMail({
      to: identifier,
      from: {
        name: 'Nordvik Megler',
        address: provider.from ?? process.env.SMTP_FROM!,
      },
      subject: `Kode til innlogging`,
      text: `Sign in using the verification code: ${token}`,
      html: html,
    })

    const failed = result.rejected.concat(result.pending).filter(Boolean)
    if (failed.length) {
      throw new Error(`Email(s) (${failed.join(', ')}) could not be sent`)
    }
  }

export const nodemailerProvider = Nodemailer({
  id: 'email',
  maxAge: 600, // 10 minutes
  server: {
    service: 'Mandrill',
    host: process.env.SMTP_HOST,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
  },
  from: process.env.SMTP_FROM,
  sendVerificationRequest,
  generateVerificationToken: async () => {
    const cookieStore = await cookies()
    const isTech = cookieStore.get('is_tech')?.value

    if (isTech) {
      console.info('Sending code for tech user')
      cookieStore.delete('is_tech')
      return '1234'
    }

    // Get 4 random numbers
    return Math.random().toString().slice(-4)
  },
})
