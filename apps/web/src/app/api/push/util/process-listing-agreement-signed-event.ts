import { VitecListingAgreementSignedEvent } from './event'

export async function processListingAgreementSignedEvent(
  event: VitecListingAgreementSignedEvent,
) {
  console.info(
    `Processing listing agreement signed event for estate ${event.eventData.estateId}, document ${event.eventData.documentId}`,
  )

  // TODO: Implement actual logic for processing the event

  if (event.isMockEvent) {
    console.info(
      `Skipping further processing for mock event, estate ${event.eventData.estateId}`,
    )
    return
  }

  console.info(
    `Finished processing listing agreement signed event for estate ${event.eventData.estateId}`,
  )
}
