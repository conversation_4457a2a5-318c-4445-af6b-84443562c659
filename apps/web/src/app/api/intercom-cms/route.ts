import type { NextRequest } from 'next/server'

import { get, set } from '@/db/kv'
import { DAY } from '@/db/util'
import { CACHE_KEYS } from '@/lib/cache-keys'
import { craftCMS } from '@/server/connector/cmsConnector'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const secret = searchParams.get('secret')

  if (secret !== process.env.INTERCOM_CMS_SECRET) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const cached = await get(CACHE_KEYS.ARTICLE.INTERCOM_CMS)

  if (cached) {
    return Response.json(cached)
  }

  const resources = await craftCMS.getAllResources({
    page: 1,
    limit: 9999,
  })

  const fullArticles = await Promise.all(
    resources?.data.map((resource) => craftCMS.getNewsBySlug(resource.slug)) ??
      [],
  )

  await set(CACHE_KEYS.ARTICLE.INTERCOM_CMS, fullArticles, DAY)

  return Response.json(fullArticles)
}
