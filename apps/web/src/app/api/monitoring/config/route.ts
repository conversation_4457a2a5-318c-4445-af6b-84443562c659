import { NextRequest, NextResponse } from 'next/server'

import { getMonitoringService } from '@/lib/monitoring'
import { AlertChannel } from '@/lib/monitoring/types'

/**
 * Monitoring Configuration API
 * GET /api/monitoring/config - Get current monitoring configuration
 * POST /api/monitoring/config - Update monitoring configuration
 */

export async function GET() {
  try {
    const monitoringService = getMonitoringService()
    const config = monitoringService.getHealthChecker().getConfig()

    return NextResponse.json({
      checkInterval: config.checkInterval,
      retryAttempts: config.retryAttempts,
      alertThreshold: config.alertThreshold,
      degradedThreshold: config.degradedThreshold,
      services: config.services.map((service) => ({
        name: service.name,
        url: service.url,
        method: service.method,
        timeout: service.timeout,
        hasCustomCheck: !!service.customCheck,
      })),
      alertChannels: config.alertChannels,
      isActive: monitoringService.isActive(),
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch monitoring configuration' },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const monitoringService = getMonitoringService()
    const body = await request.json()

    const { action, ...data } = body

    switch (action) {
      case 'start':
        monitoringService.start()
        return NextResponse.json({ message: 'Monitoring started' })

      case 'stop':
        monitoringService.stop()
        return NextResponse.json({ message: 'Monitoring stopped' })

      case 'addAlertChannel':
        if (!data.channel) {
          return NextResponse.json(
            { error: 'Alert channel configuration required' },
            { status: 400 },
          )
        }
        monitoringService.addAlertChannel(data.channel as AlertChannel)
        return NextResponse.json({ message: 'Alert channel added' })

      case 'removeAlertChannel':
        if (!data.type || !data.config) {
          return NextResponse.json(
            { error: 'Alert channel type and config required' },
            { status: 400 },
          )
        }
        monitoringService.removeAlertChannel(data.type, data.config)
        return NextResponse.json({ message: 'Alert channel removed' })

      case 'resetMetrics':
        monitoringService.resetMetrics()
        return NextResponse.json({ message: 'Metrics reset' })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to update monitoring configuration' },
      { status: 500 },
    )
  }
}
