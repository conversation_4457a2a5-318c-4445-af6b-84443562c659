import { NextRequest, NextResponse } from 'next/server'

import { getMonitoringService } from '@/lib/monitoring'

export async function GET(request: NextRequest) {
  try {
    const monitoringService = getMonitoringService()

    const service = request.nextUrl.searchParams.get('service')

    let results
    if (service) {
      // Check specific service
      const result = await monitoringService.checkService(service)
      if (!result) {
        return NextResponse.json(
          { error: `Service "${service}" not found` },
          { status: 404 },
        )
      }
      results = [result]
    } else {
      // Check all services
      results = await monitoringService.performHealthCheck()
    }

    return NextResponse.json({
      results,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to perform health check' },
      { status: 500 },
    )
  }
}
