import { NextResponse } from 'next/server'

import { getMonitoringService } from '@/lib/monitoring'

/**
 * Health Check API endpoint
 * GET /api/monitoring/health - Get current health status of all services
 * POST /api/monitoring/health - Force a health check
 */

export async function GET() {
  try {
    const monitoringService = getMonitoringService()

    const systemHealth = monitoringService.getSystemHealth()
    const lastResults = monitoringService.getLastResults()
    const metrics = monitoringService.getServiceMetrics()

    const cachedResults = await monitoringService.getCachedResults()

    return NextResponse.json({
      system: systemHealth,
      services: lastResults.length > 0 ? lastResults : cachedResults,
      metrics: Array.from(metrics.entries()).map(([service, metric]) => ({
        service,
        ...metric,
      })),
      monitoring: {
        isActive: monitoringService.isActive(),
        lastCheck: systemHealth.lastCheck,
      },
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch health status' },
      { status: 500 },
    )
  }
}
