import gql from 'graphql-tag'

import { getNextOverviewUrl } from '@/actions/get-next-overview-url'
import { sendMail } from '@/actions/mail/sendMail'
import prisma from '@/db/prisma'
import { renderSendEtakstRemindersEmail } from '@/lib/email-template/send-etakst-reminders'
import { executeRawQuery } from '@/server/nordvik-client-adaptor'
import { isNotNull } from '@/server/utils'

type EstatesResponse = {
  estatesGroup: {
    estatesEntries: {
      estateId: string
      employee: {
        firstName: string
        name: string
        email: string
      }
      address?: {
        streetAdress: string
      }
    }[]
  }
}

export async function sendEtakstReminders(
  notSentEtaksts: { id: string; estate_id: string }[],
) {
  if (notSentEtaksts.length === 0) {
    console.info('No reminders to send')
    return
  }

  const entries = await getEstateAddressAndBroker(
    notSentEtaksts.map((notSent) => notSent.estate_id),
  )

  const promises = entries.map(async (entry) => {
    try {
      await sendEtakstReminder({
        estateId: entry.estateId,
        email: entry.employee.email,
        name: entry.employee.firstName,
        address: entry.address?.streetAdress ?? '',
      })

      // Update the reminder count in the database later
      return notSentEtaksts.find(
        (notSent) => notSent.estate_id === entry.estateId,
      )?.id
    } catch (e) {
      console.error(
        `Failed to send reminder for estate ${entry.estateId} to ${entry.employee.email}:`,
        e,
      )
    }
  })

  const reminderIdsWithNulls = await Promise.all(promises)

  await prisma.etakst_check_queue.updateMany({
    where: {
      id: {
        in: reminderIdsWithNulls.filter(isNotNull),
      },
    },
    data: {
      reminder_count: {
        increment: 1,
      },
      last_reminder_at: new Date(),
    },
  })
}

async function getEstateAddressAndBroker(estateIds: string[]) {
  const { estatesGroup } = await executeRawQuery<EstatesResponse>(
    gql`
      query Estates($ids: [String]) {
        estatesGroup(ids: $ids) {
          estatesEntries {
            estateId
            employee {
              firstName
              name
              email
            }
            address {
              streetAdress
            }
          }
        }
      }
    `,
    {
      ids: estateIds,
    },
  )

  return estatesGroup.estatesEntries
}

async function sendEtakstReminder({
  estateId,
  email,
  name,
  address,
}: {
  estateId: string
  email: string
  name: string
  address: string
}) {
  const vitecLink = await getNextOverviewUrl(estateId)
  const body = await renderSendEtakstRemindersEmail({
    vitecLink: vitecLink ?? undefined,
    name,
    address,
  })
  await sendMail({
    emails: [{ email, name }],
    subject: `E-takst for ${address} er ikke delt med selger`,
    from: {
      email: process.env.NO_REPLY_EMAIL ?? '<EMAIL>',
      name: 'Nordvik',
    },
    title: '',
    initialReceiver: { email, name },
    body,
  })
}
