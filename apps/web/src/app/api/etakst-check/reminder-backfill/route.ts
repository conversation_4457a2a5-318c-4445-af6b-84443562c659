import { NextResponse } from 'next/server'

import { processEtakstReminderBackfillQueue } from '@/utils/etakst/etakst-checker'

export async function GET() {
  try {
    await processEtakstReminderBackfillQueue()
    return NextResponse.json(
      { success: true, message: 'Reminder backfill e-takst queue processed' },
      { status: 200 },
    )
  } catch (error) {
    console.error('Error processing reminder backfill e-takst queue:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Error processing reminder backfill e-takst queue',
      },
      { status: 500 },
    )
  }
}
