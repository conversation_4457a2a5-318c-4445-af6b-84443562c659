import { NextRequest, NextResponse } from 'next/server'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'
import { get, set } from '@/db/kv'
import { DAY, MINUTE } from '@/db/util'
import { CACHE_KEYS } from '@/lib/cache-keys'
import { createEstateDocumentsLoader } from '@/server/model/AssignmentDocumentStatus/helpers/estate-documents-loader'
import { providersForStatus } from '@/server/model/AssignmentDocumentStatus/helpers/provider-types'
import {
  getStrategyOrThrow,
  isBatchingStrategy,
} from '@/server/model/AssignmentDocumentStatus/strategies'
import type { StrategyFetchContext } from '@/server/model/AssignmentDocumentStatus/strategies'
import {
  ProviderDefinition,
  RawProviderStatus,
  withTimeout,
} from '@/server/model/AssignmentDocumentStatus/types'

export const dynamic = 'force-dynamic'
export const maxDuration = 30

export async function POST(
  req: NextRequest,
  props: { params: Promise<{ status: GQLEstateTabFilter }> },
) {
  const params = await props.params
  const status = params.status
  const body = await req.json().catch(() => null)
  const estates = body?.estates ?? ([] as GQLEstatesOverviewItemFragment[])

  if (estates.length === 0) {
    return new Response('Missing estateId', { status: 400 })
  }

  let keepAlive: ReturnType<typeof setInterval> | undefined
  let aborted = false

  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder()

      const closeStream = () => {
        aborted = true
        if (keepAlive) clearInterval(keepAlive)
        try {
          controller?.close()
        } catch {
          // ignore
        }
      }

      const send = (obj: unknown) => {
        if (aborted) return
        try {
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(obj)}\n\n`))
        } catch {
          closeStream()
        }
      }

      keepAlive = setInterval(() => {
        if (aborted) return
        try {
          controller.enqueue(encoder.encode(': keep-alive\n\n'))
        } catch {
          closeStream()
        }
      }, 15000)

      const tasks: Promise<void>[] = []

      const STALE_THRESHOLD_SECONDS = MINUTE / 2 // 30s

      // fetch a single status (with per-item timeout), persist to KV, and emit
      const refreshOne = async (
        estateId: string,
        p: ProviderDefinition,
        ctx?: StrategyFetchContext,
      ) => {
        if (req.signal.aborted) return
        const timeoutMs = p.timeoutMs ?? 5000
        const strategy = getStrategyOrThrow(p.type)
        const msg = await withTimeout(
          strategy.fetch(estateId, ctx),
          timeoutMs,
        ).catch((e) => ({
          type: p.type,
          state: 'ERROR' as const,
          updatedAt: new Date().toISOString(),
          message: (e as Error)?.message ?? 'Ukjent feil',
        }))
        const payload: RawProviderStatus & { cachedAt: string } = {
          ...msg,
          cachedAt: new Date().toISOString(),
        }

        // Update cache with long TTL
        await set(
          CACHE_KEYS.ASSIGNMENT_DOCUMENT_STATUS.BY_ESTATE_AND_TYPE(
            estateId,
            p.type,
          ),
          payload,
          DAY * 2,
        ).catch((e) => {
          console.warn(`Cache set failed for ${estateId}, type ${p?.type}`, e)
        })

        send({ estateId, ...payload })
      }

      // gather cache hits and refresh groups per provider
      const documentsLoader = createEstateDocumentsLoader()

      type RefreshEntry = {
        estate: GQLEstatesOverviewItemFragment
        provider: ProviderDefinition
        providerType: ProviderDefinition['type']
      }

      const refreshGroups = new Map<
        ProviderDefinition['type'],
        RefreshEntry[]
      >()

      const estateCacheTasks = estates.map(async (estate) => {
        const providers = providersForStatus(status, estate)
        for (const p of providers) {
          const key = CACHE_KEYS.ASSIGNMENT_DOCUMENT_STATUS.BY_ESTATE_AND_TYPE(
            estate.estateId,
            p.type,
          )
          const cached = await get<RawProviderStatus & { cachedAt?: string }>(
            key,
          ).catch((e) => {
            console.warn(
              `Cache get failed for ${estate.estateId}, type ${p?.type}`,
              e,
            )
          })

          if (cached) {
            send({ estateId: estate.estateId, ...cached })
          }

          let shouldRefresh = !cached
          if (cached?.cachedAt) {
            const ts = Date.parse(cached.cachedAt)
            const ageSec = Number.isNaN(ts)
              ? Infinity
              : (Date.now() - ts) / 1000
            shouldRefresh = ageSec > STALE_THRESHOLD_SECONDS
          }

          if (shouldRefresh) {
            const arr = refreshGroups.get(p.type) ?? []
            arr.push({ estate, provider: p, providerType: p.type })
            if (!refreshGroups.has(p.type)) refreshGroups.set(p.type, arr)
          }
        }
      })

      tasks.push(
        Promise.allSettled(estateCacheTasks).then(async () => {
          // per provider refresh
          const providerTasks: Promise<unknown>[] = []
          for (const [type, entries] of refreshGroups.entries()) {
            if (entries.length === 0) continue
            const strategy = getStrategyOrThrow(type)
            const loader = isBatchingStrategy(strategy)
              ? strategy.getLoader()
              : undefined
            const refreshPromises = entries.map(({ estate, provider }) =>
              refreshOne(estate.estateId, provider, {
                loader,
                documentsLoader,
                estate,
              }).catch((e) => {
                console.error(
                  `Unexpected error for estate ${estate.estateId}, provider ${type}`,
                  e,
                )
              }),
            )
            providerTasks.push(Promise.allSettled(refreshPromises))
          }
          await Promise.allSettled(providerTasks)
        }),
      )

      // Close stream after all tasks settle (no throw)
      Promise.allSettled(tasks).finally(() => {
        closeStream()
      })

      req.signal.addEventListener('abort', () => {
        closeStream()
      })
    },
    cancel() {
      if (keepAlive) clearInterval(keepAlive)
      aborted = true
    },
  })

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      Connection: 'keep-alive',
    },
  })
}
