import DataLoader from 'dataloader'

import prisma from '@/db/prisma'

export function createInspectionEventLoader() {
  return new DataLoader(async (estateIds: readonly string[]) => {
    const events = await prisma.inspection_event.findMany({
      where: {
        estate_id: {
          in: estateIds as string[],
        },
        deleted_at: null,
      },
      orderBy: {
        start: 'asc',
      },
    })

    const eventsByEstateId: Record<string, typeof events> = {}
    for (const event of events) {
      if (!event.estate_id) continue
      if (!eventsByEstateId[event.estate_id]) {
        eventsByEstateId[event.estate_id] = []
      }
      eventsByEstateId[event.estate_id].push(event)
    }
    return estateIds.map((entry) => eventsByEstateId[entry] ?? [])
  })
}
