import DataLoader from 'dataloader'

import nordvikApi from '@/server/nordvik-client-adaptor'

export function createEmployeeLoader() {
  return new DataLoader(async (employeeIds: readonly string[]) => {
    return Promise.all(
      employeeIds.map(async (employeeId) => {
        const { employee } = await nordvikApi.employeeWithRatingAndAwards({
          employeeId,
        })
        return employee
      }),
    )
  })
}
