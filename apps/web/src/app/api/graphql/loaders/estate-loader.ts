import DataLoader from 'dataloader'

import nordvikApi from '@/server/nordvik-client-adaptor'

export function createEstateLoader() {
  return new DataLoader(async (estateIds: readonly string[]) => {
    return Promise.all(
      estateIds.map(async (estateId) => {
        const { estate } = await nordvikApi.getEstateById({
          estateId,
          statuses: [-1, 0, 1, 2, 3, 4, 5],
        })
        return estate
      }),
    )
  })
}
