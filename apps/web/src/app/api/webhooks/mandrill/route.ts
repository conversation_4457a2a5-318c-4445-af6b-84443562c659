// import { Prisma } from '@nordvik/database'
// import crypto from 'crypto'
// import crypto from 'crypto'
import { NextRequest, NextResponse, after } from 'next/server'

import { Prisma } from '@nordvik/database'

import { prisma } from '@/db/prisma'

// Mandrill posts form-encoded with a 'mandrill_events' JSON array
// We verify signature when MANDRILL_WEBHOOK_KEY + URL is available

// export function verifyMandrillSignature(req: NextRequest, rawBody: string) {
//   const key = process.env.MANDRILL_WEBHOOK_KEY
//   const url = process.env.MANDRILL_WEBHOOK_URL // Full URL registered in Mandrill
//   if (!key || !url) return true // fail open if not configured (optional: change to false)

//   const sigHeader = req.headers.get('X-Mandrill-Signature')
//   if (!sigHeader) return false

//   // Mandrill signature base: url + sorted params (but when using raw body we can reconstruct via URLSearchParams)
//   const params = new URLSearchParams(rawBody)
//   // Order alphabetically by key
//   const pairs = [...params.entries()].sort(([a], [b]) => a.localeCompare(b))
//   const signatureBase = url + pairs.map(([k, v]) => k + v).join('')
//   const computed = crypto
//     .createHmac('sha1', key)
//     .update(signatureBase)
//     .digest('base64')
//   return computed === sigHeader
// }

interface MandrillEventMsgMeta {
  email_audit_id?: string
  [key: string]: unknown
}

interface MandrillOpenEntry {
  ts?: number
  ip?: string
  location?: string | null
  ua?: string
  [key: string]: unknown
}

interface MandrillEventMsg {
  email?: string
  subject?: string
  tags?: string[]
  state?: string
  diag?: string
  metadata?: MandrillEventMsgMeta
  opens?: MandrillOpenEntry[] | number
  [key: string]: unknown
}

interface MandrillEvent {
  _id: string
  event: string
  ts: number
  msg?: MandrillEventMsg
  opens?: unknown
  clicks?: unknown
  [key: string]: unknown
}

export async function POST(req: NextRequest) {
  const rawBody = await req.text() // body is form-encoded
  //   if (!verifyMandrillSignature(req, rawBody)) {
  //     return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
  //   }

  after(async () => {
    const form = new URLSearchParams(rawBody)
    const eventsRaw = form.get('mandrill_events')
    if (!eventsRaw) {
      return console.error('No mandrill_events found')
    }

    let events: MandrillEvent[] = []
    try {
      events = JSON.parse(eventsRaw)
    } catch (e) {
      return console.error('Failed to parse mandrill_events', e)
    }

    const updates: Promise<unknown>[] = []

    for (const ev of events) {
      const email = ev.msg?.email
      const messageId = ev._id

      if (!email || !messageId) continue

      const eventTime = new Date(ev.ts * 1000)
      const statusEvent = ev.event

      // Map Mandrill events to our status values
      const getStatusMapping = (event: string): string => {
        switch (event) {
          case 'open':
            return 'opened'
          case 'hard_bounce':
          case 'soft_bounce':
            return 'bounced'
          case 'reject':
          case 'reject_spam':
            return 'rejected'
          case 'send':
            return 'sent'
          default:
            return event
        }
      }

      const mappedStatus = getStatusMapping(statusEvent)

      // Determine earliest open time (if any)
      let earliestOpenDate: Date | null = null
      let openCount = 0
      if (Array.isArray(ev.msg?.opens) && ev.msg.opens.length > 0) {
        const openTs = (ev.msg.opens as MandrillOpenEntry[])
          .map((o) => (typeof o?.ts === 'number' ? (o.ts as number) : null))
          .filter((v): v is number => typeof v === 'number')

        if (openTs.length) {
          openCount = openTs.length
          earliestOpenDate = new Date(Math.min(...openTs) * 1000)
        }
      }
      // Fallback: if event itself is an open and we have no opens array
      if (!earliestOpenDate && mappedStatus === 'opened') {
        openCount = 1
        earliestOpenDate = eventTime
      }

      // Expect email_audit_id only
      const emailAuditId = ev.msg?.metadata?.email_audit_id
      if (!emailAuditId || typeof emailAuditId !== 'string') {
        // Without audit id we can't store recipient event
        continue
      }

      updates.push(
        prisma.email_audit_recipient.upsert({
          where: {
            message_id_recipient_email: {
              message_id: messageId,
              recipient_email: email,
            },
          },
          create: {
            email_audit_id: emailAuditId,
            recipient_email: email,
            message_id: messageId,
            status: mappedStatus,
            sent_at: eventTime,
            last_event_at: eventTime,
            opened_at: earliestOpenDate ?? (openCount > 0 ? eventTime : null),
            open_count: openCount,
            bounce_type:
              mappedStatus === 'bounced' ? (ev.msg?.diag ?? null) : null,
            reject_reason:
              mappedStatus === 'rejected' ? (ev.msg?.diag ?? null) : null,
            raw_last_event: ev as Prisma.InputJsonObject,
          },
          update: {
            open_count: openCount > 0 ? { set: openCount } : undefined,
            // Set opened_at only if we don't already have one and we have an earliestOpenDate
            opened_at: earliestOpenDate ?? (openCount > 0 ? eventTime : null),
            last_event_at: eventTime,
            status: mappedStatus !== 'sent' ? mappedStatus : undefined,
            bounce_type:
              mappedStatus === 'bounced' ? (ev.msg?.diag ?? null) : undefined,
            reject_reason:
              mappedStatus === 'rejected' ? (ev.msg?.diag ?? null) : undefined,
            raw_last_event: ev as Prisma.InputJsonObject,
          },
        }),
      )
    }

    await Promise.all(updates)
  })

  return NextResponse.json({ success: true })
}
