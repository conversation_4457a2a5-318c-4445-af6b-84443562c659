'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Close,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@radix-ui/react-dialog'
import { FingerprintIcon } from 'lucide-react'
import { signIn } from 'next-auth/webauthn'
import dynamic from 'next/dynamic'
import React from 'react'

import { Button } from '@nordvik/ui/button'
import { DialogContentWrapper } from '@nordvik/ui/dialog'
import { DialogCloseButton, DialogContainer } from '@nordvik/ui/dialog-blocks'
import { TextButton } from '@nordvik/ui/text-button'
import { useToast } from '@nordvik/ui/toaster'

import { GQLCmsArticleType } from '@/api/generated-client'
import { NordvikLoader } from '@/components/nordvik-loader'
import { useTrackEvent } from '@/lib/analytics/track-event'

const DynamicArticlePage = dynamic(
  () => import('@/components/articles/article-page'),
  {
    ssr: false,
    loading() {
      return <NordvikLoader />
    },
  },
)

export function PasskeyButton() {
  const [loading, setLoading] = React.useState(false)
  const { toast } = useToast()
  const handleOnClick = async () => {
    setLoading(true)
    try {
      await signIn('passkey')
    } catch (error) {
      console.error('Passkey signin failed', error)
      setLoading(false)
      toast({
        variant: 'destructive',
        title: 'Innlogging med Passkey mislyktes',
        duration: 1000 * 30,
        description: (
          <div className="space-y-1.5">
            <p>
              Det ser ut til at innloggingen med Passkey ble avbrutt. Hvis du
              ikke har satt opp en Passkey, kan du klikke på{' '}
              <em>&ldquo;Hva er Passkey?&rdquo;</em> for mer informasjon.
            </p>
            <p>
              iCloud og Google Chrome deler Passkey på tvers av enheter om man
              er logget inn med samme Apple ID eller Google-konto. Om du har
              lagt det til på annen enhet men ikke får brukt den her, så kan du
              legge til en ny for denne enheten.
            </p>
          </div>
        ),
      })
    }
  }
  return (
    <Button
      variant="outline"
      loading={loading}
      className="w-full"
      iconStart={<FingerprintIcon />}
      onClick={handleOnClick}
      size="lg"
    >
      Bruk Passkey
    </Button>
  )
}

export function PasskeyDialog() {
  const trackEvent = useTrackEvent()
  return (
    <Dialog
      onOpenChange={(open) => {
        if (open) {
          trackEvent('open_passkey_dialog')
        }
      }}
    >
      <DialogTrigger asChild>
        <TextButton className="typo-body-sm">Hva er Passkey?</TextButton>
      </DialogTrigger>
      <DialogContentWrapper>
        <DialogContent asChild>
          <DialogContainer size="lg">
            <DialogTitle className="sr-only">Hva er Passkey?</DialogTitle>
            <DialogClose asChild>
              <DialogCloseButton>Lukk</DialogCloseButton>
            </DialogClose>
            <DynamicArticlePage
              slug="passkey"
              type={GQLCmsArticleType.Resource}
            />
          </DialogContainer>
        </DialogContent>
      </DialogContentWrapper>
    </Dialog>
  )
}
