'use client'

import { AnimatePresence, motion, useAnimation } from 'framer-motion'
import { OTPInput, REGEXP_ONLY_DIGITS, SlotProps } from 'input-otp'
import { useRouter } from 'next/navigation'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import { NordvikLoaderLogo } from '@/components/nordvik-loader'

export function OTPForm({ email }: { email: string }) {
  const [isPending, setIsPending] = React.useState(false)
  const otpFormRef = React.useRef<HTMLFormElement>(null)
  const controls = useAnimation()
  const router = useRouter()
  const otpInputRef = React.useRef<HTMLInputElement>(null)
  const [otp, setOtp] = React.useState('')

  async function handleOtpSubmit(event?: React.FormEvent<HTMLFormElement>) {
    event?.preventDefault()
    controls.start('pending', { type: 'spring', damping: 20, stiffness: 150 })
    setIsPending(true)

    await new Promise((resolve) => setTimeout(resolve, 1000))

    const form = event?.currentTarget || otpFormRef.current
    if (!form) return

    try {
      if (otp.length !== 4) {
        throw new Error('Invalid OTP')
      }

      const url = new URL('/api/auth/callback/email', window.location.origin)
      url.searchParams.set('token', otp)
      url.searchParams.set('email', email)
      url.searchParams.set('callbackUrl', '/')
      const response = await fetch(url, { method: 'POST' })
      if (!response.ok) {
        throw new Error('Invalid OTP')
      }

      const responseUrl = new URL(response.url)

      if (responseUrl.searchParams.has('error')) {
        throw new Error(responseUrl.searchParams.get('error')!)
      }

      router.push('/')
    } catch (error) {
      controls.start('shake', { duration: 0.3, delay: 0.2 })
      setOtp('')
      form.focus()
      console.error(error)
      setIsPending(false)
    }
  }

  return (
    <form onSubmit={handleOtpSubmit} ref={otpFormRef} className="relative">
      <div className="typo-detail-md mb-4 ink-subtle text-center">
        Engangskode tilsendt på e-post
      </div>
      <motion.div
        animate={controls}
        style={{ overflow: 'clip' }}
        variants={{
          initial: {
            x: 0,
            scale: 1,
            opacity: 1,
          },
          shake: {
            x: [0, 6, -6, 6, -6, 0],
            scale: 1,
            opacity: 1,
          },
          pending: {
            x: 0,
            scale: 0.95,
            opacity: 0.6,
          },
        }}
      >
        <OTPInput
          maxLength={4}
          autoFocus
          textAlign="center"
          name="otp"
          pushPasswordManagerStrategy="none"
          data-1p-ignore="true"
          value={otp}
          onChange={(otp) => setOtp(otp)}
          onComplete={() => handleOtpSubmit()}
          ref={otpInputRef}
          containerClassName={cn(
            'self-stretch [&_[data-input-otp]]:!text-[2.4rem] [&_[data-input-otp]]:!tracking-[1.5em] [&_[data-input-otp]]:!indent-[0.6em]',
          )}
          pattern={REGEXP_ONLY_DIGITS}
          render={({ slots }) => (
            <div className="flex gap-4 justify-center">
              {slots.map((slot, idx) => (
                <Slot key={idx} {...slot} />
              ))}
            </div>
          )}
        />
      </motion.div>
      <AnimatePresence>
        {isPending && (
          <motion.div
            className="absolute inset-0 pt-12 pb-5"
            transition={{ type: 'spring', damping: 20, stiffness: 150 }}
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 0.9, scale: 1 }}
            exit={{ opacity: 0, scale: 1.1 }}
          >
            <NordvikLoaderLogo />
          </motion.div>
        )}
      </AnimatePresence>
    </form>
  )
}

function Slot(props: SlotProps) {
  return (
    <div
      className={cn(
        'relative group flex items-center justify-center',
        'font-medium ink-subtle text-[2.4rem] text-center',
        'bg-interactive-top w-[1.8em] h-[2em]  rounded-lg',
        'group-focus-within:bg-interactive-subtle',
        props.isActive && 'bg-interactive-subtle',
      )}
    >
      <div className="group-has-[input[data-input-otp-placeholder-shown]]:opacity-20">
        {props.char ?? props.placeholderChar}
      </div>
      {props.hasFakeCaret && <FakeCaret />}
    </div>
  )
}

// You can emulate a fake textbox caret!
function FakeCaret() {
  return (
    <div className="absolute pointer-events-none inset-0 flex items-center justify-center animate-caret-blink">
      <div className="w-[2px] h-8 bg-ink-on-brand rounded-sm" />
    </div>
  )
}
