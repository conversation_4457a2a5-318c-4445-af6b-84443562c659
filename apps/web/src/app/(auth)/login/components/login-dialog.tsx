'use client'

import { AnimatePresence, motion } from 'framer-motion'
import Image from 'next/image'
import React from 'react'

import { Separator } from '@nordvik/ui/separator'

import { EmailForm } from './email-form'
import { OTPForm } from './otp-form'
import { PasskeyButton, PasskeyDialog } from './passkey'

export function LoginDialog({ initialEmail }: { initialEmail?: string }) {
  const [email, setEmail] = React.useState('')

  return (
    <div
      data-theme="dark"
      className="flex flex-col items-center bg-root max-w-full w-[26rem] relative pt-12 pb-8 sm:rounded-md overflow-clip"
    >
      <Image
        alt=""
        height={77}
        src="/logo.svg"
        width={240}
        priority
        className="max-w-none h-[77px]"
      />

      <AnimatePresence mode="popLayout" initial={false}>
        {!email ? (
          <motion.div
            key="email"
            initial={{ opacity: 0, x: -60 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -60 }}
            transition={{ type: 'spring', damping: 20, stiffness: 250 }}
            className="self-stretch"
          >
            <EmailForm
              initialEmail={initialEmail}
              onEmailSubmit={(email) => setEmail(normalizeEmail(email))}
            />
          </motion.div>
        ) : (
          <motion.div
            key="otp"
            initial={{ opacity: 0, x: 60 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 60 }}
            transition={{ type: 'spring', damping: 20, stiffness: 150 }}
            className="mt-8 px-4 mb-6 self-stretch"
          >
            <OTPForm email={email} />
          </motion.div>
        )}
      </AnimatePresence>

      <Separator className="my-8" />
      <div className="flex flex-col items-center gap-2">
        <PasskeyButton />
        <PasskeyDialog />
      </div>
    </div>
  )
}

function normalizeEmail(email = '') {
  return email.trim().toLowerCase()
}
