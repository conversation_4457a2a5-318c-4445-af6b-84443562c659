import React from 'react'

import { Button } from '@nordvik/ui/button'
import { Input } from '@nordvik/ui/input'
import { Label } from '@nordvik/ui/label'

import { setCookie } from '@/utils/cookies'

export function EmailForm({
  initialEmail,
  onEmailSubmit,
}: {
  initialEmail?: string
  onEmailSubmit: (email: string) => void
}) {
  const [error, setError] = React.useState<string | null>(null)
  const [isPending, setIsPending] = React.useState(false)

  async function handleEmailSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    setError(null)
    setIsPending(true)
    try {
      const email = e.currentTarget.email.value
      const response = await fetch('/login/email', {
        method: 'POST',
        body: JSON.stringify({ email }),
      })
      if (response.ok) {
        setCookie('login-email', email, { days: 90 })
        onEmailSubmit(email)
      } else {
        throw new Error(await response.text())
      }
    } catch (error) {
      setError(error.message)
    } finally {
      setIsPending(false)
    }
  }

  return (
    <form key="email" onSubmit={handleEmailSubmit} className="self-stretch">
      <Label className="block px-10 mt-4 ink-subtle">
        E-postadresse
        <Input
          variant="fill"
          placeholder="<EMAIL>"
          type="email"
          name="email"
          data-1p-ignore="true"
          autoComplete="email"
          defaultValue={initialEmail || ''}
          errorMessage={error}
        />
        <Button
          className="mt-4 w-full"
          type="submit"
          loading={isPending}
          size="lg"
        >
          Få kode på e-post
        </Button>
      </Label>
    </form>
  )
}
