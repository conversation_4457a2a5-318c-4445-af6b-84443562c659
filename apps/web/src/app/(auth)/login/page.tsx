import type { Metadata } from 'next'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

import { Toaster } from '@nordvik/ui/toaster'

import { auth } from '@/auth'
import { backgroundVideos } from '@/components/looping-brand-videos/background-videos'
import { BrandLoopVideoPlayer } from '@/components/looping-brand-videos/video-player'

import { LoginDialog } from './components/login-dialog'

export const metadata: Metadata = {
  title: 'Logg inn',
  description: 'Logg inn på Nordvik Megler',
}

export default async function LoginPage() {
  const session = await auth()

  // Should be handled by middleware
  if (session) {
    redirect('/')
  }

  const randomVideoIndex = Math.floor(Math.random() * backgroundVideos.length)

  const initialEmail = (await cookies()).get('login-email')?.value

  return (
    <div data-theme="dark" className="absolute inset-0 flex-center bg-root">
      <BrandLoopVideoPlayer
        startIndex={randomVideoIndex}
        className="hidden sm:block bg-[#001c1f]"
      />
      <div className="relative z-30 max-w-full">
        <LoginDialog initialEmail={initialEmail} />
      </div>
      <Toaster />
    </div>
  )
}
