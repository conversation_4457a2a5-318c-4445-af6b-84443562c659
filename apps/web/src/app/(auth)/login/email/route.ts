import { AuthError } from 'next-auth'

import { signIn } from '@/auth'

export async function POST(request: Request) {
  const { email } = await request.j<PERSON>()

  try {
    await signInEmail(email)
    return new Response(null, { status: 200 })
  } catch (error) {
    return new Response(error.message, { status: 400 })
  }
}

async function signInEmail(email: string) {
  if (!email) throw new Error('Mangler e-postadresse')
  try {
    await signIn('email', {
      email,
      redirect: false,
    })
  } catch (error) {
    if (error instanceof AuthError) {
      switch (error.type) {
        case 'EmailSignInError':
        case 'AccessDenied':
          if (email.endsWith('@nordvikbolig.no')) {
            throw new Error(
              'Ingen tilgang. Husk å bruke e-postadressen du logger inn på Next, ikke alias.',
            )
          }
          throw new Error(
            'Du har ikke tilgang til Nordvik Megler. Ta kontakt med support om dette er feil.',
          )
        default:
          throw new Error(
            'Noe gikk galt med autentiseringen. Prøv igjen senere.',
          )
      }
    }
    throw error
  }
}
