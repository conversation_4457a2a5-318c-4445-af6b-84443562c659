import { upsertListingAgreement } from '@befaring/actions/listing-agreement'
import { cache } from 'react'

export const cachedUpsertListingAgreement = cache(
  async (
    estateId: string,
    discardBudgetposts = false,
    priceSuggestion?: number,
  ) =>
    upsertListingAgreement({
      estateId,
      discardBudgetposts,
      priceSuggestion,
    }),
)

export type CachedUpsertListingAgreement = NonNullable<
  Awaited<ReturnType<typeof cachedUpsertListingAgreement>>
>
