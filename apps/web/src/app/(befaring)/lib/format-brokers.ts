import type { GQLGetBrokerEstateQuery } from '@/api/generated-client'

import { BrokerRole, brokerTypes } from './broker-constants'

export interface ListingAgreementBroker {
  employeeId: string
  name: string
  mobilePhone: string
  email: string
  title: string
  roleName: string
  role: number
  imageSrc: string
}

export const isAuthorizedRepresentative = (
  employeeId: string,
  brokers: NonNullable<GQLGetBrokerEstateQuery['estate']>['brokers'],
) => {
  const broker = brokers?.find((b) => b?.employeeId === employeeId)
  return broker?.employeeRoles?.some((role) => role?.typeId === 6)
}

/**
 *
 * 1. Takes `brokersIdWithRoles` from the `estate` and formats it to return `roleName` and `role` for each broker.
 * 2. Sorts the brokers by role: 2 (responsible), 1 (main), 3 (assistant)
 * 3. Removes duplicates, keeping brokers with role 2
 *
 */
export function formatBrokersForContract(
  estate: Pick<
    NonNullable<GQLGetBrokerEstateQuery['estate']>,
    'brokers' | 'brokersIdWithRoles'
  >,
): ListingAgreementBroker[] {
  let brokers =
    estate?.brokersIdWithRoles?.filter(
      (b) => b?.brokerRole !== BrokerRole.SecondaryAssistant,
    ) ?? []
  const responsibleBroker = getBrokerByRole(BrokerRole.Responsible, {
    brokers: estate?.brokers,
    brokersIdWithRoles: estate?.brokersIdWithRoles,
  })

  if (
    responsibleBroker &&
    !brokers.find((b) => b?.brokerRole === BrokerRole.Responsible)
  ) {
    brokers.push({
      ...responsibleBroker,
      brokerRole: BrokerRole.Responsible,
    })
  }
  // Remove duplicates, keeping brokers with role 2
  brokers = brokers.filter((broker, index, self) =>
    broker?.brokerRole === 2
      ? true
      : broker?.employeeId !==
        self.find((b) => b?.brokerRole === 2)?.employeeId,
  )
  // Sort brokers by roles: 2 (responsible), 1 (main), 3 (assistant)
  brokers.sort((a, b) => {
    const rolePriority = [
      BrokerRole.Responsible,
      BrokerRole.Main,
      BrokerRole.Assistant,
    ]
    if (!a?.brokerRole || !b?.brokerRole) return 0
    if (!rolePriority.includes(a?.brokerRole)) return 0
    return (
      rolePriority.indexOf(a?.brokerRole) - rolePriority.indexOf(b?.brokerRole)
    )
  })

  return brokers
    .map((broker) => {
      if (!broker) return null
      let roleName = broker.brokerRole
        ? brokerTypes[broker.brokerRole].label
        : null

      if (isAuthorizedRepresentative(broker.employeeId, estate?.brokers)) {
        roleName = broker.employee.title ?? 'Fullmektig'
      }

      return {
        employeeId: broker.employeeId,
        name: broker.employee.name ?? '',
        mobilePhone: broker.employee.mobilePhone ?? '',
        email: broker.employee.email ?? '',
        title: broker.employee.title ?? '',
        roleName,
        role: broker.brokerRole ?? '',
        imageSrc: broker.employee.image?.small,
      }
    })
    .filter(Boolean) as ListingAgreementBroker[]
}

export const getBrokerByRole = (
  role: BrokerRole,
  estateBrokers: {
    brokers: NonNullable<GQLGetBrokerEstateQuery['estate']>['brokers']
    brokersIdWithRoles: NonNullable<
      GQLGetBrokerEstateQuery['estate']
    >['brokersIdWithRoles']
  },
) => {
  if (!estateBrokers.brokers || !estateBrokers.brokersIdWithRoles) return null

  let broker = estateBrokers.brokersIdWithRoles.find(
    (b) => b?.brokerRole === role,
  )

  if (
    role === BrokerRole.Responsible &&
    (!broker ||
      isAuthorizedRepresentative(broker.employeeId, estateBrokers.brokers))
  ) {
    broker = estateBrokers.brokersIdWithRoles.find(
      (broker) =>
        broker?.brokerRole === BrokerRole.Main &&
        !isAuthorizedRepresentative(broker.employeeId, estateBrokers.brokers),
    )
  }

  // Fallback to role 3 as responsible broker, in case is not set in Vitec
  if (
    role === BrokerRole.Responsible &&
    (!broker ||
      isAuthorizedRepresentative(broker.employeeId, estateBrokers.brokers))
  ) {
    broker = estateBrokers.brokersIdWithRoles.find(
      (broker) =>
        broker?.brokerRole === BrokerRole.Assistant &&
        !isAuthorizedRepresentative(broker.employeeId, estateBrokers.brokers),
    )
  }
  const employeeRoles = estateBrokers.brokers.find(
    (b) => b?.employeeId === broker?.employeeId,
  )?.employeeRoles

  return broker
    ? { ...broker, roleName: brokerTypes[role].label, employeeRoles }
    : null
}
