type GetDepartmentName = {
  departmentNumber?: number | null | undefined
  legalName?: string | null
} | null

export const getDepartmentName = (department?: GetDepartmentName) => {
  if (!department?.departmentNumber || !department?.legalName) {
    return 'Nordvik Oppgjør AS'
  }

  // Ullevå<PERSON> should use
  if (department?.departmentNumber === 23) {
    return department.legalName
  }
  return 'Nordvik Oppgjør AS'
}
