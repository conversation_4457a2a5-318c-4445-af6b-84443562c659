import { BrokerR<PERSON> } from '@befaring/lib/broker-constants'
import { getBrokerByRole } from '@befaring/lib/format-brokers'

import { NextPrivateContact } from '@/actions/next/types-next'
import { GQLGetBrokerEstateQuery } from '@/api/generated-client'

export type ValuationMissingDataValues =
  | 'budsjett'
  | 'selgere'
  | 'ansvarlig megler'

export const validateMissingValues = (
  sellers: Pick<NextPrivateContact, 'contactId'>[],
  estate: GQLGetBrokerEstateQuery['estate'],
) => {
  const missingData: ValuationMissingDataValues[] = []

  if (sellers.length === 0) missingData.push('selgere')

  if (
    !getBrokerByRole(BrokerRole.Responsible, {
      brokers: estate?.brokers,
      brokersIdWithRoles: estate?.brokersIdWithRoles,
    })
  ) {
    missingData.push('ansvarlig megler')
  }

  return missingData
}
