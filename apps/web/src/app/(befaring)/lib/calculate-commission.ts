export function calculateCommission(
  {
    commissionIsPercentageBased,
    feePercentage,
    suggestedPrice,
    fixedCommission,
  }: {
    commissionIsPercentageBased: boolean
    feePercentage: number | null
    suggestedPrice?: number | null
    fixedCommission?: number | null
  },
  options?: {
    round: boolean
  },
) {
  const commission = commissionIsPercentageBased
    ? ((suggestedPrice ?? 0) * (feePercentage ?? 0)) / 100
    : (fixedCommission ?? 0)

  if (options?.round) {
    return Math.round(commission)
  }

  return commission
}
