import { cache } from 'react'
import 'server-only'

import {
  GQLGetBrokerEstateQuery,
  GetBrokerEstateDocument,
} from '@/api/generated-client'
import { gqlServerFetch } from '@/api/gqlServerFetch'

export const preloadEstate = (id: string) => {
  void cachedEstate(id)
}

export const cachedEstate = cache(async (estateId: string) => {
  const { data } = await gqlServerFetch<GQLGetBrokerEstateQuery>(
    GetBrokerEstateDocument,
    {
      estateId,
    },
  )

  return data?.estate
})

export type CachedEstate = NonNullable<Awaited<ReturnType<typeof cachedEstate>>>
