'use client'

import React from 'react'

import { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { EventProperties, useTrackEvent } from '@/lib/analytics/track-event'

type EventType =
  | `inspection_${string}`
  | `listing_agreement_${string}`
  | `valuation_agreement_${string}`

export function useTrackListingAgreement(
  estate: Partial<GQLGetBrokerEstateQuery['estate']>,
) {
  const trackEvent = useTrackEvent()

  return (event: EventType, properties?: EventProperties) => {
    const inspectionEvent = estate?.activities?.find(
      (event) => event.type === 1,
    )
    trackEvent(event, {
      estate: {
        id: estate?.estateId,
        isValuation: estate?.isValuation ?? false,
        estateTypeExternal: estate?.estateTypeExternal,
        streetAddress: estate?.address?.streetAddress,
        totalPrice: estate?.estatePrice?.totalPrice,
        department: {
          name: estate?.department?.name,
          departmentId: estate?.department?.departmentId,
        },
        inspection: inspectionEvent
          ? {
              start: inspectionEvent.start,
              end: inspectionEvent.end,
            }
          : undefined,
        broker: {
          employeeId: estate?.broker?.employeeId,
          name: estate?.broker?.name,
          email: estate?.broker?.email,
        },
        ...(typeof properties?.estate === 'object' ? properties.estate : {}),
      },
      ...properties,
    })
  }
}

export function TrackListingAgreementOnce({
  event,
  properties,
  estate,
}: {
  event: EventType
  properties?: EventProperties
  estate: GQLGetBrokerEstateQuery['estate']
}) {
  const trackEvent = useTrackListingAgreement(estate)
  const [hasTracked, setHasTracked] = React.useState(false)

  React.useEffect(() => {
    if (!hasTracked) {
      trackEvent(event, properties)
      setHasTracked(true)
    }
  }, [event, properties, trackEvent, hasTracked])

  return null
}
