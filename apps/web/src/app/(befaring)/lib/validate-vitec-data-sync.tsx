import type { NextAssignmentBudget } from '@/actions/next/types-next'

export type ValidateVitecDataSync = ReturnType<typeof validateVitecDataSync>

export const validateVitecDataSync = (
  listingAgreement: {
    suggestedPrice?: number | null
    feePercentage?: number | null
    commission?: number
  },
  listingAgreementBudget: Pick<
    NextAssignmentBudget,
    'baseCommission' | 'feePercent' | 'estimatedCommission' | 'type'
  >,
  vitecPriceSuggestion?: number | null,
) => {
  const suggestedPriceVitec = vitecPriceSuggestion
  const suggestedPriceDatabase = listingAgreement?.suggestedPrice
  const feePercentageVitec = listingAgreementBudget?.feePercent
  const feePercentageDatabase = listingAgreement?.feePercentage
  const commissionVitec = listingAgreementBudget?.estimatedCommission
  const commissionDatabase = listingAgreement?.commission

  const type = listingAgreementBudget.type

  const suggestedPriceMatches = suggestedPriceVitec === suggestedPriceDatabase
  const feePercentageMatches = feePercentageVitec === feePercentageDatabase
  const commissionMatches = commissionVitec === commissionDatabase

  return {
    type,
    suggestedPriceVitec,
    suggestedPriceDatabase,
    feePercentageVitec,
    feePercentageDatabase,
    commissionVitec,
    commissionDatabase,
    isSynced:
      suggestedPriceMatches &&
      (type === 1 ? feePercentageMatches : commissionMatches),
  }
}
