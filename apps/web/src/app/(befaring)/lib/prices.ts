export const hourlyCommissionRate = 3500
export const hourlyCommissionEstimatedHours = 67

export interface SellerInsurance {
  type: string
  premiumRate: number
  minimumPremium: number
  maximumPremium: number
  additionalInfo: string
  estimatedPremium?: number
  sum?: number | null
}

type InsuranceDataKeys =
  | 'andel_aksje'
  | 'seksjon'
  | 'eget_gnr_og_bnr'
  | 'fritid'

const insuranceData: Record<InsuranceDataKeys, SellerInsurance> = {
  andel_aksje: {
    type: 'Boliger med andel/aksjenummer',
    premiumRate: 2.67,
    minimumPremium: 4200,
    maximumPremium: 31000,
    additionalInfo:
      'Premien beregnes av salgssummen, og det beregnes ikke premie av fellesgjelden.',
  },
  seksjon: {
    type: 'Boliger med seksjonsnummer',
    premiumRate: 4.56,
    minimumPremium: 6600,
    maximumPremium: 37000,
    additionalInfo:
      'Premien beregnes av salgssummen, og det beregnes ikke premie av fellesgjelden.',
  },
  eget_gnr_og_bnr: {
    type: 'Boliger med eget gnr & bnr',
    premiumRate: 5.69,
    minimumPremium: 12400,
    maximumPremium: 62000,
    additionalInfo: 'Tomter: Eneboligtomter følger eneboligpris.',
  },
  fritid: {
    type: 'Fritidsboliger/fritidstomter',
    premiumRate: 6.32,
    minimumPremium: 9600,
    maximumPremium: 62000,
    additionalInfo:
      'Fritidsleilighet med seksjon/andelsnummer, prises som leiligheter.',
  },
} as const

export function getEstateInsuranceData({
  estateType,
  ownershipType,
  suggestedPrice,
  hasInsurance,
  collectiveDebt,
}: {
  estateType: string | null | undefined
  ownershipType: string | null | undefined
  suggestedPrice?: number | null
  hasInsurance?: boolean | null
  collectiveDebt?: number | null
}): SellerInsurance | null {
  let insuranceType: InsuranceDataKeys | null = null

  if (!estateType || !ownershipType) return null

  if (['Aksje', 'Andel', 'Obligasjon'].includes(ownershipType)) {
    insuranceType = 'andel_aksje'
  } else if (ownershipType === 'Eierseksjon') {
    insuranceType = 'seksjon'
  } else if (ownershipType === 'Eiet' && estateType !== 'Fritidseiendom') {
    insuranceType = 'eget_gnr_og_bnr'
  } else if (estateType === 'Fritidseiendom') {
    insuranceType = 'fritid'
  }

  if (insuranceType) {
    const data = insuranceData[insuranceType]
    const suggestedPriceWithoutDebt =
      (suggestedPrice ?? 0) - (collectiveDebt ?? 0)
    data.estimatedPremium = Math.max(
      data.minimumPremium,
      Math.min(
        data.maximumPremium,
        (suggestedPriceWithoutDebt ?? 0) * (data.premiumRate / 1000),
      ),
    )

    if (hasInsurance) {
      return { ...data, sum: data.estimatedPremium }
    }

    return data
  }
  return null
}
