import uniqBy from 'lodash/uniqBy'
import * as yup from 'yup'

import type {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'

export enum ValidatedFieldsListingAgreement {
  suggested_price = 'suggested_price',
  married_or_in_partnership = 'married_or_in_partnership',
  is_common_estate = 'is_common_estate',
  owner_is_seller = 'owner_is_seller',
  fee_percentage = 'fee_percentage',
  commission = 'commission',
  waive_withdrawal_right = 'waive_withdrawal_right',
  no_previous_brokers = 'no_previous_brokers',
  previous_brokers = 'previous_brokers',
  receive_loan_offer = 'receive_loan_offer',
  recipient_loan_offer = 'recipient_loan_offer',
  seller_is_shareholder = 'seller_is_shareholder',
  valuation = 'valuation',
}
export const validatedFieldsTranslation: Record<
  ValidatedFieldsListingAgreement,
  string
> = {
  suggested_price: 'prisantydning',
  no_previous_brokers: 'tidligere meglere',
  previous_brokers: 'tidligere meglere',
  married_or_in_partnership: 'sivilstand',
  is_common_estate: 'felles bolig',
  owner_is_seller: 'eier/selgerrelasjon',
  fee_percentage: 'provisjonsprosent',
  commission: 'provisjon',
  waive_withdrawal_right: 'angrerett',
  receive_loan_offer: 'motta lånetilbud',
  recipient_loan_offer: 'mottaker av låneforhandling',
  seller_is_shareholder: 'aksjeeier',
  valuation: 'verdivurdering kostnader',
} as const

export const listingAgreementValuationSchema = yup.object().shape({
  waive_withdrawal_right: yup
    .boolean()
    .required('Mangler informasjon om oppstart av eiendomsmeglingstjenesten'),
})

export const listingAgreementSchema = yup.object().shape({
  suggested_price: yup
    .number()
    .required('Mangler informasjon om prisantydning'),
  married_or_in_partnership: yup
    .boolean()
    .required('Mangler informasjon om sivilstand'),
  is_common_estate: yup
    .boolean()
    .nullable()
    .when('married_or_in_partnership', (married_or_in_partnership, schema) => {
      return married_or_in_partnership[0]
        ? schema.required('Mangler informasjon om felleseie')
        : schema
    }),
  owner_is_seller: yup
    .boolean()
    .nullable()
    .when(['$estate'], function ([estate], schema) {
      return estate?.ownership !== 2
        ? schema.required('Mangler informasjon om eier/selgerrelasjon')
        : schema
    }),
  seller_is_shareholder: yup
    .boolean()
    .nullable()
    .when(['$estate'], function ([estate], schema) {
      return estate?.ownership === 2
        ? schema.required('Mangler informasjon om aksjeeier')
        : schema
    }),
  waive_withdrawal_right: yup
    .boolean()
    .required('Mangler informasjon om angrerett'),
  no_previous_brokers: yup
    .boolean()
    .required('Mangler informasjon om evt. andre meglere'),
  previous_brokers: yup
    .string()
    .nullable()
    .when('no_previous_brokers', (no_previous_brokers, schema) => {
      return no_previous_brokers[0] === false
        ? schema.trim().required('Mangler navn på tidligere megler')
        : schema
    }),
  recipient_loan_offer: yup
    .string()
    .when('receive_loan_offer', ([receive_loan_offer], schema) =>
      receive_loan_offer
        ? schema.trim().required('Velg kontaktperson for Storebrand Bank')
        : schema.nullable(),
    ),
})

export const listingAgreementCompanySchema = yup.object().shape({
  suggested_price: yup
    .number()
    .required('Mangler informasjon om prisantydning'),
  is_common_estate: yup
    .boolean()
    .nullable()
    .when('married_or_in_partnership', (married_or_in_partnership, schema) => {
      return married_or_in_partnership[0]
        ? schema.required('Mangler informasjon om felleseie')
        : schema
    }),
  owner_is_seller: yup.boolean().required('Mangler informasjon om hjemmel'),
  waive_withdrawal_right: yup
    .boolean()
    .required('Mangler informasjon om angrerett'),
  no_previous_brokers: yup
    .boolean()
    .required('Mangler informasjon om evt. andre meglere'),
  previous_brokers: yup
    .string()
    .nullable()
    .when('no_previous_brokers', (no_previous_brokers, schema) => {
      return no_previous_brokers[0] === false
        ? schema.trim().required('Mangler navn på tidligere megler')
        : schema
    }),
})

export const yupSellerAsPersonSchema = yup.object().shape({
  contactId: yup.string().required('Mangler kontakt-ID'),
  contactType: yup.number().required('Mangler kontakt type'), // 1 for company, 2 for person

  // Fields required for contactType = 1
  socialSecurity: yup.string().required('Mangler fødselsnummer'),
  email: yup.string().email('Ugyldig e-post').required('Mangler e-post'),
  mobilePhone: yup
    .string()
    .matches(/^\+?[1-9]\d{0,14}(?:\s\d{1,14})*$/, 'Ugyldig telefonnummer')
    .required('Mangler telefonnummer'),
  firstName: yup.string().required('Mangler fornavn'),
  lastName: yup.string().required('Mangler etternavn'),

  postalAddress: yup.string().required('Mangler adresse'),
  postalCode: yup.string().required('Mangler postnummer'),
  city: yup.string().required('Mangler poststed'),
})

export const yupSellerSchema = yup.object().shape({
  contactId: yup.string().required('Mangler kontakt-ID'),
  contactType: yup.number().required('Mangler kontakt type'), // 1 for company, 2 for person

  // Conditional validation based on contactType
  organisationNumber: yup.lazy((_, { parent }) =>
    parent.contactType === 1
      ? yup.string().required('Mangler organisasjonsnummer')
      : yup.string().notRequired(),
  ),
  socialSecurity: yup.lazy((_, { parent }) =>
    parent.contactType === 0
      ? yup.string().required('Mangler fødselsnummer')
      : yup.string().notRequired(),
  ),
  email: yup.lazy((_, { parent }) =>
    parent.contactType === 0
      ? yup.string().email('Ugyldig e-post').required('Mangler e-post')
      : yup.string().notRequired(),
  ),
  mobilePhone: yup.lazy((_, { parent }) =>
    parent.contactType === 0
      ? yup
          .string()
          .matches(/^\+?[1-9]\d{0,14}(?:\s\d{1,14})*$/, 'Ugyldig telefonnummer')
          .required('Mangler telefonnummer')
      : yup.string().notRequired(),
  ),
  firstName: yup.lazy((_, { parent }) =>
    parent.contactType === 0
      ? yup.string().required('Mangler fornavn')
      : yup.string().notRequired(),
  ),
  lastName: yup.lazy((_, { parent }) =>
    parent.contactType === 0
      ? yup.string().required('Mangler etternavn')
      : yup.string().notRequired(),
  ),

  // Fields required for both types
  postalAddress: yup.string().required('Mangler adresse'),
  postalCode: yup.string().required('Mangler postnummer'),
  city: yup.string().required('Mangler poststed'),
})

export const validateSellerPhoneNumber = (phoneNumber?: string): boolean => {
  if (!phoneNumber) return true // Let required validation handle empty values

  // Remove all spaces and normalize the input
  const cleanedValue = phoneNumber.replace(/\s/g, '')

  // Check for country codes at the beginning
  const countryCodeMatch = cleanedValue.match(/^(\+47|0047)(.*)$/)

  if (countryCodeMatch) {
    const phoneWithoutCountryCode = countryCodeMatch[2]
    // Norwegian mobile numbers: 8 digits starting with 4, 9, or specific patterns
    return /^[49]\d{7}$/.test(phoneWithoutCountryCode)
  }

  // No country code detected - should be 8-digit Norwegian number
  // Norwegian mobile numbers: 8 digits starting with 4 or 9
  return /^[49]\d{7}$/.test(cleanedValue)
}

type RequiredNotNull<T> = {
  [P in keyof T]: NonNullable<T[P]>
}

type Ensure<T, K extends keyof T> = T & RequiredNotNull<Pick<T, K>>

export type ValidatedSeller = Ensure<
  NextPrivateContact,
  | 'contactId'
  | 'socialSecurity'
  | 'email'
  | 'mobilePhone'
  | 'firstName'
  | 'lastName'
  | 'postalAddress'
  | 'postalCode'
  | 'city'
>

export const sellerContactSchema = yup.object().shape({
  mobilePhone: yup
    .string()
    .transform((value) => value?.replace(/\s+/g, ''))
    .matches(/^\+?[1-9]\d{1,14}$/, 'Ugyldig telefonnummer')
    .required('Mangler informasjon om telefonnummer'),
  email: yup.string().email('Ugyldig e-post'),
})

export const optionalSellerContactSchema = yup.object().shape({
  mobilePhone: yup
    .string()
    .transform((value) => value?.replace(/\s+/g, ''))
    .matches(/^\+?[1-9]\d{1,14}$/, 'Ugyldig telefonnummer'),
  email: yup.string().email('Ugyldig e-post'),
})

export function validateSellersWithProxy(
  sellers: NextPrivateContactWithProxy[],
) {
  return uniqBy(
    sellers.map((seller) =>
      seller.proxy
        ? yupSellerAsPersonSchema.validateSync(seller.proxy)
        : yupSellerAsPersonSchema.validateSync(seller),
    ),
    'contactId',
  )
}
