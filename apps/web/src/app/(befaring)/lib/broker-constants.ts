export enum BrokerRole {
  Main = 1,
  Responsible = 2,
  Assistant = 3,
  Settlement = 4,
  SecondaryAssistant = 5,
}

const brokerRoleLabels: Record<BrokerRole, string> = {
  [BrokerRole.Main]: 'Oppdragets saksbehandler',
  [BrokerRole.Responsible]: 'An<PERSON><PERSON><PERSON>g megler for oppdraget',
  [BrokerRole.Assistant]: 'Oppdragets saksbehandler',
  [BrokerRole.Settlement]: 'Oppgjørsansvarlig',
  [BrokerRole.SecondaryAssistant]: 'Medhjelper',
}

const brokerRoleKeys: Record<BrokerRole, string> = {
  [BrokerRole.Main]: 'MainBroker',
  [BrokerRole.Responsible]: 'ResponsibleBroker',
  [BrokerRole.Assistant]: 'Assistant',
  [BrokerRole.Settlement]: 'ResponsibleSettlement',
  [BrokerRole.SecondaryAssistant]: 'SecondaryAssistant',
}

export const brokerTypes: Record<BrokerRole, { key: string; label: string }> = {
  1: {
    key: brokerRoleKeys[BrokerRole.Main],
    label: brokerRoleLabels[BrokerRole.Main],
  },
  2: {
    key: brokerRoleKeys[BrokerRole.Responsible],
    label: brokerRoleLabels[BrokerRole.Responsible],
  },
  3: {
    key: brokerRoleKeys[BrokerRole.Assistant],
    label: brokerRoleLabels[BrokerRole.Assistant],
  },
  4: {
    key: brokerRoleKeys[BrokerRole.Settlement],
    label: brokerRoleLabels[BrokerRole.Settlement],
  },
  5: {
    key: brokerRoleKeys[BrokerRole.SecondaryAssistant],
    label: brokerRoleLabels[BrokerRole.SecondaryAssistant],
  },
}
