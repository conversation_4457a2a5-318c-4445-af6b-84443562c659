export function uppercaseFirst(str: string) {
  return str.charAt(0)?.toUpperCase() + str.slice(1)
}

function we(who: unknown[], capitalize?: boolean) {
  return who.length > 1
    ? capitalize
      ? 'Vi'
      : 'vi'
    : capitalize
      ? 'Jeg'
      : 'jeg'
}

function you(who: unknown[], capitalize?: boolean) {
  return who.length > 1
    ? capitalize
      ? 'Dere'
      : 'dere'
    : capitalize
      ? 'Du'
      : 'du'
}

function they(who: unknown[], capitalize?: boolean) {
  return who.length > 1
    ? capitalize
      ? 'De'
      : 'de'
    : capitalize
      ? 'Den'
      : 'den'
}

export function pronoun(
  who: unknown[] = [],
  options: {
    perspective: 'first' | 'second' | 'third'
    capitalize?: boolean
  },
) {
  const capitalize = options.capitalize
  const perspective = options.perspective ?? 'first'

  switch (perspective) {
    case 'first':
      return we(who, capitalize)
    case 'second':
      return you(who, capitalize)
    case 'third':
      return they(who, capitalize)
  }
}

/**
 * Write possible outputs
 * Singular:
 *  possessive: false => deg/Deg
 *  possessive: true => dine/Dine
 *
 * Plural:
 *  possessive: false => deres/Deres
 *  possessive: true => dine/Dine
 */
export function getPronounObject(
  who: unknown[],
  capitalize?: boolean,
  possessive?: boolean,
) {
  if (possessive) {
    return who.length > 1
      ? capitalize
        ? 'Deres'
        : 'deres'
      : capitalize
        ? 'Dine'
        : 'dine'
  }
  return who.length > 1
    ? capitalize
      ? 'Dere'
      : 'dere'
    : capitalize
      ? 'Deg'
      : 'deg'
}

type Adjectives = 'trygg' | 'klar'

export function conjugateAdjectives(adjective: Adjectives, plural: boolean) {
  let result = ''
  switch (adjective) {
    case 'trygg':
      result = plural ? 'trygge' : 'trygg'
      break
    case 'klar':
      result = plural ? 'klare' : 'klar'
      break
  }
  return result
}

export function registeredPartners(who: unknown[]) {
  return who.length > 1 ? 'registrerte partnere' : 'registrert partner'
}
