'use client'

import { Smartphone } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Badge } from '@nordvik/ui/badge'
import { Button } from '@nordvik/ui/button'

import { UserAvatar } from '@/components/user-avatar'
import { Signee } from '@/hooks/use-up-to-date-signers'
import { useUserContext } from '@/lib/UserContext'
import { formatPhoneNumber } from '@/lib/format-phone-number'

import { AgreementSigneesMoreMenu } from './agreement-signees-more-menu'

export function AgreementSignees({
  signees,
  broker,
}: {
  signees: Signee[]
  broker?: Signee
}) {
  return (
    <>
      {signees?.map((signee) => (
        <UserStatus key={signee.id} {...signee} />
      ))}
      {broker ? <UserStatus {...broker} phone={undefined} /> : null}
    </>
  )
}
function UserStatus({
  firstName,
  lastName,
  signedAt,
  externalSignerId,
  phone,
  url,
}: {
  firstName?: string
  lastName?: string
  signedAt?: string
  externalSignerId?: string
  phone?: string
  url?: string
}) {
  const { user } = useUserContext()

  const name = `${firstName} ${lastName}`
  const signed = !!signedAt

  return (
    <div className="flex items-center justify-between py-3.5 gap-1 border-b border-muted">
      <div className="flex items-center space-x-2 justify-center">
        <UserAvatar user={{ name }} />
        <div>
          <div className="flex items-center space-x-2">
            <p className="typo-label-md">{name}</p>
            <Badge
              variant={signed ? 'bright-green' : 'beige'}
              className={cn({ 'order-1': !user })}
            >
              {signed ? 'Signert' : 'Ikke signert'}
            </Badge>
          </div>
          {phone && (
            <p className="flex gap-1 typo-body-sm ink-muted items-center">
              <Smartphone size={16} /> {formatPhoneNumber(phone)}
            </p>
          )}
        </div>
      </div>
      <SigningActions
        url={url}
        name={name}
        externalSignerId={externalSignerId}
        signed={signed}
      />
    </div>
  )
}

function SigningActions({
  url,
  externalSignerId,
  name,
  signed,
}: {
  url?: string
  externalSignerId?: string
  name: string
  signed: boolean
}) {
  const { user } = useUserContext()

  const unsignedWithLink = url && !signed
  if (!unsignedWithLink) {
    return null
  }

  const currentBrokerIsSigner = user && user.employeeId === externalSignerId

  return currentBrokerIsSigner ? (
    <Button href={url} variant="outline" size="sm">
      Signer avtale
    </Button>
  ) : (
    <AgreementSigneesMoreMenu url={url} name={name} />
  )
}
