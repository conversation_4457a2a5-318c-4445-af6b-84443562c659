'use client'

import { BrokerRole } from '@befaring/lib/broker-constants'
import { getBrokerByRole } from '@befaring/lib/format-brokers'
import uniqBy from 'lodash/uniqBy'
import Image from 'next/image'

import { Avatar, AvatarFallback } from '@nordvik/ui/avatar'

import type { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { getBrokerInitials } from '@/lib/getBrokerInitials'

export default function YourTeam({
  estate,
}: {
  estate: GQLGetBrokerEstateQuery['estate']
}) {
  if (!estate?.brokers) return null

  const brokers = uniqBy(
    [
      getBrokerByRole(BrokerRole.Main, {
        brokers: estate?.brokers,
        brokersIdWithRoles: estate?.brokersIdWithRoles,
      }),
      getBrokerByRole(BrokerRole.Assistant, {
        brokers: estate?.brokers,
        brokersIdWithRoles: estate?.brokersIdWithRoles,
      }),
    ],
    (broker) => broker?.employeeId,
  ).filter(Boolean)

  return (
    <div className="flex flex-col grow-0 basis-[40%] gap-4 md:pr-4 justify-end">
      {brokers.map((broker) => {
        if (!broker) return null

        return (
          <div className="flex items-center gap-2" key={broker.employeeId}>
            <Avatar className="size-12 md:size-[4.5rem] flex-center">
              {broker.employee.image?.small ? (
                <Image
                  src={broker.employee.image.small}
                  alt="logo"
                  width={72}
                  height={72}
                />
              ) : (
                <AvatarFallback className="typo-title-md ink-[black]">
                  {getBrokerInitials(broker.employee.name ?? '')}
                </AvatarFallback>
              )}
            </Avatar>
            <div className="flex flex-col min-w-0">
              <span className="typo-body-md font-medium">
                {broker.employee.name}
              </span>
              <span
                title={broker.employee.title || undefined}
                className="typo-body-md ink-muted truncate max-w-[30ch]"
              >
                {broker.employee.title}
              </span>
            </div>
          </div>
        )
      })}
    </div>
  )
}
