export function generateOfferHtml(body: string) {
  return `
    <!DOCTYPE html>
      <html lang="no">
        <head>
          <link rel="preconnect" href="https://fonts.googleapis.com">
          <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          <link href="https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap" rel="stylesheet">
          <script src="https://cdn.tailwindcss.com"></script>
          <meta name="viewport" content="width=1192, initial-scale=1" />
          <style>
            body, html {
              margin: 0;
              padding: 0;
              height: 100%;
              width: 100%;
              font-family: 'Source Sans 3', sans-serif;
              color: rgb(0, 45, 50);
              font-size: 20px;
            }
            @page {
              size: A4;
              margin: 0;
            }
          </style>
        </head>
        <body>${body}</body>
      </html>
  `
}
