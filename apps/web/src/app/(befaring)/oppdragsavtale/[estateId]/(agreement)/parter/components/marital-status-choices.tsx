'use client'

import { useUpdateListingAgreementCustomerInfo } from '@befaring/hooks/use-update-listing-agreement-customer-info'
import { ValidatedFieldsListingAgreement } from '@befaring/lib/check-valid-fields'
import { pronoun, registeredPartners } from '@befaring/lib/text-formatting'
import React, { startTransition } from 'react'

import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'

import { NextPrivateContact } from '@/actions/next/types-next'

import { useBudget } from '../../../../../context/budget-context'
import { mapBooleanToYesNo } from '../form-helpers'

import { Subheading } from './subheading'
import { hasFieldError } from './utils'

export function MaritalStatusChoices({
  sellers,
}: {
  sellers: NextPrivateContact[]
}) {
  const {
    locked,
    listingAgreement,
    errorFields,
    customerInfo,
    setCustomerInfo,
    setIsUpdating,
  } = useBudget()

  const we = pronoun(sellers, { perspective: 'first', capitalize: true })
  const [isMarriedOrInPartnership, setIsMarriedOrInPartnership] =
    React.useState(customerInfo.married_or_in_partnership)
  const isCommonEstate = customerInfo.is_common_estate

  const updateInfo = useUpdateListingAgreementCustomerInfo(listingAgreement.id)

  return (
    <div
      className="flex flex-col gap-4"
      data-fields={ValidatedFieldsListingAgreement.married_or_in_partnership}
    >
      <Subheading
        hasError={
          hasFieldError('married_or_in_partnership', errorFields) ||
          (Boolean(isMarriedOrInPartnership) &&
            hasFieldError('is_common_estate', errorFields))
        }
      >
        Sivilstand
      </Subheading>
      <RadioGroup
        disabled={locked}
        defaultValue={mapBooleanToYesNo(isMarriedOrInPartnership)}
        className="flex gap-x-6 gap-y-4 flex-col"
        onValueChange={async (value) => {
          setIsUpdating(true)
          setIsMarriedOrInPartnership(value === 'yes')
          startTransition(() => {
            setCustomerInfo({
              ...customerInfo,
              married_or_in_partnership: value === 'yes',
            })
          })
          await updateInfo({ marriedOrInPartnership: value === 'yes' })
        }}
      >
        <div className="flex gap-2">
          <RadioGroupItem
            className="mt-0.5 shrink-0"
            id="marriedOrInPartnership"
            value="yes"
          />
          <label htmlFor="marriedOrInPartnership" className="typo-body-md">
            {we} er gift eller {registeredPartners(sellers)}
          </label>
        </div>
        {isMarriedOrInPartnership && (
          <RadioGroup
            id="is_common_estate"
            disabled={locked}
            defaultValue={mapBooleanToYesNo(isCommonEstate)}
            className="flex gap-x-6 gap-y-4 flex-col ml-6"
            onValueChange={async (value) => {
              setIsUpdating(true)
              startTransition(() => {
                setCustomerInfo({
                  ...customerInfo,
                  is_common_estate: value === 'yes',
                })
              })
              await updateInfo({ isCommonEstate: value === 'yes' })
            }}
          >
            <div className="flex gap-2">
              <RadioGroupItem
                className="mt-0.5 shrink-0"
                id="isCommonEstate"
                value="yes"
              />
              <label htmlFor="isCommonEstate" className="typo-body-md">
                Oppdraget gjelder felles bolig, uavhengig av hvem som står
                oppført i grunnboken som eier
              </label>
            </div>
            <div className="flex gap-2">
              <RadioGroupItem
                className="mt-0.5 shrink-0"
                id="notCommonEstate"
                value="no"
              />
              <label htmlFor="notCommonEstate" className="typo-body-md">
                Oppdraget gjelder ikke felles bolig
              </label>
            </div>
          </RadioGroup>
        )}
        <div className="flex gap-2">
          <RadioGroupItem
            className="mt-0.5 shrink-0"
            id="notMarriedOrInPartnership"
            value="no"
          />
          <label htmlFor="notMarriedOrInPartnership" className="typo-body-md">
            {we} er ikke gift eller {registeredPartners(sellers)}
          </label>
        </div>
      </RadioGroup>
    </div>
  )
}
