'use client'

import { unlockListingAgreement } from '@befaring/actions/listing-agreement'
import { useRouter } from 'next/navigation'
import React, { useEffect, useTransition } from 'react'

import { Button } from '@nordvik/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { useToast } from '@nordvik/ui/toaster'

import { useBudget } from '../../../../context/budget-context'

export default function UnlockDialog({
  trigger,
}: {
  trigger: string | React.ReactNode | ((isLoading: boolean) => React.ReactNode)
}) {
  const [loading, setLoading] = React.useState(false)
  const onConfirm = useOnConfirm(setLoading)

  return (
    <Dialog>
      <DialogTrigger asChild>
        {typeof trigger === 'function' ? trigger(loading) : trigger}
      </DialogTrigger>
      <DialogContent title="Avbryt signering" size="md">
        <DialogDescription>
          <PERSON><PERSON><PERSON><PERSON> du endringer i oppdragsavtalen etter dette må du sende til
          signering på nytt, og informere kunden om at de skal signere den nye
          avtalen.
        </DialogDescription>
        <DialogFooter>
          <DialogClose asChild>
            <Button size="md" variant="ghost">
              Lukk
            </Button>
          </DialogClose>
          <Button size="md" onClick={onConfirm} loading={loading}>
            Avbryt signering
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

const useOnConfirm = (setLoading) => {
  const { toast } = useToast()
  const { listingAgreement, setDocumentId } = useBudget()
  const [isPending, startTransition] = useTransition()
  const router = useRouter()

  useEffect(() => {
    if (!isPending) {
      setLoading(false)
    }
  }, [isPending, setLoading])

  return async () => {
    setLoading(true)
    try {
      await unlockListingAgreement(listingAgreement)
      setDocumentId(null)

      startTransition(() => {
        router.refresh()
      })
    } catch (error) {
      toast({
        title: 'Kunne ikke låse opp oppdragsavtalen',
        variant: 'destructive',
      })
      setLoading(false)
    }
  }
}
