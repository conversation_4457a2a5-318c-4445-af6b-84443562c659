'use client'

import { TextButton } from '@nordvik/ui/text-button'

import ReadMoreDrawer from '../../components/read-more-drawer'

export function WaiveWithdrawalRightsReadMore() {
  return (
    <ReadMoreDrawer
      title="Kostnader"
      trigger={<TextButton>Les mer</TextButton>}
    >
      <ul className="space-y-4 mt-2 list-image-dot pl-4">
        <li className="typo-body-md">
          En oppdragsavtale mellom en forbruker og en eiendomsmegler kan være
          inngått på en slik måte at forbrukeren har rett til å gå fra avtalen
          uten å oppgi noen grunn for dette. Angrerett forutsetter i
          utgangspunktet at oppdragsavtalen er inngått mens begge parter
          samtidig var til stede på et sted utenfor eiendomsmeglerens kontor.
        </li>
        <li className="typo-body-md">
          Fr<PERSON><PERSON> for å benytte angrerett er 14 dager, og løper fra dagen etter
          at oppdragsavtalen er signert av forbruker og eiendomsmegler, jfr.
          angrerettloven §§ 20 og 21. Standardformular for å utøve angrerett er
          vedlagt oppdragsavtalen. Det er ikke krav om at angrerettskjemaet
          benyttes, men det anbefales at Oppdragsgiver gir Oppdragstaker
          skriftlig beskjed.
        </li>
        <li className="typo-body-md">
          Angrerett gjelder ikke etter at oppdraget er fullført, forutsatt at
          oppdragsgiveren uttrykkelig har samtykket til at leveringen av
          eiendomsmeglingsoppdraget starter før utløpet av angrefristen på 14
          dager, og har erkjent at det ikke er angrerett etter at tjenesten er
          fullført.
        </li>
        <li className="typo-body-md">
          Hvis oppdragsgiver benytter seg av angreretten, etter å uttrykkelig ha
          bedt eiendomsmeglingsforetaket om å begynne på oppdraget, skal
          oppdragsgiver betale et beløp som står i forhold til det som er levert
          frem til det tidspunkt Oppdragsgiver ga melding om bruk av
          angreretten.
        </li>
      </ul>
    </ReadMoreDrawer>
  )
}
