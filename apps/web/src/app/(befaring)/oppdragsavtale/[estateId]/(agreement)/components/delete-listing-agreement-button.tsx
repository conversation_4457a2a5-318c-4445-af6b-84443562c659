'use client'

import { deleteListingAgreement } from '@befaring/actions/listing-agreement'
import { useTrackListingAgreement } from '@befaring/lib/track-listing-agreement'
import { Trash2Icon } from 'lucide-react'
import { useState } from 'react'

import { But<PERSON> } from '@nordvik/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { useToast } from '@nordvik/ui/toaster'

import { useUserContext } from '@/lib/UserContext'

export default function DeleteListingAgreementButton({
  estateId,
}: {
  estateId: string
}) {
  const trackEvent = useTrackListingAgreement({ estateId })
  const { user } = useUserContext()
  const [isDeleting, setIsDeleting] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const { toast } = useToast()

  if (!user) return null

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await deleteListingAgreement(estateId)
      toast({
        title: 'Oppdragsavtale slettet',
        variant: 'success',
      })
      trackEvent('listing_agreement_deleted')
      setIsDialogOpen(false)
    } catch (error) {
      toast({
        title: 'Oppdragsavtale kunne ikke slettes',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger>
          <Trash2Icon className="w-3.5 h-3.5" />
        </DialogTrigger>
        <DialogContent title="Slett oppdragsavtale" size="md">
          <DialogDescription>
            Bruker vil ikke kunne se denne oppdragsavtalen lenger. Denne
            handlingen kan ikke angres, men du kan opprette en ny
            oppdragsavtale.
          </DialogDescription>
          <DialogFooter>
            <DialogClose asChild>
              <Button size="md" variant="ghost">
                Avbryt
              </Button>
            </DialogClose>
            <Button
              size="md"
              variant="default"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Sletter...' : 'Slett oppdragsavtale'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
