import { CircleAlert } from 'lucide-react'

export function Subheading({
  children,
  hasError,
}: {
  children: React.ReactNode
  hasError?: boolean
}) {
  if (hasError) {
    return (
      <div className="flex gap-2 items-center">
        <div className="max-md:mt-0.5 self-start">
          <CircleAlert
            data-theme="dark"
            className="size-5 md:size-6 shrink-0 ink-danger"
          />
        </div>
        <h3 className="typo-label-lg">{children}</h3>
      </div>
    )
  }
  return <h3 className="typo-label-lg">{children}</h3>
}
