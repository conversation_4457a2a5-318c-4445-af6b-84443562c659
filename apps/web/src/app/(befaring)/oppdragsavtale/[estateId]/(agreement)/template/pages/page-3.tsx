import type { ContractData } from '..'

import HourlyBudget from '../../components/hourly-budget'
import Checkbox from '../checkbox'
import CheckboxSection from '../checkbox-section'
import ConditionalRender from '../conditional-render'
import Footer from '../footer'
import FullPage from '../full-page'
import { SectionHeader, SectionSubHeader } from '../section-header'

export function Page3({ contractData }: { contractData: ContractData }) {
  return (
    <FullPage>
      <SectionHeader className="mt-6">
        3. <PERSON>ari<PERSON><PERSON>, oppsigelse og fullmakter
      </SectionHeader>

      <ConditionalRender
        condition={contractData.sellerIsCompany}
        elseContent={
          <p className="mt-4">
            Dersom ikke annet er avtalt, er oppdragets varighet seks måneder fra
            inngåelsen av denne oppdragsavtalen. Oppdraget kan fornyes for
            inntil seks måneder av gangen. Fornyelse skal være skriftlig.
          </p>
        }
      >
        <p>Dersom ikke annet er avtalt, lø<PERSON> oppdraget til det sies opp.</p>
      </ConditionalRender>

      <p className="mt-2">
        Oppdragsavtalen kan sies opp skriftlig av partene uten varsel.
      </p>
      <p className="mt-2">
        Oppdragstaker har ikke fullmakt til å handle på vegne av Oppdragsgiver
        uten i de forhold som fremkommer i denne oppdragsavtalen. Oppdragstaker
        har fullmakt til å avvise bud som har kortere akseptfrist enn 30
        minutter, eller har forbehold som gjør at handel ikke kan sluttes. Bud
        som avvises etter denne fullmakten har Oppdragstaker ikke plikt til å
        forelegge for Oppdragsgiver.
      </p>

      <SectionHeader className="mt-6">4. Vederlag</SectionHeader>
      <CheckboxSection
        title="Oppdragsgiver kan velge mellom timebasert vederlag (punkt 4.1) eller
      provisjonsbasert vederlag (punkt 4.2). Uansett hvilket av alternativene
      som avtales, påløper kostnader i forbindelse med utlegg og andre utgifter."
        checkboxes={[
          {
            label:
              'Oppdragsgiver bekrefter at Oppdragstaker har gitt tilbud basert på timepris, jfr. emgl § 7-2 (2)',
            checked: true,
          },
        ]}
      />

      <div className="flex items-center gap-4">
        <SectionSubHeader>4.1. Timebasert vederlag</SectionSubHeader>
        <Checkbox
          className="mt-6"
          checked={contractData.commission.type === 'hourly'}
        />
      </div>

      <HourlyBudget
        suggestedPrice={contractData.estate.suggestedPrice}
        sellers={contractData.sellers}
        budgetSum={contractData.budget.sum}
      />

      <Footer pageNr={3} />
    </FullPage>
  )
}
