'use client'

import { updateListingAgreementCustomerInfo } from '@befaring/actions/listing-agreement'
import { ValidatedFieldsListingAgreement } from '@befaring/lib/check-valid-fields'
import { pronoun } from '@befaring/lib/text-formatting'
import { startTransition } from 'react'

import { useToast } from '@nordvik/ui/toaster'

import { NextPrivateContactWithProxy } from '@/actions/next/types-next'

import { useBudget } from '../../../../../context/budget-context'
import { mapBooleanToYesNo, yesNoChoices } from '../form-helpers'

import ChoiceBlock from './choice-block'
import { hasFieldError } from './utils'

export default function SellerIsShareHolder({
  sellers,
}: {
  sellers: NextPrivateContactWithProxy[]
}) {
  const {
    listingAgreement,
    customerInfo,
    setCustomerInfo,
    locked,
    errorFields,
  } = useBudget()
  const { toast } = useToast()

  const you = pronoun(sellers, { perspective: 'second' })

  async function handleUpdateField(value: string) {
    const seller_is_shareholder = value === 'yes'
    try {
      startTransition(() => {
        setCustomerInfo({ ...customerInfo, seller_is_shareholder })
      })
      await updateListingAgreementCustomerInfo({
        listingAgreementId: listingAgreement.id,
        sellerIsShareholder: seller_is_shareholder,
      })
    } catch (error) {
      toast({
        title: 'Kunne ikke oppdatere eierskapet av boligen',
        variant: 'destructive',
      })
    }
  }

  return (
    <ChoiceBlock
      id={ValidatedFieldsListingAgreement.seller_is_shareholder}
      choices={yesNoChoices}
      title={`Er ${you} registrert som aksjeeier til aksjeboligen?`}
      description={`Aksjeeier er den som står oppført som eier i aksjeeierboken. Dersom ${you} ikke er oppført som aksjeeier må ${you} fremlegge gyldig fullmakt fra den som eier aksjen.`}
      name="isSellerAlsoShareholder"
      direction="row"
      defaultValue={mapBooleanToYesNo(customerInfo.seller_is_shareholder)}
      onValueChange={handleUpdateField}
      disabled={locked}
      hasError={hasFieldError('seller_is_shareholder', errorFields)}
    />
  )
}
