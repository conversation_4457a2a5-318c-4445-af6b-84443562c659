'use client'

import { useMemo } from 'react'

import { cn } from '@nordvik/theme/cn'

import type { MergedBudgetPost } from '@/actions/next/types-next'
import { formatCurrency } from '@/lib/formatCurrency'

import { useBudget } from '../../../../context/budget-context'

import { IncomePostRow } from './income-post-row'
import { OutlayPostRow } from './outlay-post-row'

export const indentTableRowClassName = 'pl-[1.375rem] sm:pl-[2rem]'
// Order of posts in the budget table. Provisjon is passed in prependedRow prop so it is always first. Oppgjør is always last.
const keywords = ['tilrettelegging', 'nordvik grunnpakke', 'visning']

export default function BudgetTable({
  type,
  title,
  description,
  data,
  totalLabel = 'Totalt',
  sumAddition,
  prependedRow,
}: {
  type: 'income' | 'outlay'
  title: string
  description?: string | React.ReactNode
  data: { posts: MergedBudgetPost[]; sum: number | null | undefined }
  totalLabel?: string | React.ReactNode
  sumAddition?: number | null
  prependedRow?: React.ReactNode
}) {
  const { editMode } = useBudget()

  const orderedPosts = useMemo(() => {
    const copied = [...data.posts]
    return copied.sort((a, b) => {
      const aNameLower = a.name.toLowerCase()
      const bNameLower = b.name.toLowerCase()

      // Ensure "oppgjør" is always the last item
      if (aNameLower.includes('oppgjør')) {
        return 1
      }
      if (bNameLower.includes('oppgjør')) {
        return -1
      }

      const aKeywordIndex = keywords.findIndex((keyword) =>
        aNameLower.includes(keyword),
      )
      const bKeywordIndex = keywords.findIndex((keyword) =>
        bNameLower.includes(keyword),
      )

      if (aKeywordIndex !== -1 && bKeywordIndex !== -1) {
        return aKeywordIndex - bKeywordIndex
      }
      if (aKeywordIndex !== -1) {
        return -1
      }
      if (bKeywordIndex !== -1) {
        return 1
      }

      // Fallback to date sorting if no keywords are found
      const aCreatedAt = new Date(a.created_at).getTime()
      const bCreatedAt = new Date(b.created_at).getTime()
      return aCreatedAt - bCreatedAt
    })
  }, [data.posts])

  const PostRow = type === 'income' ? IncomePostRow : OutlayPostRow

  return (
    <div className="flex flex-col">
      <div className="px-[--padding-x] pt-8 pb-4 sm:py-6">
        <h3 className="typo-body-md font-medium">{title}</h3>
        {typeof description === 'string' && (
          <p className="ink-subtle">{description}</p>
        )}
        {typeof description === 'object' && description}
      </div>

      <div className="flex flex-col divide-y divide-muted last:border-b last:border-muted">
        {prependedRow}
        {orderedPosts.map((post) => (
          <PostRow key={post.budgetpostId} post={post} editMode={editMode} />
        ))}
        <div className="flex items-center gap-1.5 sm:gap-3 px-[--padding-x] py-4">
          <span
            className={cn(
              'typo-body-sm sm:typo-body-md font-medium grow',
              indentTableRowClassName,
            )}
          >
            {totalLabel}
          </span>
          {typeof data.sum === 'number' && (
            <span className="typo-body-sm sm:typo-body-md sm:font-medium font-medium">
              {formatCurrency(data.sum + (sumAddition ?? 0))}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}
