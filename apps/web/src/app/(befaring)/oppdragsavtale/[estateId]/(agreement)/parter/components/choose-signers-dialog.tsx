'use client'

import { useUpdateSeller } from '@befaring/hooks/use-update-seller'
import { AlertCircle } from 'lucide-react'
import React, { Fragment } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Alert, AlertDescription } from '@nordvik/ui/alert'
import { Badge } from '@nordvik/ui/badge'
import { Button } from '@nordvik/ui/button'
import { Checkbox } from '@nordvik/ui/checkbox'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { Separator } from '@nordvik/ui/separator'
import { TextButton } from '@nordvik/ui/text-button'

import { getNextOverviewUrl } from '@/actions/get-next-overview-url'
import { ContactRole, contactRoleTypes } from '@/actions/next/types'
import {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'
import { saveCompanySigners } from '@/actions/signing/save-company-signers'
import { formatOrgNr } from '@/utils/format-org'
import { getLinkToObjectTabInNext } from '@/utils/get-link-to-object-tab-in-next'

import EditSellerDialog, {
  onlySellerSchema,
} from '../../../../../components/seller-section/edit-seller-dialog'

const sameSignerIds = (a, b) =>
  a.length === b.length && a.every((i) => b.includes(i))

const missing = (msg) => <span className="ink-danger">{msg}</span>

const renderAddress = (
  seller: Pick<
    NextPrivateContactWithProxy,
    'postalAddress' | 'postalCode' | 'city'
  >,
) =>
  seller.postalAddress && seller.postalCode && seller.city
    ? `${seller.postalAddress}, ${seller.postalCode} ${seller.city}`
    : missing('Mangler')

type LocalContact = NextPrivateContact & {
  main?: boolean
  roleName?: string
  role?: ContactRole
}

type ChooseSignersDialogProps = {
  estateId: string
  contacts: LocalContact[]
  company: Pick<
    NextPrivateContactWithProxy,
    | 'contactId'
    | 'proxy'
    | 'companyName'
    | 'organisationNumber'
    | 'postalAddress'
    | 'postalCode'
    | 'city'
  >
  signers: NextPrivateContact[]
  open?: boolean
  onOpenChange?: (open: boolean, hasChanges?: boolean) => void
  children?: React.ReactNode
}

export function ChooseSignersDialog({
  estateId,
  company,
  signers: initialSigners,
  contacts,
  open,
  onOpenChange,
  children,
}: ChooseSignersDialogProps) {
  const [signers, setSigners] = React.useState<string[]>(
    initialSigners.map((s) => s.contactId),
  )

  const [loading, setLoading] = React.useState(false)

  const signersChanged = React.useMemo(
    () =>
      !sameSignerIds(
        initialSigners.map((s) => s.contactId),
        signers,
      ),
    [initialSigners, signers],
  )

  const saveWithSigners = async () => {
    setLoading(true)
    try {
      if (!company.contactId) {
        setLoading(false)
        return
      }
      await saveCompanySigners({
        signers,
        estateId,
        companyId: company.contactId,
      })

      onOpenChange?.(false, true)
    } finally {
      setLoading(false)
    }
  }

  const sortedContacts = contacts.sort((a) => (a.main ? -1 : 1))

  const toggleSigner = (contactId: string) => {
    setSigners((prevSigners) =>
      prevSigners.includes(contactId)
        ? prevSigners.filter((s) => s !== contactId)
        : [...prevSigners, contactId],
    )
  }

  const signerCanBeChanged = !company.proxy

  const ContactItem = ({ contact }: { contact: LocalContact }) => {
    const { handleSubmit } = useUpdateSeller(contact)
    const role =
      contact.roleName ??
      (contact.role ? contactRoleTypes[contact.role.baseType] : undefined)

    const hasMissingInfo = !onlySellerSchema.isValidSync({ seller: contact })

    return (
      <div
        key={contact.contactId}
        className="flex items-center justify-between"
      >
        <div className="flex gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-root-muted">
            <span className="typo-label-sm">
              {contact.firstName?.charAt(0)}
              {contact.lastName?.charAt(0)}
            </span>
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="font-medium">
                {contact.firstName} {contact.lastName}
              </span>
              {contact.main && (
                <Badge variant="bright-green">Hovedkontakt</Badge>
              )}
            </div>
            {hasMissingInfo && (
              <div className="ink-danger typo-body-sm">
                Mangler informasjon for å kunne signere
              </div>
            )}
            {role && <p>Rolle: {role}</p>}
            <EditSellerDialog
              seller={contact}
              title={
                contact.roleName === 'Fullmektig'
                  ? 'Fullmektig'
                  : 'Kontaktperson'
              }
              handleSubmit={handleSubmit}
              buttonComponent={<TextButton>Rediger</TextButton>}
            />
          </div>
        </div>
        <Checkbox
          onCheckedChange={() => toggleSigner(contact.contactId)}
          checked={signers.includes(contact.contactId)}
          disabled={!signerCanBeChanged}
        />
      </div>
    )
  }

  const handleOpenChange = (open: boolean) => {
    setSigners(initialSigners.map((s) => s.contactId))
    onOpenChange?.(open)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent title="Rediger informasjon" size="md">
        <DialogDescription divider>
          <section title="Firma">
            <div className="typo-body-md flex flex-col gap-1">
              <span className="typo-body-md font-medium">
                {company.companyName}
              </span>
              <span>
                Org.nr:{' '}
                {formatOrgNr(company.organisationNumber) ?? missing('Mangler')}
              </span>
              <span>Adresse: {renderAddress(company)}</span>
            </div>
            <TextButton
              onClick={async () => {
                const nextUrl = await getNextOverviewUrl(estateId)
                if (!nextUrl) return
                const link = getLinkToObjectTabInNext(nextUrl, 'relatedparties')

                window.open(link, '_blank')
              }}
            >
              Rediger i Next
            </TextButton>
          </section>

          {signers?.length === 0 &&
            message('Velg en eller flere som skal signere avtalen')}
          {company.proxy && message('Kun fullmektig kan signere')}

          <section title="Signeringsrettigheter" className="my-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Kontaktpersoner</h3>
                <span className="typo-body-sm font-medium">Skal signere</span>
              </div>

              <div className="space-y-4">
                {sortedContacts.map((contact) => (
                  <Fragment key={contact.contactId}>
                    <ContactItem contact={contact} />
                    <Separator
                      className={cn({ 'last:hidden': !company.proxy })}
                    />
                  </Fragment>
                ))}
              </div>
            </div>
          </section>
          {company.proxy && (
            <section title="fullmektig" className="my-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Fullmektig</h3>
                  <span className="typo-body-sm font-medium">Skal signere</span>
                </div>
                <ContactItem contact={company.proxy} />
              </div>
            </section>
          )}
        </DialogDescription>
        <DialogFooter>
          {signerCanBeChanged ? (
            <>
              <DialogClose asChild>
                <Button size="md" variant="ghost">
                  Avbryt
                </Button>
              </DialogClose>
              <Button
                size="md"
                onClick={saveWithSigners}
                loading={loading}
                disabled={loading || !signersChanged}
              >
                Lagre
              </Button>
            </>
          ) : (
            <DialogClose asChild>
              <Button size="md" variant="ghost">
                Lukk
              </Button>
            </DialogClose>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

function message(msg: string) {
  return (
    <section title="general-attention" className="my-4">
      <Alert variant="default" className="pl-2">
        <div className="flex gap-2 items-center typo-body-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{msg}</AlertDescription>
        </div>
      </Alert>
    </section>
  )
}
