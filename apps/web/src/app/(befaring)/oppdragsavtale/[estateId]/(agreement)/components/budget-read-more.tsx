'use client'

import React from 'react'

import { TextButton } from '@nordvik/ui/text-button'

import { formatCurrency } from '@/lib/formatCurrency'

import { useBudget } from '../../../../context/budget-context'

import HourlyBudget, { calculateHours } from './hourly-budget'
import ReadMoreDrawer from './read-more-drawer'

export default function BudgetReadMore({ sellers }: { sellers: unknown[] }) {
  const { suggestedPrice, budgetSum } = useBudget()

  const { totalHourlyCommission, totalNrOfHours } = calculateHours(
    suggestedPrice,
    budgetSum,
  )

  return (
    <div className="flex my-4 px-[--padding-x] gap-4">
      <div className="flex flex-col typo-body-md">
        <p className="font-medium">Timebasert tilbud</p>
        <p className="ink-subtle">
          Oppdraget kan også leveres som timebasert vederlag, estimert til{' '}
          {formatCurrency(totalHourlyCommission)} ({totalNrOfHours}
          &nbsp;timer).{' '}
          <ReadMoreDrawer
            title="Timebasert vederlag"
            trigger={<TextButton>Se timebasert tilbud</TextButton>}
          >
            <HourlyBudget
              suggestedPrice={suggestedPrice}
              budgetSum={budgetSum}
              sellers={sellers}
            />
          </ReadMoreDrawer>
        </p>
      </div>
    </div>
  )
}
