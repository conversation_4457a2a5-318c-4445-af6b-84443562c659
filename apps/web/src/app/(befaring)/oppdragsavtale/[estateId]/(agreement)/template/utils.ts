import { ContractData, ValuationContractData } from '.'

/**
 * Groups brokers by role for PDF template
 */
export function groupBrokersByRole(
  brokers: ContractData['brokers'],
): Record<string, ContractData['brokers']> {
  if (!brokers) {
    return {}
  }
  const grouped: Record<string, ContractData['brokers']> = {}
  brokers.forEach((broker) => {
    if (!broker.roleName) {
      return
    }

    if (!grouped[broker.roleName]) {
      grouped[broker.roleName] = []
    }
    grouped[broker.roleName]!.push(broker)
  })
  return grouped
}

export const getMatrikkelDetails = ({
  estate,
}: ContractData | ValuationContractData) => {
  const partOwnershipName = estate.partOwnership.partName

  // Define matrikkel type
  type MatrikkelEntry = {
    gnr?: string | number
    bnr?: string | number
    snr?: string | number
    ownPart?: string
  }

  // Helper function to format a single matrikkel entry
  const formatMatrikkel = (matrikkel: MatrikkelEntry) => {
    if (!matrikkel) return ''

    const gnr = matrikkel.gnr?.toString() || ''
    const bnr = matrikkel.bnr?.toString() || ''
    const snr = matrikkel.snr?.toString()
    const ownPart = matrikkel.ownPart

    let formatted = `gnr. ${gnr}, bnr. ${bnr}`
    if (snr) {
      formatted += `, snr. ${snr}`
    }
    if (ownPart && estate.ownership === 4) {
      formatted += ` (Ideell andel ${ownPart})`
    }
    return formatted
  }

  // Handle matrikkel entries (now always an array)
  const formatMultipleMatrikkel = () => {
    // estate.matrikkel is always an array now
    const matrikkelEntries = estate.matrikkel
      .filter((m: MatrikkelEntry) => m && (m.gnr || m.bnr)) // Filter out invalid entries
      .map(formatMatrikkel)
      .filter(Boolean) // Remove empty strings

    if (matrikkelEntries.length === 0) {
      return 'Matrikkel: Ikke tilgjengelig'
    } else if (matrikkelEntries.length === 1) {
      return `Matrikkel: ${matrikkelEntries[0]}`
    } else {
      return `Matrikkel: ${matrikkelEntries.join(' - ')}`
    }
  }

  switch (estate.ownership) {
    case 0:
      return formatMultipleMatrikkel()
    case 1: {
      const partOwnership = estate.partOwnership
      return `Matrikkel: andelsnr. ${partOwnership.partNumber} i ${partOwnershipName}${
        partOwnership.partOrgNumber
          ? `, org.nr. ${partOwnership.partOrgNumber}`
          : ''
      }`
    }
    case 2:
    case 3: {
      const partOwnership = estate.partOwnership
      return `Matrikkel: aksjenr. ${partOwnership.estateHousingCooperativeStockNumber}, aksjeboenhentsnr. ${partOwnership.estateHousingCooperativeStockHousingUnitNumber} i ${partOwnershipName}${
        partOwnership.partOrgNumber
          ? `, org.nr. ${partOwnership.partOrgNumber}`
          : ''
      }`
    }
    case 4:
      return formatMultipleMatrikkel()
    default:
      return formatMultipleMatrikkel()
  }
}

// Function to get commune details
export const getCommuneDetails = (
  estate: ContractData['estate'] | ValuationContractData['estate'],
) => {
  // estate.matrikkel is always an array now, get municipality from the first valid entry
  const firstMatrikkel = estate.matrikkel.find((m) => m && m.municipality)
  const municipality = firstMatrikkel?.municipality?.replace('OSLO', 'Oslo')
  return `i ${municipality || 'ukjent'} kommune.`
}
