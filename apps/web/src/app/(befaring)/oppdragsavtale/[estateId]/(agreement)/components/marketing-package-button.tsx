'use client'

import { Eye, MousePointerClick, Target } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Checkbox } from '@nordvik/ui/checkbox'

import { Icons } from '@/components/icons'
import { formatCurrency } from '@/lib/formatCurrency'

const CHANNEL_ICON = {
  default: <Target className="size-3.5 sm:size-4" />,
  snapchat: <Icons.snapchat className="size-3.5 sm:size-4" />,
  googleDisplayAds: <Icons.google className="size-3.5 sm:size-4" />,
} as const

export default function MarketingPackageButton({
  name,
  views,
  clicksInterval,
  price,
  checked,
  disabled,
  onClick,
  channels,
}: {
  name?: string
  views?: string
  clicksInterval?: string
  price?: number
  checked: boolean | null
  disabled?: boolean
  onClick: () => void
  channels: { id: string; title: string }[]
}) {
  function handleKeyDown(e: React.KeyboardEvent<HTMLDivElement>) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.stopPropagation()
      onClick()
    }
  }

  return (
    <div
      className={cn(
        'flex flex-col min-w-0 rounded-sm outline outline-subtle focus-visible:outline-2 focus-visible:outline-emphasis w-full',
        checked && 'outline-2 outline-active',
        disabled && 'cursor-default',
      )}
      role="button"
      tabIndex={0}
      onKeyDown={!disabled ? handleKeyDown : undefined}
      onClick={!disabled ? onClick : undefined}
    >
      <div className="flex items-center gap-1 px-3 pb-1 pt-3 sm:gap-2 sm:px-4 sm:pb-2 sm:pt-4">
        <Checkbox
          checked={checked ?? false}
          tabIndex={-1}
          disabled={disabled}
        />
        <span className="typo-title-xs flex-1 min-w-0 truncate">{name}</span>
        <span className="typo-body-sm sm:typo-body-md ml-auto px-2 sm:px-3 whitespace-nowrap shrink-0">
          {formatCurrency(price)}
        </span>
      </div>

      <div className="typo-body-sm flex flex-col min-w-0 p-3 sm:p-4 ink-subtle">
        <div className="flex items-center gap-1.5 sm:gap-2">
          <Eye className="size-3.5 sm:size-4" />
          <span className="truncate">{views} visninger</span>
        </div>
        <div className="flex items-center gap-1.5 sm:gap-2">
          <MousePointerClick className="size-3.5 sm:size-4" />
          <span className="truncate">{clicksInterval} Klikk</span>
        </div>
        {channels.map((channel) => {
          return (
            <div
              key={channel.id}
              className="flex items-center gap-1.5 sm:gap-2"
            >
              {CHANNEL_ICON[channel.id] || CHANNEL_ICON.default}
              <span className="truncate">Inkluderer {channel.title}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}
