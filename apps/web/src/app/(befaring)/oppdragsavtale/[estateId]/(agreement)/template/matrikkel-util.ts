import {
  GQLGetBrokerEstateQuery,
  GQLLandIdentificationMatrix,
} from '@/api/generated-client'

// Define matrikkel entry type that extends the GraphQL type
type MatrikkelEntry = Omit<GQLLandIdentificationMatrix, '__typename'> & {
  municipality?: string
}

// Define the return type for transformed matrikkel data
export type TransformedMatrikkelEntry = {
  gnr: string
  bnr: string
  snr: string
  municipality: string
  ownPart: string
}

export function transformMatrikkelData({
  matrikkel,
  address,
  landIdentificationMatrix,
}: NonNullable<
  GQLGetBrokerEstateQuery['estate']
>): TransformedMatrikkelEntry[] {
  if (Array.isArray(matrikkel) && matrikkel.length > 0) {
    // Multiple matrikkel entries - return as array
    return matrikkel
      .filter((m: MatrikkelEntry) => m.gnr || m.bnr) // Filter out invalid entries
      .map((m: MatrikkelEntry) => {
        return {
          gnr: m.gnr?.toString() ?? '',
          bnr: m.bnr?.toString() ?? '',
          snr: m.snr?.toString() ?? '',
          municipality: address?.municipality ?? '',
          ownPart: m.ownPart?.toString() ?? '',
        }
      })
  } else {
    // Single matrikkel entry - return as array for consistency
    return [
      {
        gnr: landIdentificationMatrix?.gnr?.toString() ?? '',
        bnr: landIdentificationMatrix?.bnr?.toString() ?? '',
        snr: landIdentificationMatrix?.snr?.toString() ?? '',
        municipality: address?.municipality ?? '',
        ownPart: landIdentificationMatrix?.ownPart?.toString() ?? '',
      },
    ]
  }
}
