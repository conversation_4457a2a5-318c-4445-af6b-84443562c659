'use client'

import { updateListingAgreement } from '@befaring/actions/listing-agreement'
import { calculateCommission } from '@befaring/lib/calculate-commission'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@radix-ui/react-collapsible'
import isNaN from 'lodash/isNaN'
import {
  startTransition,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { Input } from '@nordvik/ui/input'
import { useToast } from '@nordvik/ui/toaster'

import { useEstateId } from '@/hooks/use-estateid'
import { formatCurrency } from '@/lib/formatCurrency'

import { useBudget } from '../../../../context/budget-context'

import CollapseIcon from './collapse-icon'

const budgetCaptions = {
  1: 'basert på estimert salgssum',
  2: 'fastpris',
  3: 'timespris',
} as const

export default function ComissionRow({ editMode }: { editMode?: boolean }) {
  const {
    budget,
    commission,
    commissionIsPercentageBased,
    feePercentage,
    setFeePercentage,
    setFixedCommission,
    initialCommission,
    suggestedPrice,
  } = useBudget()

  const { toast } = useToast()

  const [open, setOpen] = useState(false)
  const [feePercentageInput, setFeePercentageInput] = useState(
    feePercentage?.toString(),
  )
  const [fixedCommissionInput, setFixedCommissionInput] = useState(
    commission.toString(),
  )

  const commissionFromPercentageInput = useMemo(
    () =>
      calculateCommission(
        {
          commissionIsPercentageBased,
          feePercentage: Number(feePercentageInput),
          suggestedPrice,
          fixedCommission: Number(fixedCommissionInput),
        },
        { round: true },
      ),
    [
      commissionIsPercentageBased,
      feePercentageInput,
      fixedCommissionInput,
      suggestedPrice,
    ],
  )

  const hasChange = useMemo(() => {
    const feeStr = feePercentage?.toString()
    const commissionStr = commission.toString()
    return commissionIsPercentageBased
      ? feePercentageInput !== null && feePercentageInput !== feeStr
      : fixedCommissionInput !== null && fixedCommissionInput !== commissionStr
  }, [
    commissionIsPercentageBased,
    feePercentage,
    commission,
    feePercentageInput,
    fixedCommissionInput,
  ])

  const handleClose = useCallback(() => {
    if (hasChange) {
      toast({
        title: 'Endringer forkastet',
        description: 'Ny provisjon ble ikke lagret',
      })
    }
    setOpen(false)
    setFeePercentageInput(feePercentage?.toString())
    setFixedCommissionInput(commission.toString())
  }, [commission, feePercentage, hasChange, toast])

  useEffect(() => {
    if (open && !editMode && hasChange) {
      handleClose()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- Not using useCallback here
  }, [open, editMode, hasChange])

  const estateId = useEstateId()

  const budgetCaption = useMemo(
    () => budgetCaptions[budget.type],
    [budget.type],
  )

  const handleCollapse = useCallback(
    (openChange: boolean) => {
      if (!openChange) {
        handleClose()
      } else {
        setOpen(openChange)
      }
    },
    [handleClose],
  )

  const handleSubmit = useCallback(async () => {
    setOpen(false)

    // useOptimistic requires a parent form with an action or wrap in startTransition
    startTransition(() => {
      commissionIsPercentageBased
        ? setFeePercentage(Number(feePercentageInput))
        : setFixedCommission(Number(fixedCommissionInput))
    })
    try {
      if (!estateId) {
        throw new Error('EstateId is not defined')
      }
      await updateListingAgreement({
        estateId,
        commission: commissionIsPercentageBased
          ? null
          : Number(fixedCommissionInput),
        feePercentage: commissionIsPercentageBased
          ? Number(feePercentageInput)
          : null,
      })
      setFeePercentageInput(Number(feePercentageInput).toString())
      setFixedCommissionInput(Number(fixedCommissionInput).toString())
    } catch (error) {
      toast({
        title: 'Kunne ikke oppdatere provisjon',
        description: 'Prøv igjen senere',
        variant: 'destructive',
      })
    }
  }, [
    commissionIsPercentageBased,
    estateId,
    feePercentageInput,
    fixedCommissionInput,
    setFeePercentage,
    setFixedCommission,
    toast,
  ])

  const handleKeySubmit = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        if (hasChange) {
          handleSubmit()
        }
      }
    },
    [handleSubmit, hasChange],
  )

  const hasReducedPrice = commission < initialCommission

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value
      value = value.replace(/,/g, '.')

      if (!isNaN(Number(value)) || value === '') {
        setFeePercentageInput(value)
      }
    },
    [],
  )

  const handleFixedCommissionChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFixedCommissionInput(e.target.value)
    },
    [],
  )

  return (
    <Collapsible open={Boolean(open)} onOpenChange={handleCollapse}>
      {editMode && open ? (
        <div className="flex p-4 items-center gap-1.5 sm:gap-3 sm:px-6">
          <CollapsibleTrigger
            disabled={!editMode}
            className="self-start sm:self-center"
          >
            <CollapseIcon open={open} enabled={editMode} />
          </CollapsibleTrigger>
          <div className="flex max-md:flex-col gap-1.5 sm:gap-3 md:items-center justify-between w-full">
            <span className="typo-body-sm sm:typo-body-md grow">
              Provisjon ({budgetCaption})
            </span>
            <div className="flex gap-4 items-center ">
              {commissionIsPercentageBased && (
                <Input
                  type="text"
                  name="feePercentage"
                  step="0.1"
                  value={feePercentageInput?.toString().replace('.', ',')}
                  wrapperClassName={cn('grow')}
                  addonRight="%"
                  onChange={handleInputChange}
                  onKeyDown={handleKeySubmit}
                />
              )}
              <Input
                type="number"
                disabled={commissionIsPercentageBased}
                name="grossAmount"
                step="1000"
                value={
                  commissionIsPercentageBased
                    ? commissionFromPercentageInput
                    : fixedCommissionInput
                }
                wrapperClassName={cn('grow')}
                addonRight="kr"
                onChange={handleFixedCommissionChange}
                onKeyDown={handleKeySubmit}
              />
            </div>
          </div>
        </div>
      ) : (
        <CollapsibleTrigger disabled={!editMode} asChild>
          <button
            className={cn(
              'flex p-4 items-center gap-1.5 sm:gap-3 sm:px-6 w-full',
              editMode && 'hover:bg-interactive-muted',
            )}
          >
            <CollapseIcon
              open={open}
              enabled={editMode}
              className="max-sm:mt-1.5"
            />

            <span className="typo-body-sm sm:typo-body-md grow text-left">
              Provisjon ({budgetCaption})
            </span>

            {budget.type === 1 && (
              <div className="rounded typo-body-sm whitespace-nowrap sm:typo-body-md bg-light-green-subtle px-2 py-1 sm:px-3 sm:py-1">
                {feePercentage} %
              </div>
            )}

            <div className="flex max-sm:flex-col gap-1 sm:gap-2 whitespace-nowrap flex-wrap justify-end text-right typo-body-sm sm:typo-body-md">
              {hasReducedPrice && (
                <span className="ink-muted line-through">
                  {formatCurrency(initialCommission)}
                </span>
              )}

              <span className={cn(hasReducedPrice && 'ink-success')}>
                {formatCurrency(commission)}
              </span>
            </div>
          </button>
        </CollapsibleTrigger>
      )}

      <CollapsibleContent asChild>
        <div className="flex flex-col gap-2 pb-4 pl-[60px] pr-6">
          {Boolean(editMode) && (
            <div className="flex gap-2 self-end">
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={handleClose}
              >
                Avbryt
              </Button>
              <Button
                type="button"
                size="sm"
                disabled={!hasChange}
                onClick={handleSubmit}
              >
                Lagre
              </Button>
            </div>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
