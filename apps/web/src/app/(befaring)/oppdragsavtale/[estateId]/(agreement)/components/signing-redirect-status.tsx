'use client'

import { InfoIcon } from 'lucide-react'
import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'

import { useToast } from '@nordvik/ui/toaster'

const reasons = {
  cancel: 'Signeringen ble avbrutt',
  error: 'Det oppstod en feil under signeringen',
}

export default function SigningRedirectStatus() {
  const signicatReason = useSearchParams().get('signicatReason')

  const { toast } = useToast()

  useEffect(() => {
    if (signicatReason) {
      const reason = reasons[signicatReason]
      toast({
        title: reason,
        icon: <InfoIcon className="ink-danger" size={24} />,
        variant: 'destructive',
      })
    }
  }, [signicatReason, toast])

  return null
}
