'use client'

import { updateListingAgreementCustomerInfo } from '@befaring/actions/listing-agreement'
import { ValidatedFieldsListingAgreement } from '@befaring/lib/check-valid-fields'
import { pronoun } from '@befaring/lib/text-formatting'
import { startTransition } from 'react'

import { useToast } from '@nordvik/ui/toaster'

import type { NextPrivateContactWithProxy } from '@/actions/next/types-next'

import { useBudget } from '../../../../../context/budget-context'
import { mapBooleanToYesNo, yesNoChoices } from '../form-helpers'

import ChoiceBlock from './choice-block'
import { hasFieldError } from './utils'

export default function OwnerIsSeller({
  sellers,
}: {
  sellers: NextPrivateContactWithProxy[]
}) {
  const {
    listingAgreement,
    customerInfo,
    setCustomerInfo,
    locked,
    errorFields,
  } = useBudget()
  const { toast } = useToast()

  const you = pronoun(sellers, { perspective: 'second' })

  async function handleUpdateField(value: string) {
    const owner_is_seller = value === 'yes'
    try {
      startTransition(() => {
        setCustomerInfo({ ...customerInfo, owner_is_seller })
      })
      await updateListingAgreementCustomerInfo({
        listingAgreementId: listingAgreement.id,
        ownerIsSeller: owner_is_seller,
      })
    } catch (error) {
      toast({
        title: 'Kunne ikke oppdatere eierskapet av boligen',
        variant: 'destructive',
      })
    }
  }

  return (
    <ChoiceBlock
      id={ValidatedFieldsListingAgreement.owner_is_seller}
      choices={yesNoChoices}
      title={`Har ${you} hjemmel til eiendommen?`}
      description={`Hjemmelshaver er den som står oppført i grunnboken som eier. Dersom ${you} ikke eier eiendommen må ${you} fremlegge gyldig fullmakt.`}
      name="isOwnerAndSellerTheSame"
      direction="row"
      defaultValue={mapBooleanToYesNo(customerInfo.owner_is_seller)}
      onValueChange={handleUpdateField}
      disabled={locked}
      hasError={hasFieldError('owner_is_seller', errorFields)}
    />
  )
}
