'use client'

import React from 'react'

import { cn } from '@nordvik/theme/cn'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { Drawer, DrawerContent, DrawerTrigger } from '@nordvik/ui/drawer'
import { TextButton } from '@nordvik/ui/text-button'

export default function ReadMoreDrawer({
  trigger = <TextButton>Les mer</TextButton>,
  title = 'Les mer',
  children,
  className,
}: {
  trigger?: React.ReactNode
  title?: string
  children: React.ReactNode
  className?: string
}) {
  return (
    <>
      <Dialog>
        <DialogTrigger asChild className="max-lg:hidden inline">
          {trigger}
        </DialogTrigger>
        <DialogContent
          title={title}
          size="lg"
          className={cn('max-w-3xl', className)}
        >
          <DialogDescription>{children}</DialogDescription>
        </DialogContent>
      </Dialog>

      <Drawer>
        <DrawerTrigger asChild className="lg:hidden inline">
          {trigger}
        </DrawerTrigger>
        <DrawerContent>
          <div className="overflow-auto">
            <div className={cn('flex flex-col gap-4 px-4', className)}>
              <h2 className="typo-title-sm">{title}</h2>
              {children}
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  )
}
