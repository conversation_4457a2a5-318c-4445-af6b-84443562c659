import type { NordvikNoGQLEmployee } from '@/server/nordvik-client'

import { CollapsibleSection } from '../../../../components/collapsible-section'

export default function EmployeeSection({
  employees,
}: {
  employees?: NordvikNoGQLEmployee[]
}) {
  return (
    <CollapsibleSection title="Oppdragsgiver">
      <div className="flex flex-col gap-4 p-4">
        <h2 className="typo-title-sm"><PERSON>r du spørsmål om oppdragsavtalen?</h2>

        {employees?.map((employee) => (
          <div key={employee.id}>{employee.name}</div>
        ))}
      </div>
    </CollapsibleSection>
  )
}
