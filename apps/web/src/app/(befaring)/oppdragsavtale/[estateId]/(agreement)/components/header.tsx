import { cachedEstate } from '../../../../lib/cached-estate'

import PriceSuggestion from './price-suggestion'
import YourTeam from './your-team'

export default async function Header({
  estateId,
  disabled,
  isValuation,
}: {
  estateId: string
  disabled?: boolean
  isValuation?: boolean
}) {
  const estate = await cachedEstate(estateId)

  return (
    <div data-theme="dark" className="bg-root">
      <div className="container flex max-md:flex-col gap-8 md:gap-10 py-8">
        <div className="flex grow flex-col basis-[60%] gap-7">
          <div className="flex flex-col gap-1">
            <h1 className="typo-display-md sm:typo-display-lg leading-none">
              {estate?.address?.streetAddress}
            </h1>
            <span className="typo-body-md capitalize">{`${estate?.address?.zipCode} ${estate?.address?.city?.toLowerCase()}`}</span>
          </div>

          {!isValuation && <PriceSuggestion disabled={disabled} />}
        </div>
        <YourTeam estate={estate} />
      </div>
    </div>
  )
}
