import React from 'react'

import { Contract, ValuationContract } from '../..'

import { pdfListingAgreementTestData } from './pdf-listing-agreement-test-data'

export default {
  title: 'PDF / Listing Agreement',
  parameters: {
    fullscreen: true,
  },
}

export const AgreementPDF = () => (
  <A4Paper>
    <Contract data={pdfListingAgreementTestData} />
  </A4Paper>
)
AgreementPDF.storyName = 'Oppdragsavtale'
export const ValuationAgreementPDF = () => (
  <A4Paper>
    <ValuationContract data={pdfListingAgreementTestData} />
  </A4Paper>
)
ValuationAgreementPDF.storyName = 'Verdivurdering'

function A4Paper({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex justify-center my-4">
      <div className="[&_#contract-document]:space-y-4 [&_#contract-document>div]:shadow-sm [&_#contract-document>div]:w-[1032px] [&_#contract-document>div]:h-[1459px]">
        {children}
      </div>
    </div>
  )
}
