import { cachedSigners } from '@befaring/actions/sellers'
import { cachedEstate } from '@befaring/lib/cached-estate'
import { cachedUpsertListingAgreement } from '@befaring/lib/cached-upsert-listing-agreement'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

import { Link } from '@nordvik/ui/global-navigation-progress/link'
import { TextButton } from '@nordvik/ui/text-button'

import {
  type GQLBrokerEstateSeller,
  GQLInspectionLeadType,
} from '@/api/generated-client'
import { TrackPageVisit } from '@/components/track-page-visit'
import prisma from '@/db/prisma'
import { getCurrentUser } from '@/lib/session'
import { getLeads } from '@/server/model/InspectionLead/get-leads'

import { SendContractDialog } from '../components/send-contract-dialog'
import SignerStatusSection from '../components/signer-status-section'

import BrokersSection from './components/brokers-section'
import ConsentSection from './components/consent-section'
import { ErrorSection } from './components/error-section'
import LoanOfferSection from './components/loan-offer-section'
import { SellerSectionParties } from './components/seller-section'
import { SendOfferDialogWrapper } from './components/send-offer-dialog-wrapper'
import WaiveWithdrawalRightsSection from './components/waive-withdrawal-rights-section'

export const maxDuration = 30

export type MergedSellers = GQLBrokerEstateSeller

export default async function Page(props: {
  params: Promise<{ estateId: string }>
}) {
  const [{ estateId }, user] = await Promise.all([
    props.params,
    getCurrentUser(),
  ])

  if (!estateId) {
    console.error('No estateId provided')
    redirect('/')
  }

  const [
    estate,
    listingAgreement,
    sellers,
    financingLeads,
    cookieStore,
    budget,
  ] = await Promise.all([
    cachedEstate(estateId),
    prisma.listing_agreements.findFirst({
      where: { estate_id: estateId },
      select: {
        signicat_document_id: true,
      },
    }),
    cachedSigners(estateId),
    getLeads({ estateId, leadType: GQLInspectionLeadType.Financing }),
    cookies(),
    cachedUpsertListingAgreement(estateId),
  ])

  if (!listingAgreement) {
    console.error('No listing agreement found for estate', estateId)
    redirect(`/oppdragsavtale/${estateId}`)
  }

  if (!estate) {
    console.error('No estate found for estate', estateId)
    redirect(`/oppdragsavtale/${estateId}`)
  }

  const locked = !!listingAgreement.signicat_document_id

  const signers = estate?.hasCompanySeller
    ? await cachedSigners(estateId, true, true)
    : sellers

  const hasRequestedFinancing =
    financingLeads.filter((entry) => entry.successful).length > 0

  const cid = cookieStore.get('cid')?.value
  const ecid = cookieStore.get('ecid')?.value

  const canEdit = !locked && (Boolean(user) || Boolean(cid) || Boolean(ecid))

  const showSendContractDialog = estate && canEdit

  const mainContact = estate.sellers.find((seller) => seller.mainContact)

  return (
    <>
      <TrackPageVisit pageId="befaring-agreement/parties" estateId={estateId} />
      <SignerStatusSection estateId={estateId} />
      <ErrorSection />
      <div className="sm:container max-sm:mt-6 mb-8 sm:my-8 md:my-12 flex flex-col gap-6 md:gap-8">
        <SellerSectionParties
          sellers={sellers}
          estateId={estateId}
          signers={signers}
          ownership={estate?.ownership || 0}
          canEdit={canEdit}
          user={user}
        />
        <BrokersSection estate={estate} />
        <WaiveWithdrawalRightsSection sellers={signers} />
        <ConsentSection sellers={signers} />
        {!hasRequestedFinancing && !estate.hasCompanySeller && (
          <LoanOfferSection mainContact={mainContact} />
        )}
        <div className="max-sm:px-[--padding-x] flex max-md:flex-col-reverse gap-5 items-center">
          <Link href={`/oppdragsavtale/${estateId}`} className="md:mr-auto">
            <TextButton subtle bold iconStart="arrow">
              Tilbake
            </TextButton>
          </Link>
          <div className="flex flex-wrap gap-2 empty:hidden [&_button]:grow max-md:w-full">
            {Boolean(user && !locked) && (
              <SendOfferDialogWrapper
                estate={estate}
                sellers={signers}
                budget={budget}
              />
            )}
            {showSendContractDialog && (
              <SendContractDialog estate={estate} sellers={signers} />
            )}
          </div>
        </div>
      </div>
    </>
  )
}
