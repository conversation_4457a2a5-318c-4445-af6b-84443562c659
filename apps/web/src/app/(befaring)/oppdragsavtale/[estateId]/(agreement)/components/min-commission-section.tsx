'use client'

import { MINIMUM_COMMISSION } from '@/utils/constants'

import { useBudget } from '../../../../context/budget-context'
import ConditionalRender from '../template/conditional-render'

export function MinCommissionSection() {
  const { budget, commission } = useBudget()

  const minimumCommission =
    commission < MINIMUM_COMMISSION ? commission : MINIMUM_COMMISSION

  return (
    <>
      <ConditionalRender condition={budget.type === 1}>
        <h3 className="font-medium">Provisjonsbasert vederlagt</h3>
        <p>
          N<PERSON>r handelen er kommet i stand, betaler oppdragsgiver en provisjon som
          beløper seg til:
        </p>
        <p>
          Ved provisjonsbasert vederlag har oppdragstaker alltid krav på en
          minsteprovisjon på kr. {minimumCommission.toLocaleString('no-NO')}
          ,- inkl. mva.
        </p>
      </ConditionalRender>

      <ConditionalRender condition={budget.type !== 1}>
        <h3 className="font-medium">Fastprisbasert vederlagt</h3>
        <p>
          <PERSON><PERSON><PERSON> handelen er kommet i stand, betaler oppdragsgiver en fastpris som
          beløper seg til:
        </p>
        <p>
          Ved fastprisbasert vederlag har oppdragstaker alltid krav på en
          minsteprovisjon på kr. {minimumCommission.toLocaleString('no-NO')}
          ,- inkl. mva.
        </p>
      </ConditionalRender>
    </>
  )
}
