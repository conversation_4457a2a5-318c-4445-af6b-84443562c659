'use server'

import prisma from '@/db/prisma'
import { formatDate } from '@/lib/dates'

import DeleteListingAgreementButton from './delete-listing-agreement-button'

export default async function LastUpdateStatus({
  estateId,
}: {
  estateId: string
}) {
  const lastUpdatedQuery = await prisma.$queryRaw<
    (Record<string, Date> | undefined)[]
  >`
    SELECT MAX(l.updated_at) as listing_updated_at, MAX(b.updated_at) AS posts_updated_at FROM listing_agreements l
    LEFT JOIN budget_posts b on l.id=b.listing_agreement_id
    WHERE estate_id = ${estateId}
    GROUP BY b.listing_agreement_id;
`

  const row = lastUpdatedQuery[0]

  if (!row) {
    return null
  }

  let lastUpdated = row.listing_updated_at

  if (row.posts_updated_at > lastUpdated) {
    lastUpdated = row.posts_updated_at
  }

  return (
    <div className="mt-10 container flex justify-center items-center gap-2">
      <p className="typo-body-sm sm:typo-body-md text-center ink-muted">
        Oppdraget ble sist oppdatert{' '}
        {formatDate(lastUpdated, "d. MMMM 'kl.' HH:mm")}
      </p>

      <DeleteListingAgreementButton estateId={estateId} />
    </div>
  )
}
