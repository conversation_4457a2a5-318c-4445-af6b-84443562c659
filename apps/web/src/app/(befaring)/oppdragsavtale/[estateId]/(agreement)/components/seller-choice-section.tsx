import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'

export default function SellerChoiceSection({
  choices,
  name,
  title,
  description,
  direction = 'column',
}: {
  choices: { value: string; label: string }[]
  name: string
  title: string
  description?: string
  direction?: 'row' | 'column'
}) {
  const flexDirectionClassName = direction === 'row' ? 'flex-row' : 'flex-col'
  return (
    <div className="flex flex-col gap-4">
      <h3 className="typo-label-lg">{title}</h3>
      <RadioGroup
        className={`flex gap-x-6 gap-y-2 ${flexDirectionClassName}`}
        name={name}
      >
        {choices.map((choice) => (
          <div className="flex gap-2" key={choice.value}>
            <RadioGroupItem value={choice.value} id={name + choice.value} />
            <label htmlFor={name + choice.value} className="typo-body-md">
              {choice.label}
            </label>
          </div>
        ))}
      </RadioGroup>
      <span className="typo-body-sm ink-subtle">{description}</span>
    </div>
  )
}
