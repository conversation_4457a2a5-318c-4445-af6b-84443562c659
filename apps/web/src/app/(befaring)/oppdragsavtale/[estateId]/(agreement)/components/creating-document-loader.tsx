import React from 'react'

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogTitle,
} from '@nordvik/ui/alert-dialog'

import { NordvikLoaderLogo } from '@/components/nordvik-loader'

export function CreatingDocumentLoader({
  children,
  open,
  onOpenChange,
}: {
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="w-80 rounded-md">
        <div className="flex flex-col items-center">
          <div className="pb-6 w-[50px] mt-4">
            <NordvikLoaderLogo />
          </div>
          <AlertDialogTitle className="typo-title-sm m-0 mt-1">
            Sender til signering
          </AlertDialogTitle>
          <div className="typo-body-sm text-center mt-2 ink-muted">
            {children}
          </div>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  )
}
