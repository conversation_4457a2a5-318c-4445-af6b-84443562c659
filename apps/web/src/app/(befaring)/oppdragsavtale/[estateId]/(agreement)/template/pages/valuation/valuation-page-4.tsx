import type { ValuationContractData } from '../..'
import Footer from '../../footer'
import FullPage from '../../full-page'

export function ValuationPage4({
  contractData,
}: {
  contractData: ValuationContractData
}) {
  const { department } = contractData
  return (
    <FullPage>
      <p className="">Oppdragsnummer: {contractData.assignmentNumber}</p>
      <div className="my-12 flex flex-col items-center">
        <h2 className="mb-2 text-2xl font-medium">ANGRESKJEMA</h2>
        <p>ved kjøp av ikke finansielle tjenester</p>
      </div>
      <p className="mb-8">
        Fyll ut og returner dette skjemaet dersom dere ønsker å gå fra avtalen
      </p>
      <p className="mb-2">Utfylt skjema sendes til:</p>
      <p className="font-medium">{department.legalName || department.name}</p>
      <p className="">{department.streetAddress}</p>
      <p className="">
        {department.postalCode} {department.city}
      </p>
      <p className="mb-2">E-post: {department.email}</p>
      <p className="mt-6">
        Vi underretter herved om at vi ønsker å gå fra vår avtale om kjøp av
        følgende tjenester (spesifiser nedenfor):
      </p>

      <hr className="mt-16" />
      <hr className="mt-16" />

      <p className="mt-16">Avtalen ble inngått den (sett inn dato): </p>
      <p className="mt-16">Forbrukernes navn:</p>
      <hr className="mt-16" />
      <p className="mt-12">Forbrukernes adresse:</p>
      <hr className="mt-16" />
      <p className="mt-16">Dato:</p>

      <hr className="mt-28" />
      <p className="mb-2">(Forbrukernes signatur)</p>

      <Footer pageNr={4} totalPages={4} />
    </FullPage>
  )
}
