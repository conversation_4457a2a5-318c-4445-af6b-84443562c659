import { BrokerRole } from '@befaring/lib/broker-constants'
import { isEmpty } from 'lodash'

import { sortBySpecificOrder } from '@/lib/sort-by-specific-order'
import { formatOrgNr } from '@/utils/format-org'

import type { ValuationContractData } from '../..'
import Footer from '../../footer'
import FullPage from '../../full-page'
import { SectionHeader, SectionSubHeader } from '../../section-header'
import Grid from '../../table'
import { groupBrokersByRole } from '../../utils'

export function ValuationPage1({
  contractData: data,
}: {
  contractData: ValuationContractData
}) {
  // Group brokers by their roles
  const groupedBrokers = groupBrokersByRole(data.brokers || []) ?? {}

  // Keep the order of roles as they appear in the brokers array
  const orderedRoles = sortBySpecificOrder(
    data.brokers ?? [],
    [
      BrokerRole.Main,
      BrokerRole.Responsible,
      BrokerRole.Assistant,
      BrokerRole.SecondaryAssistant,
      BrokerRole.Settlement,
    ],
    'roleName',
  )
    .map((broker) => broker.roleName)
    .filter((value, index, self) => self.indexOf(value) === index)

  return (
    <FullPage>
      <p>Oppdragsnummer: {data.assignmentNumber}</p>
      <SectionHeader>1. Partene</SectionHeader>
      <SectionSubHeader>1.1. Oppdragsgiver</SectionSubHeader>

      {data.sellers.map((seller) =>
        seller.contactType === 1 ? (
          <Grid
            columns={2}
            key={`${seller.name}${seller.email}`}
            cells={[
              `Navn: ${seller.companyName}`,
              `Telefon: ${seller.mobilePhone ?? ''}`,
              `Org.nr: ${formatOrgNr(seller.orgNumber)}`,
              `E-post: ${seller.email ?? ''}`,
              `Postadresse: ${seller.postalAddress}`,
              `Poststed: ${seller.postalCode} ${seller.city}`,
            ]}
          />
        ) : (
          <Grid
            columns={2}
            key={`${seller.name}${seller.email}`}
            cells={[
              `Navn: ${seller.name}`,
              `Telefon: ${seller.mobilePhone}`,
              `Fødselsnummer: ${seller.socialSecurity}`,
              `E-post: ${seller.email}`,
              `Postadresse: ${seller.postalAddress}`,
              `Poststed: ${seller.postalCode} ${seller.city}`,
            ]}
          />
        ),
      )}

      <SectionSubHeader>1.2. Oppdragstaker</SectionSubHeader>
      <p className="mt-4">
        {data.department.legalName}. Foretaksnr.{' '}
        {data.department.organisationNumber}.
      </p>
      <p>
        {`${data.department.streetAddress}, ${data.department.postalCode} `}
        <span className="capitalize">
          {data.department.city?.toLowerCase()}
        </span>
      </p>
      {data.department.phone && <p>Telefon {data.department.phone}</p>}
      {orderedRoles?.map((roleName) => (
        <div key={roleName}>
          <p className="mt-6 mb-4 font-medium">{roleName}</p>
          {!isEmpty(groupedBrokers) &&
            groupedBrokers[roleName]!.map((broker, i) => (
              <Grid
                key={i}
                columns={3}
                cells={[
                  broker.name,
                  `Mobil: ${broker.mobilePhone}`,
                  `E-post: ${broker.email}`,
                ]}
              />
            ))}
        </div>
      ))}
      {data.officeExpert !== undefined && (
        <>
          <p className="mt-6 font-medium">Fagansvarlig:</p>
          <Grid
            columns={3}
            cells={[
              data.officeExpert?.name,
              `Mobil: ${data.officeExpert?.mobilePhone}`,
              `E-post: ${data.officeExpert?.email}`,
            ]}
          />
        </>
      )}
      <p className="mt-6 font-medium">
        Øvrige ansatte på kontoret som kan bistå med oppdraget:
      </p>
      <p className="mt-2">
        {data.department.employees?.join(', ') || 'Ingen ansatte'}
      </p>
      <p className="mt-6 font-medium">Det økonomiske oppgjøret foretas av:</p>
      <p className="mt-2">Nordvik Oppgjør AS</p>
      <p>Postboks 397 Skøyen, 0213 Oslo</p>
      <p>Tlf: 90412700</p>
      <p>Fax: 22607455</p>

      <Footer pageNr={1} totalPages={4} />
    </FullPage>
  )
}
