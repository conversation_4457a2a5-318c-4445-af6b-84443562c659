'use client'

import { RefreshCcwIcon } from 'lucide-react'
import { useEffect } from 'react'

import { Button } from '@nordvik/ui/button'

import { EmptyState } from '@/components/empty-state'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error(error)
  }, [error])

  return (
    <EmptyState
      className="mt-16 grow"
      illustration="error"
      actions={
        <Button onClick={() => reset()} iconEnd={<RefreshCcwIcon />} size="lg">
          Prøv igjen
        </Button>
      }
      title="Noe gikk galt med henting av data"
      description="Prøv igjen nå eller om litt. Om det vedvarer ta kontakt med support."
    />
  )
}
