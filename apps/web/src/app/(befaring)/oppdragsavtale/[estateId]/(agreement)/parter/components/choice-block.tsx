import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'

import { Subheading } from './subheading'

export default function ChoiceBlock<T extends string>({
  choices,
  name,
  title,
  description,
  direction = 'column',
  defaultValue,
  onValueChange,
  disabled,
  hasError,
  id,
}: {
  choices: { value: T; label: string }[]
  name: string
  title: string
  description?: string | React.ReactNode
  direction?: 'row' | 'column'
  defaultValue?: T | undefined
  onValueChange?: (value: T) => void
  disabled?: boolean
  hasError?: boolean
  id?: string
}) {
  const flexDirectionClassName = direction === 'row' ? 'flex-row' : 'flex-col'
  return (
    <div className="flex flex-col gap-1" data-fields={id}>
      <Subheading hasError={hasError}>{title}</Subheading>
      {typeof description === 'string' ? (
        <span className="typo-body-md ink-subtle">{description}</span>
      ) : (
        description
      )}
      <RadioGroup
        disabled={disabled}
        defaultValue={defaultValue}
        className={`flex gap-x-6 mt-3 gap-y-4 ${flexDirectionClassName}`}
        name={name}
        onValueChange={onValueChange}
      >
        {choices.map((choice) => (
          <div className="flex gap-2" key={choice.value}>
            <RadioGroupItem
              className="mt-0.5 shrink-0"
              value={choice.value}
              id={name + choice.value}
            />
            <label htmlFor={name + choice.value} className="typo-body-md">
              {choice.label}
            </label>
          </div>
        ))}
      </RadioGroup>
    </div>
  )
}
