import type { ContractData } from '..'
import { registeredPartners } from '@befaring/lib/text-formatting'

import { formatOrgNr } from '@/utils/format-org'

import CheckboxSection from '../checkbox-section'
import ConditionalRender from '../conditional-render'
import Footer from '../footer'
import FullPage from '../full-page'
import { contractPronoun } from '../generate-contract'
import { SectionHeader, SectionSubHeader } from '../section-header'
import Grid from '../table'

export function Page1({ contractData: data }: { contractData: ContractData }) {
  const we = contractPronoun(data, { perspective: 'first', capitalize: true })
  const you = contractPronoun(data, { perspective: 'second' })
  const they = contractPronoun(data, { perspective: 'third' })

  return (
    <FullPage>
      <p>Oppdragsnummer: {data.assignmentNumber}</p>
      <SectionHeader>1. Partene</SectionHeader>
      <SectionSubHeader>1.1. Oppdragsgiver</SectionSubHeader>

      {data.sellers.map((seller) =>
        seller.contactType === 1 ? (
          <Grid
            columns={2}
            key={`${seller.name}${seller.email}`}
            cells={[
              `Navn: ${seller.companyName}`,
              `Telefon: ${seller.mobilePhone ?? ''}`,
              `Org.nr: ${formatOrgNr(seller.orgNumber)}`,
              `E-post: ${seller.email ?? ''}`,
              `Postadresse: ${seller.postalAddress}`,
              `Poststed: ${seller.postalCode} ${seller.city}`,
            ]}
          />
        ) : (
          <Grid
            columns={2}
            key={`${seller.name}${seller.email}`}
            cells={[
              `Navn: ${seller.name}`,
              `Telefon: ${seller.mobilePhone}`,
              `Fødselsnummer: ${seller.socialSecurity}`,
              `E-post: ${seller.email}`,
              `Postadresse: ${seller.postalAddress}`,
              `Poststed: ${seller.postalCode} ${seller.city}`,
            ]}
          />
        ),
      )}

      <ConditionalRender condition={!data.sellerIsCompany}>
        <CheckboxSection
          title="Bekreftelse på Oppdragsgivers sivilstand:"
          checkboxes={[
            {
              checked: data.marriedOrInPartnership === true,
              label: `${we} er gift eller ${registeredPartners(data.sellers)}`,
            },
            {
              checked:
                data.marriedOrInPartnership === true &&
                data.isCommonEstate === true,
              indented: true,
              label:
                'Oppdraget gjelder felles bolig, uavhengig av hvem som står oppført i grunnboken som eier',
            },
            {
              checked:
                data.marriedOrInPartnership === true &&
                data.isCommonEstate === false,
              indented: true,
              label: 'Oppdraget gjelder ikke felles bolig',
            },
            {
              label: `${we} er ikke gift eller ${registeredPartners(data.sellers)}`,
              checked: data.marriedOrInPartnership === false,
            },
          ]}
        />
      </ConditionalRender>

      {data.estate.ownership === 2 ? (
        <CheckboxSection
          checkboxes={[
            { label: 'Ja', checked: !!data.sellerIsShareholder },
            { label: 'Nei', checked: !data.sellerIsShareholder },
          ]}
          horizontal
          title={`Er ${you} registrert som aksjeeier til aksjeboligen?`}
          description={`Aksjeeier er den som står oppført som eier i aksjeeierboken. Dersom ${you} ikke er oppført som aksjeeier må ${you} fremlegge gyldig fullmakt fra den som eier aksjen.`}
        />
      ) : (
        <CheckboxSection
          checkboxes={[
            { label: 'Ja', checked: !!data.ownerIsSeller },
            { label: 'Nei', checked: !data.ownerIsSeller },
          ]}
          horizontal
          title={`Er Oppdragsgiver og hjemmelshave ${they} samme?`}
          description={`Dersom Oppdragsgiver og hjemmelshaver ikke er ${they} samme, skal Oppdragsgiver fremlegge gyldig fullmakt.`}
        />
      )}

      <p className="mt-6">
        Hvis det er flere oppdragsgivere gir disse hverandre gjensidig fullmakt
        til å treffe ehver besluttning i anledning salget. Slike beslutninger
        kan for eksempel være bestilling av tjenester, godkjenning av
        salgsmateriale eller beslutninger under budrunde og ved aksept av bud.
        En beslutning fra en av oppdragsgiverne vil dermed være bindende for
        alle.
      </p>

      <Footer pageNr={1} />
    </FullPage>
  )
}
