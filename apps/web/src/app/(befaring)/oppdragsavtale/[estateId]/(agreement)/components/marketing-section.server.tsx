import { getMarketingPackages } from '@/server/model/MarketingPackages/factory'

import MarketingSectionClient from './marketing-section.client'

export default async function MarketingSection() {
  const packages = await getMarketingPackages({
    active: true,
    publicVisible: true,
    type: 'nordvik',
  })

  return (
    <MarketingSectionClient
      marketingPackages={packages.map((p) => ({
        id: p.id,
        name: p.name,
        shortName: p.shortName,
        active: p.active,
        public: p.public,
        packageId: p.packageId,
        productTag: p.productTag,
        price: p.price,
        views: p.views,
        clicks: p.clicks,
        channels: p.channels,
      }))}
    />
  )
}
