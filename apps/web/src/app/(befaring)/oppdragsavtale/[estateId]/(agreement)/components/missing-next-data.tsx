import { ArrowUpRight, CheckCircleIcon, CircleIcon } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'

import { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { EmptyState } from '@/components/empty-state'
import { NextSyncEstate } from '@/components/next-sync-estate'

import { AgreementMissingDataValues } from './utils'

const defaultFields: AgreementMissingDataValues[] = [
  'ansvarlig megler',
  'selgere',
  'prisantydning',
  'provisjon',
  'budsjett',
]

export default function MissingNextData({
  missingData,
  linkToNext,
  estate,
  allFields = defaultFields,
}: {
  missingData: AgreementMissingDataValues[]
  linkToNext?: string | null
  estate: GQLGetBrokerEstateQuery['estate']
  allFields?: AgreementMissingDataValues[]
}) {
  const isMissingResponsibleBroker = missingData.includes('ansvarlig megler')

  const checklist: { label: AgreementMissingDataValues; checked: boolean }[] =
    allFields.map((field) => ({
      label: field,
      checked: !missingData.includes(field),
    }))

  return (
    <EmptyState
      className="mt-20"
      illustration={isMissingResponsibleBroker ? 'no-broker' : 'no-data'}
      title="Mangler informasjon om oppdraget"
      description="For å bruke oppdragsavtalen må du først fylle ut dette på oppdraget i Next."
      actions={
        <>
          {estate?.estateId && (
            <NextSyncEstate
              estateId={estate?.estateId}
              name={estate?.address?.streetAddress}
            >
              <Button variant="tertiary" size="sm">
                Oppdater oppdrag
              </Button>
            </NextSyncEstate>
          )}
          <Button
            variant="tertiary"
            size="sm"
            href={linkToNext ?? 'https://nop.vitecnext.no/'}
            target="_blank"
            iconEnd={<ArrowUpRight />}
          >
            Gå til Next
          </Button>
        </>
      }
    >
      <div className="flex flex-col gap-2">
        {checklist.map((item) => (
          <MissingDataItem key={item.label} {...item} />
        ))}
      </div>
    </EmptyState>
  )
}

function MissingDataItem({
  label,
  checked,
}: {
  label: AgreementMissingDataValues
  checked: boolean
}) {
  return (
    <div className="flex items-center gap-2">
      {checked ? (
        <CheckCircleIcon className="ink-success" />
      ) : (
        <CircleIcon className="ink-disabled" />
      )}
      <div
        className={cn(
          'typo-body-md first-letter:uppercase',
          checked ? 'ink-default' : 'ink-disabled',
        )}
      >
        {label}
      </div>
    </div>
  )
}
