import { ChevronDown } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'

export default function CollapseIcon({
  open,
  enabled,
  className,
}: {
  open: boolean
  enabled?: boolean
  className?: string
}) {
  if (enabled) {
    return (
      <ChevronDown
        className={cn(
          'shrink-0 ink-muted transition-transform ease-in-out max-sm:self-start size-4 sm:size-5 max-sm:mt-0.5',
          {
            'rotate-180': open,
          },
          className,
        )}
      />
    )
  }

  return <div className="size-4 sm:size-5" />
}
