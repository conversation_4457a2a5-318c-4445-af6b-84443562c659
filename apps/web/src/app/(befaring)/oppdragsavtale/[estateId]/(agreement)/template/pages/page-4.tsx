import type { ContractData } from '..'
import { calculateCommission } from '@befaring/lib/calculate-commission'

import { MINIMUM_COMMISSION } from '@/utils/constants'

import Checkbox from '../checkbox'
import CostTable from '../cost-table'
import CurrencyCell from '../currency-cell'
import Footer from '../footer'
import FullPage from '../full-page'
import { SectionSubHeader } from '../section-header'
import Grid from '../table'

export function Page4({ contractData }: { contractData: ContractData }) {
  const { budget } = contractData
  return (
    <FullPage>
      <FourPointTwo contractData={contractData} />

      <p className="mt-2">
        I tillegg til avtalt provisjon, påløper kostnader for tilrettelegging,
        markedsføring, oppgjør og kostnader i forbindelse med visning, som
        spesifisert i punkt 4.3.
      </p>

      <SectionSubHeader>4.3. Kostnadsoppstilling</SectionSubHeader>

      <br className="mt-6" />

      {Object.entries(budget.costs).map(([title, rows]) => (
        <CostTable key={title} title={title} rows={rows} />
      ))}

      <CostTable
        title="Sum kostnader"
        rows={[
          {
            description:
              'Stipulerte totale kostnader eksklusive Boligselgerforsikring',
            cost: budget.sum + (budget.marketingPackage?.price ?? 0),
          },
          {
            description:
              'Stipulerte totale kostnader inklusive Boligselgerforsikring',
            cost:
              budget.sum +
              (budget.marketingPackage?.price ?? 0) +
              (budget.sellerInsurance?.estimatedPremium ?? 0),
          },
        ]}
      />

      <Footer pageNr={4} />
    </FullPage>
  )
}

function FourPointTwo({ contractData }: { contractData: ContractData }) {
  const commission = calculateCommission(
    {
      suggestedPrice: contractData.estate.suggestedPrice || null,
      commissionIsPercentageBased:
        contractData.commission.type === 'percentage',
      feePercentage: contractData?.commission.feePercentage || null,
      fixedCommission: contractData?.commission.fixedCommission || null,
    },
    { round: true },
  )

  const isCommisionLowerThanMinimum = commission < MINIMUM_COMMISSION

  if (contractData.commission.type === 'percentage') {
    return (
      <>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-4">
            <SectionSubHeader>4.2. Provisjonsbasert vederlag</SectionSubHeader>
            <Checkbox className="mt-6" checked />
          </div>
        </div>
        <p className="mt-4 font-medium">
          Når handelen er kommet i stand, betaler oppdragsgiver en provisjon som
          beløper seg til:
        </p>
        <Grid
          columns={2}
          cellClassName="py-1"
          showRaster={false}
          cells={[
            'Antatt salgssum (eventuell fellesgjeld vil bli lagt til denne summen):',
            <CurrencyCell
              key="Antatt salgssum"
              value={contractData.estate.suggestedPrice}
            />,
            <span key="%">
              Provisjon{' '}
              <strong>{contractData.commission.feePercentage}%</strong> av
              antatt salgssum utgjør:
            </span>,
            <CurrencyCell
              key="utgjør"
              value={
                contractData.estate.suggestedPrice *
                (contractData.commission.feePercentage / 100)
              }
            />,
          ]}
        />
        <p className="mt-4">
          Ved provisjonsbasert vederlag har oppdragstaker alltid krav på en
          minsteprovisjon på kr.{' '}
          {(isCommisionLowerThanMinimum
            ? commission
            : MINIMUM_COMMISSION
          ).toLocaleString('no-NO')}
          ,- inkl. mva.
        </p>
      </>
    )
  } else if (contractData.commission.type === 'fixed') {
    return (
      <>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-4">
            <SectionSubHeader>4.2. Fastprisbasert vederlag</SectionSubHeader>
            <Checkbox className="mt-6" checked />
          </div>
        </div>
        <p className="mt-4 font-medium">
          Når handelen er kommet i stand, betaler oppdragsgiver en fastpris som
          beløper seg til:
        </p>
        <Grid
          columns={2}
          cellClassName="py-1"
          showRaster={false}
          cells={[
            'Fastpris',
            <CurrencyCell
              key="Antatt salgssum"
              value={contractData.commission.fixedCommission}
            />,
          ]}
        />
        <p className="mt-4">
          Ved fastprisbasert vederlag har oppdragstaker alltid krav på en
          minsteprovisjon på kr{' '}
          {(isCommisionLowerThanMinimum
            ? commission
            : MINIMUM_COMMISSION
          ).toLocaleString('no-NO')}
          ,- inkl. mva.
        </p>
      </>
    )
  }
}
