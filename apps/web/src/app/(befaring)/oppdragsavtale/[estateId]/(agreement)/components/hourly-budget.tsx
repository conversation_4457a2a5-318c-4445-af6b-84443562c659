'use client'

import { hourlyCommissionRate } from '@befaring/lib/prices'
import React from 'react'

import { formatCurrency } from '@/lib/formatCurrency'

import Grid from '../template/table'

export default function HourlyBudget({
  suggestedPrice,
  budgetSum,
}: {
  suggestedPrice?: number | null
  budgetSum: number
  sellers: unknown[]
}) {
  const { totalHourlyCommission, cells, totalNrOfHours } = calculateHours(
    suggestedPrice,
    budgetSum,
  )

  return (
    <div className="">
      <p className="typo-body-md">
        Antall timer på oppdraget er estimert til {totalNrOfHours} timer.
        Timesats er: kr.{' '}
        {formatCurrency(hourlyCommissionRate, { suffix: false })} inkl. mva.
      </p>

      <p>
        Dersom Oppdragsgiver velger timebasert vederlag, estimeres prisen til{' '}
        <span className="font-medium">
          kr. {formatCurrency(totalHourlyCommission, { suffix: false })} inkl.
          mva.
        </span>
      </p>
      <p className="typo-body-md mt-4">
        Påkrevde arbeidstimer vil variere med oppdragets omfang og art.
        Nødvendig arbeid i saken kan derfor gå utover estimert timeantall. Det
        estimerte timeantallet er ikke bindende for Oppdragstaker. Oppdragsgiver
        vil bli fakturert for påløpt tid utover det estimerte timeantallet.
        Oppdragstaker plikter å varsle Oppdragsgiver dersom timeantallet
        forventes å vesentlig overstige det estimerte timeantallet.
      </p>

      <Grid
        showRaster={false}
        columns={2}
        cells={cells
          .map((cell) => [
            cell.title,
            <div className="w-full text-right" key={cell.title}>
              {cell.value} timer
            </div>,
          ])
          .flat()}
      />
    </div>
  )
}

export function calculateHours(suggestedPrice?: number | null, minPrice = 0) {
  const calculator = (baseMultiplier = 1) => {
    let multiplier = baseMultiplier
    if (suggestedPrice) {
      const basePrice = 19200000 // 19.2 MNOK
      const multiples = Math.floor(suggestedPrice / basePrice)
      multiplier = Math.max(Math.pow(1.5, multiples), multiplier)
    }

    const cells = [
      {
        title: 'Klargjøring',
        value: Math.ceil(25 * multiplier),
      },
      {
        title:
          'Visninger (forberedelser, gjennomføring og etterarbeid) – her har vi kun tatt høyde for to visningsrunder',
        value: Math.ceil(15 * multiplier),
      },
      {
        title: 'Interesser og bud',
        value: Math.ceil(12 * multiplier),
      },
      {
        title: 'Utforming av kontrakt/kontraktsmøte',
        value: Math.ceil(7 * multiplier),
      },
      {
        title:
          'Gjennomføring av handel (klargjøring til og gjennomføring av oppgjør)',
        value: Math.ceil(8 * multiplier),
      },
    ]

    const totalNrOfHours = cells.reduce((acc, cell) => acc + cell.value, 0)

    return {
      cells,
      totalNrOfHours,
      totalHourlyCommission: totalNrOfHours * hourlyCommissionRate,
    }
  }

  for (let i = 1; i < 10; i += 0.5) {
    const result = calculator(i)
    if (result.totalHourlyCommission >= minPrice) {
      return result
    }
  }

  // This should never happen
  return calculator()
}
