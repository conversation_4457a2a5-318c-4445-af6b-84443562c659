'use client'

import { Section } from '@befaring/components/section'
import { useUpdateListingAgreementCustomerInfo } from '@befaring/hooks/use-update-listing-agreement-customer-info'
import { ValidatedFieldsListingAgreement } from '@befaring/lib/check-valid-fields'
import { pronoun } from '@befaring/lib/text-formatting'
import debounce from 'lodash/debounce'
import { startTransition, useCallback, useMemo, useState } from 'react'

import { Input } from '@nordvik/ui/input'
import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'

import { NextPrivateContact } from '@/actions/next/types-next'

import { useBudget } from '../../../../../context/budget-context'

import { Subheading } from './subheading'
import { hasFieldError } from './utils'

export default function ConsentSection({
  sellers,
}: {
  sellers: NextPrivateContact[]
}) {
  const {
    listingAgreement,
    locked,
    errorFields,
    customerInfo,
    setCustomerInfo,
    setIsUpdating,
  } = useBudget()
  const [showPreviousBrokers, setShowPreviousBrokers] = useState(
    customerInfo.no_previous_brokers === false,
  )
  const [previousBrokers, setPreviousBrokers] = useState<string>(
    customerInfo.previous_brokers ?? '',
  )

  const updateInfo = useUpdateListingAgreementCustomerInfo(listingAgreement.id)

  const handleUpdateUpdateNoPreviousBrokers = (value: string) => {
    setIsUpdating(true)
    setShowPreviousBrokers(value === 'no')

    startTransition(() => {
      setCustomerInfo({
        ...customerInfo,
        no_previous_brokers: value === 'yes',
      })
    })

    void updateInfo(
      { noPreviousBrokers: value === 'yes' },
      `Kunne ikke oppdatere informasjon om tidligere meglere`,
    )
  }

  const debouncedUpdate = useMemo(
    () =>
      debounce((value: string) => {
        setIsUpdating(true)
        updateInfo(
          { previousBrokers: value },
          `Kunne ikke oppdatere informasjon om tidligere meglere`,
        )
      }, 1000),
    [updateInfo, setIsUpdating],
  )

  const handleUpdatePreviousBrokers = useCallback(
    (value: string) => {
      setIsUpdating(true)
      // Update state immediately
      startTransition(() => {
        setCustomerInfo((prevState) => ({
          ...prevState,
          previous_brokers: value,
        }))
      })

      setPreviousBrokers(value)

      // Call the debounced update function
      return debouncedUpdate(value)
    },
    [setCustomerInfo, debouncedUpdate, setIsUpdating],
  )

  const noPreviousBrokersChoice = [
    {
      value: 'yes',
      label: 'Nei',
    },
    {
      value: 'no',
      label: 'Ja',
    },
  ]

  const name = 'consent'
  const previousBrokersValue =
    customerInfo.no_previous_brokers === null
      ? undefined
      : customerInfo.no_previous_brokers
        ? 'yes'
        : 'no'
  const you = pronoun(sellers, { perspective: 'second' })
  return (
    <Section>
      <div className="flex flex-col gap-4">
        <div
          className="flex flex-col gap-1"
          data-fields={ValidatedFieldsListingAgreement.no_previous_brokers}
        >
          <Subheading
            hasError={
              hasFieldError('no_previous_brokers', errorFields) ||
              hasFieldError('previous_brokers', errorFields)
            }
          >
            Har andre meglere jobbet med salg av eiendommen de siste tre
            månedene?
          </Subheading>
          <p className="typo-body-md ink-subtle">
            Da kan det hende {you} må betale eiendomsmegler vederlag.
          </p>
        </div>

        <RadioGroup
          className="flex flex-col gap-4"
          name={name}
          onValueChange={handleUpdateUpdateNoPreviousBrokers}
          defaultValue={previousBrokersValue}
          disabled={locked}
        >
          {noPreviousBrokersChoice.map((choice) => (
            <div className="flex gap-2" key={choice.value}>
              <RadioGroupItem
                className="mt-0.5"
                value={choice.value}
                id={name + choice.value}
              />
              <label htmlFor={name + choice.value} className="typo-body-md">
                {choice.label}
              </label>
            </div>
          ))}
        </RadioGroup>

        {showPreviousBrokers && (
          <div className="flex flex-col gap-1">
            <Input
              type="text"
              disabled={locked}
              placeholder="Navn på megler(e)"
              name={ValidatedFieldsListingAgreement.previous_brokers}
              error={hasFieldError('previous_brokers', errorFields)}
              onChange={(e) => {
                handleUpdatePreviousBrokers(e.target.value)
              }}
              value={previousBrokers}
            />
            <p className="ink-subtle typo-body-sm">
              Oppdragsgiver er selv ansvarlig for å avslutte ev. oppdrag med
              andre oppdragstakere fra de siste tre månedene. Oppdragsgiver
              svarer selv for ev. krav som måtte oppstå dersom tidligere
              oppdragstakere krever vederlag og dekning av utlegg.
            </p>
          </div>
        )}
      </div>
    </Section>
  )
}
