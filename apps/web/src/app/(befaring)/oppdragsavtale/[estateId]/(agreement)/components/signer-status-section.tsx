'use server'

import { Suspense, cache } from 'react'

import { updateSigners } from '@/actions/signicat/update-signers'
import prisma from '@/db/prisma'
import { getCurrentUser } from '@/lib/session'

import Agreement, { type AgreementProps } from './agreement/agreement'

const cachedListingAgreement = cache((estateId) =>
  prisma.listing_agreements.findFirst({
    where: { estate_id: estateId },
    include: {
      signers: true,
      offer_access_tokens: {
        where: {
          valid: true,
          deleted_at: null,
        },
      },
    },
  }),
)

type ListingAgreement = NonNullable<
  Awaited<ReturnType<typeof cachedListingAgreement>>
>

export default async function SignerStatusSection({
  estateId,
}: {
  estateId: string
}) {
  const agreement = await cachedListingAgreement(estateId)

  if (!agreement?.signicat_document_id) {
    return null
  }

  return (
    <Suspense>
      <AgreementWrapper {...agreement} />
    </Suspense>
  )
}

async function AgreementWrapper({
  signers,
  offer_access_tokens,
  ...agreement
}: ListingAgreement) {
  const user = await getCurrentUser()

  // If the agreement is signed and the user is not a broker, do not show the agreement
  if (agreement.signing_finished_at && !user) {
    return null
  }

  // Only show status section if there are signers in Signicat, except show for the broker (if user is truthy) if there is a valid offer access link
  if (!signers || !signers.length) {
    if (agreement.signicat_document_id) {
      try {
        await updateSigners({
          id: agreement.id,
          signicat_document_id: agreement.signicat_document_id,
          estate_id: agreement.estate_id,
        })
      } catch (error) {
        console.error('Failed to update signers', error)
        return null
      }
    }
    return null
  }

  if (!(signers.length > 0 || (user && offer_access_tokens))) {
    return null
  }

  const agreementProps: AgreementProps = {
    createdAt: agreement?.initiated_signing_at?.toISOString() ?? undefined,
    deadline: agreement.deadline_for_signing?.toISOString(),
    isSigned: !!agreement.signing_finished_at,
    signees: signers.map((signer) => ({
      id: signer.id,
      email: signer.email,
      externalSignerId: signer.external_signer_id,
      firstName: signer.first_name ?? undefined,
      lastName: signer.last_name ?? undefined,
      signedAt: signer.signed_at?.toISOString(),
      title: signer.title ?? undefined,
      url: signer.url ?? undefined,
    })),
  }

  return <Agreement {...agreementProps} />
}
