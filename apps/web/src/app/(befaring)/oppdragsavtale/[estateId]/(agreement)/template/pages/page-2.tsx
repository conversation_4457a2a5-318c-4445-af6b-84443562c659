import type { ContractData } from '..'
import { BrokerRole } from '@befaring/lib/broker-constants'
import isEmpty from 'lodash/isEmpty'

import { sortBySpecificOrder } from '@/lib/sort-by-specific-order'

import Footer from '../footer'
import FullPage from '../full-page'
import { SectionHeader, SectionSubHeader } from '../section-header'
import Grid from '../table'
import {
  getCommuneDetails,
  getMatrikkelDetails,
  groupBrokersByRole,
} from '../utils'

export function Page2({ contractData }: { contractData: ContractData }) {
  const partOwnershipName = contractData.estate.partOwnership.partName
  const businessContact = contractData.estate.businessContact

  // Group brokers by their roles
  const groupedBrokers = groupBrokersByRole(contractData.brokers || []) ?? {}

  // Keep the order of roles as they appear in the brokers array
  const orderedRoles = sortBySpecificOrder(
    contractData.brokers ?? [],
    [
      BrokerRole.Main,
      Broker<PERSON><PERSON>.Responsible,
      BrokerRole.Assistant,
      BrokerRole.SecondaryAssistant,
      BrokerRole.Settlement,
    ],
    'roleName',
  )
    .map((broker) => broker.roleName)
    .filter((value, index, self) => self.indexOf(value) === index)

  const matrikkelDetails = getMatrikkelDetails(contractData)
  const communeDetails = getCommuneDetails(contractData.estate)

  return (
    <FullPage>
      <SectionSubHeader>1.2. Oppdragstaker</SectionSubHeader>
      <p className="mt-4">
        {contractData.department.legalName}. Foretaksnr.{' '}
        {contractData.department.organisationNumber}.
      </p>
      <p>{`${contractData.department.streetAddress}, ${contractData.department.postalCode} ${contractData.department.city}`}</p>
      {contractData.department.phone && (
        <p>Telefon {contractData.department.phone}</p>
      )}
      {orderedRoles?.map((roleName) => (
        <div key={roleName}>
          <p className="mt-6 mb-4 font-medium">{roleName}</p>
          {!isEmpty(groupedBrokers) &&
            groupedBrokers[roleName]!.map((broker, i) => (
              // eslint-disable-next-line react/no-array-index-key -- No unique id available
              <Grid
                key={i}
                columns={3}
                cells={[
                  broker.name,
                  `Mobil: ${broker.mobilePhone}`,
                  `E-post: ${broker.email}`,
                ]}
              />
            ))}
        </div>
      ))}
      {contractData.officeExpert !== undefined && (
        <>
          <p className="mt-6 font-medium">Fagansvarlig:</p>
          <Grid
            columns={3}
            cells={[
              contractData.officeExpert?.name,
              `Mobil: ${contractData.officeExpert?.mobilePhone}`,
              `E-post: ${contractData.officeExpert?.email}`,
            ]}
          />
        </>
      )}
      <p className="mt-6 font-medium">
        Øvrige ansatte på kontoret som kan bistå med oppdraget:
      </p>
      <p className="mt-2">
        {contractData.department.employees?.join(', ') || 'Ingen ansatte'}
      </p>
      <p className="mt-6 font-medium">Det økonomiske oppgjøret foretas av:</p>
      <p className="mt-2">Nordvik Oppgjør AS</p>
      <p>Postboks 397 Skøyen, 0213 Oslo</p>
      <p>Tlf: 90412700</p>
      <p>Fax: 22607455</p>
      <SectionHeader className="mt-12">2. Eiendommen</SectionHeader>
      <SectionSubHeader>2.1. Oppdragets karakter</SectionSubHeader>
      <p className="mt-2">
        Oppdraget gjelder salg av{' '}
        {contractData.estate.estateType?.toLowerCase()} (
        {contractData.ownershipType?.toLowerCase()})
      </p>

      <SectionSubHeader>2.2. Betegnelse</SectionSubHeader>
      <p className="mt-2">
        {`Adresse: ${contractData.estate.address}, ${contractData.estate.postalOffice}`}
      </p>

      <p>{`${matrikkelDetails} ${communeDetails}`}</p>

      {businessContact && (
        <p className="mt-2">{`${businessContact} er forretningsfører for ${partOwnershipName}`}</p>
      )}
      <Footer pageNr={2} />
    </FullPage>
  )
}
