'use client'

import { useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import { useToast } from '@nordvik/ui/toaster'

import { useUserContext } from '@/lib/UserContext'
import { formatCurrency } from '@/lib/formatCurrency'

import { useBudget } from '../../../../context/budget-context'

const formatPrice = (value: number | null) =>
  value ? formatCurrency(value, { currency: '' }) : ''
const clearPriceFormatting = (value: string) => value.replace(/\D/g, '')
const parsePrice = (value: string) => parseInt(value.replace(/\s/g, ''))

export default function PriceSuggestion({
  disabled: isDisabled,
}: {
  disabled?: boolean
}) {
  const { toast } = useToast()
  const { user } = useUserContext()

  const { suggestedPrice, setSuggestedPrice, locked } = useBudget()
  const [inputPrice, setInputPrice] = useState(formatPrice(suggestedPrice))

  const disabled = isDisabled || locked || !user

  const handleUpdateSuggestion = async (number: number) => {
    if (number === suggestedPrice) return

    try {
      await setSuggestedPrice(number)
    } catch (error) {
      toast({
        title: 'Feil ved oppdatering av prisvurdering',
        description: 'Forsøk å oppdatere siden',
        variant: 'destructive',
      })
    }
  }

  const width = Math.max(
    (inputPrice.match(/\d/g)?.length ?? 0) * 27 +
      (inputPrice.match(/\s/g)?.length ?? 0) * 8 +
      10,
    180,
  )

  return (
    <div className="flex flex-col gap-1">
      <label
        className="typo-detail-md uppercase ink-muted"
        htmlFor="priceSuggestion"
      >
        Prisantydning
      </label>
      <div className="flex items-baseline">
        <input
          disabled={disabled}
          className={cn(
            'typo-display-xl border-b border-muted bg-transparent rounded-none [&::-webkit-inner-spin-button]:appearance-none disabled:opacity-100',
            {
              'cursor-default border-b-[transparent]': !user,
            },
          )}
          style={{ width: `${width}px` }}
          value={inputPrice}
          readOnly={!user}
          onFocus={(e) => setInputPrice(clearPriceFormatting(e.target.value))}
          onBlur={async (e) => {
            const price = parsePrice(e.target.value)
            setInputPrice(formatPrice(price))
            await handleUpdateSuggestion(price)
          }}
          onChange={(e) => setInputPrice(e.target.value.replace(/\D/g, ''))}
        />
        <span className="typo-body-lg ml-2">kr</span>
      </div>
    </div>
  )
}
