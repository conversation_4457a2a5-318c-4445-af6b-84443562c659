import { Copy, FileSymlink, MoreVertical } from 'lucide-react'

import { Button } from '@nordvik/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@nordvik/ui/dropdown-menu'
import { Link } from '@nordvik/ui/global-navigation-progress/link'

import { CopyToClipboard } from '@/components/copy-button'

export function AgreementSigneesMoreMenu({
  url,
  name,
}: {
  url: string
  name: string
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          iconOnly={<MoreVertical />}
          className="ml-1 self-center"
        >
          Signeringsvalg
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem asChild>
          <Link href={url}>
            <FileSymlink className="h-4 w-4" />
            Signer nå
          </Link>
        </DropdownMenuItem>
        <CopyToClipboard
          notifcationText={`Lenken til ${name} er kopiert`}
          value={url}
        >
          <DropdownMenuItem>
            <Copy className="h-4 w-4" />
            Kopier lenken
          </DropdownMenuItem>
        </CopyToClipboard>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
