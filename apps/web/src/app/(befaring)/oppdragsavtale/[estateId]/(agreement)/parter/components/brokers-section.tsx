'use client'

import { BrokersInfoSection } from '@befaring/components/brokers-info-section'
import { Section } from '@befaring/components/section'

import type { GQLGetBrokerEstateQuery } from '@/api/generated-client'

export default function BrokersSection({
  estate,
}: {
  estate: GQLGetBrokerEstateQuery['estate']
}) {
  if (!estate) return null

  return (
    <Section title="Oppdragstaker">
      <BrokersInfoSection estate={estate} />
    </Section>
  )
}
