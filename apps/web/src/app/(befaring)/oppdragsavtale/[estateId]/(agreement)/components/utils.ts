import { ListingAgreementResponse } from '@befaring/actions/types'
import { BrokerRole } from '@befaring/lib/broker-constants'
import { getBrokerByRole } from '@befaring/lib/format-brokers'

import { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import { GQLGetBrokerEstateQuery } from '@/api/generated-client'

export type AgreementMissingDataValues =
  | 'budsjett'
  | 'prisantydning'
  | 'provisjon'
  | 'selgere'
  | 'ansvarlig megler'

export const validateMissingValues = (
  listingAgreementData: ListingAgreementResponse,
  sellers: Pick<NextPrivateContactWithProxy, 'contactId'>[],
  estate: GQLGetBrokerEstateQuery['estate'],
) => {
  const { budget, mergedIncomePosts } = listingAgreementData

  const missingData: AgreementMissingDataValues[] = []

  if (mergedIncomePosts.length === 0) missingData.push('budsjett')

  const priceQuote =
    budget.type === 1
      ? budget.baseCommission
      : estate?.estatePrice?.priceSuggestion ||
        estate?.estatePriceModel?.priceSuggestion

  if (!priceQuote || priceQuote === 0) missingData.push('prisantydning')

  if (budget.estimatedCommission === 0) missingData.push('provisjon')

  if (sellers.length === 0 && !estate?.hasCompanySeller)
    missingData.push('selgere')

  if (
    !getBrokerByRole(BrokerRole.Responsible, {
      brokers: estate?.brokers,
      brokersIdWithRoles: estate?.brokersIdWithRoles,
    })
  ) {
    missingData.push('ansvarlig megler')
  }

  return missingData
}
