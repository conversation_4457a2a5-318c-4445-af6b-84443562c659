'use client'

import { updateListingAgreement } from '@befaring/actions/listing-agreement'
import debounce from 'lodash/debounce'
import React from 'react'

import { Textarea } from '@nordvik/ui/textarea'

import { useUserContext } from '@/lib/UserContext'

import {
  CollapsibleSection,
  SectionContent,
} from '../../../../components/collapsible-section'
import { useBudget } from '../../../../context/budget-context'

export default function SeparateProvisionSection({
  estateId,
}: {
  estateId: string
}) {
  const { locked, listingAgreement } = useBudget()
  const { user } = useUserContext()
  const [separateProvision, setSeparateProvision] = React.useState(
    listingAgreement.separate_provision,
  )

  const debouncedUpdate = React.useMemo(
    () =>
      debounce(async (value: string | null, eId: string) => {
        try {
          await updateListingAgreement({
            separateProvision: value ?? undefined,
            estateId: eId,
          })
        } catch (error) {
          console.error('Failed to update separate provision', error)
        }
      }, 500),
    [],
  )

  const handleOnChange = React.useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value
      setSeparateProvision(value)
      if (user) {
        debouncedUpdate(value, estateId)
      }
    },
    [debouncedUpdate, estateId, user],
  )

  React.useEffect(() => {
    return () => {
      debouncedUpdate.cancel()
    }
  }, [debouncedUpdate])

  if ((locked || !user) && !separateProvision) {
    return null
  }

  return (
    <CollapsibleSection
      title="Særskilte bestemmelser"
      defaultOpen={
        separateProvision && separateProvision.length > 0 ? true : false
      }
    >
      <SectionContent className="flex flex-col gap-6 px-[--padding-x] pb-6">
        <div className="flex flex-col gap-4">
          <p className="typo-body-md">
            Bestemmelser satt av eiendomsmegler som gjelder spesielt for dette
            oppdraget. Følgende bestemmelser går foran eventuelle generelle
            bestemmelser som ellers ville gjeldt:
          </p>
          {!locked && user ? (
            <Textarea
              className="min-h-28"
              value={separateProvision ?? undefined}
              onChange={handleOnChange}
            />
          ) : (
            <div className="whitespace-pre-wrap">{separateProvision}</div>
          )}
        </div>
      </SectionContent>
    </CollapsibleSection>
  )
}
