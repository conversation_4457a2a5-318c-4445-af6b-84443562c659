'use client'

import { cn } from '@nordvik/theme/cn'

import { Signee } from '@/hooks/use-up-to-date-signers'
import { DATE_TIME_FORMAT } from '@/lib/constants'
import { formatDate } from '@/lib/dates'

import type { AgreementProps } from './agreement'

export function AgreementHeader({
  signees,
  broker,
  deadline,
  createdAt,
  brokerPerspective,
}: Pick<AgreementProps, 'deadline' | 'createdAt' | 'brokerPerspective'> & {
  signees: Signee[]
  broker?: Signee
}) {
  const nrOfSignatues = signees.filter((signee) => signee.signedAt).length

  const isSigned = nrOfSignatues === signees.length && broker?.signedAt
  const isPartiallySigned = nrOfSignatues > 0 || broker?.signedAt

  const isNotSignedByAnyone = !isSigned && !isPartiallySigned

  let headingText = ''
  let subHeadingText: string | null = null

  if (isSigned && deadline) {
    headingText = 'Avtalen er signert av alle parter'
    subHeadingText = `Oppdraget er gyldig frem til ${formatDate(deadline, 'd. MMM yyyy')}`
  } else if (isPartiallySigned && deadline) {
    headingText = `Signeringsstatus`
    subHeadingText = `Frist for signering: ${formatDate(deadline, DATE_TIME_FORMAT)}`
  } else if (createdAt) {
    headingText = 'Oppdragsavtalen er sendt til signering'
    if (brokerPerspective) {
      subHeadingText =
        'Hvis du vil gjøre endringer må du avbryte signeringen først.'
    }
  }

  return (
    <div
      className={cn('rounded-t-md bg-gray-subtle px-[--padding-x] py-4', {
        'bg-gold-muted': !isSigned && isPartiallySigned,
        'bg-success-muted': isSigned,
      })}
    >
      <div className="flex max-sm:flex-col mb-0.5 justify-between items-baseline">
        <p suppressHydrationWarning className="typo-body-md font-medium">
          {headingText}
        </p>
        {createdAt ? (
          <p
            className="typo-body-sm ink-muted lowercase"
            suppressHydrationWarning
          >
            {formatDate(createdAt, DATE_TIME_FORMAT)}
          </p>
        ) : null}
      </div>
      {Boolean(brokerPerspective && isNotSignedByAnyone) && (
        <p
          suppressHydrationWarning
          className="typo-body-sm"
        >{`${subHeadingText} `}</p>
      )}
    </div>
  )
}
