import {
  Contract,
  type ContractData,
  ValuationContract,
  type ValuationContractData,
} from '.'
import { renderToStaticMarkup } from 'react-dom/server'

export function renderContract(data: ContractData) {
  const string = renderToStaticMarkup(<Contract data={data} />)
  return string
}

export function renderValuationContract(data: ValuationContractData) {
  const string = renderToStaticMarkup(<ValuationContract data={data} />)
  return string
}
