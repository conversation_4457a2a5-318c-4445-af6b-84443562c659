'use client'

import { FileText, LoaderCircle } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { TextButton } from '@nordvik/ui/text-button'
import { useToast } from '@nordvik/ui/toaster'

import { getListingAgreementPdfBuffer } from '@/actions/get-listing-agreement-pdf-buffer'
import { LoadingIndicator } from '@/components/loading-indicator'
import { useEstateId } from '@/hooks/use-estateid'
import { downloadBlob, openBlobInNewTab } from '@/lib/download-blob'
import { useNativeBridge } from '@/lib/use-native-bridge'

import UnlockDialog from '../unlock-dialog'

import type { AgreementProps } from './agreement'

export function AgreementFooter({
  allSigned,
  brokerPerspective,
}: Pick<AgreementProps, 'brokerPerspective'> & {
  allSigned: boolean
}) {
  const { toast } = useToast()
  const { isWebView } = useNativeBridge()
  const [isLoading, setIsLoading] = React.useState(false)
  const estateId = useEstateId() as string

  const fileName = 'Oppdragsavtale Nordvik'

  const openBlob = async () => {
    setIsLoading(true)

    const base64Bufferstring = await getListingAgreementPdfBuffer(estateId)

    if (!base64Bufferstring) {
      setIsLoading(false)
      return toast({
        title: 'Kunne ikke laste ned PDF',
        variant: 'destructive',
      })
    }

    const blob = new Blob([Buffer.from(base64Bufferstring, 'base64')], {
      type: 'application/pdf',
    })

    setIsLoading(false)

    isWebView
      ? downloadBlob(blob, `${fileName}.pdf`)
      : openBlobInNewTab(blob, `${fileName}.pdf`)
  }

  return (
    <div className="flex max-sm:flex-col justify-between sm:items-center py-3.5 gap-3.5">
      <div className="flex gap-1.5 items-center">
        <div className="flex items-baseline gap-1.5">
          <FileText
            size={16}
            className="text-textbutton-ink translate-y-[0.18rem] shrink-0"
          />

          <TextButton
            className={cn(
              'typo-body-md',
              brokerPerspective && allSigned && 'my-1',
            )}
            onClick={openBlob}
            disabled={isLoading}
          >
            {fileName}.pdf
          </TextButton>
        </div>
        {isLoading && <LoadingIndicator limitSteps={2} />}
      </div>
      {brokerPerspective && !allSigned && <AbortSigning />}
    </div>
  )
}

function AbortSigning() {
  return (
    <UnlockDialog
      trigger={(isLoading: boolean) => (
        <Button disabled={isLoading} variant="tertiary" size="sm">
          {isLoading && (
            <LoaderCircle size={16} className="mr-2 animate-spin" />
          )}
          Avbryt signering
        </Button>
      )}
    />
  )
}
