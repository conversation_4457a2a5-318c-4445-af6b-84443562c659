import { cn } from '@nordvik/theme/cn'

export default function Grid({
  showRaster = true,
  cells,
  columns = 2,
  cellClassName,
}: {
  showRaster?: boolean
  cells: (string | React.ReactNode)[]
  columns: number
  cellClassName?: string
}) {
  return (
    <div
      className={cn('mt-4 grid', {
        'border-b border-r border-[#999]': showRaster,
      })}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
      }}
    >
      {cells.map((cell) => (
        <div
          key={cell?.toString()}
          className={cn(
            'px-2 py-1',
            { 'border-l border-t border-[#999]': showRaster },
            cellClassName,
          )}
        >
          {cell}
        </div>
      ))}
    </div>
  )
}
