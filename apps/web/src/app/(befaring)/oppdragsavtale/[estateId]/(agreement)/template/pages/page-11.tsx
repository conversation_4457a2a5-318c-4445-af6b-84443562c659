import type { ContractData } from '..'

import CheckboxSection from '../checkbox-section'
import ConditionalRender from '../conditional-render'
import Footer from '../footer'
import FullPage from '../full-page'
import { contractPronoun } from '../generate-contract'

export function Page11({ contractData }: { contractData: ContractData }) {
  const we = contractPronoun(contractData, {
    perspective: 'first',
    capitalize: true,
  })

  return (
    <FullPage>
      <ConditionalRender condition={!contractData.sellerIsCompany}>
        <h3 className="mt-6">
          Forbrukers avklaring av oppstart av eiendomsmeglingstjenesten,
          inkludert tilleggstjenester (eks. foto/styling):
        </h3>

        <CheckboxSection
          checkboxes={[
            {
              label: `${we} ønsker at Oppdragstaker skal sette i gang arbeidet i henhold til oppdragsavtalen, herunder starte levering av tilknyttede tjenester, før angrefristen på 14 dager har utløpt. ${we} erkjenner at angreretten har gått tapt når tjenesten er levert.`,
              checked: contractData.waiveWithdrawalRight,
            },
            {
              label: `${we} ønsker IKKE at Oppdragstaker skal sette i gang arbeidet i henhold til oppdragsavtalen, herunder starte levering av tilknyttede tjenester, før angrefristen på 14 dager har utløpt.`,
              checked: !contractData.waiveWithdrawalRight,
            },
          ]}
        />
      </ConditionalRender>

      <p className="mt-4">
        Oppdragsgiver er selv ansvarlig for å avslutte ev. oppdrag med andre
        oppdragstakere. Oppdragsgiver svarer selv for ev. krav som måtte oppstå
        dersom tidligere oppdragstakere krever vederlag og dekning av utlegg.
      </p>

      <p className="mt-4 font-medium">
        Oppdragsgiver oppfordres til å sette seg godt inn i bestemmelsene om
        Oppdragstakers gjennomføring av oppdraget i eiendomsmeglerloven kapittel
        6, og Oppdragstakers krav på vederlag kapittel 7.
      </p>
      <Footer pageNr={11} />
    </FullPage>
  )
}
