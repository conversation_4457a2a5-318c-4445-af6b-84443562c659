'use client'

import { updateBudgetPost } from '@befaring/actions/budgetPost'
import { Collapsible } from '@radix-ui/react-collapsible'
import { useCallback, useEffect, useState, useTransition } from 'react'

import { useToast } from '@nordvik/ui/toaster'

import type { MergedBudgetPost } from '@/actions/next/types-next'
import { useEstateId } from '@/hooks/use-estateid'

import { useBudget } from '../../../../context/budget-context'

import { BudgetPostRowContent } from './budget-post-row-content'

export function IncomePostRow({
  post,
  editMode,
  hideDescription,
  forceOpen,
}: {
  post: MergedBudgetPost
  editMode?: boolean
  hideDescription?: boolean
  forceOpen?: boolean
}) {
  const [open, setOpen] = useState(false)
  const [, startTransition] = useTransition()
  const estateId = useEstateId()
  const { toast } = useToast()
  const { setIncomePosts } = useBudget()

  useEffect(() => {
    if (!editMode) {
      setOpen(false)
    }
    if (forceOpen) {
      setOpen(true)
    }
  }, [editMode, forceOpen])

  const handleSubmit = useCallback(
    async (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault()

      const formData = new FormData(e.currentTarget)
      const name = formData.get('name') as string
      const description = formData.get('description') as string
      const amountWithTaxIncluded = formData.get('amount') as string

      setOpen(false)

      // useOptimistic requires a parent form with an action or wrap in startTransition
      startTransition(async () => {
        setIncomePosts((prevPosts) =>
          prevPosts.map((prevPost) =>
            prevPost.budgetpostId === post.budgetpostId
              ? {
                  ...prevPost,
                  name,
                  description,
                  amountWithTaxIncluded: Number(amountWithTaxIncluded),
                }
              : prevPost,
          ),
        )

        try {
          if (!estateId) {
            throw new Error('EstateId is not defined')
          }

          await updateBudgetPost(
            {
              budgetPostId: post.budgetpostId,
              estateId,
              title: name,
              description,
              price: Number(amountWithTaxIncluded),
            },
            post,
          )
        } catch (error) {
          toast({
            title: 'Kunne ikke oppdatere budsjettposten',
            description: 'Prøv igjen senere',
            variant: 'destructive',
          })
        }
      })
    },
    [estateId, post, setIncomePosts, startTransition, toast],
  )

  return (
    <Collapsible open={open} onOpenChange={setOpen} asChild>
      <form onSubmit={handleSubmit}>
        <BudgetPostRowContent
          post={post}
          editMode={editMode}
          open={open}
          setOpen={setOpen}
          hideDescription={!!hideDescription}
        />
      </form>
    </Collapsible>
  )
}
