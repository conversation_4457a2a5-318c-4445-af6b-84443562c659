'use client'

import { parseAsStringLiteral, useQueryState } from 'nuqs'

import { cn } from '@nordvik/theme/cn'
import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'

export const commissionTypeKeys = ['percent', 'hourly', 'fixed'] as const

const commissionTypes: Record<
  (typeof commissionTypeKeys)[number],
  {
    label: string
    description: string
  }
> = {
  percent: {
    label: 'Provisjonsbasert',
    description:
      '<PERSON> betaler for provisjon av salgssum i tillegg til faste kostnader.',
  },
  hourly: {
    label: 'Timebasert vederlag',
    description:
      'Oppdragsgiver blir fakturert for påløpt tid basert på en fast timessats.',
  },
  fixed: {
    label: 'Fastpris',
    description:
      'Provisjon er satt til en fast sum. Andre kostnader og utlegg kommer i tillegg.',
  },
}

export function useCommissionType() {
  return [
    ...useQueryState(
      'commissionType',
      parseAsStringLiteral(commissionTypeKeys).withDefault('percent'),
    ),
    commissionTypeKeys,
  ] as const
}

export function CommissionSelector() {
  const [commissionType, setCommissionType] = useCommissionType()

  return (
    <RadioGroup>
      <div className="flex w-full gap-4 max-lg:flex-col lg:grid lg:grid-cols-3">
        {commissionTypeKeys.map((type) => {
          const selected = type === commissionType
          return (
            <label
              key={type}
              htmlFor={type}
              className={cn(
                'flex grow flex-col items-center justify-between gap-2 rounded-lg border-2 border-muted p-2 text-center lg:p-4',
                selected && 'border-inputs-border-active',
              )}
            >
              <div className="flex flex-col gap-2">
                <span className="typo-title-sm">
                  {commissionTypes[type].label}
                </span>
                <span className="typo-body-sm">
                  {commissionTypes[type].description}
                </span>
              </div>
              <RadioGroupItem
                checked={selected}
                id={type}
                value={type}
                onClick={() => setCommissionType(type)}
              />
            </label>
          )
        })}
      </div>
    </RadioGroup>
  )
}
