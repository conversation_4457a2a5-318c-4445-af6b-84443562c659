import { BrokerRole } from '@befaring/lib/broker-constants'
import { getBrokerByRole } from '@befaring/lib/format-brokers'
import Image from 'next/image'

import { TextButton } from '@nordvik/ui/text-button'

import { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { BrokerAvatar } from '@/app/(protected)/(sidebar)/toppliste/components/filters/leader-board/broker-avatar'
import { EmptyState } from '@/components/empty-state'
import { formatPhoneNumber } from '@/lib/format-phone-number'

export const NoAccessToAgreement = ({
  estate,
}: {
  estate: GQLGetBrokerEstateQuery['estate']
}) => {
  const mainBroker = getBrokerByRole(BrokerRole.Main, {
    brokers: estate?.brokers,
    brokersIdWithRoles: estate?.brokersIdWithRoles,
  })

  return (
    <div className="flex flex-col">
      <div className="bg-brand-muted p-4 flex justify-center mb-20 md:mb-32">
        <Image src="/logo.svg" alt="" width={173} height={56} />
      </div>
      <EmptyState
        illustration="no-data"
        title="Oppdragsavtalen er ikke lenger tilgjengelig"
        description="Megler gjør endringer i oppdragsavtalen. Du vil få en sms og e-post med lenke til den nye avtalen når det er klart. Kontakt megler hvis du har noen spørsmål. "
      />

      {mainBroker ? (
        <div className="text-center flex flex-col items-center mt-20 mb:mt-32">
          <BrokerAvatar
            broker={{
              name: mainBroker.employee.name ?? 'Megler',
              avatarUrl: mainBroker.employee.image?.small,
            }}
            size="large"
            className="md:size-[4.5rem]"
          />
          <p className="mt-2 typo-body-md font-medium">
            {mainBroker.employee?.name}
          </p>
          <p className="typo-body-md">{mainBroker.employee.title}</p>
          <TextButton
            href={`mailto:${mainBroker.employee.email}`}
            className="mb-1 mt-2"
          >
            {mainBroker.employee.email}
          </TextButton>
          {mainBroker.employee.mobilePhone && (
            <TextButton href={`tel:${mainBroker.employee.mobilePhone}`}>
              {formatPhoneNumber(mainBroker.employee.mobilePhone)}
            </TextButton>
          )}
        </div>
      ) : null}
    </div>
  )
}
