'use client'

import React from 'react'

import { Signee, useUpToDateSigners } from '@/hooks/use-up-to-date-signers'
import { useUserContext } from '@/lib/UserContext'

import { useBudget } from '../../../../../context/budget-context'

import { AgreementFooter } from './agreement-footer'
import { AgreementHeader } from './agreement-header'
import { AgreementSignees } from './agreement-signees'

export interface AgreementProps {
  createdAt?: string
  deadline?: string
  isSigned?: boolean
  brokerPerspective?: boolean
  signees: Signee[]
}

export type SignicatSigner = {
  id: string
  external_signer_id: string
  first_name?: string | null
  last_name?: string | null
  signed_at?: Date | null
  url?: string | null
}

function Agreement({
  createdAt,
  deadline,
  signees: initialSigners,
}: AgreementProps) {
  const { user } = useUserContext()
  const { documentId } = useBudget()

  const {
    sellers: signees,
    brokers: [broker],
  } = useUpToDateSigners({
    documentId,
    initialSigners,
    enabled: initialSigners.some((signee) => !signee.signedAt),
  })

  if (!documentId) {
    return null
  }

  const brokerPerspective = Boolean(user)

  return (
    <>
      <div data-theme="dark" className="bg-root">
        <div data-theme="light" className="sm:container">
          <AgreementHeader
            signees={signees}
            broker={broker}
            deadline={deadline}
            createdAt={createdAt}
            brokerPerspective={brokerPerspective}
          />
        </div>
      </div>
      <div className="sm:container">
        <div className="sm:rounded-b-md bg-root px-[--padding-x] not-last:border-b not-last:border-muted">
          <AgreementSignees
            signees={signees}
            broker={brokerPerspective ? broker : undefined}
          />
          {Boolean(createdAt) && (
            <AgreementFooter
              allSigned={
                !!signees?.every((signee) => !!signee.signedAt) &&
                !!broker?.signedAt
              }
              brokerPerspective={brokerPerspective}
            />
          )}
        </div>
      </div>
    </>
  )
}

export default Agreement
