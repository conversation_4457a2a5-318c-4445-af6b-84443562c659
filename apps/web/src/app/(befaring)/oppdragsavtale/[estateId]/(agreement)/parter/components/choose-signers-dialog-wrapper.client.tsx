'use client'

import React from 'react'

import { ContactRole } from '@/actions/next/types'
import { NextPrivateContactWithProxy } from '@/actions/next/types-next'

import { ChooseSignersDialog } from './choose-signers-dialog'

export function ChooseSignersDialogClientWrapper({
  open,
  company,
  contacts = [],
  contactRoles = [],
  signers: signRightsHolders = [],
  estateId,
  handleOnClose,
  children,
}: {
  open: boolean
  handleOnClose?: (open: boolean, hasChanges: boolean) => void
  company: Pick<
    NextPrivateContactWithProxy,
    'contactId' | 'proxy' | 'companyName' | 'organisationNumber'
  >
  contacts?: NextPrivateContactWithProxy[]
  contactRoles?: ContactRole[] | null
  signers?: string[]
  estateId: string
  children?: React.ReactNode
}) {
  const mainContact = contactRoles
    ? contacts.find(
        (contact) => contact.contactId === contactRoles[0]?.childContactId,
      )
    : undefined

  const signers = company.proxy
    ? [company.proxy]
    : contacts.filter((contact) =>
        signRightsHolders.includes(contact.contactId),
      )

  return (
    <ChooseSignersDialog
      estateId={estateId}
      open={open}
      onOpenChange={handleOnClose}
      company={company}
      signers={signers}
      contacts={contacts.map((contact) => ({
        ...contact,
        main: contact.contactId === mainContact?.contactId,
        roleName: contactRoles?.find(
          (role) => role.childContactId === contact.contactId,
        )?.contactRoleName,
      }))}
    >
      {children}
    </ChooseSignersDialog>
  )
}
