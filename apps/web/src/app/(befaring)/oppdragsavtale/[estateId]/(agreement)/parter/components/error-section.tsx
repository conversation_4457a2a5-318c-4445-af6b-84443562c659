'use client'

import { AnimatePresence, motion } from 'framer-motion'
import { AlertCircleIcon } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Alert, AlertDescription, AlertTitle } from '@nordvik/ui/alert'

import { useBudget } from '../../../../../context/budget-context'

export function ErrorSection() {
  const { errorFields } = useBudget()

  const handleScrollToError = (path?: string) => {
    if (!path) {
      return
    }
    const element = document.querySelector(`[data-fields*="${path}"]`)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }

  return (
    <AnimatePresence>
      {errorFields && errorFields.length > 0 && (
        <motion.div
          initial={{
            height: 0,
            opacity: 0,
          }}
          animate={{
            height: 'auto',
            opacity: 1,
          }}
          exit={{
            height: 0,
            opacity: 0,
          }}
          className="flex flex-col gap-3 overflow-hidden"
        >
          <div className="bg-danger-subtle w-full" id="listing-agreement-error">
            <div className={cn('md:container')}>
              <Alert Icon={AlertCircleIcon} variant="destructive">
                <AlertTitle>Mangler informasjon</AlertTitle>
                <AlertDescription>
                  <ul className="list-image-dot list-inside col-start-2">
                    {errorFields.map((field) => (
                      <li
                        onClick={() => handleScrollToError(field.path)}
                        key={field.message}
                        className="typo-body-md cursor-pointer hover:ink-subtle"
                      >
                        {field.message}
                      </li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
