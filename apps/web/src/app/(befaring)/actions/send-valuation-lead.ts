'use server'

import { getLatestValuation } from '@/actions/next/estate-documents'
import { riskCompletionCheck } from '@/actions/risk-check-completed'
import { sendLeads } from '@/app/api/signicat/util/send-leads'

import { cachedListingAgreement } from '../lib/cached-listing-agreement'

export async function sendValuationLead(estateId: string) {
  const [riskCheckmark, listingAgreement, valuation] = await Promise.all([
    riskCompletionCheck(estateId).then(({ isComplete }) => isComplete),
    cachedListingAgreement(estateId),
    getLatestValuation(estateId),
  ])

  if (
    !riskCheckmark ||
    !listingAgreement?.receive_loan_offer ||
    !listingAgreement.signing_finished_at ||
    !listingAgreement.signicat_document_id ||
    !valuation
  ) {
    return null
  }

  await sendLeads(
    { id: listingAgreement.signicat_document_id, externalId: estateId },
    true,
  )
}
