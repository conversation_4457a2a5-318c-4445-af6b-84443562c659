'use server'

import {
  formatListingAgreement,
  mergeBudgetPosts,
} from '@befaring/lib/budget-helpers'
import { cachedListingAgreement } from '@befaring/lib/cached-listing-agreement'
import { calculateCommission } from '@befaring/lib/calculate-commission'
import { Decimal } from '@prisma/client/runtime/library'
import { waitUntil } from '@vercel/functions'
import { addDays } from 'date-fns/addDays'
import { revalidatePath } from 'next/cache'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

import { Prisma } from '@nordvik/database'
import { Document, DocumentStatus } from '@nordvik/signicat-express-sdk/types'

import { logListingAgreementInteraction } from '@/actions/log-listing-agreement-interaction'
import { contactUpdate } from '@/actions/next/contact-update'
import { getEstateBudget } from '@/actions/next/get-estate-budget'
import { postActivity } from '@/actions/next/post-activity'
import { SIGNICAT_BROKER_TITLE } from '@/actions/signing/get-broker-signer'
import { syncEstate } from '@/actions/sync/sync-estate'
import { GQLListingAgreementInteractionType } from '@/api/generated-client'
import { fetchVitecHubApi } from '@/api/vitec-hub-api'
import { sendInitiatedSigningMailToBroker } from '@/app/api/signicat/util/send-initiated-signing-mail-to-broker'
import { sendSlackMessage } from '@/app/api/vercel-webhook/send-slack-message'
import prisma from '@/db/prisma'
import { MINUTE } from '@/db/util'
import signicat from '@/external-services/signicat/client'
import { decodeBase64 } from '@/lib/base64'
import { CACHE_KEYS } from '@/lib/cache-keys'
import { getBaseUrl } from '@/lib/getBaseUrl'
import retry from '@/lib/retry'
import { getCurrentUser } from '@/lib/session'
import { craftCMS } from '@/server/connector/cmsConnector'
import { BudgetPostDescription } from '@/server/model/JobsListing/types'
import { SIGNICAT_DAYS_FOR_SIGNING } from '@/utils/constants'
import { withCache } from '@/utils/with-cache'

import { cachedEstate } from '../lib/cached-estate'
import { LoanOfferComment } from '../verdivurdering/[estateId]/din-bolig/page'

import { ListingAgreementResponse } from './types'

const INITIAL_VALUATION_PRICE = 3900
const VALUATION_BUDGET_POST_NAME = 'Verdivurdering'

export async function upsertListingAgreement({
  estateId,
  priceSuggestion,
  discardBudgetposts = false,
}: {
  estateId: string
  priceSuggestion?: number
  discardBudgetposts?: boolean
}): Promise<ListingAgreementResponse> {
  try {
    var [budget, estate, existingListingAgreement] = await Promise.all([
      retry(() => getEstateBudget(estateId), 2),
      cachedEstate(estateId),
      cachedListingAgreement(estateId),
    ])
  } catch (error) {
    console.error(error)
    throw new Error('Failed to fetch budget')
  }

  if (!existingListingAgreement) {
    syncEstate(estateId)
  }

  // If type is not percentage based (type=1), use priceSuggestion
  const suggested_price =
    budget.type === 1 ? budget.baseCommission : priceSuggestion

  const update = {
    suggested_price:
      !existingListingAgreement?.suggested_price ||
      Number(existingListingAgreement?.suggested_price) === 0
        ? suggested_price
        : undefined,
    initial_commission:
      Number(existingListingAgreement?.initial_commission) === 0
        ? budget.estimatedCommission
        : undefined,
    initial_fee_percentage:
      existingListingAgreement?.initial_fee_percentage === 0
        ? budget.feePercent
        : undefined,
    fee_percentage:
      existingListingAgreement?.fee_percentage === 0
        ? budget.feePercent
        : undefined,
    commission:
      Number(existingListingAgreement?.commission) === 0
        ? budget.estimatedCommission
        : undefined,
    is_valuation: estate?.isValuation,
  }

  const agreementData = await prisma.listing_agreements.upsert({
    where: { estate_id: estateId },
    update,
    create: {
      estate_id: estateId,
      suggested_price: suggested_price,
      commission: budget.estimatedCommission,
      initial_commission: budget.estimatedCommission,
      fee_percentage: budget.feePercent,
      initial_fee_percentage: budget.feePercent,
      is_valuation: estate?.isValuation,
    },
  })

  const listingAgreement = formatListingAgreement(agreementData)

  if (!existingListingAgreement) {
    void logListingAgreementInteraction({
      listing_agreements_id: listingAgreement.id,
      event_type: 'created',
    })
  }

  if (estate?.isValuation) {
    const hasValuationPost = budget.otherIncomeBudgetPosts.some((post) =>
      post.name
        .toLocaleLowerCase()
        .includes(VALUATION_BUDGET_POST_NAME.toLocaleLowerCase()),
    )

    if (!hasValuationPost) {
      const valuationPost = {
        budgetpostId: -1,
        name: VALUATION_BUDGET_POST_NAME,
        amountWithTaxIncluded: 0,
        amountWithTaxExcluded: 0,
        userId: 'system',
        status: 0,
        lastChanged: new Date().toISOString(),
        changedBy: 'system',
      }

      budget.otherIncomeBudgetPosts.push(valuationPost)
    }
  }

  const allBudgetPosts = [
    ...budget.otherIncomeBudgetPosts,
    ...budget.outlayBudgetPosts,
    ...budget.otherExpensesBudgetPosts,
  ]

  const suggestedPrice = listingAgreement.suggested_price
  const feePercentage = listingAgreement.fee_percentage
  const commissionIsPercentageBased = budget.type === 1

  const commission = calculateCommission({
    commissionIsPercentageBased,
    feePercentage,
    suggestedPrice,
    fixedCommission: listingAgreement.commission,
  })

  const initialCommission = commissionIsPercentageBased
    ? ((suggestedPrice ?? 0) * (listingAgreement.initial_fee_percentage ?? 0)) /
      100
    : (listingAgreement.initial_commission ?? 0)

  if (discardBudgetposts) {
    return {
      budget,
      isNewListingAgreement: !existingListingAgreement,
      listingAgreement,
      mergedOutlayPosts: [],
      mergedIncomePosts: [],
      mergedDiscountPost: undefined,
      derived: {
        suggestedPrice,
        feePercentage,
        commission,
        initialCommission,
        initialFeePercentage: listingAgreement.initial_fee_percentage,
        commissionIsPercentageBased,
      },
    }
  }

  let budgetPostDescriptions: BudgetPostDescription[] = []

  try {
    const response = await withCache(
      CACHE_KEYS.BUDGET_POST.BUDGET_POST_DESCRIPTIONS,
      () => craftCMS.getBudgetPostsDescriptions(),
      MINUTE,
    )
    budgetPostDescriptions = response?.budgetPosts ?? []
  } catch (error) {
    console.error(error)
  }

  const localBudgetPosts = await retry(() =>
    Promise.all(
      allBudgetPosts.map(({ budgetpostId, amountWithTaxIncluded, name }) => {
        const description = budgetPostDescriptions.find((entry) =>
          name?.toLowerCase().includes(entry.heading.toLowerCase()),
        )?.description

        const isValuation = name
          .toLocaleLowerCase()
          .includes(VALUATION_BUDGET_POST_NAME.toLowerCase())

        return prisma.budget_posts.upsert({
          where: {
            next_budget_post_id: budgetpostId,
          },
          create: {
            listing_agreement_id: listingAgreement.id,
            next_budget_post_id: budgetpostId,
            initial_price: isValuation
              ? INITIAL_VALUATION_PRICE
              : amountWithTaxIncluded,
            description,
          },
          update: {},
        })
      }),
    ),
  )

  const mergedOutlayPosts = mergeBudgetPosts(
    localBudgetPosts,
    budget.outlayBudgetPosts,
  )

  const mergedDiscountPost = mergeBudgetPosts(localBudgetPosts, [
    ...budget.otherExpensesBudgetPosts,
    ...budget.otherIncomeBudgetPosts,
  ]).find((post) =>
    post.name.toLowerCase().includes('rabatt vederlag/provisjon'),
  )

  const mergedIncomePosts = mergeBudgetPosts(
    localBudgetPosts,
    budget.otherIncomeBudgetPosts.filter(
      (post) => post.budgetpostId !== mergedDiscountPost?.budgetpostId,
    ),
  )

  return {
    budget,
    isNewListingAgreement: !existingListingAgreement,
    listingAgreement,
    mergedOutlayPosts,
    mergedIncomePosts,
    mergedDiscountPost,
    derived: {
      suggestedPrice,
      feePercentage,
      commission,
      initialCommission,
      initialFeePercentage: listingAgreement.initial_fee_percentage,
      commissionIsPercentageBased,
    },
  }
}

export async function addDocumentInfoToListingAgreement({
  estateId,
  document,
}: {
  estateId: string
  document: Document
}) {
  try {
    const [existingDocument, signers] = await Promise.all([
      prisma.listing_agreements.findUnique({
        where: { estate_id: estateId },
        select: { signicat_document_id: true },
      }),
      signicat.listSigners(document.documentId),
    ])

    if (
      existingDocument?.signicat_document_id &&
      existingDocument.signicat_document_id !== document.documentId
    ) {
      console.error(
        `addDocumentInfoToListingAgreement: Document already exists for estate ${estateId}. Current document id: ${existingDocument.signicat_document_id}`,
      )

      sendSlackMessage(
        [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Error addDocumentInfoToListingAgreement: Document already exists for estate ${estateId}. Current document id: ${existingDocument.signicat_document_id}`,
            },
          },
        ],
        'NORDVIK_ERRORS',
      )
    }

    await prisma.listing_agreements.update({
      where: { estate_id: estateId },
      data: {
        signicat_document_id: document.documentId,
        updated_at: new Date(),
        deadline_for_signing: addDays(new Date(), SIGNICAT_DAYS_FOR_SIGNING),
        signers: {
          upsert: signers.map((signer) => ({
            where: {
              external_signer_id: signer.externalSignerId,
              id: signer.id,
            },
            create: {
              external_signer_id: signer.externalSignerId,
              url: signer.url,
              id: signer.id,
              email: signer.signerInfo.email!,
              title: signer.signerInfo.title,
              phone: signer.signerInfo.mobile?.number,
              first_name: signer.signerInfo.firstName,
              last_name: signer.signerInfo.lastName,
            },
            update: {
              url: signer.url,
              email: signer.signerInfo.email!,
              title: signer.signerInfo.title,
              phone: signer.signerInfo.mobile?.number,
              first_name: signer.signerInfo.firstName,
              last_name: signer.signerInfo.lastName,
            },
          })),
        },
      },
    })

    revalidatePath('/oppdragsavtale')
  } catch (error) {
    console.error(error)
    await sendSlackMessage(
      [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `Error adding document id to listing agreement. Estate: ${estateId}, document id: ${document.documentId}`,
          },
        },
        { type: 'divider' },
        {
          type: 'section',
          text: { type: 'mrkdwn', text: JSON.stringify(error) },
        },
      ],
      'NORDVIK_ERRORS',
    )
    throw new Error('Failed to add document id to listing agreement')
  }
}

export async function updateSuggestedPrice(
  estateId: string,
  suggestedPrice: number,
) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('User not found in updateSuggestedPrice')
  }
  try {
    await prisma.listing_agreements.update({
      where: { estate_id: estateId },
      data: {
        suggested_price: suggestedPrice,
        updated_at: new Date(),
      },
    })
    // VITEC API is not ready for this kind of very high-level data manipulation
    // await fetchVitecHubApi(`Estates/${estateId}/Data?automatic=26`, {
    //   method: "POST",
    //   body: JSON.stringify({
    //     doubleFields: [{ type: 6, value: suggestedPrice }]
    //   })
    // });
  } catch (error) {
    console.error(error)
    throw new Error('Failed to update suggested price')
  }
  revalidatePath('/oppdragsavtale', 'layout')
}

async function updateListingAgreementHistory(
  where: Prisma.listing_agreementsWhereUniqueInput,
  changes: Record<string, unknown>,
) {
  const existingRecord = await prisma.listing_agreements.findUnique({
    where,
  })

  if (!existingRecord) {
    throw new Error('Listing agreement not found')
  }

  delete changes.updated_at

  const changesDiff: Record<string, { from: unknown; to: unknown }> = {}
  for (const key in changes) {
    if (Object.prototype.hasOwnProperty.call(changes, key)) {
      const oldValue = existingRecord[key]
      const newValue = changes[key]

      // for proper comparison (e.g., for Decimal fields), convert them to strings
      const oldComparable = oldValue?.toString()
      const newComparable = newValue?.toString()

      if (oldComparable !== newComparable) {
        changesDiff[key] = { from: oldValue, to: newValue }
      }
    }
  }

  return addListingAgreementHistory(existingRecord.estate_id, changesDiff)
}

export async function addListingAgreementHistory(
  estate_id: string,
  changesDiff: Record<string, { from: unknown; to: unknown }>,
) {
  if (Object.keys(changesDiff).length === 0) {
    return
  }

  const user = await getCurrentUser()
  const contact_id = (await cookies()).get('cid')?.value

  return prisma.listing_agreement_history.create({
    data: {
      estate_id,
      changes: changesDiff as Prisma.InputJsonValue,
      logged_in: !!user,
      employeeId: user?.employeeId,
      contact_id,
    },
  })
}

export async function updateListingAgreement({
  estateId,
  commission,
  feePercentage,
  sellerInsurance,
  marketingPackage,
  separateProvision,
}: {
  estateId: string
  commission?: number | null
  feePercentage?: number | null
  sellerInsurance?: boolean
  marketingPackage?: string | null
  separateProvision?: string
}) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('User not found in updateListingAgreement')
  }

  const updateData: Prisma.listing_agreementsUpdateInput = {
    commission,
    seller_insurance: sellerInsurance,
    marketing_package: marketingPackage,
    fee_percentage: feePercentage ? new Decimal(feePercentage) : undefined,
    separate_provision: separateProvision,
    updated_at: new Date(),
  }

  // Remove keys with undefined values so we don't overwrite fields unintentionally
  Object.keys(updateData).forEach(
    (key) => updateData[key] === undefined && delete updateData[key],
  )

  try {
    await prisma.listing_agreements.update({
      where: { estate_id: estateId },
      data: updateData,
    })

    waitUntil(
      updateListingAgreementHistory({ estate_id: estateId }, updateData).catch(
        console.warn,
      ),
    )

    revalidatePath('/oppdragsavtale')
  } catch (error) {
    console.error(error)
    throw new Error('Failed to update listing agreement')
  }
}

export interface CustomerInfo {
  marriedOrInPartnership?: boolean
  isCommonEstate?: boolean | null
  ownerIsSeller?: boolean
  waiveWithdrawalRight?: boolean
  noPreviousBrokers?: boolean
  previousBrokers?: string
  receiveLoanOffer?: boolean
  recipientLoanOffer?: string
  loanOfferComment?:
    | Record<string, string | boolean | null>
    | Prisma.InputJsonObject
  sellerIsShareholder?: boolean
  sellerInsurance?: boolean
  marketingPackage?: string | null
}

export async function updateListingAgreementCustomerInfo({
  listingAgreementId,
  marriedOrInPartnership,
  isCommonEstate,
  ownerIsSeller,
  waiveWithdrawalRight,
  noPreviousBrokers,
  previousBrokers,
  receiveLoanOffer,
  recipientLoanOffer,
  loanOfferComment,
  sellerIsShareholder,
  sellerInsurance,
  marketingPackage,
}: {
  listingAgreementId: string
} & CustomerInfo) {
  const updateData: Prisma.listing_agreementsUpdateInput = {
    married_or_in_partnership: marriedOrInPartnership,
    is_common_estate: isCommonEstate,
    owner_is_seller: ownerIsSeller,
    waive_withdrawal_right: waiveWithdrawalRight,
    no_previous_brokers: noPreviousBrokers,
    previous_brokers: previousBrokers,
    receive_loan_offer: receiveLoanOffer,
    recipient_loan_offer: recipientLoanOffer,
    seller_is_shareholder: sellerIsShareholder,
    seller_insurance: sellerInsurance,
    marketing_package: marketingPackage,
    loan_offer_comment: loanOfferComment,
    updated_at: new Date(),
  }

  // Remove keys with undefined values so we don't overwrite fields unintentionally
  Object.keys(updateData).forEach(
    (key) => updateData[key] === undefined && delete updateData[key],
  )

  try {
    await prisma.$transaction(async () => {
      await updateListingAgreementHistory(
        { id: listingAgreementId },
        updateData,
      ).catch(console.warn)

      await prisma.listing_agreements.update({
        where: {
          id: listingAgreementId,
        },
        data: updateData,
      })
    })
    revalidatePath('/oppdragsavtale', 'layout')
    revalidatePath('/verdivurdering', 'layout')
  } catch (error) {
    console.error(error)
    throw new Error('Kunne ikke oppdatere kundeinformasjon')
  }
}

export async function createContact({
  data,
  estateId,
}: {
  data: {
    contactId?: string
    firstName?: string
    lastName?: string
    mobilePhone?: string
    email?: string
    address?: string
    postalCode?: string
    city?: string
    socialSecurity?: string
  }
  estateId: string
}) {
  try {
    const newContactId = await contactUpdate({ ...data, contactType: 0 })

    await fetchVitecHubApi(
      `Estates/${estateId}/Contacts/${newContactId}/ContactRelation`,
      {
        body: JSON.stringify({
          contactRelationName: 'Selger',
          source: 'nordvik-megler-nextjs',
        }),
        method: 'POST',
      },
    )
    revalidatePath('/oppdragsavtale')
  } catch (error) {
    console.error(error)
    throw new Error('Failed to update contact')
  }
}

export async function unlockListingAgreement({
  id,
  signicat_document_id,
}: {
  id: string | null
  signicat_document_id: string | null
}) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('User not found in unlockListingAgreement')
  }

  if (!id) {
    throw new Error('Missing listing agreement id')
  }

  try {
    if (signicat_document_id) {
      try {
        await retry(() =>
          signicat.cancelDocument(
            signicat_document_id,
            'Megler har avbrutt signeringen av dokumentet for å gjøre endringer i oppdragsavtalen.',
          ),
        )
      } catch (error) {
        console.error('Failed to cancel document', error)
        // throw new Error('Failed to unlock listing agreement')
      }
    }

    try {
      await retry(async () => {
        const updated = await prisma.listing_agreements.update({
          where: { id },
          data: {
            signicat_document_id: null,
            updated_at: new Date(),
            initiated_signing_at: null,
            deadline_for_signing: null,
            signers: {
              deleteMany: {
                listing_agreements_id: id,
              },
            },
          },
          select: {
            estate_id: true,
            signers: {
              select: { title: true, external_signer_id: true },
            },
          },
        })

        revalidatePath(`/oppdrag/detaljer/${updated.estate_id}`)
        revalidatePath(
          `/oppdragsavtale/${updated.estate_id}/(header)`,
          'layout',
        )

        await prisma.offer_access_tokens.updateMany({
          where: { listing_agreement_id: id },
          data: { valid: false },
        })

        const date = new Date()

        let employeeId = user.employeeId

        if (!employeeId) {
          // Find the broker who initiated the signing
          const found = updated.signers.find(
            (signer) => signer.title === SIGNICAT_BROKER_TITLE,
          )?.external_signer_id

          if (found) {
            employeeId = decodeBase64(found)
          }
        }

        try {
          await postActivity({
            estateId: updated.estate_id,
            title: 'Oppdragsavtale er avbrutt',
            note: 'Oppdragsavtalen er avbrutt og signeringen er avsluttet.',
            start: date.toISOString(),
            end: date.toISOString(),
            doneDate: date.toISOString(),
            typeName: 'Oppdragsavtale',
            url: `${getBaseUrl()}/oppdrag/detaljer/${updated.estate_id}`,
            employeeId,
          })

          void logListingAgreementInteraction({
            listing_agreements_id: id,
            event_type: 'withdrawn',
            employee_id: user.employeeId,
          })
        } catch (error) {
          console.error('Failed to post activity', error)
        }
      })
    } catch (error) {
      console.error('Error while updating db', error)
      throw new Error('Failed to unlock listing agreement')
    }
  } catch (error) {
    console.error(error)
    throw new Error('Failed to unlock listing agreement')
  }
  revalidatePath('/oppdragsavtale')
}

export async function getCurrentSellerSignLink({
  documentId,
  contactId,
}: {
  documentId: string
  contactId?: string
}) {
  const signers = await signicat.listSigners(documentId)
  const signer = signers.find((entry) => entry.externalSignerId === contactId)

  return signer?.url
}

export async function deleteListingAgreement(estateId: string) {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('User not found')
  }
  try {
    await prisma.$transaction(async (tx) => {
      const listingAgreement = await tx.listing_agreements.findMany({
        where: { estate_id: estateId },
      })

      for (const agreement of listingAgreement) {
        if (!listingAgreement) {
          throw new Error('Listing agreement not found')
        }

        return Promise.all([
          tx.budget_posts.deleteMany({
            where: { listing_agreement_id: agreement.id },
          }),
          tx.offer_access_tokens.updateMany({
            where: { listing_agreement_id: agreement.id },
            data: { valid: false, deleted_at: new Date() },
          }),
          tx.signicat_signer.deleteMany({
            where: { listing_agreements_id: agreement.id },
          }),
          tx.listing_agreements.delete({
            where: { estate_id: estateId },
          }),
        ])
      }
    })
  } catch (error) {
    console.error(error)
    throw new Error('Failed to delete listing agreement')
  }
  revalidatePath('/oppdragsavtale')
  redirect('/oppdrag')
}

export async function initiateSigning(
  listingAgreementId: string,
  initiatedBySignerId?: string,
) {
  const response = await prisma.listing_agreements.findUnique({
    where: { id: listingAgreementId },
    select: {
      initiated_signing_at: true,
      signicat_document_id: true,
      estate_id: true,
      id: true,
    },
  })

  if (response?.initiated_signing_at) {
    if (response.signicat_document_id) {
      return null
    }

    const summaries = await signicat.listDocumentSummaries({
      externalId: response.estate_id,
      limit: 1,
    })

    const existingDocument = summaries.data.at(0)

    if (
      existingDocument &&
      existingDocument.status.documentStatus === DocumentStatus.Unsigned
    ) {
      await prisma.listing_agreements.update({
        where: { id: listingAgreementId },
        data: {
          signicat_document_id: existingDocument.documentId,
        },
      })
    }

    // check if initiated_signing_at is less than half an hour ago
    const now = new Date()
    const initiated = new Date(response.initiated_signing_at)
    if (now.getTime() - initiated.getTime() < 30 * 60 * 1000) {
      return null
    }
  }

  // Update Initiate Signing
  const result = await prisma.listing_agreements.update({
    where: { id: listingAgreementId },
    data: {
      initiated_signing_at: new Date(),
      updated_at: new Date(),
    },
    select: {
      id: true,
      signicat_document_id: true,
      recipient_loan_offer: true,
    },
  })

  if (result.recipient_loan_offer === null) {
    await logLeadRequest({
      listingAgreementId,
      initiatedBySignerId,
    })
  }

  if (response?.estate_id && response.id && initiatedBySignerId) {
    try {
      await sendInitiatedSigningMailToBroker(
        response.estate_id,
        initiatedBySignerId,
      )
    } catch (error) {
      console.error('Failed to send initiated signing mail to broker', error)
    }
  }

  return JSON.stringify(result)
}

async function logLeadRequest({
  listingAgreementId,
  initiatedBySignerId,
}: {
  listingAgreementId: string
  initiatedBySignerId?: string
}) {
  const user = await getCurrentUser()

  const oa = await prisma.listing_agreements.findUnique({
    where: { id: listingAgreementId },
    select: { loan_offer_comment: true, receive_loan_offer: true },
  })

  if (!oa?.receive_loan_offer) {
    return
  }

  const comment = oa?.loan_offer_comment as LoanOfferComment | null

  await logListingAgreementInteraction({
    event_type: GQLListingAgreementInteractionType.FinancingRequested,
    listing_agreements_id: listingAgreementId,
    seller_id: initiatedBySignerId,
    employee_id: initiatedBySignerId ? undefined : user?.employeeId,
    extra_data: {
      source: comment?.source,
      hasCoBuyer: comment?.has_co_buyer,
      buyProcessStatus: comment?.buy_process_status,
    },
  })
}

export async function retractInitiatedSigning(listingAgreementId: string) {
  return prisma.listing_agreements.update({
    where: { id: listingAgreementId },
    data: { initiated_signing_at: null, updated_at: new Date() },
  })
}

export async function getListingAgreementDocumentId(
  listing_agreement_id: string,
) {
  const result = await prisma.listing_agreements.findUnique({
    where: { id: listing_agreement_id },
    select: {
      signicat_document_id: true,
    },
  })

  return result?.signicat_document_id
}
