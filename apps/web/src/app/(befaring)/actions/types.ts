import {
  MergedBudgetPost,
  NextAssignmentBudget,
} from '@/actions/next/types-next'

import { ListingAgreement } from '../lib/budget-helpers'

export interface ListingAgreementResponse {
  isNewListingAgreement: boolean
  budget: NextAssignmentBudget
  listingAgreement: ListingAgreement
  mergedOutlayPosts: MergedBudgetPost[]
  mergedIncomePosts: MergedBudgetPost[]
  mergedDiscountPost?: MergedBudgetPost
  derived: {
    suggestedPrice: number | null
    feePercentage: number | null
    commission: number
    initialCommission: number
    initialFeePercentage: number | null
    commissionIsPercentageBased: boolean
  }
  separateProvision?: string
}
