'use server'

import { waitUntil } from '@vercel/functions'
import { revalidatePath } from 'next/cache'

import { fetchVitecHubApi } from '@/api/vitec-hub-api'
import prisma from '@/db/prisma'
import { getCurrentUser } from '@/lib/session'

import { addListingAgreementHistory } from './listing-agreement'

export async function createBudgetPost({
  title,
  estateId,
  price,
  productTag,
}: {
  title?: string
  description?: string
  estateId: string
  price: number
  productTag: string
}) {
  try {
    const data = await fetchVitecHubApi<{
      budgetPostId: string
      status: number
    }>(`Estates/${estateId}/BudgetPost`, {
      body: JSON.stringify({ grossAmount: price, title, productTag }),
      method: 'POST',
    })

    waitUntil(
      addListingAgreementHistory(estateId, {
        budget_post: {
          from: null,
          to: {
            name: title,
            budgetPostId: data.budgetPostId,
            price: price,
            productTag,
          },
        },
      }).catch(console.warn),
    )

    revalidatePath('/oppdragsavtale')
    return data
  } catch (error) {
    console.error(error)
    throw new Error('Failed to add budget post')
  }
}

export async function updateBudgetPost(
  {
    title,
    description,
    estateId,
    budgetPostId,
    price,
  }: {
    estateId: string
    budgetPostId: number
    price?: number
    title?: string
    description?: string
  },
  current: {
    name: string
    amountWithTaxIncluded: number
    description?: string | null
  },
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      throw new Error('User not found')
    }

    let data

    const isValuation =
      budgetPostId < 0 &&
      current.name.toLocaleLowerCase().includes('verdivurdering')

    if (isValuation) {
      data = createBudgetPost({
        title,
        estateId,
        price: price === 0 ? 0.001 : price!,
        productTag: 'ETAKST',
      })
    } else {
      data = await fetchVitecHubApi<{
        budgetPostId: string
        status: number
      }>(`Estates/${estateId}/BudgetPost`, {
        body: JSON.stringify({
          budgetPostId,
          grossAmount: price === 0 ? 0.001 : price,
          description: title, // title is description in the Vitec API
        }),
        method: 'PUT',
      })

      await prisma.budget_posts.update({
        where: { next_budget_post_id: budgetPostId },
        data: {
          updated_at: new Date(),
          description,
        },
      })
    }

    if (current) {
      waitUntil(
        logChanges({
          estateId,
          newData: {
            name: title,
            price: price,
            description,
          },
          oldData: {
            name: current.name,
            price: current.amountWithTaxIncluded,
            description: current.description,
          },
        }),
      )
    }

    revalidatePath(`/oppdragsavtale/${estateId}`)
    return data
  } catch (error) {
    console.error(error)
    throw new Error('Failed to update budget post')
  }
}

type BudgetPostState = {
  name?: string
  price?: number
  description?: string | null
}

async function logChanges({
  newData,
  oldData,
  estateId,
}: {
  estateId: string
  newData: BudgetPostState
  oldData: BudgetPostState
}) {
  type FieldChanges = Partial<BudgetPostState>
  const changes: Record<string, { from: FieldChanges; to: FieldChanges }> = {
    budget_post: {
      from: {},
      to: {},
    },
  }

  if (newData.name !== oldData.name) {
    changes.budget_post.from.name = oldData.name
    changes.budget_post.to.name = newData.name
  }

  if (newData.price !== oldData.price) {
    changes.budget_post.from.price = oldData.price
    changes.budget_post.to.price = newData.price
  }

  if (newData.description !== oldData.description) {
    changes.budget_post.from.description = oldData.description
    changes.budget_post.to.description = newData.description
  }

  const hasChanges = Object.keys(changes.budget_post.from).length > 0

  if (hasChanges) {
    try {
      await addListingAgreementHistory(estateId, changes)
    } catch (error) {
      console.warn('Failed to log changes:', error)
    }
  }
}
