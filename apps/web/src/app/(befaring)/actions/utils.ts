import { RelationsContact } from '@/actions/next/estate-contact-relations'
import Estate from '@/server/model/BrokerEstate/model'
import BrokerEstateSeller from '@/server/model/BrokerEstateSeller/model'

import {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '../../../actions/next/types-next'

type SellerAddress = {
  postalAddress?: string
  postalCode?: string
  city?: string
}

function getAddressWithFallback(
  contact: NextPrivateContact,
  estateAddress?: Partial<Estate['address']> | null,
): SellerAddress {
  return {
    postalAddress: contact.postalAddress || estateAddress?.streetAddress || '',
    postalCode: contact.postalCode || estateAddress?.zipCode || '',
    city: contact.city || estateAddress?.city || '',
  }
}

export function formatSecuredSocialSecurity(
  socialSecurity: string | null | undefined,
): string | null {
  if (!socialSecurity) return null
  const cleaned = socialSecurity.replace(/\s/g, '')
  return `${cleaned.substring(0, 6)} ******`
}

function formatSocialSecurity(
  socialSecurity: string | null | undefined,
): string | null {
  if (!socialSecurity) return null
  const cleaned = socialSecurity.replace(/\s/g, '')
  return cleaned
}
export function formatSigner({
  estateAddress,
  signerContact,
  proxyContact,
  spouseContacts,
}: {
  estateAddress?: Partial<Estate['address']>
  signerContact: NextPrivateContact
  estateSellers?: Partial<BrokerEstateSeller>[]
  proxyContact?: NextPrivateContact
  spouseContacts?: RelationsContact[]
}): NextPrivateContactWithProxy | null {
  if (!signerContact) {
    return null
  }

  const signerAddress = getAddressWithFallback(signerContact, estateAddress)
  const isSpouse = spouseContacts?.find(
    (c) => c.contactId === signerContact.contactId,
  )
  const sellerDataWithProxy: NextPrivateContactWithProxy = {
    ...signerContact,
    ...signerAddress,
    relationType: isSpouse ? 25 : 1,
    socialSecurity:
      formatSocialSecurity(signerContact.socialSecurity) ?? undefined,
  }

  if (proxyContact) {
    const proxyAddress = getAddressWithFallback(proxyContact, estateAddress)

    sellerDataWithProxy.proxy = {
      ...proxyContact,
      ...proxyAddress,
      relationType: 9,
      socialSecurity:
        formatSocialSecurity(proxyContact.socialSecurity) ?? undefined,
    }
  }

  return sellerDataWithProxy
}
