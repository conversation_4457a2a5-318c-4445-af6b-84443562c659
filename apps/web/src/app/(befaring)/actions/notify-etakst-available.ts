import { listing_agreements } from '@nordvik/database'

import { getShortUrl } from '@/actions/get-short-url'
import { sendMail } from '@/actions/mail/sendMail'
import { nextSendSMS } from '@/actions/next/sendSMS'
import { GQLListingAgreementInteractionType } from '@/api/generated-client'
import { getMessageTemplateOverride } from '@/app/(protected)/(sidebar)/oppdrag/detaljer/[slug]/components/edit-automated-email/message-template-override-handlers'
import { TemplateIds } from '@/components/template-renderer/types'
import prisma from '@/db/prisma'
import { renderEtakstAvailableEmail } from '@/lib/email-template/etakst-available'
import { isProd } from '@/lib/getBaseUrl'
import { getEstateById } from '@/server/model/BrokerEstate/factory'

import { getInspectionCustomerLink } from './get-offer-customer-link'

export async function notifyEtakstAvailable({
  estateId,
  documentId,
  channels,
}: {
  estateId: string
  documentId?: string
  channels: {
    email?: boolean
    sms?: boolean
  }
}): Promise<listing_agreements | undefined> {
  if (!channels || (!channels.email && !channels.sms)) {
    console.error('No channels provided for notification')
    return undefined
  }

  const audit = await prisma.etakst_audit
    .create({
      data: {
        estate_id: estateId,
        document_id: documentId,
        notification_type: Object.keys(channels)
          .filter((key) => channels[key])
          .join(', '),
        is_successful: false,
      },
    })
    .catch((e) => {
      console.error(`Error creating audit record:`, e)
    })

  try {
    const estate = await getEstateById({ id: estateId })
    const listingAgreement = await estate?.listingAgreement()
    const sellers = await listingAgreement?.sellerSigners()

    if (!estate || !sellers || sellers.length === 0) {
      console.error(
        `No sellers found for estate ${estateId}, skipping notification`,
      )
      return undefined
    }

    const authenticatedLink = await getInspectionCustomerLink({
      estateId,
      isValuation: true,
      segment: 'start',
    })

    const templateOverride = await getMessageTemplateOverride({
      templateId: TemplateIds.ETAKST_AVAILABLE_TEMPLATE,
      estateId,
      employeeId: estate.broker?.employeeId,
    })

    const channelPromises: Promise<unknown>[] = []

    if (channels.email) {
      console.info(
        `Sending email notification for estate ${estateId} (Document ID: ${documentId})`,
      )

      let bccSent = false

      const emailPromises = sellers.map(async (seller) => {
        const customerName = `${seller.firstName} ${seller.lastName}`.trim()
        const url = new URL(authenticatedLink)

        url.searchParams.set('cid', seller.externalSignerId)

        const address = estate.address?.streetAddress

        const subject =
          templateOverride?.subject ||
          (address ? `E-takst for ${address} er klar` : 'Din e-takst er klar')

        const emailBody = await renderEtakstAvailableEmail({
          withStorebrand: listingAgreement?.hasStorebrandLead,
          title: subject,
          customerName: seller.firstName,
          estateName: address || estate.heading,
          authenticatedLink: url.toString(),
          templateOverrideHtml: templateOverride.emailHtml,
        })

        // only send BCC once
        const bcc = bccSent ? undefined : estate.broker?.email
        bccSent = true

        return sendMail({
          title: '',
          subject,
          emails: [{ email: seller.email, name: customerName }],
          body: emailBody,
          blindCopyTo: bcc,
          from: {
            email: process.env.NO_REPLY_EMAIL ?? '<EMAIL>',
            name: estate.broker?.name || 'Nordvik',
          },
          initialReceiver: [{ email: seller.email, name: customerName }],
          includeSignatureForEmployeeId: estate.broker?.employeeId,
          context: {
            type: 'estate',
            id: estate.id,
          },
        })
      })

      channelPromises.push(...emailPromises)
    }

    const departmentId =
      (isProd && estate.department?.departmentId?.toString()) || '1'

    if (channels.sms) {
      for (const reciever of sellers) {
        let recipientNumber = reciever.phone

        if (!isProd) {
          const testPhone = process.env.TEST_PHONE?.split(';')[0]
          if (!testPhone) {
            throw new Error('Missing TEST_PHONE in env')
          }
          recipientNumber = testPhone
        }

        if (!recipientNumber) {
          console.error(
            `No phone number found for seller ${reciever.externalSignerId}, skipping sending sign link`,
          )
          continue
        }

        const firstName = reciever.firstName
        const url = new URL(authenticatedLink)

        const shortUrl = await getShortUrl(url.toString())

        url.searchParams.set('cid', reciever.externalSignerId)

        const brokerName = estate.broker?.name

        channelPromises.push(
          nextSendSMS({
            UseSenderFallback: true,
            RecipientNumber: recipientNumber!,
            text: `Hei, ${firstName}\n\nE-taksten din er nå klar. Se den her: ${shortUrl} \n\nVennlig hilsen\n${brokerName}\nNordvik`,
            DepartmentId: departmentId,
            ContactId: reciever.externalSignerId,
            FromEmployeeId: estate.broker?.employeeId,
            EstateId: estateId,
            Source: 'Nordvik',
          }),
        )
      }
    }

    await Promise.all(channelPromises)

    console.info(
      `E-takst is now available for estate ${estateId} (Document ID: ${documentId})`,
    )

    const [la] = await Promise.all([
      prisma.listing_agreements.update({
        where: {
          estate_id: estateId,
        },
        data: {
          listing_agreement_interactions: {
            create: {
              event_timestamp: new Date(),
              event_type: GQLListingAgreementInteractionType.EtakstSent,
              created_at: new Date(),
              seller_id: undefined,
            },
          },
        },
      }),
      audit &&
        prisma.etakst_audit
          .update({
            where: {
              id: audit.id,
            },
            data: {
              is_successful: true,
              response: {
                message: 'E-takst document available and notification sent',
              },
            },
          })
          .catch((e) => {
            console.error(`Error updating audit record:`, e)
          }),
    ])

    return la
  } catch (error) {
    console.error(
      `Error sending e-takst availability notification for estate ${estateId}:`,
      error,
    )

    if (audit) {
      try {
        await prisma.etakst_audit.update({
          where: { id: audit.id },
          data: {
            is_successful: false,
            data: {
              message: 'E-takst document available but notification failed',
            },
            response: {
              error: error instanceof Error ? error.message : String(error),
            },
          },
        })
      } catch (auditError) {
        console.error('Error creating audit record:', auditError)
      }

      return undefined
    }
  }
}
