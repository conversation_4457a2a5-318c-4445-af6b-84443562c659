'use server'

import { redirect } from 'next/navigation'

import signicat from '@/external-services/signicat/client'

export async function redirectSignee(documentId: string, contactId: string) {
  const signers = await signicat.listSigners(documentId)
  const loggedInSigner = signers.find(
    (signer) => signer.externalSignerId === contactId,
  )

  if (!loggedInSigner) {
    return
  }

  redirect(loggedInSigner.url)
}
