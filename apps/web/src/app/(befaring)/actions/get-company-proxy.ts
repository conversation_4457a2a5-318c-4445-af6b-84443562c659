'use server'

import { getContact } from '@/actions/next/contact-get'
import { NextPrivateContact } from '@/actions/next/types-next'
import prisma from '@/db/prisma'

export async function getCompanyProxy(
  seller: NextPrivateContact,
  listing_agreement_id: string,
) {
  if (seller.contactType === 0) {
    return undefined
  }

  const companySignRights = await prisma.company_sign_rights.findUnique({
    where: {
      company_contact_id_listing_agreement_id: {
        listing_agreement_id,
        company_contact_id: seller.contactId,
      },
    },
    select: {
      signers: true,
    },
  })

  if (companySignRights?.signers.length === 1) {
    try {
      return getContact(companySignRights.signers[0])
    } catch (e) {
      console.error('Failed to get contact', e)
    }
  }
}
