'use server'

import emojiRegex from 'emoji-regex'
import { after } from 'next/server'

import { getShortUrl } from '@/actions/get-short-url'
import { logListingAgreementInteraction } from '@/actions/log-listing-agreement-interaction'
import { sendMail } from '@/actions/mail/sendMail'
import { nextSendSMS } from '@/actions/next/sendSMS'
import { sendSlackMessage } from '@/app/api/vercel-webhook/send-slack-message'
import { TemplateContent } from '@/components/template-renderer/types'
import { formatDate } from '@/lib/dates'
import { getHtmlWithStyleReset } from '@/lib/email-template/get-html'
import { sendToClientContent } from '@/lib/email-template/send_to_client_content'
import { getLinkToOfferText } from '@/lib/get-link-to-offer-text'
import { isProd } from '@/lib/getBaseUrl'
import {
  AFTER_INSPECTION_TEMPLATE_LONG,
  AFTER_INSPECTION_TEMPLATE_SHORT,
} from '@/lib/templates/templates-listing-agreement'
import { getEstateById } from '@/server/model/BrokerEstate/factory'
import type Estate from '@/server/model/BrokerEstate/model'
import { isNotNull } from '@/server/utils'

import { NextPrivateContactWithProxy } from '../../../actions/next/types-next'

import { getInspectionCustomerLink } from './get-offer-customer-link'
import { markInspectionAsSent } from './inspection'
import { markInspectionAsDone } from './mark-inspection-as-done'
import { uploadEmailContentToVitec } from './upload-email-to-vitec'

export type EmailContent = TemplateContent & { html: string }
export type SmsContent = TemplateContent & { text: string }
type SendOfferContent = {
  email: EmailContent[]
  sms: SmsContent[]
}

export type Receiver = Pick<
  NextPrivateContactWithProxy,
  | 'contactId'
  | 'proxy'
  | 'email'
  | 'mobilePhone'
  | 'firstName'
  | 'lastName'
  | 'relationType'
>

export async function sendSignLinkToSellers({
  estateId,
  subject,
  content,
  extraData,
  includeListingAgreement,
  receiversWithSignRights,
  readOnlyReceivers,
  channels,
}: {
  estateId: string
  subject: string
  content: SendOfferContent
  extraData: {
    templateId: string
    modified: boolean
  }
  includeListingAgreement: boolean
  receiversWithSignRights: Receiver[]
  readOnlyReceivers: Receiver[]
  channels: {
    email: boolean
    sms: boolean
  }
}) {
  const estate = await getEstateById({ id: estateId })
  const listingAgreement = await estate?.listingAgreement()

  if (!estate) {
    return console.error(`Estate ${estateId} not found`)
  }

  const inspectionFolder = await estate.inspectionFolder()
  if (!inspectionFolder?.id) {
    throw new Error(`No inspection folder found for estate ${estateId}`)
  }
  const signLink = await getInspectionCustomerLink({
    estateId,
    inspectionId: inspectionFolder.id,
    isValuation: Boolean(estate.isValuation),
    segment:
      includeListingAgreement && !estate.isValuation ? undefined : 'start',
  })

  const promises: Promise<unknown>[] = []
  // send email with sign link to all sellers
  let allEmailAuditIds: string[] = []
  if (channels.email && content.email) {
    try {
      const { emailAuditIds } = await sendMails({
        link: signLink,
        emailContent: content.email,
        subject,
        estate,
        receiversWithSignRights,
        readOnlyReceivers,
        includeListingAgreement,
      })
      allEmailAuditIds = emailAuditIds
    } catch (error) {
      console.error('Failed to send email', error)
      throw new Error('Failed to send email')
    }
  }

  // send sms with sign link to all sellers
  if (channels.sms && content.sms) {
    promises.push(
      sendSms(
        signLink,
        content.sms,
        estate,
        receiversWithSignRights,
        readOnlyReceivers,
      ).catch((error) => {
        console.error('Failed to send SMS', error)
        throw new Error('Failed to send SMS')
      }),
    )
  }

  const receivers = [...receiversWithSignRights, ...readOnlyReceivers]

  // Extract proxies from receivers where the proxy is also a receiver
  const proxies = receivers.filter((receiver) =>
    receivers.some((r) => r.proxy?.contactId === receiver.contactId),
  )
  const sellers = receivers.filter(
    (receiver) =>
      !receivers.some((r) => r.proxy?.contactId === receiver.contactId),
  )

  const emailContent = JSON.stringify(content.email)
  const smsContent = JSON.stringify(content.sms)

  const templateId = extraData.templateId

  const receiversContactIds = receivers.map((seller) => seller.contactId)

  if (listingAgreement?.id && includeListingAgreement) {
    void logListingAgreementInteraction({
      listing_agreements_id: listingAgreement.id,
      event_type: 'sent_to_seller',
      extra_data: {
        sellers: sellers.map((seller) => seller.contactId),
        proxies: proxies.map((proxy) => proxy?.contactId),
        template: {
          templateId,
          modified: extraData.modified,
          email: emailContent,
          sms: smsContent,
        },
        channels,
      },
    })
  }
  const markPromise = markInspectionAsSent(estateId, {
    includeListingAgreement,
    channels: [
      channels.email ? 'email' : null,
      channels.sms ? 'sms' : null,
    ].filter(isNotNull),
    recipientContactIds: receiversContactIds.filter(isNotNull),
    templateId,
    modified: extraData.modified,
    email: emailContent,
    sms: smsContent,
    recipients: receivers,
    emailSubject: subject,
    emailAuditIds: allEmailAuditIds,
  })
  promises.push(markPromise)

  await Promise.all(promises)

  after(async () => {
    const afterInspectionTemplateIds: string[] = [
      AFTER_INSPECTION_TEMPLATE_LONG.id,
      AFTER_INSPECTION_TEMPLATE_SHORT.id,
    ]

    if (templateId && afterInspectionTemplateIds.includes(templateId)) {
      void markInspectionAsDone(estateId)
    }

    try {
      await uploadSendToClientEmailToVitec({
        signLink,
        emailContent: content.email,
        subject,
        estateId: estate.estateId,
        estateAddress: estate.address.streetAddress ?? '',
        receivers: [...receiversWithSignRights, ...readOnlyReceivers],
        linkToOfferText: getLinkToOfferText({
          agreementIncluded: includeListingAgreement,
          isValuation: Boolean(estate.isValuation),
        }),
      })
    } catch (error) {
      console.error(
        `[VITEC] Failed to upload email to Vitec for estate ${estate.estateId}`,
        error,
      )
    }
  })
}

async function sendSms(
  link: string,
  smsContent: SmsContent[],
  estate: Estate,
  receiversWithSignRights: Receiver[],
  readOnlyReceivers: Receiver[],
) {
  const receivers = [...receiversWithSignRights, ...readOnlyReceivers]
  if (receivers.length === 0) {
    console.info(
      `No mobile phone numbers found for estate ${estate.estateId}, skipping sending sign link`,
    )
    return
  }

  const departmentId = isProd
    ? (estate.department?.departmentId.toString() ?? '1')
    : '1'

  for (const reciever of receivers) {
    const canEdit = hasEditRights(receiversWithSignRights, reciever)
    const url = getLinkWithParams(link, reciever, 'sms', canEdit)
    const shortUrl = await getShortUrl(url.toString())

    let phoneNumber: string | null = reciever.mobilePhone ?? null
    try {
      // If not production env we send it to a test number
      if (!isProd) {
        const testPhone = process.env.TEST_PHONE?.split(';')[0]
        if (!testPhone) {
          console.error('Missing TEST_PHONE in env')
          throw new Error('Missing TEST_PHONE in env')
        }
        phoneNumber = testPhone
      }

      if (!phoneNumber) {
        console.error(
          `No phone number found for seller ${reciever.contactId}, skipping sending sign link`,
        )
        continue
      }

      const text = `${smsContent
        .map((content) =>
          content.type === 'link' &&
          content.text.includes('{listingAgreementLink}')
            ? `\n${shortUrl}\n`
            : content.text,
        )
        .join('\n')}`

      // Vitec Hub does not support emojis in SMS
      const regex = emojiRegex()
      const cleanedText = text.replace(regex, '')

      if (!reciever.contactId) {
        console.error(
          `No contactId found for seller ${reciever.contactId}, skipping sending sign link`,
        )
        continue
      }

      await nextSendSMS({
        FromEmployeeId: estate.broker?.employeeId ?? '0',
        UseSenderFallback: false,
        RecipientNumber: phoneNumber,
        text: cleanedText,
        DepartmentId: departmentId,
        ContactId: reciever.contactId,
        EstateId: estate.estateId,
        Source: 'Nordvik',
      })

      console.info(
        `[SEND OFFER DIALOG]: SMS sent to ${phoneNumber}, intial receiver: ${reciever.mobilePhone}`,
      )
    } catch (error) {
      console.error(`Failed to send SMS to ${phoneNumber}`, error)
      await sendSlackMessage(
        [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Error sendSms for estate ${estate.estateId}, phone: ${phoneNumber}`,
            },
          },
          { type: 'divider' },
          {
            type: 'section',
            text: { type: 'mrkdwn', text: error },
          },
        ],
        'NORDVIK_ERRORS',
      )
      throw new Error(`Failed to send SMS to ${phoneNumber}`)
    }
  }
}

async function sendMails({
  link,
  emailContent,
  subject,
  estate,
  receiversWithSignRights,
  readOnlyReceivers,
  includeListingAgreement,
}: {
  link: string
  emailContent: EmailContent[]
  subject: string
  estate: Estate
  receiversWithSignRights: Receiver[]
  readOnlyReceivers: Receiver[]
  includeListingAgreement: boolean
}): Promise<{ emailAuditIds: string[] }> {
  const receivers = [...receiversWithSignRights, ...readOnlyReceivers]
  if (receivers.length === 0) {
    console.info(
      `No email addresses for estate ${estate.estateId}, skipping sign link`,
    )
    return { emailAuditIds: [] }
  }

  console.info(
    `[Send til kunde] Sending email to ${receivers.length} sellers for estate ${estate.estateId}`,
  )

  const emailSubject =
    subject || `Oppdragsavtale for ${estate.address.streetAddress}`
  const from = estate.broker ?? {
    email: process.env.NO_REPLY_EMAIL ?? '<EMAIL>',
    name: 'Nordvik',
  }

  let bccSent = false

  const emailAuditIds: string[] = []
  for (const receiver of receivers) {
    // If not production env we send it to a test email
    let email: string | null | undefined = receiver.email

    if (process.env.VERCEL_ENV !== 'production') {
      email = process.env.TEST_EMAIL!.split(';')[0]
    }

    if (!email) {
      console.error(
        `No email address found for seller ${receiver.contactId}, skipping sending sign link`,
      )
      continue
    }

    const canEdit = hasEditRights(receiversWithSignRights, receiver)
    const url = getLinkWithParams(link, receiver, 'email', canEdit)

    try {
      const { emailAuditId } = await sendMail({
        title: '',
        emails: [
          {
            email,
            name: `${receiver.firstName} ${receiver.lastName}`,
            contactId: receiver.contactId,
          },
        ],
        subject: emailSubject,
        body: getSendToClientEmailHtml({
          url: url.toString(),
          emailContent,
          subject: emailSubject,
          linkToOfferText: getLinkToOfferText({
            agreementIncluded: includeListingAgreement,
            isValuation: Boolean(estate.isValuation),
          }),
        }),
        includeSignatureForEmployeeId: estate.broker?.employeeId,
        from,
        blindCopyTo: bccSent ? undefined : estate.broker?.email,
        initialReceiver: {
          email: receiver.email,
          name: `${receiver.firstName} ${receiver.lastName}`,
          role: canEdit
            ? receiver.relationType === 25
              ? 'spouse'
              : 'signer'
            : 'read only',
        },
        context: {
          type: 'estate-offer-sign-link',
          id: estate.id,
        },
      })
      if (emailAuditId) emailAuditIds.push(emailAuditId)

      console.info(
        `[SEND OFFER DIALOG]: Email sent to ${email}, intial receiver: ${receiver.email}`,
      )
      bccSent = true
    } catch (error) {
      console.error(`Failed to send email to ${email}`, error)
      await sendSlackMessage(
        [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Error sendMails for estate ${estate.estateId}, email: ${email}`,
            },
          },
          { type: 'divider' },
          {
            type: 'section',
            text: { type: 'mrkdwn', text: error },
          },
        ],
        'NORDVIK_ERRORS',
      )
      throw new Error(`Failed to send email to ${email}`)
    }
  }
  return { emailAuditIds }
}

function hasEditRights(
  receiversWithSignRights: Receiver[],
  receiver: Receiver,
) {
  return receiversWithSignRights.some((r) => r.contactId === receiver.contactId)
}

function getLinkWithParams(
  link: string,
  receiver: Receiver,
  source: 'email' | 'sms',
  canEdit: boolean,
) {
  if (!receiver.contactId) {
    throw new Error('Missing contactId')
  }
  // Adds CID for signers and ECID for readOnly accesses. Seller that has proxy(fullmektig) and extraContact will have ECID
  const url = new URL(link)

  if (canEdit) {
    url.searchParams.set('cid', receiver.contactId)
  } else {
    url.searchParams.set('ecid', receiver.contactId)
  }
  url.searchParams.set('source', source)

  return url
}

function getSendToClientEmailHtml({
  url,
  emailContent,
  subject,
  linkToOfferText,
}: {
  url: string
  emailContent: EmailContent[]
  subject: string
  linkToOfferText?: string
}) {
  return getHtmlWithStyleReset(
    sendToClientContent({
      url: url.toString(),
      content: emailContent,
      linkToOfferText,
    }),
    subject,
  )
}

async function uploadSendToClientEmailToVitec({
  signLink,
  emailContent,
  subject,
  estateId,
  estateAddress,
  receivers,
  linkToOfferText,
}: {
  signLink: string
  emailContent: EmailContent[]
  subject: string
  estateId: string
  estateAddress: string
  receivers: { contactId?: string }[]
  linkToOfferText: string
}) {
  const emailContentHtml = getSendToClientEmailHtml({
    url: signLink,
    emailContent,
    subject,
    linkToOfferText,
  })

  const date = formatDate(new Date(), 'dd.MM.yy HH:mm')

  uploadEmailContentToVitec({
    emailContentHtml,
    subject,
    estateId,
    title: `E-post til ${estateAddress} - ${date}`,
    receivers,
  })
}
