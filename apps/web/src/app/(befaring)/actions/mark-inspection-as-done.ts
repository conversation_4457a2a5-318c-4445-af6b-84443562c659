import { fetchVitecHubApi } from '@/api/vitec-hub-api'
import { ActivityType } from '@/server/model/BrokerEstate/types'

export async function markInspectionAsDone(estateId: string) {
  const activities = await fetchVitecHubApi<
    {
      id: string
      type: ActivityType
      done: boolean
    }[]
  >(`Estates/${estateId}/Activities`)

  for (const activity of activities) {
    if (activity.type === ActivityType.Inspection && !activity.done) {
      try {
        await fetchVitecHubApi(`Activities?ActivityId=${activity.id}`, {
          method: 'PUT',
        })
      } catch (error) {
        console.error(
          `Failed to mark inspection as done for activity ${activity.id}:`,
          error,
        )
      }
    }
  }
}
