'use server'

import prisma from '@/db/prisma'
import { getBaseUrl } from '@/lib/getBaseUrl'
import retry from '@/lib/retry'

export async function getOfferCustomerLink({
  estateId,
  listingAgreementId,
}: {
  estateId: string
  listingAgreementId: string
}) {
  console.info('Getting offer customer link', estateId, listingAgreementId)

  try {
    const tokenEntry = await retry(async () => {
      const accessToken = await prisma.offer_access_tokens.findFirst({
        where: {
          listing_agreement_id: listingAgreementId,
          estate_id: estateId,
          valid: true,
        },
      })

      if (!accessToken) {
        return await prisma.offer_access_tokens.create({
          data: {
            listing_agreement_id: listingAgreementId,
            estate_id: estateId,
            valid: true,
          },
        })
      }
      return accessToken
    })

    return `${getBaseUrl()}/oppdragsavtale/${estateId}?viewToken=${tokenEntry.token}`
  } catch (error) {
    console.error('Failed to get offer customer link', error)
    throw error
  }
}

export async function getInspectionCustomerLink({
  estateId,
  inspectionId,
  listingAgreementId,
  isValuation,
  segment,
}: {
  estateId: string
  inspectionId?: string
  listingAgreementId?: string
  isValuation: boolean
  segment?: 'start'
}) {
  let accessToken = await prisma.offer_access_tokens.findFirst({
    where: {
      estate_id: estateId,
      valid: true,
    },
  })

  if (!accessToken) {
    accessToken = await prisma.offer_access_tokens.create({
      data: {
        inspection_folder_id: inspectionId,
        listing_agreement_id: listingAgreementId,
        estate_id: estateId,
        valid: true,
      },
    })
  }

  const url = new URL(getBaseUrl())

  if (isValuation) {
    url.pathname = `/verdivurdering/${estateId}`
  } else {
    url.pathname = `/oppdragsavtale/${estateId}`
  }

  if (segment) {
    url.pathname += `/${segment}`
  }

  url.searchParams.set('viewToken', accessToken.token)

  return url.toString()
}
