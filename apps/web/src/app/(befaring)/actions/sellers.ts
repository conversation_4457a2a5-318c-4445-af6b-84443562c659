'use server'

import { NoEstateError } from '@befaring/lib/errors'
import uniqBy from 'lodash/uniqBy'
import { cache } from 'react'

import { getContact } from '@/actions/next/contact-get'
import { getEstateContactInfo } from '@/actions/next/estate-contact-info'
import { getEstateContactRelations } from '@/actions/next/estate-contact-relations'
import type {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'
import prisma from '@/db/prisma'
import { getEstateById } from '@/server/model/BrokerEstate/factory'
import { withCache } from '@/utils/with-cache'

import { formatSigner } from './utils'

type SignersDataProps = {
  estateId: string
  onlyUnique?: boolean
  signersIfCompany?: boolean
}

export const cachedSigners = cache(
  async (estate: string, signersIfCompany = false, onlyUnique = false) =>
    await getSignersData({
      estateId: estate,
      onlyUnique,
      signersIfCompany,
    }),
)

export async function getSignersData({
  estateId,
  onlyUnique = false,
  signersIfCompany = false,
}: SignersDataProps): Promise<NextPrivateContactWithProxy[]> {
  const [estateContactInfo, spouseRelations] = await withCache(
    `getSignersData:${estateId}`,
    () =>
      Promise.all([
        getEstateContactInfo(estateId).catch(),
        getEstateContactRelations(estateId, [25]),
      ]),
    3,
  )

  let estateSellers = estateContactInfo?.sellers

  if (!estateSellers) {
    const estate = await getEstateById({ id: estateId })
    if (!estate) {
      throw new NoEstateError()
    }
    estateSellers = estate.sellers.map<NextPrivateContact>((seller) => {
      return {
        ...seller,
        departmentId: estate.departmentId!,
        contactType: seller.contactType ?? 1,
        contactId: seller.contactId,
      }
    })
  }

  const spouseContacts =
    spouseRelations?.relations?.find((r) => r.relationType === 25)?.contacts ??
    []

  const spousePrivateContacts = await Promise.all(
    spouseContacts.map((contactRelation) =>
      getContactData(contactRelation.contactId),
    ),
  )

  const signersToFetch = [
    ...(estateSellers ?? []),
    ...spousePrivateContacts.filter(Boolean),
  ] as NextPrivateContactWithProxy[]

  const sellersDataPromises = signersToFetch.map(async (seller) => {
    if (!seller.contactId) {
      return []
    }

    if (signersIfCompany && seller.contactType === 1) {
      const companySigners = await getCompanySigners({
        estateId,
        companyContactId: seller.contactId,
      })

      return companySigners
    } else {
      const sellerDataWithProxy = formatSigner({
        signerContact: seller,
        spouseContacts,
        proxyContact: estateContactInfo?.proxies?.find(
          (entry) => entry.contactId === seller.contactId,
        )?.proxy,
      })

      return sellerDataWithProxy ? [sellerDataWithProxy] : []
    }
  })

  const sellersDataNested = await Promise.all(sellersDataPromises)
  const flattened = sellersDataNested.flat()

  if (onlyUnique) {
    return uniqBy(
      flattened.map((seller) => seller.proxy ?? seller),
      'contactId',
    )
  }

  return flattened
}

export async function getCompanySigners({
  estateId,
  companyContactId,
}: {
  estateId: string
  companyContactId: string
}): Promise<NextPrivateContactWithProxy[]> {
  const response = await prisma.company_sign_rights.findFirst({
    where: {
      company_contact_id: companyContactId,
      listing_agreements: { estate_id: estateId, deleted_at: null },
    },
    select: {
      signers: true,
    },
  })

  const signerIds = response?.signers ?? []

  const signerDataPromises = signerIds.map(async (signerId) => {
    const signerContact = await getContactData(signerId)
    if (signerContact) {
      return formatSigner({
        signerContact,
      })
    }
    return null
  })

  const signerDataResults = await Promise.all(signerDataPromises)
  return signerDataResults.filter(Boolean) as NextPrivateContactWithProxy[]
}

const getContactData = cache(async (contactId: string) => {
  if (!contactId) return undefined
  return withCache(
    `getContactData:${contactId}`,
    () => getContact(contactId),
    5,
  )
})
