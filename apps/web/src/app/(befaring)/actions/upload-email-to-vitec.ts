'use server'

import { postDocument } from '@/actions/next/estate-documents'
import { DocumentTypeEnum } from '@/actions/next/types-next'
import { convertHtmlToPdf } from '@/actions/signing/document-sign'
import { isNotNull } from '@/server/utils'

export async function uploadEmailContentToVitec({
  emailContentHtml,
  subject,
  estateId,
  title,
  receivers,
}: {
  emailContentHtml: string
  subject: string
  estateId: string
  title?: string
  receivers?: {
    contactId?: string
  }[]
}) {
  const emailContentPdf = await convertHtmlToPdf(emailContentHtml)

  const pdfBuffer = Buffer.from(emailContentPdf, 'base64')

  await postDocument({
    estateId: estateId,
    documentContent: pdfBuffer,
    docType: DocumentTypeEnum.EmailSent,
    extension: 'pdf',
    head: title || subject,
    contactIdList: receivers
      ?.map((seller) => seller.contactId)
      .filter(isNotNull),
  })
  console.info(
    `[Email uploaded to vitec] ${title || subject} - estateId: ${estateId}`,
  )
}
