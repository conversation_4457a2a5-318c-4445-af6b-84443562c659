import { preloadEstate } from '@befaring/lib/cached-estate'
import { Suspense } from 'react'

import { TrackPageVisit, TrackingPageId } from '@/components/track-page-visit'

import { RelevantEstates } from './components/relevant-estates'
import { LoadingRelatedEstates, RelatedEstates } from './related-estates'
import {
  AreaPriceStatistics,
  LoadingAreaPriceStatistics,
} from './statistics/area-price-statistics'

export async function AreaPage({
  estateId,
  pageId,
}: {
  estateId: string
  pageId: TrackingPageId
}) {
  preloadEstate(estateId)

  return (
    <div data-theme="dark" className="bg-root grow">
      <TrackPageVisit estateId={estateId} pageId={pageId} />
      <section className="container [--gap-y:3.5rem] md:[--gap-y:5rem] [--gap-x:4rem]">
        <div className="mt-20 mb-10">
          <h1 className="typo-display-xl">Ditt område</h1>
          <div className="typo-body-md ink-muted">
            Generell boligprisutvikling for ditt område og fylke/region.
          </div>
        </div>
        <div className="flex flex-col gap-[--gap-y] mb-[--gap-y] pb-[--gap-y]">
          <Suspense fallback={<LoadingAreaPriceStatistics />}>
            <AreaPriceStatistics estateId={estateId} />
          </Suspense>

          <Suspense>
            <RelevantEstates estateId={estateId} />
          </Suspense>

          <Suspense fallback={<LoadingRelatedEstates />}>
            <RelatedEstates estateId={estateId} />
          </Suspense>
        </div>
      </section>
    </div>
  )
}
