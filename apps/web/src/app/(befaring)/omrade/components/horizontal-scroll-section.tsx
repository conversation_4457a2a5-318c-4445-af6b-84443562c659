import { HorizontalScrollContainer } from '@/components/broker-profile/presentation/components/scroll-container'

export function HorizontalScrollSection({
  title,
  description,
  children,
}: {
  children: React.ReactNode
  title: string
  description: string
}) {
  return (
    <section className="pt-7">
      <div className="mb-5">
        <h2 className="typo-display-sm">{title}</h2>
        <div className="typo-body-md ink-muted max-w-[75vw]">{description}</div>
      </div>

      <HorizontalScrollContainer
        listClassName="
        [--overflow:--container-padding] lg:[--overflow:0]
        [--gap-x:1rem] gap-x-[--gap-x]
        auto-cols-[70%]
        md:auto-cols-[calc((100%-var(--gap-x)*2)/3)]
        pb-5
      "
        alignChildrenTop
      >
        {children}
      </HorizontalScrollContainer>
    </section>
  )
}
