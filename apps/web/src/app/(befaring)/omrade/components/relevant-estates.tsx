'use server'

import prisma from '@/db/prisma'

import { RelevantEstatesScroll } from './relevant-estates-scroll'

export async function RelevantEstates({ estateId }: { estateId: string }) {
  const response = await prisma.inspection_folders.findUnique({
    where: {
      estate_id: estateId,
    },
    select: {
      relevant_links: true,
    },
  })

  return (
    <RelevantEstatesScroll
      initialLinks={response?.relevant_links}
      estateId={estateId}
    />
  )
}
