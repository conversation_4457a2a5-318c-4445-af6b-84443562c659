'use client'

import { capitalize } from 'lodash'

import { useBefaringRelevantEstatesQuery } from '@/api/generated-client'
import { LinkItem } from '@/app/(protected)/(sidebar)/profil/components/links'

import { HorizontalScrollSection } from './horizontal-scroll-section'

export function RelevantEstatesScroll({
  estateId,
  initialLinks = [],
}: {
  estateId: string
  initialLinks?: string[]
}) {
  const { data } = useBefaringRelevantEstatesQuery(
    {
      estateId,
    },
    {
      initialData: {
        inspectionFolder: {
          relevantLinks: initialLinks,
        },
      },
      select: (data) => data.inspectionFolder?.relevantLinks,
    },
  )

  if (!data || data.length === 0) return null

  const pluralizedEstates = data.length > 1 ? 'boliger' : 'bolig'

  return (
    <HorizontalScrollSection
      title={`Relevante ${pluralizedEstates}`}
      description={`${capitalize(pluralizedEstates)} som er relevante for ditt salg i form av størrelse, prisantydning eller målgruppe.`}
    >
      {data.map((entry) => (
        <LinkItem key={entry} link={entry} />
      ))}
    </HorizontalScrollSection>
  )
}
