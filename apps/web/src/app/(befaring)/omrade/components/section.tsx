import { motion } from 'framer-motion'

import { cn } from '@nordvik/theme/cn'

const basisCn = {
  full: 'basis-full',
  '8/12': 'basis-8/12',
  '9/12': 'basis-9/12',
  '10/12': 'basis-10/12',
  '11/12': 'basis-11/12',
} as const

export function Section({
  children,
  basis = 'full',
  title,
  description,
  action,
}: {
  basis: keyof typeof basisCn
  children: React.ReactNode
  title?: React.ReactNode
  description?: React.ReactNode
  action?: React.ReactNode
}) {
  return (
    <motion.div
      className={cn('flex flex-col @container/section', basisCn[basis])}
    >
      <div className="flex gap-2 justify-between flex-wrap">
        <div className="flex flex-col empty:hidden">
          {title && (
            <h2 className="typo-display-sm flex items-center gap-2">{title}</h2>
          )}
          {description && (
            <div className="typo-body-sm ink-muted mt-1 flex items-baseline gap-2">
              {description}
            </div>
          )}
        </div>
        {action && <div>{action}</div>}
      </div>
      <div className="mt-6 only:mt-0 flex flex-col grow transition-opacity duration-150">
        {children}
      </div>
    </motion.div>
  )
}
