query areaStatistics($postalCode: String!, $years: Float!) {
  priceStatistics(postalCode: $postalCode, years: $years) {
    indexes {
      id
      type
      date
      avgSalesTime
      region
      area
      avgSqmPrice
      indexChange12Months
      indexChange4Quarter
      indexChange5Years
      indexChange10Years
    }
    secondaryIndexes {
      id
      type
      date
      avgSalesTime
      region
      area
      avgSqmPrice
      indexChange12Months
      indexChange4Quarter
      indexChange5Years
      indexChange10Years
    }
  }
}
