import { cachedEstate } from '@befaring/lib/cached-estate'

import {
  AreaStatisticsDocument,
  GQLAreaStatisticsQuery,
  GQLAreaStatisticsQueryVariables,
} from '@/api/generated-client'
import { gqlServerFetch } from '@/api/gqlServerFetch'
import { EiendomsverdiLinkProps } from '@/external-services/eiendomsverdi/get-eiendomsverdi-link'

import { SalesHistory } from '../../verdivurdering/[estateId]/din-bolig/components/sales-history-section'

import { ClientStatisticsWrapper } from './components/client-statistics-wrapper'

const ClientStatisticsWrapperClassName =
  'flex gap-4 border-b border-muted pb-4 flex-col md:flex-row md:flex-wrap space-y-2'

export async function AreaPriceStatistics({
  estateId,
  promise,
}: {
  estateId: string
  promise?: Promise<SalesHistory>
}) {
  const estate = await cachedEstate(estateId)

  if (!estate || !estate.address?.zipCode) return null

  const statisticsResponse = await gqlServerFetch<
    GQLAreaStatisticsQuery,
    GQLAreaStatisticsQueryVariables
  >(AreaStatisticsDocument, {
    postalCode: estate.address.zipCode,
    years: 11,
  })

  return (
    <ClientStatisticsWrapper
      className={ClientStatisticsWrapperClassName}
      statistics={statisticsResponse.data?.priceStatistics}
      promise={promise}
      estate={{
        address: estate.address,
        landIdentificationMatrix: estate.landIdentificationMatrix,
        estateType: estate.estateType,
        ownership: estate.ownership,
        partOwnership: estate.partOwnership,
      }}
    />
  )
}

export function LoadingAreaPriceStatistics() {
  return (
    <ClientStatisticsWrapper
      className={ClientStatisticsWrapperClassName}
      statistics={{
        indexes: [],
        secondaryIndexes: [],
      }}
      loading
      estate={
        {
          address: {},
          landIdentificationMatrix: {},
        } as EiendomsverdiLinkProps
      }
    />
  )
}
