'use client'

import { cn } from '@nordvik/theme/cn'

import { EiendomsverdiLinkProps } from '@/external-services/eiendomsverdi/get-eiendomsverdi-link'
import { formatCurrency } from '@/lib/formatCurrency'

import { EiendomsverdiButton } from './eiendomsverdi-button'

export default function PropertyStats({
  primary,
  secondary,
  loading,
  estate,
}: {
  loading?: boolean
  primary: {
    change: number
    salesTime: number
    sqmPrice: number
  }
  secondary?: {
    name: string
    change: number
    salesTime: number
    sqmPrice: number
  }
  estate: EiendomsverdiLinkProps
}) {
  const changeFormatter = new Intl.NumberFormat('no', {
    signDisplay: 'exceptZero',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
    style: 'percent',
  })

  return (
    <div className="md:ml-2 flex flex-col gap-y-6 grow md:w-[20%] w-auto md:pl-3">
      <DataEntry
        label="Kvadratmeterpris"
        value={formatCurrency(primary.sqmPrice)}
        loading={loading}
        secondaryValue={
          secondary
            ? `${secondary.name}: ${formatCurrency(secondary.sqmPrice)}`
            : null
        }
      />

      <DataEntry
        label="Endring siste år"
        value={changeFormatter.format(primary.change)}
        loading={loading}
        secondaryValue={
          secondary
            ? `${secondary.name}: ${changeFormatter.format(secondary.change)}`
            : null
        }
      />

      <DataEntry
        label="Snitt salgstid"
        value={`${primary.salesTime.toFixed(0)} dager`}
        loading={loading}
        secondaryValue={
          secondary
            ? `${secondary.name}: ${secondary.salesTime.toFixed(0)} dager`
            : null
        }
      />

      <EiendomsverdiButton loading={loading} estate={estate} />

      <p className="ink-muted mt-auto typo-body-sm max-w-xl">
        Prisstatistikk er basert på tall og beregninger hentet fra Eiendom Norge
        og Eiendomsverdi AS.
      </p>
    </div>
  )
}

function DataEntry({
  label,
  value,
  secondaryValue,
  loading,
}: {
  label: string
  value: string
  secondaryValue?: React.ReactNode
  loading?: boolean
}) {
  return (
    <div className="flex flex-col gap-y-1">
      <h2 className="typo-body-sm-bold">{label}</h2>
      <div className="flex flex-wrap items-baseline gap-x-2 gap-y-1">
        <span
          className={cn(
            'typo-display-md',
            loading && 'masked-placeholder-text',
          )}
        >
          {value}
        </span>
        {secondaryValue && (
          <span
            className={cn(
              'ink-muted whitespace-nowrap typo-body-md',
              loading && 'masked-placeholder-text',
            )}
          >
            {secondaryValue}
          </span>
        )}
      </div>
    </div>
  )
}
