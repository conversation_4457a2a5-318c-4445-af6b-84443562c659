'use client'

import { useEiendomsverdiLink } from '@befaring/hooks/use-eiendomsverdi-link'
import { ArrowUpRight } from 'lucide-react'

import { Button } from '@nordvik/ui/button'

import { EiendomsverdiLinkProps } from '@/external-services/eiendomsverdi/get-eiendomsverdi-link'
import { useUserContext } from '@/lib/UserContext'

export function EiendomsverdiButton({
  loading,
  estate,
}: {
  loading?: boolean
  estate: EiendomsverdiLinkProps
}) {
  const { user } = useUserContext()
  const { handleOnClick, debouncedFetchLink } = useEiendomsverdiLink(estate)

  return user ? (
    <>
      <Button
        loading={loading}
        size="sm"
        onClick={handleOnClick}
        onMouseOver={debouncedFetchLink}
        className="w-fit md:hidden"
        variant="outline"
        iconEnd={<ArrowUpRight />}
      >
        Gå til Eiendomsverdi
      </Button>
      <Button
        loading={loading}
        size="lg"
        onClick={handleOnClick}
        onMouseOver={debouncedFetchLink}
        className="w-fit hidden md:inline-flex"
        variant="outline"
        iconEnd={<ArrowUpRight />}
      >
        Gå til Eiendomsverdi
      </Button>
    </>
  ) : null
}
