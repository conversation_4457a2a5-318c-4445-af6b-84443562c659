'use client'

import { SalesHistory } from '@befaring/verdivurdering/[estateId]/din-bolig/components/sales-history-section'
import { motion } from 'framer-motion'
import { isNumber } from 'lodash'
import { ArrowLeft } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import * as ButtonTabs from '@nordvik/ui/button-tabs'

import { GQLAreaStatisticsQuery } from '@/api/generated-client'
import { AnimateNumber, AnimateNumberGroup } from '@/components/animate-number'
import { Sale } from '@/external-services/eiendomsverdi/get-sales-of-estate'

import { Section } from '../../components/section'

import { AreaPriceChart } from './area-price-chart'
import { TabValue, tabs } from './client-statistics-wrapper'

export type IndexEntry = {
  date: string
  avgSqmPrice: number
  price?: number
}

export function AreaPriceChartSection({
  statistics,
  promise,
  setActiveTab,
  activeTab,
}: {
  statistics: GQLAreaStatisticsQuery['priceStatistics']
  sales?: Sale[]
  setActiveTab: (value: TabValue) => void
  activeTab: TabValue
  promise?: Promise<SalesHistory>
}) {
  const title = extractPrimaryTitle(statistics)

  const { sales, size } = promise
    ? React.use(promise)
    : { sales: [], size: null }

  const salesWithAvgSqmPrice = getSalesWithAvgSqmPrice(sales, size)

  const percentageChange = getPercentageChangeForTab(
    statistics.indexes,
    activeTab,
  )

  return (
    <Section
      title={title}
      description={
        isNumber(percentageChange) ? (
          <PercentageChange
            percentageChange={percentageChange}
            description="endring per m2"
          />
        ) : (
          <span className="masked-placeholder-text">Endring per m2</span>
        )
      }
      basis="8/12"
      action={
        <ButtonTabs.Root
          value={activeTab}
          className="mt-1"
          onValueChange={setActiveTab}
        >
          <ButtonTabs.List size="sm">
            {tabs.map((tab) => (
              <ButtonTabs.Trigger key={tab.value} value={tab.value}>
                {tab.label}
              </ButtonTabs.Trigger>
            ))}
          </ButtonTabs.List>
        </ButtonTabs.Root>
      }
    >
      <AreaPriceChart
        className="grow pt-4"
        statistics={statistics}
        sales={salesWithAvgSqmPrice}
        xAxisAutoSpreadTicks={Number(activeTab) > 5}
        showFullYear={activeTab === '1'}
        primaryName={title}
      />
    </Section>
  )
}

function getPercentageChangeForTab(
  primary: GQLAreaStatisticsQuery['priceStatistics']['indexes'],
  activeTab: TabValue,
) {
  const first = primary[0]
  const last = primary[primary.length - 1]

  if (activeTab === '1') {
    if (last?.indexChange4Quarter) {
      return last.indexChange4Quarter
    }
    if (last?.indexChange12Months) {
      return last.indexChange12Months
    }
  }

  if (activeTab === '5' && last?.indexChange5Years) {
    return last.indexChange5Years
  }

  if (activeTab === '10' && last?.indexChange10Years) {
    return last.indexChange10Years
  }

  if (first?.avgSqmPrice && last?.avgSqmPrice) {
    return (last.avgSqmPrice - first.avgSqmPrice) / first.avgSqmPrice
  }

  return 0
}

function extractPrimaryTitle(
  statistics: GQLAreaStatisticsQuery['priceStatistics'],
) {
  const first = statistics.indexes?.at(0)
  const area = first?.area || first?.region

  return cleanIndexTitle(area)
}

function PercentageChange({
  percentageChange,
  showPercentage = true,
  successFrom = 0,
  description,
}: {
  percentageChange?: number
  showPercentage?: boolean
  successFrom?: number
  description?: string
}) {
  const value = percentageChange ?? 0
  return (
    <div
      className={cn(
        'flex items-center ink-muted typo-body-sm gap-1',
        value > successFrom && 'ink-success',
        value < successFrom && 'ink-gold',
      )}
    >
      <span
        className={cn(
          'transition inline-flex items-baseline gap-0.5',
          percentageChange === undefined && 'masked-placeholder-text',
        )}
      >
        <AnimateNumberGroup>
          {showPercentage ? (
            <span className="translate-y-[0.125em]">
              <MotionArrow
                className={cn('size-3')}
                layout="position"
                transition={{
                  type: 'spring',
                  stiffness: 100,
                  damping: 20,
                }}
                animate={{
                  rotate: value > 0 ? 90 : value < 0 ? -90 : 0,
                }}
              />
            </span>
          ) : undefined}
          <AnimateNumber
            value={value}
            format={{ style: 'percent', maximumFractionDigits: 1 }}
          />
        </AnimateNumberGroup>
      </span>
      {description && <span>{description}</span>}
    </div>
  )
}

const MotionArrow = motion.create(ArrowLeft)

function getSalesWithAvgSqmPrice(sales: Sale[] | null, size: number | null) {
  if (!size || !sales) return []

  return sales.reduce((acc, sale) => {
    const price = sale.salesPrice ?? sale.askingPrice
    const date =
      sale.saleDate ?? sale.judicialTransferDate ?? sale.registrationDate

    if (price && date) {
      acc.push({
        date: date.slice(0, 10),
        price,
        avgSqmPrice: Math.round(price / size),
      })
    }

    return acc
  }, [] as IndexEntry[])
}

export function cleanIndexTitle(title: string = ''): string {
  if (!title) return ''
  return (
    title
      .replace('Oslo:', '')
      .replace('Fylke', '')
      .replace(':', '')
      // if a dot doesn't have a space after it, add a space
      .replace(/(\.)(?=\S)/g, '$1 ')
  )
}
