'use client'

import { differenceInDays } from 'date-fns'
import { isSameDay } from 'date-fns/isSameDay'
import { Line, LineChart, ResponsiveContainer, XAxis, YAxis } from 'recharts'

import { cn } from '@nordvik/theme/cn'
import { themeCSSVariables } from '@nordvik/theme/variables'
import { ChartContainer, ChartTooltip } from '@nordvik/ui/chart'

import { GQLAreaStatisticsQuery } from '@/api/generated-client'
import { lineWithDashedEnd } from '@/app/(protected)/(sidebar)/dashboard/components/parts/line-with-dashed-end'
import { formatDate } from '@/lib/dates'
import { formatCurrency } from '@/lib/formatCurrency'

import { IndexEntry, cleanIndexTitle } from './area-price-chart-section'

const CustomTooltip = ({
  active,
  payload,
  primaryName,
  secondaryName,
}: {
  active?: boolean
  payload?: {
    payload: { date: number; actualSalePrice?: number }
    name: string
    value: number
    dataKey: string
  }[]
  primaryName?: string
  secondaryName?: string
}) => {
  if (active && payload && payload.length) {
    const isSalePoint = payload[0].dataKey === 'actualAvgSqmPrice'
    return (
      <div
        data-theme="light"
        className="bg-root shadow-lg py-2 pl-3 pr-5 typo-body-sm rounded-sm flex flex-col gap-1"
      >
        <p className="font-medium capitalize">
          {isSalePoint
            ? formatDate(payload[0].payload.date)
            : formatQuarter(payload[0].payload.date, true)}
        </p>
        {payload
          .filter((a) => a.name !== 'dashed')
          .sort((a) => (a.dataKey === 'primary' ? -1 : 0))
          .map((item, index: number) => (
            <div key={index}>
              {isSalePoint && item.payload.actualSalePrice && (
                <div className="flex justify-between gap-4 text-sm mb-1">
                  <div className="inline-flex gap-1.5 font-medium ink-success">
                    <div className="size-2 bg-[currentColor] rounded-full self-center" />
                    Salgspris
                  </div>
                  <span>{formatCurrency(item.payload.actualSalePrice)}</span>
                </div>
              )}
              <div className="flex justify-between gap-4 text-sm">
                <div
                  className={cn('inline-flex gap-1.5 font-medium', {
                    'text-chart-fill-gold':
                      item.name === 'primary' ||
                      item.name === 'actualAvgSqmPrice',
                    'text-chart-stroke-strong': item.name === 'secondary',
                  })}
                >
                  <div className="size-2 bg-[currentColor] rounded-full self-center" />
                  {item.name === 'primary'
                    ? primaryName
                    : item.name === 'secondary'
                      ? secondaryName
                      : item.name === 'actualAvgSqmPrice'
                        ? 'Faktisk salgspris'
                        : item.name}
                </div>
                <span>{formatCurrency(item.value)}</span>
              </div>
            </div>
          ))}
      </div>
    )
  }
  return null
}

function formatQuarter(date: string | number | Date, showFullYear = false) {
  return formatDate(new Date(date), `Q'kv.' ${showFullYear ? 'yyyy' : 'yy'}`)
}

export function AreaPriceChart({
  statistics,
  sales = [],
  className,
  showFullYear,
  primaryName,
  xAxisAutoSpreadTicks,
}: {
  statistics: GQLAreaStatisticsQuery['priceStatistics']
  sales?: IndexEntry[]
  className?: string
  showFullYear?: boolean
  primaryName?: string
  xAxisAutoSpreadTicks?: boolean
}) {
  const combinedData = prepareChartData({ statistics, sales })

  const hasPrediction = combinedData.at(-1)?.prediction

  const secondaryName = cleanIndexTitle(
    statistics.secondaryIndexes?.[0]?.region,
  )

  return (
    <div className={className}>
      <ChartContainer
        config={{
          Primary: {
            label: 'Primary',
            color: themeCSSVariables.chart.stroke.gold,
          },
          Secondary: {
            label: 'Secondary',
            color: themeCSSVariables.chart.stroke.subtle,
          },
        }}
        className="w-full md:h-[300px] h-[200px]"
      >
        <ResponsiveContainer width="100%" height={300}>
          <LineChart
            data={combinedData}
            margin={{ top: 5, right: 5, left: 5, bottom: 0 }}
          >
            <XAxis
              dataKey="date"
              hide={showFullYear}
              axisLine={false}
              tickLine={false}
              padding={{ left: 10 }}
              tick={{ fill: '#9CA3AF', fontSize: 12 }}
              minTickGap={50}
              dy={10}
              interval={xAxisAutoSpreadTicks ? 'preserveEnd' : 0}
              tickFormatter={(date) => {
                const dateObj = new Date(date)
                if (xAxisAutoSpreadTicks) {
                  return dateObj.getFullYear().toString()
                }

                const quarter = dateObj.getMonth()
                return quarter === 0 ? dateObj.getFullYear().toString() : '' // Only show Q1 labels
              }}
            />
            <YAxis
              orientation="right"
              axisLine={false}
              tickCount={4}
              tickLine={false}
              padding={{ bottom: 10 }}
              tickFormatter={(value) =>
                formatCurrency(value, { suffix: false })
              }
              tick={{ fill: '#9CA3AF', fontSize: 12 }}
              tickMargin={10}
              domain={[
                (dataMin) => Math.floor(dataMin / 5000) * 5000,
                (dataMax) => Math.ceil(dataMax / 5000) * 5000,
              ]}
            />
            <ChartTooltip
              cursor={{
                strokeDasharray: '3 3',
                strokeOpacity: 0.5,
              }}
              content={
                <CustomTooltip
                  primaryName={primaryName}
                  secondaryName={secondaryName}
                />
              }
            />

            <Line
              animationId={2}
              key="secondary"
              connectNulls
              type="monotone"
              dataKey="secondary"
              stroke="var(--color-Secondary)"
              strokeWidth={2}
              fill="var(--color-Secondary)"
              dot={{
                r: 0,
              }}
              activeDot={{
                r: 5,
                strokeWidth: 2,
                stroke: 'white',
              }}
            />

            {hasPrediction ? (
              lineWithDashedEnd({
                dashArrayIndex: combinedData.findLastIndex(
                  (point) => !point.prediction && point.primary,
                ),
                data: combinedData,
                animationId: 1,
                key: 'primary',
                connectNulls: true,
                type: 'monotone',
                dataKey: 'primary',
                stroke: 'var(--color-Primary)',
                tooltipType: 'none',
                strokeWidth: 2,
                fill: 'var(--color-Primary)',
                dot: {
                  r: 0,
                  strokeWidth: 0,
                },
                activeDot: {
                  r: 5,
                  strokeWidth: 2,
                  stroke: 'white',
                },
              })
            ) : (
              <Line
                animationId={1}
                key="primary"
                connectNulls
                type="monotone"
                dataKey="primary"
                stroke="var(--color-Primary)"
                strokeWidth={2}
                fillOpacity={1}
                fill="var(--color-Primary)"
                dot={{
                  r: 0,
                  strokeWidth: 0,
                }}
                activeDot={{
                  r: 5,
                  strokeWidth: 2,
                  stroke: 'white',
                }}
              />
            )}

            <Line
              type="linear"
              dataKey="actualAvgSqmPrice"
              fill={themeCSSVariables.fill.success.bold}
              name="Kvadratmeterpris"
              strokeWidth={0}
              r={4}
              activeDot={{
                r: 5,
                cursor: 'pointer',
                fill: themeCSSVariables.fill.success.bold,
                stroke: 'white',
              }}
            />
          </LineChart>
        </ResponsiveContainer>
      </ChartContainer>
      <div title="legend" className="flex gap-6 mt-4">
        {primaryName && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-gold-emphasis" />
            <span className="text-sm">{primaryName}</span>
          </div>
        )}
        {secondaryName && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-chart-fill-brand-stronger" />
            <span className="text-sm">{secondaryName}</span>
          </div>
        )}
        {sales && sales.length > 0 && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-success-bold" />
            <span className="text-sm">Solgt</span>
          </div>
        )}
      </div>
    </div>
  )
}

export type ChartPoint = {
  date: number
  primary?: number
  secondary?: number
  actualAvgSqmPrice?: number
  actualSalePrice?: number
  prediction?: boolean
}

function prepareChartData({
  statistics,
  sales,
}: {
  statistics: GQLAreaStatisticsQuery['priceStatistics']
  sales: IndexEntry[]
}): ChartPoint[] {
  const { indexes = [], secondaryIndexes = [] } = statistics

  const combinedMap = new Map<number, ChartPoint>()

  let minDate = new Date().getTime()

  for (const entry of indexes) {
    const timeKey = new Date(entry.date)
    const secondary = secondaryIndexes.find((m) => isSameDay(m.date, timeKey))

    combinedMap.set(timeKey.getTime(), {
      date: timeKey.getTime(),
      primary: entry.avgSqmPrice,
      secondary: secondary?.avgSqmPrice,
    })

    minDate = Math.min(minDate, timeKey.getTime())
  }

  for (const sale of sales) {
    const timeKey = new Date(sale.date)
    const existingEntry = combinedMap.get(timeKey.getTime())
    if (existingEntry) {
      existingEntry.actualAvgSqmPrice = sale.avgSqmPrice
      existingEntry.actualSalePrice = sale.price
    }
    // skip if the date is before the first index date
    if (timeKey.getTime() < minDate) {
      continue
    }
    combinedMap.set(timeKey.getTime(), {
      date: timeKey.getTime(),
      actualSalePrice: sale.price,
      actualAvgSqmPrice: sale.avgSqmPrice,
    })
  }

  const combinedData = Array.from(combinedMap.values()).sort(
    (a, b) => a.date - b.date,
  )

  const lastDataPoint = combinedData.findLast(
    (point) => point.primary || point.secondary,
  )

  const lastSecondary = secondaryIndexes.at(-1)

  if (
    lastDataPoint?.date &&
    lastSecondary?.date &&
    differenceInDays(new Date(lastSecondary.date), lastDataPoint.date) > 20
  ) {
    if (!lastSecondary?.avgSqmPrice || !lastDataPoint.secondary) {
      return combinedData
    }

    // calculate change since last data point
    const change =
      (lastSecondary.avgSqmPrice - lastDataPoint.secondary) /
      lastSecondary.avgSqmPrice

    // add data point to the end of the array with prediction of the primary based on the change
    if (!lastDataPoint.primary) {
      return combinedData
    }

    const predictedPrimary = lastDataPoint.primary * (1 + change)

    combinedData.push({
      date: new Date(lastSecondary.date).getTime(),
      primary: predictedPrimary,
      prediction: true,
      secondary: lastSecondary.avgSqmPrice,
    })
  }

  return combinedData
}
