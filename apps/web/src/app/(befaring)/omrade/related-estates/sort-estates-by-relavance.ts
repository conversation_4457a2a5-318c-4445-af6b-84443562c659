import { uniqBy } from 'lodash'

import { CachedEstate } from '../../lib/cached-estate'

import { getEstates } from './fetch-related-estates'
import { APARTMENTS, HOUSES, isApartment } from './util'

/**
 * Base multipliers/penalties for each factor.
 * Will be scaled by a user-assigned weight (1–10).
 */
export const BASE_FACTORS = {
  estateTypeMismatch: 100,
  distancePerKm: 30,
  brokerMismatch: 20,
  departmentMismatch: 15,
  pricePct: 10,
  sizePct: 10,
  bedrooms: 5,
  popularityBoost: -2,
}

export type RelevancyWeights = typeof BASE_FACTORS

export type WeightFields = {
  estateType: number
  distance: number
  broker: number
  department: number
  price: number
  size: number
  popularity: number
  bedRooms: number
}

export const DEFAULT_WEIGHTS: WeightFields = {
  estateType: 10,
  distance: 8,
  broker: 5,
  price: 5,
  size: 5,
  department: 2,
  bedRooms: 2,
  popularity: 2,
}

type Estate = Awaited<ReturnType<typeof getEstates>>[number]

/**
 * Sort estates by weighted priority and return an array of score objects:
 *
 * Factors:
 *   1) Same estateTypeId
 *   2) Estate sold by current broker
 *   3) Estate sold by current broker's department
 *   4) Distance to current estate
 *   5) Price % difference
 *   6) Size % difference
 *   7) Popularity
 *
 * Doubling a weight doubles that factor's impact on the total score.
 */
export function sortEstatesByWeightedRelevancy({
  estates,
  relevantTo,
  weights = DEFAULT_WEIGHTS,
}: {
  estates: Estate[]
  relevantTo: CachedEstate
  weights?: WeightFields
}) {
  if (!Array.isArray(estates) || estates.length === 0) return []

  const estateIsApartment =
    relevantTo.estateTypeId && isApartment(relevantTo.estateTypeId)

  function getPartialScores(estate: Estate) {
    const partials: WeightFields = {
      estateType: 0,
      broker: 0,
      department: 0,
      distance: 0,
      price: 0,
      size: 0,
      bedRooms: 0,
      popularity: 0,
    }

    // 1) EstateType mismatch => penalty
    if (
      relevantTo.estateTypeId &&
      estate.estateTypeId !== relevantTo.estateTypeId
    ) {
      if (
        estate.estateTypeId &&
        getMatchingTypes(relevantTo.estateTypeId)?.includes(
          Number(estate.estateTypeId),
        )
      ) {
        partials.estateType =
          BASE_FACTORS.estateTypeMismatch * weights.estateType * 0.15
      } else {
        partials.estateType =
          BASE_FACTORS.estateTypeMismatch * weights.estateType
      }
    }

    // 2) Broker mismatch => penalty
    if (
      !estate.brokersIdWithRoles?.some(
        (entry) => entry?.employeeId === relevantTo.broker?.employeeId,
      )
    ) {
      partials.broker = BASE_FACTORS.brokerMismatch * weights.broker
    }

    // 3) Department mismatch => penalty
    if (
      relevantTo.department?.departmentId &&
      estate.departmentId !== relevantTo.department.departmentId
    ) {
      partials.department = BASE_FACTORS.departmentMismatch * weights.department
    }

    // 4) Distance => distance(km) * base factor * weight
    if (
      estate.latitude != null &&
      estate.longitude != null &&
      relevantTo.latitude != null &&
      relevantTo.longitude != null
    ) {
      const distanceKm = haversineDistance(
        estate.latitude,
        estate.longitude,
        relevantTo.latitude,
        relevantTo.longitude,
      )
      partials.distance =
        distanceKm * BASE_FACTORS.distancePerKm * weights.distance

      if (estateIsApartment) {
        partials.distance *= 10
      }
    }

    // 5) Price => % difference * base factor * weight
    const estateSoldPrice = getEstatePrice(estate)
    const relevantSoldPrice = getEstatePrice(relevantTo)

    const pricePctDiff = percentageDifference(
      estateSoldPrice,
      relevantSoldPrice,
    )

    partials.price = pricePctDiff * BASE_FACTORS.pricePct * weights.price

    // 6) Size => % difference * base factor * weight
    const estateSize = getEstateSize(estate)
    const relevantSize = getEstateSize(relevantTo)

    const sizePctDiff = percentageDifference(estateSize, relevantSize)

    partials.size = sizePctDiff * BASE_FACTORS.sizePct * weights.size

    // 7) Less bedrooms => penalty
    if (estate.noOfBedRooms) {
      const relevantBedRooms = relevantTo.noOfBedRooms
      if (relevantBedRooms) {
        if (estate.noOfBedRooms < relevantBedRooms) {
          partials.bedRooms =
            (relevantBedRooms - estate.noOfBedRooms) *
            BASE_FACTORS.bedrooms *
            weights.bedRooms
        } else if (estate.noOfBedRooms > relevantBedRooms) {
          // Penalty for having more bedrooms than the relevant estate is less severe
          partials.bedRooms =
            (estate.noOfBedRooms - relevantBedRooms) *
            BASE_FACTORS.bedrooms *
            weights.bedRooms *
            0.33 // 1/3
        }
      }
    }

    // 8) Popularity => base factor * weight * -1 (higher popularity => lower score)
    partials.popularity =
      (estate.stats?.interested || 0) *
      BASE_FACTORS.popularityBoost *
      weights.popularity

    return partials
  }

  const scoredEstates = uniqBy<Estate>(estates, 'estateId').map((estate) => {
    const partialScores = getPartialScores(estate)
    const total = Object.values(partialScores).reduce(
      (acc, val) => acc + val,
      0,
    )

    return {
      item: estate,
      score: total, // Summed total
      scores: partialScores, // Detailed breakdown
    }
  })

  // Sort ascending by total score
  scoredEstates.sort((a, b) => a.score - b.score)

  return scoredEstates
}

/**
 * Compute the % difference between `value` and `base`.
 * If `base` is 0 or undefined, return 0.
 */
function percentageDifference(value: number, base: number) {
  if (!base) return 0
  return (Math.abs(value - base) / base) * 100
}

/**
 * Returns the haversine distance in kilometers between two lat/lon points.
 */
export function haversineDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
) {
  const R = 6371 // Earth's radius in km
  const toRad = (val: number) => (val * Math.PI) / 180
  const dLat = toRad(lat2 - lat1)
  const dLon = toRad(lon2 - lon1)

  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) ** 2

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

export function getEstatePrice(estate: CachedEstate | Estate) {
  return (
    estate.estatePriceModel?.soldPrice ||
    estate.estatePrice?.soldPrice ||
    estate.estatePriceModel?.priceSuggestion ||
    estate.estatePrice?.priceSuggestion ||
    0
  )
}

export function getEstateSize(estate: CachedEstate | Estate) {
  return (
    estate.sumArea?.braI ||
    estate.sumArea?.pRom ||
    estate.sumArea?.bra ||
    estate.areaSize?.BRAItotal ||
    0
  )
}

export function getMatchingTypes(type?: string): readonly number[] {
  if (!type) return []
  const intType = parseInt(type, 10)
  if (HOUSES.includes(intType)) return HOUSES
  if (APARTMENTS.includes(intType)) return APARTMENTS
  return [intType]
}
