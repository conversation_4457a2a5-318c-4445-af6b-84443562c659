import { GQLBrokerEstate } from '@/api/generated-client'
import { LinkPreview } from '@/app/(protected)/(sidebar)/profil/components/links'
import { OpenGraphData } from '@/app/api/open-graph/types'
import { formatDate } from '@/lib/dates'
import { formatCurrency } from '@/lib/formatCurrency'
import {
  getSignedNordvikboligEstateUrl,
  isNordvikboligUrlValid,
} from '@/utils/auth/get-signed-nordvikbolig-estate-url'
import { estateImageBlurredFallback, externaLinks } from '@/utils/constants'

export async function RelatedEstateCard({
  estate,
  addData,
  showSqmPrice = false,
}: {
  estate: Pick<
    GQLBrokerEstate,
    | 'estateId'
    | 'images'
    | 'soldDate'
    | 'images'
    | 'areaSize'
    | 'estateSizeModel'
    | 'estatePriceModel'
    | 'estatePrice'
  > & { sumArea?: Pick<NonNullable<GQLBrokerEstate['sumArea']>, 'braI'> }
  addData: OpenGraphData & { url: string }
  showSqmPrice?: boolean
}) {
  const firstImage = estate.images?.[0]?.medium ?? estateImageBlurredFallback

  const size =
    estate.sumArea?.braI ||
    estate.areaSize?.BRAItotal ||
    estate.estateSizeModel?.primaryRoomArea ||
    0

  const price =
    estate.estatePriceModel?.soldPrice ||
    estate.estatePrice?.soldPrice ||
    estate.estatePriceModel?.priceSuggestion ||
    estate.estatePrice?.priceSuggestion ||
    0

  return (
    <RelatedEstatePreview
      addData={{ ...addData, image: firstImage }}
      estateInfo={{
        size,
        price,
        soldDate: estate.soldDate,
      }}
      isLoading={false}
      showSqmPrice={showSqmPrice}
    />
  )
}

export function RelatedEstatePreview({
  estateInfo: estate,
  addData,
  isLoading,
  showSqmPrice = false,
}: {
  estateInfo: {
    size?: number
    price?: number
    soldDate?: Date | string
  }
  addData?: OpenGraphData & { url?: string }
  isLoading: boolean
  showSqmPrice?: boolean
}) {
  return (
    <a
      href={addData?.url}
      target="_blank"
      className="block group/link"
      aria-busy={isLoading}
    >
      <LinkPreview linkData={addData} isLoading={isLoading}>
        <div className="mt-4 typo-body-xs md:typo-body-sm ink-muted flex items-center justify-between">
          {estate.soldDate && (
            <div className="hidden lg:flex items-center gap-2">
              <span className="hidden xl:inline text-sm font-medium group-aria-[busy=true]/link:masked-placeholder-text">
                {formatDate(estate.soldDate)}
              </span>
              <span className="xl:hidden text-xs font-medium group-aria-[busy=true]/link:masked-placeholder-text">
                {formatDate(estate.soldDate, 'dd.MM.yy')}
              </span>
            </div>
          )}
          {estate.size && (
            <div className="flex items-center gap-2">
              <span className="text-xs xl:text-sm font-medium group-aria-[busy=true]/link:masked-placeholder-text">
                {estate.size} m²
              </span>
            </div>
          )}
          {estate.price && (
            <div className="flex items-center gap-2">
              <span className="group-aria-[busy=true]/link:masked-placeholder-text">
                Solgt for:
              </span>
              <span className="text-xs xl:text-sm font-medium group-aria-[busy=true]/link:masked-placeholder-text">
                {showSqmPrice && estate.size
                  ? `${formatCurrency(estate.price / estate.size)}/m²`
                  : formatCurrency(estate.price)}
              </span>
            </div>
          )}
        </div>
      </LinkPreview>
    </a>
  )
}

export async function getEstateAdUrlData(
  estate: Pick<
    GQLBrokerEstate,
    'estateId' | 'hjemUrl' | 'finn' | 'address' | 'heading'
  >,
) {
  const nordvkboligLink = getSignedNordvikboligEstateUrl(estate.estateId)

  const isNordvikboligLinkValid = await isNordvikboligUrlValid(nordvkboligLink)

  if (isNordvikboligLinkValid) {
    return {
      siteName: 'nordvikbolig.no',
      url: nordvkboligLink,
      title: estate.address?.streetAddress,
      description: estate.heading,
      favicon: 'https://static.nordvikbolig.no/icons/favicon.ico',
    }
  }

  const hjemLinkData = await getHjemLinkData(estate)

  if (hjemLinkData) {
    return hjemLinkData
  }

  if (!estate.finn?.finnCode) {
    return null
  }

  return {
    siteName: 'finn.no',
    url: externaLinks.finn(estate.finn.finnCode),
    title: estate.address?.streetAddress,
    description: estate.heading,
    favicon: 'https://www.finn.no/favicon.ico',
  }
}

async function getHjemLinkData(
  estate: Pick<GQLBrokerEstate, 'hjemUrl' | 'address' | 'heading'>,
) {
  const isValid = await isHjemUrlValid(estate.hjemUrl)
  if (!isValid) {
    return null
  }

  return {
    siteName: 'hjem.no',
    url: estate.hjemUrl,
    title: estate.address?.streetAddress,
    description: estate.heading,
    favicon: 'https://www.hjem.no/favicon.ico',
  }
}

async function isHjemUrlValid(url?: string) {
  if (!url) {
    return false
  }

  const hjemPropertyId = url.split('/').pop()

  if (!hjemPropertyId) {
    return false
  }

  const apiUrl = new URL(
    'https://apigw.hjem.no/search-backend/api/v2/properties',
  )

  apiUrl.searchParams.append('ids[]', hjemPropertyId)
  apiUrl.searchParams.append('availability[]', 'all')
  apiUrl.searchParams.append('limit', '1')

  const result = await fetch(apiUrl.toString())

  try {
    const response = await result.json()
    return response.data.length > 0
  } catch (error) {
    console.info('Error fetching Hjem.no property', error)
    return false
  }
}
