import { subMonths } from 'date-fns'
import { subYears } from 'date-fns/subYears'

import {
  FindEstatesDocument,
  GQLFindEstatesQuery,
  GQLFindEstatesQueryVariables,
} from '@/api/generated-client'
import { gqlServerFetch } from '@/api/gqlServerFetch'
import { DAY } from '@/db/util'
import { getAttributes } from '@/external-services/eiendomsverdi/get-attributes'
import { withCache } from '@/utils/with-cache'

import { CachedEstate } from '../../lib/cached-estate'

import { sortEstatesByWeightedRelevancy } from './sort-estates-by-relavance'
import { APARTMENTS, HOUSES, PropertyType, isApartment, isHouse } from './util'

export async function fetchRelatedEstates(relateToEstate: CachedEstate) {
  return withCache(
    `fetchRelatedEstates:${relateToEstate.estateId}`,
    () => fetchRelatedEstatesTo(relateToEstate),
    DAY,
  )
}

async function fetchRelatedEstatesTo(relateToEstate: CachedEstate) {
  const estateIsApartment =
    relateToEstate.estateTypeId && isApartment(relateToEstate.estateTypeId)

  const estateTypeIdFilter = () => {
    if (!relateToEstate.estateTypeId) return undefined
    if (isHouse(relateToEstate.estateTypeId)) return HOUSES
    if (estateIsApartment) return APARTMENTS
    return [Number(relateToEstate.estateTypeId)]
  }

  const noOfBedRooms = () => {
    if (
      estateIsApartment &&
      relateToEstate.noOfBedRooms &&
      relateToEstate.noOfBedRooms < 3
    ) {
      return relateToEstate.noOfBedRooms
    }
    return undefined
  }

  let size = relateToEstate.sumArea?.braI ?? relateToEstate.areaSize?.BRAItotal

  if (!size) {
    const response = await getAttributes(relateToEstate)
    const attributes = response?.data?.attributes

    size =
      attributes?.usableArea.internalUsableArea.value ??
      attributes?.primaryArea.value

    if (size) {
      relateToEstate.areaSize = {
        ...relateToEstate.areaSize,
        BRAItotal: size,
      }
    }
  }

  const ranges = {
    price: relateToEstate.estatePrice?.priceSuggestion
      ? {
          min: Math.round(relateToEstate.estatePrice.priceSuggestion * 0.85),
          max: Math.round(relateToEstate.estatePrice.priceSuggestion * 1.35),
        }
      : undefined,
    size: size
      ? {
          min: Math.round(size * 0.8),
          max: Math.round(size * 1.5),
        }
      : undefined,
  }

  let radius = estateIsApartment ? 2 : 6

  const city = relateToEstate.address?.city

  if (city?.toLocaleLowerCase() === 'oslo') {
    radius *= 0.5
  }

  let soldDateAfter = subYears(new Date(), 1)

  const location =
    relateToEstate.latitude !== 0 && relateToEstate.longitude !== 0
      ? {
          latitude: relateToEstate.latitude,
          longitude: relateToEstate.longitude,
        }
      : {
          city: relateToEstate.address?.city,
        }

  const findRelevantEstates = async () =>
    getEstates({
      soldDateAfter: soldDateAfter.toISOString(),
      statuses: [3, 5],
      baseType: ['used'],
      noBedRooms: noOfBedRooms()?.toString(),
      estateType: estateTypeIdFilter()?.map(String),
      price: ranges.price,
      size: ranges.size,
      radius: radius,
      sortBy: 'soldDate',
      limit: 100,
      ...location,
    })

  try {
    let foundEstates = await findRelevantEstates()

    if (foundEstates.length < 3) {
      radius *= 1.5
      soldDateAfter = subMonths(soldDateAfter, 6)
      ranges.size = undefined
      foundEstates = await findRelevantEstates()
    }

    if (
      foundEstates.length < 3 &&
      Number(relateToEstate.estateTypeId) === PropertyType.Fritidseiendom
    ) {
      radius *= 2
      foundEstates = await findRelevantEstates()
    }

    return sortEstatesByWeightedRelevancy({
      estates: foundEstates.filter((estate) => !!estate.soldDate),
      relevantTo: relateToEstate,
    })
  } catch (error) {
    console.error('Failed to fetch related estates', error)
    return []
  }
}

export async function getEstates(
  filters: GQLFindEstatesQueryVariables['filters'],
) {
  const response = await gqlServerFetch<
    GQLFindEstatesQuery,
    GQLFindEstatesQueryVariables
  >(FindEstatesDocument, { filters })

  return response.data?.findEstates ?? []
}
