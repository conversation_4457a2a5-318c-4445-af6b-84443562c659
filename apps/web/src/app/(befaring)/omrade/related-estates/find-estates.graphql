query findEstates($filters: findEstatesFilters!) {
  findEstates(filters: $filters) {
    estateId
    estateType
    estateTypeId
    address {
      streetAddress
      zipCode
      city
    }
    noOfBedRooms
    estatePriceModel {
      soldPrice
      priceSuggestion
    }
    estatePrice {
      soldPrice
      priceSuggestion
    }
    soldDate

    brokersIdWithRoles {
      employeeId
    }

    departmentId

    latitude
    longitude

    stats {
      interested
    }
    sumArea {
      braI
      pRom
      bra
    }

    areaSize {
      BRAItotal
    }

    hjemUrl

    finn {
      finnCode
    }

    heading

    images {
      medium
    }
  }
}
