export enum PropertyType {
  Enebolig = 1,
  Tomannsbolig = 2,
  <PERSON><PERSON><PERSON><PERSON> = 3,
  Selveierleilighet = 4,
  Andelsleilighet = 5,
  <PERSON>ksjeleilighet = 6,
  Fritidseiendom = 7,
  <PERSON><PERSON> = 8,
  <PERSON><PERSON><PERSON> = 9,
  <PERSON><PERSON><PERSON><PERSON> = 10,
  Kombinasjonslokale = 11,
  Garasje = 12,
  <PERSON><PERSON> = 13,
  Hotell = 14,
  Industri = 15,
  ForretningButikk = 16,
  Landbruk = 17,
  Småbruk = 18,
  // 19 is missing in the list
  ServeringslokaleKantine = 20,
  // 21, 22 missing in the list
  Obligasjonsleilighet = 23,
  Flermannsbolig = 24,
  Næringstomt = 25,
  Garasje2 = 26, // same name "Garasje" but different ID
  LandbrukBolig = 27,
}

export const HOUSES = [
  PropertyType.Enebolig,
  PropertyType.Tomannsbolig,
  PropertyType.Rekkehus,
  PropertyType.Flermannsbolig,
] as const

export const APARTMENTS = [
  PropertyType.Selveierleilighet,
  PropertyType.Andelsleilighet,
  PropertyType.Aksjeleilighet,
  PropertyType.Obligasjonsleilighet,
] as const

export function isHouse(estateTypeId: number | string): boolean {
  return HOUSES.includes(Number(estateTypeId))
}

export function isApartment(estateTypeId: number | string): boolean {
  return APARTMENTS.includes(Number(estateTypeId))
}
