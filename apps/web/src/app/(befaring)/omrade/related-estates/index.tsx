import { cachedEstate } from '@befaring/lib/cached-estate'

import { OpenGraphData } from '@/app/api/open-graph/types'
import { DAY } from '@/db/util'
import { withCache } from '@/utils/with-cache'

import { HorizontalScrollSection } from '../components/horizontal-scroll-section'

import { fetchRelatedEstates } from './fetch-related-estates'
import {
  RelatedEstateCard,
  RelatedEstatePreview,
  getEstateAdUrlData,
} from './related-estate-card'

export async function RelatedEstates({ estateId }: { estateId: string }) {
  const baseEstate = await cachedEstate(estateId)

  if (!baseEstate?.broker) {
    return null
  }

  const fetchedEstates = await fetchRelatedEstates(baseEstate).catch(() => [])

  const estatesWithAddData: {
    estate: Awaited<ReturnType<typeof fetchRelatedEstates>>[number]['item']
    addData: OpenGraphData & { url: string }
  }[] = []

  for (const entry of fetchedEstates) {
    if (estatesWithAddData.length >= 6) break

    const estate = entry.item
    if (!estate) continue

    const addData = await withCache(
      `getEstateAdUrlData:${estate.estateId}`,
      () => getEstateAdUrlData(estate),
      DAY,
    )

    if (!addData || !addData.url) continue

    estatesWithAddData.push({
      estate,
      addData: { ...addData, url: addData.url },
    })
  }

  if (estatesWithAddData.length === 0) {
    return null
  }

  return (
    <HorizontalScrollSection
      title="Tidligere salg i nærheten"
      description="Lignende boliger som er solgt i ditt område."
    >
      {estatesWithAddData.map((entry) => (
        <RelatedEstateCard key={entry.estate.estateId} {...entry} />
      ))}
    </HorizontalScrollSection>
  )
}

export function LoadingRelatedEstates() {
  return (
    <HorizontalScrollSection
      title="Tidligere salg i nærheten"
      description="Lignende boliger som er solgt i ditt område."
    >
      {[1, 2, 3].map((_, index) => (
        <RelatedEstatePreview
          key={index}
          isLoading
          estateInfo={{
            size: 100,
            price: 1000000,
            soldDate: '2025-01-01',
          }}
        />
      ))}
    </HorizontalScrollSection>
  )
}
