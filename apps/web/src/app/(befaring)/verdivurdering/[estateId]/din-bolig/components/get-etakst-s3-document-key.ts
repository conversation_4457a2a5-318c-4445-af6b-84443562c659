import prisma from '@/db/prisma'

export async function getEtakstS3DocumentKey(estateId: string): Promise<
  | {
      s3_document_key: string
      last_changed?: string
    }
  | undefined
> {
  if (!estateId) {
    console.warn('getEtakstS3Document<PERSON><PERSON> called with invalid estateId')
    return undefined
  }

  const queue = await prisma.etakst_check_queue.findFirst({
    where: { estate_id: estateId },
    select: {
      s3_document_key: true,
      completed_at: true,
      last_checked_at: true,
    },
  })

  if (queue?.s3_document_key) {
    return {
      s3_document_key: queue.s3_document_key,
      last_changed:
        queue.completed_at?.toISOString() ??
        queue.last_checked_at?.toISOString(),
    }
  }

  const etakst = await prisma.estate_etakst.findFirst({
    where: { estate_id: estateId },
    orderBy: { created_at: 'desc' },
    select: { s3_document_key: true, updated_at: true },
  })

  if (etakst?.s3_document_key) {
    return {
      s3_document_key: etakst.s3_document_key,
      last_changed: etakst.updated_at?.toISOString(),
    }
  }

  return undefined
}
