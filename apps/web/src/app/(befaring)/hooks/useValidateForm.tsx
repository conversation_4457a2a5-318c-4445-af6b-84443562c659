import React, { useState } from 'react'
import { AnyObject, Maybe, ObjectSchema, ValidationError } from 'yup'

import { GQLBrokerEstate } from '@/api/generated-client'

export type ValidationErrorItem = {
  path: string
  message: string
  readableFieldName?: string
}

export type ValidationResult<T extends Maybe<AnyObject>> = {
  data: Partial<T>
  errors: ValidationErrorItem[] | null
}

const getErrors = <T extends Maybe<AnyObject>>(
  error: ValidationError,
  fieldsTranslation?: Partial<Record<keyof T, string>>,
) => {
  return error.inner.map((err) => ({
    path: err.path || '',
    message: err.message,
    readableFieldName: fieldsTranslation?.[err.path as keyof T] || err.path,
  }))
}

export function useValidateForm<T extends Maybe<AnyObject>>(
  estate: Pick<GQLBrokerEstate, 'ownership'>,
  schema: ObjectSchema<AnyObject>,
  options?: { fieldsTranslation: Partial<Record<keyof T, string>> },
): {
  validate: (data: Partial<T>) => Promise<ValidationResult<T>>
  validateSync: (data: Partial<T>) => ValidationResult<T>
  errors: ValidationErrorItem[] | null
} {
  const { fieldsTranslation } = options || {}
  const [errors, setErrors] = useState<ValidationErrorItem[] | null>(null)

  const validate = React.useCallback(
    async (
      data: Partial<T>,
    ): Promise<{ data: Partial<T>; errors: ValidationErrorItem[] | null }> => {
      try {
        await schema.validate(data, { abortEarly: false, context: { estate } })
        setErrors(null)
        return { data, errors: null }
      } catch (error) {
        if (error instanceof ValidationError) {
          console.error('Error in validate', error)
          const formattedErrors = getErrors(error, fieldsTranslation)
          setErrors(formattedErrors)
          return { data, errors: formattedErrors }
        } else {
          setErrors(null)
          return { data, errors: null }
        }
      }
    },
    [schema, fieldsTranslation, estate],
  )

  const validateSync = React.useCallback(
    (data: Partial<T>) => {
      try {
        schema.validateSync(data, { abortEarly: false, context: { estate } })
        setErrors(null)
        return { data, errors: null }
      } catch (error) {
        console.error('Error in validateSync', error)
        return {
          data,
          errors: getErrors(error, fieldsTranslation),
        }
      }
    },
    [schema, fieldsTranslation, estate],
  )

  return { validate, errors, validateSync }
}
