import { useRouter } from 'next/navigation'

import { useToast } from '@nordvik/ui/toaster'

import { OfferContact } from '@/components/send-offer-dialog/types'

import { updateContact } from '../actions/update-contact'
import { EditSellersFormValues } from '../components/seller-section/edit-seller-dialog'

export function useUpdateSeller(seller: OfferContact) {
  const { toast } = useToast()
  const router = useRouter()

  const {
    firstName,
    lastName,
    mobilePhone,
    email,
    postalAddress,
    postalCode,
    city,
    socialSecurity,
    proxy,
  } = seller

  async function handleSubmit(formData: EditSellersFormValues) {
    const updatedSellerContact = {
      // not editable
      contactId: seller.contactId,
      contactType: 0,
      firstName,
      lastName,
      socialSecurity,
      // editable
      mobilePhone: (formData.seller.mobilePhone ?? mobilePhone)?.replaceAll(
        ' ',
        '',
      ),
      email: formData.seller.email ?? email,
      postalAddress: formData.seller.postalAddress ?? postalAddress,
      postalCode: formData.seller.postalCode ?? postalCode,
      city: formData.seller.city ?? city,
    }

    const updatedProxyContact = proxy
      ? {
          // not editable
          contactId: proxy.contactId,
          contactType: 0,
          firstName: proxy.firstName,
          lastName: proxy.lastName,
          socialSecurity: proxy.socialSecurity,
          // editable
          mobilePhone: (
            formData.proxy?.mobilePhone ?? proxy.mobilePhone
          )?.replace(' ', ''),
          email: formData.proxy?.email ?? proxy.email,
          postalAddress: formData.proxy?.postalAddress ?? proxy.postalAddress,
          postalCode: formData.proxy?.postalCode ?? proxy.postalCode,
          city: formData.proxy?.city ?? proxy.city,
        }
      : undefined

    try {
      await Promise.all([
        updateContact(updatedSellerContact),
        proxy && updatedProxyContact && updateContact(updatedProxyContact),
      ])

      toast({
        title: 'Selger oppdatert',
        variant: 'success',
      })
    } catch (error) {
      toast({
        title: 'Kunne ikke oppdatere selger',
        variant: 'destructive',
      })
    }

    router.refresh()
  }
  return { handleSubmit }
}
