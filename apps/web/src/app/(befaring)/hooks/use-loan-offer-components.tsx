'use client'

import { getSignersData } from '@befaring/actions/sellers'
import { useQuery } from '@tanstack/react-query'

import { Label } from '@nordvik/ui/label'
import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'

export function useEligibleSigners(estateId: string, enabled = true) {
  return useQuery({
    enabled,
    queryKey: ['eligibleSigners', estateId],
    queryFn: () => getSignersData({ estateId, onlyUnique: true }),
  })
}

export function SingleContact(contact: {
  firstName?: string
  lastName?: string
}) {
  return (
    <span>
      Kontaktperson: {contact.firstName} {contact.lastName}
    </span>
  )
}

export function EligibleSigners({
  estateId,
  currentRecipient,
  onChange,
  isDisabled,
  titleClassName = 'font-medium mb-2',
}: {
  estateId: string
  currentRecipient?: string
  onChange: (value: string) => void
  isDisabled: boolean
  titleClassName?: string
}) {
  const { data } = useEligibleSigners(estateId)

  switch (data?.length) {
    case 0:
    case undefined:
      return null
    case 1:
      // return <SingleContact {...data[0]} />
      return null
    default:
      return (
        <div className="mt-4">
          <h4 className={titleClassName}>Velg kontaktperson</h4>
          <RadioGroup
            className="flex items-center gap-6 sm:flex-row flex-col"
            defaultValue={currentRecipient}
            onValueChange={onChange}
            disabled={isDisabled}
          >
            {data?.map((entry) => (
              <div
                className="flex items-center space-x-2"
                key={entry.contactId}
              >
                <RadioGroupItem value={entry.contactId} id={entry.contactId} />
                <Label
                  className="font-normal !typo-body-md"
                  htmlFor={entry.contactId}
                >
                  {[entry.firstName, entry.lastName].filter(Boolean).join(' ')}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
      )
  }
}

export async function getDefaultRecipient(
  signers: { contactId: string }[] | undefined,
  currentRecipient?: string,
) {
  // If there is only one eligible signer, we can return that contactId
  if (signers?.length === 1) {
    return signers[0].contactId
  }

  // If the recipient is already set, don't change it
  if (currentRecipient) {
    return undefined
  }

  return undefined
}
