'use client'

import { CustomerInfo } from '@befaring/context/budget-context'
import { BuyProcessStatus } from '@befaring/lib/budget-helpers'
import { StorebrandRequestSource } from '@befaring/verdivurdering/types'
import React, { startTransition } from 'react'

import { Prisma } from '@nordvik/database'

type SetCustomerInfoFunction = React.Dispatch<
  React.SetStateAction<CustomerInfo>
>

interface ServerUpdateData {
  loanOfferComment?: Prisma.JsonObject
  recipientLoanOffer?: string
  receiveLoanOffer?: boolean
  [key: string]: unknown
}

export function createLoanOfferHandlers({
  customerInfo,
  setCustomerInfo,
  source,
  onServerUpdate,
}: {
  customerInfo: CustomerInfo
  setCustomerInfo: SetCustomerInfoFunction

  source: StorebrandRequestSource
  onServerUpdate?: (updates: ServerUpdateData) => void
}) {
  const comment =
    (customerInfo.loan_offer_comment as {
      buy_process_status?: BuyProcessStatus | null
      has_co_buyer?: boolean | null
      source?: StorebrandRequestSource
    }) || {}

  const updateLoanOfferComment = (updates: Partial<typeof comment>) => {
    const newComment = {
      ...(customerInfo.loan_offer_comment as Prisma.JsonObject),
      ...updates,
      source,
    }

    startTransition(() => {
      setCustomerInfo((prevState) => ({
        ...prevState,
        loan_offer_comment: newComment,
      }))
    })

    // Call server update if provided
    if (onServerUpdate) {
      onServerUpdate({ loanOfferComment: newComment })
    }
  }

  const handleUpdateBuyProcess = (value: BuyProcessStatus) => {
    updateLoanOfferComment({ buy_process_status: value })
  }

  const handleUpdateHasCoBuyer = (value: string) => {
    updateLoanOfferComment({ has_co_buyer: value === 'true' })
  }

  return {
    comment,
    updateLoanOfferComment,
    handleUpdateBuyProcess,
    handleUpdateHasCoBuyer,
  }
}
