import { debounce } from 'lodash'
import { useRouter } from 'next/navigation'

import { useToast } from '@nordvik/ui/toaster'

import {
  EiendomsverdiLinkProps,
  getEiendomsverdiLink,
} from '@/external-services/eiendomsverdi/get-eiendomsverdi-link'

export const useEiendomsverdiLink = (estate: EiendomsverdiLinkProps) => {
  const { toast } = useToast()
  const router = useRouter()
  // Link gets cached for a few minutes
  const fetchLink = () =>
    getEiendomsverdiLink(estate).catch((error) => console.error(error))

  const handleOnClick = async () => {
    const link = await fetchLink()

    if (typeof link === 'string') {
      console.info('Opening Eiendomsverdi link')
      window.open(link, '_blank')
    }
  }

  const debouncedFetchLink = debounce(async () => {
    const link = await fetchLink()
    if (typeof link !== 'string') {
      toast({
        title: 'Fant ingen Eiendomsverdi lenke',
        variant: 'destructive',
      })
      return
    }
    if (typeof link === 'string') {
      router.prefetch(link)
    }
  }, 200)

  return {
    handleOnClick,
    debouncedFetchLink,
  }
}
