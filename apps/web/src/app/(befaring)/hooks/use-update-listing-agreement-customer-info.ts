'use client'

import {
  CustomerInfo,
  updateListingAgreementCustomerInfo,
} from '@befaring/actions/listing-agreement'
import { useRouter } from 'next/navigation'

import { useToast } from '@nordvik/ui/toaster'

export function useUpdateListingAgreementCustomerInfo(
  listingAgreementId: string,
  withRefresh = true,
) {
  const { toast } = useToast()
  const router = useRouter()
  return async (fields: Partial<CustomerInfo>, errorOnFail?: string) => {
    try {
      await updateListingAgreementCustomerInfo({
        listingAgreementId,
        ...fields,
      })
    } catch (error) {
      console.error(error)
      toast({
        title: errorOnFail ?? `Kunne ikke oppdatere`,
        variant: 'destructive',
      })
    }

    if (withRefresh) {
      router.refresh()
    }
  }
}
