import { useBudget } from '@befaring/context/budget-context'
import { BuyProcessStatus } from '@befaring/lib/budget-helpers'
import { StorebrandRequestSource } from '@befaring/verdivurdering/types'
import debounce from 'lodash/debounce'
import { useCallback, useMemo, useState } from 'react'

import { useUpdateListingAgreementCustomerInfo } from './use-update-listing-agreement-customer-info'

export const useLoanOffetCommentUpdate = (source: StorebrandRequestSource) => {
  const { listingAgreement, customerInfo } = useBudget()
  const updateInfo = useUpdateListingAgreementCustomerInfo(listingAgreement.id)

  const comment = useMemo(
    () =>
      (customerInfo.loan_offer_comment ?? {}) as {
        buy_process_status?: BuyProcessStatus
        has_co_buyer?: boolean
        source?: StorebrandRequestSource
      },
    [customerInfo.loan_offer_comment],
  )

  const [loanOfferComment, setLoanOfferComment] = useState<{
    buy_process_status?: BuyProcessStatus | null
    has_co_buyer?: boolean | null
    source?: StorebrandRequestSource
  }>(comment)

  const debouncedUpdateComment = useCallback(
    (newLoanOfferComment: {
      buy_process_status?: BuyProcessStatus | null
      has_co_buyer?: boolean | null
      source?: StorebrandRequestSource
    }) => {
      const debounceFn = debounce(async () => {
        await updateInfo({
          loanOfferComment: {
            buy_process_status: newLoanOfferComment.buy_process_status ?? null,
            has_co_buyer: newLoanOfferComment.has_co_buyer ?? null,
            source,
          },
        })
      }, 600)
      debounceFn()
    },
    [updateInfo, source],
  )

  const handleUpdateBuyProcess = (value: BuyProcessStatus) => {
    const newLoanOfferComment = {
      ...loanOfferComment,
      buy_process_status: value,
    }
    setLoanOfferComment(newLoanOfferComment)
    debouncedUpdateComment(newLoanOfferComment)
  }

  const handleUpdateHasCoBuyer = (value: string) => {
    const newLoanOfferComment = {
      ...loanOfferComment,
      has_co_buyer: value === 'true',
    }
    setLoanOfferComment(newLoanOfferComment)
    debouncedUpdateComment(newLoanOfferComment)
  }

  return {
    loanOfferComment,
    handleUpdateBuyProcess,
    handleUpdateHasCoBuyer,
  }
}
