import { updateContact } from '@befaring/actions/update-contact'
import { useRouter } from 'next/navigation'

import { useToast } from '@nordvik/ui/toaster'

import { NextPrivateContactWithProxy } from '@/actions/next/types-next'

import { EditCompanyFormValues } from '../components/seller-section/edit-company-dialog'

export function useUpdateCompany(company: NextPrivateContactWithProxy) {
  const { toast } = useToast()
  const router = useRouter()

  const { contactId, organisationNumber, postalAddress, postalCode, city } =
    company

  async function handleSubmit(formData: EditCompanyFormValues) {
    const updatedCompanyContact = {
      contactType: 1,
      contactId,
      organisationNumber: formData.organisationNumber ?? organisationNumber,
      postalAddress: formData.postalAddress ?? postalAddress,
      postalCode: formData.postalCode ?? postalCode,
      city: formData.city ?? city,
    }

    try {
      await updateContact(updatedCompanyContact, true)

      toast({
        title: '<PERSON>rma oppdatert',
        variant: 'success',
      })
    } catch (error) {
      toast({
        title: 'Kunne ikke oppdatere firma',
        variant: 'destructive',
      })
    }

    router.refresh()
  }

  return { handleSubmit }
}
