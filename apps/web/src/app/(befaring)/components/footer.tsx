'use client'

import { useBudgetPriceProxy } from '@befaring/context/budget-proxy-context'
import { AnimatePresence, Variants, motion } from 'framer-motion'
import { useParams, usePathname } from 'next/navigation'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Link } from '@nordvik/ui/global-navigation-progress/link'

import { LogoWithBorder } from '@/components/logo'
import { useUserContext } from '@/lib/UserContext'
import { formatCurrency } from '@/lib/formatCurrency'

const MotionLink = motion(Link)

const variants: Variants = {
  showPrice: {
    marginInline: 0,
    flexGrow: 1,
  },
  hidePrice: {
    marginInline: 'auto',
    flexGrow: 0,
  },
  showLinks: {
    transition: {
      staggerChildren: 0.015,
      staggerDirection: 1,
    },
  },
  hideLinks: {
    transition: {
      staggerChildren: 0.015,
      staggerDirection: -1,
    },
  },
}

export default function Footer({
  hasPublishedAgreement,
  links,
  rootPrefix,
  startLink,
}: {
  hasPublishedAgreement: boolean
  links: { label: string; href: string }[]
  rootPrefix: string
  startLink?: string
}) {
  const { user } = useUserContext()
  const scrollContainerRef = React.useRef<HTMLDivElement>(null)
  const params = useParams()
  const rootPathname = `${rootPrefix}/${params.estateId}`
  const pathname = usePathname()

  const { budgetTotalReduction, budgetGrandTotal } = useBudgetPriceProxy()
  const hideFooter = pathname.includes('suksess')
  const showListingPrice =
    rootPathname === pathname || `${rootPathname}/parter` === pathname

  const [hasMounted, setHasMounted] = React.useState(false)

  React.useEffect(() => {
    setHasMounted(true)
  }, [])

  React.useEffect(() => {
    // Hydration fix. Don't scroll to the end of the menu if the component has not mounted yet
    if (!hasMounted) return

    const scrollNode = scrollContainerRef.current
    if (
      scrollNode &&
      showListingPrice &&
      typeof budgetGrandTotal === 'number'
    ) {
      scrollNode.scrollTo({
        left: scrollNode.scrollWidth,
        behavior: 'smooth',
      })
    }
  }, [showListingPrice, budgetGrandTotal, hasMounted])

  if (!hasMounted || hideFooter) {
    return null
  }

  return (
    <motion.div
      data-theme="dark"
      layoutRoot
      className={cn(
        'fixed h-[--footer-height,8rem] inset-x-0 bottom-0 flex items-center justify-center pointer-events-none',
      )}
    >
      <motion.div
        layoutScroll
        ref={scrollContainerRef}
        style={{ WebkitOverflowScrolling: 'touch' }}
        className="flex h-[3.75rem] overflow-x-auto whitespace-nowrap grow max-w-screen-2xl hide-scrollbar px-[--container-padding] pointer-events-none [@media(pointer:coarse)]:pointer-events-auto"
      >
        <motion.div
          className="bg-root-muted flex rounded-sm pointer-events-auto"
          variants={variants}
          whileHover="showLinks"
          initial={false}
          transition={{ type: 'spring', stiffness: 250, damping: 30 }}
          animate={showListingPrice ? 'showPrice' : 'hidePrice'}
          translate="no"
        >
          {startLink && <StartLink href={`${rootPathname}/${startLink}`} />}

          <div className="border-l h-[44%] self-center border-l-muted" />

          <div className="flex gap-4 px-4">
            {links.map((link) => (
              <PageLink key={link.href} href={`${rootPathname}/${link.href}`}>
                {link.label}
              </PageLink>
            ))}
          </div>

          <div className="ml-auto self-stretch overflow-hidden flex">
            <AnimatePresence initial={false} mode="popLayout">
              {showListingPrice ? (
                <motion.div
                  key="price"
                  initial={{ x: '20px', opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ type: 'spring', stiffness: 150, damping: 20 }}
                  className="flex items-center pl-5 gap-3 pr-5 pb-px justify-end max-sm:min-w-[calc(100vw-3rem-var(--container-padding)*2)]"
                >
                  <div className="typo-body-md">
                    <span className="font-medium">Totalpris</span>{' '}
                    <span className="ink-muted">inkl. mva.</span>
                  </div>
                  <div className="flex gap-2">
                    {typeof budgetTotalReduction === 'number' &&
                      budgetTotalReduction !== 0 && (
                        <span className="ink-muted">
                          ({formatCurrency(budgetTotalReduction)})
                        </span>
                      )}
                    <span>{formatCurrency(budgetGrandTotal || 0)}</span>
                  </div>
                </motion.div>
              ) : hasPublishedAgreement || user ? (
                <MotionLink
                  key="listing"
                  href={rootPathname}
                  initial={{ x: '20px', opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ type: 'spring', stiffness: 150, damping: 20 }}
                  className="my-2.5 mx-4 ink-[black] bg-gold-emphasis flex items-center rounded-sm typo-label-md px-3 py-2.5"
                  translate="no"
                >
                  Oppdragsavtale
                </MotionLink>
              ) : null}
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  )
}

function StartLink({ href }) {
  const pathname = usePathname()
  const isActive = pathname === href
  return (
    <Link
      className={cn(
        'p-4 hover:ink-gold relative',
        isActive ? 'ink-gold' : 'ink-muted',
      )}
      href={href}
      translate="no"
    >
      {isActive && (
        <motion.div
          layoutId="page-active-line"
          style={{ height: 0 }}
          transition={{ type: 'spring', stiffness: 250, damping: 20 }}
          className="absolute top-0 inset-x-0"
        />
      )}
      <LogoWithBorder />
    </Link>
  )
}

function PageLink({
  href,
  children,
}: {
  href: string
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const isActive = pathname === href

  return (
    <Link
      aria-current={isActive ? 'page' : undefined}
      className="flex flex-col justify-center relative typo-detail-md ink-muted transition-colors hover:ink-default aria-[current=page]:ink-default"
      href={href}
      translate="no"
      onClick={(event) => {
        // Prevent browser translation from interfering with navigation
        event.stopPropagation()

        // Scroll into view
        event.currentTarget.scrollIntoView({
          behavior: 'smooth',
          inline: 'center',
        })
      }}
    >
      {isActive && (
        <motion.div
          layoutId="page-active-line"
          transition={{ type: 'spring', stiffness: 200, damping: 30 }}
          className="bg-gold-emphasis h-0.5 absolute top-0 inset-x-0"
        />
      )}
      {children}
    </Link>
  )
}
