import { SubSection } from '@befaring/components/sub-section'
import { BuyProcessStatus } from '@befaring/lib/budget-helpers'
import { HandCoinsIcon, HomeIcon, SearchIcon } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { ChoiceCards } from '@nordvik/ui/choice-card'

import { Illustration } from '@/components/illustration'

type StorebrandCheckbox = {
  label: string
  icon: React.ReactNode
  name: string
  value: string
}

export const valuationCheckboxes: StorebrandCheckbox[] = [
  {
    name: BuyProcessStatus.FINANCING,
    value: BuyProcessStatus.FINANCING,
    label: 'Jeg skal refinansiere',
    icon: <HandCoinsIcon />,
  },
  {
    name: BuyProcessStatus.LOOKING,
    value: BuyProcessStatus.LOOKING,
    label: 'Jeg er på boligjakt',
    icon: <SearchIcon />,
  },
  {
    name: BuyProcessStatus.BIDDING,
    value: BuyProcessStatus.BIDDING,
    label: 'Jeg skal i budrunde',
    icon: <Illustration name="bidding" className="size-4" />,
  },
  {
    name: BuyProcessStatus.BOUGHT,
    value: BuyProcessStatus.BOUGHT,
    label: 'Jeg har allerede kjøpt',
    icon: <HomeIcon />,
  },
]

const oppdragsavtaleCheckboxes: StorebrandCheckbox[] = [
  {
    name: BuyProcessStatus.LOOKING,
    value: BuyProcessStatus.LOOKING,
    label: 'Jeg er på boligjakt',
    icon: <SearchIcon />,
  },
  {
    name: BuyProcessStatus.BIDDING,
    value: BuyProcessStatus.BIDDING,
    label: 'Jeg skal i budrunde',
    icon: <Illustration name="bidding" className="size-4" />,
  },
  {
    name: BuyProcessStatus.BOUGHT,
    value: BuyProcessStatus.BOUGHT,
    label: 'Jeg har allerede kjøpt',
    icon: <HomeIcon />,
  },
]

export function StorebrandAgreementForm({
  buyProcessStatus,
  hasCoBuyer,
  isDisabled,
  handleUpdateBuyProcess,
  handleUpdateHasCoBuyer,
  isValuation,
}: {
  buyProcessStatus?: BuyProcessStatus | null
  hasCoBuyer?: boolean | null
  isDisabled: boolean
  handleUpdateBuyProcess: (value: BuyProcessStatus) => void
  handleUpdateHasCoBuyer: (value: string) => void
  isValuation: boolean
}) {
  const saleProcessCheckboxes = isValuation
    ? valuationCheckboxes
    : oppdragsavtaleCheckboxes

  const coBuyerCheckboxes = [
    {
      label: 'Ja',
      value: 'true',
      name: 'hasCoBuyer',
      className: cn('px-6'),
    },
    {
      label: 'Nei',
      value: 'false',
      name: 'hasCoBuyer',
      className: cn('px-6'),
    },
  ]
  return (
    <>
      <SubSection title="Hvor er du i kjøpsprosessen?" className="mt-6">
        <ChoiceCards
          value={buyProcessStatus ?? ''}
          onChange={handleUpdateBuyProcess}
          items={saleProcessCheckboxes}
          className={cn('grid grid-cols-1 gap-4', {
            'grid-cols-1': saleProcessCheckboxes.length === 1,
            '@md/section:grid-cols-2': saleProcessCheckboxes.length === 2,
            '@2xl/section:grid-cols-3': saleProcessCheckboxes.length === 3,
            '@md/section:grid-cols-2 @4xl/section:grid-cols-4':
              saleProcessCheckboxes.length === 4,
          })}
          variant="card"
          withRadio={false}
          disabled={isDisabled}
        />
      </SubSection>
      <SubSection title="Skal du kjøpe sammen med noen?" className="mt-6">
        <ChoiceCards
          value={hasCoBuyer?.toString() ?? ''}
          onChange={handleUpdateHasCoBuyer}
          items={coBuyerCheckboxes}
          className="flex gap-2 w-max"
          variant="chip"
          size="sm"
          withRadio={false}
          disabled={isDisabled}
        />
      </SubSection>
    </>
  )
}
