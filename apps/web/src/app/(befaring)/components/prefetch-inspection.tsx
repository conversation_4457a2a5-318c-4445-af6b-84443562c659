'use client'

import { PrefetchKind } from 'next/dist/client/components/router-reducer/router-reducer-types'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

import { useEstateId } from '@/hooks/use-estateid'

const prefetchOptions = {
  kind: PrefetchKind.FULL,
}

export enum PrefetchPages {
  Fremdriftsplan = 'fremdriftsplan',
  Salgsprosess = 'salgsprosess',
  Meg<PERSON> = 'megler',
  Omrade = 'omrade',
  Start = 'start',
  DinBolig = 'din-bolig',
}

export function PrefetchInspectionPages({
  rootPrefix,
  pages,
}: {
  rootPrefix: string
  pages: (typeof PrefetchPages)[keyof typeof PrefetchPages][]
}) {
  const router = useRouter()
  const estateId = useEstateId()

  useEffect(() => {
    if (!estateId) return
    const baseUrl = `${rootPrefix}/${estateId}`
    pages.forEach((page) => {
      router.prefetch(`${baseUrl}/${page}`, prefetchOptions)
    })
  }, [router, estateId, rootPrefix, pages])

  return null
}
