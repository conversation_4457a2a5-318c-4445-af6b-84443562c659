import { Edit2 } from 'lucide-react'

import { Switch } from '@nordvik/ui/switch'

export function EditModeSwitch({
  editMode,
  setEditMode,
}: {
  editMode: boolean
  setEditMode: (editMode: boolean) => void
}) {
  return (
    <>
      <Switch
        size="lg"
        variant="success"
        checked={editMode}
        onClick={(e) => e.stopPropagation()}
        onCheckedChange={setEditMode}
        className="max-sm:hidden"
      >
        <Edit2 size={16} />
      </Switch>
      <Switch
        size="sm"
        variant="success"
        checked={editMode}
        onClick={(e) => e.stopPropagation()}
        onCheckedChange={setEditMode}
        className="sm:hidden"
      >
        <Edit2 size={12} />
      </Switch>
    </>
  )
}
