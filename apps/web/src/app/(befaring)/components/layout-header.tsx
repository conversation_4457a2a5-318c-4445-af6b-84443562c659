'use client'

import { ListingAgreementResponse } from '@befaring/actions/types'
import {
  ArrowLeftIcon,
  FullscreenIcon,
  MinimizeIcon,
  NotebookPenIcon,
  PencilIcon,
} from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import * as Sheet from '@nordvik/ui/sheet'

import { NextPrivateContact } from '@/actions/next/types-next'
import {
  GQLAgreementAndInspectionQuery,
  GQLListingAgreementStatus,
} from '@/api/generated-client'
import { EditInspectionSheet } from '@/components/edit-inspection/edit-inspection-sheet'
import ProfileMenu from '@/components/profile-menu/profile-menu'
import { SendOfferDialog } from '@/components/send-offer-dialog/send-offer-dialog'
import { useUserContext } from '@/lib/UserContext'

import { NotesSheetContent } from './notes-sheet-content'
import { useFullscreen } from './use-fullscreen'

export function Header({
  estateId,
  isValuation,
  sendOfferData,
}: {
  estateId: string
  isValuation: boolean
  sendOfferData: {
    estate: GQLAgreementAndInspectionQuery['estate']
    listingAgreement?: ListingAgreementResponse | null
    signersData?: NextPrivateContact[]
  }
}) {
  const { user } = useUserContext()
  const { isFullscreen, toggleFullscreen } = useFullscreen()
  const [showNotes, setShowNotes] = React.useState(false)

  return (
    <header
      data-theme="dark"
      className={cn(
        'px-[--container-padding] py-2 bg-root flex items-center border-b border-b-muted',
        isFullscreen &&
          'absolute top-1 left-0 opacity-40 transition-opacity duration-300 hover:opacity-100 w-full z-10 bg-transparent border-b-0',
      )}
    >
      {!isFullscreen && (
        <Button
          href={`/oppdrag/detaljer/${estateId}`}
          iconStart={<ArrowLeftIcon />}
          className="px-0"
          variant="unstyled"
          size="lg"
        >
          <span className="max-md:sr-only">Tilbake til oppdrag</span>
        </Button>
      )}
      <div className="flex ml-auto items-center gap-2">
        <Button
          variant="ghost"
          iconOnly={<NotebookPenIcon />}
          onClick={() => setShowNotes(true)}
          size="sm"
        >
          Notater
        </Button>
        {!isFullscreen && (
          <EditInspectionSheet
            estateId={estateId}
            isValuation={isValuation}
            modal={false} // Enable to scroll the page when editing
            trigger={
              <Button variant="ghost" iconOnly={<PencilIcon />} size="sm">
                Rediger
              </Button>
            }
          />
        )}
        <Button
          variant="ghost"
          iconOnly={isFullscreen ? <MinimizeIcon /> : <FullscreenIcon />}
          onClick={toggleFullscreen}
          size="sm"
        >
          {isFullscreen ? 'Avslutt fullskjerm' : 'Vis i fullskjerm'}
        </Button>
        {!isFullscreen && (
          <div className="flex items-center gap-2">
            <SendOfferButton
              estate={sendOfferData.estate}
              signersData={sendOfferData.signersData}
              listingAgreementBudget={sendOfferData.listingAgreement}
            />

            <ProfileMenu user={user} />
          </div>
        )}
      </div>
      <Sheet.Sheet open={showNotes} onOpenChange={setShowNotes}>
        <Sheet.SheetPortal>
          <NotesSheetContent estateId={estateId} />
        </Sheet.SheetPortal>
      </Sheet.Sheet>
    </header>
  )
}

function SendOfferButton({
  estate,
  signersData,
  listingAgreementBudget,
}: {
  estate: GQLAgreementAndInspectionQuery['estate']
  signersData?: NextPrivateContact[]
  listingAgreementBudget?: ListingAgreementResponse | null
}) {
  const status = estate?.listingAgreement?.status
  const isLocked = status
    ? [
        GQLListingAgreementStatus.PartialSigned,
        GQLListingAgreementStatus.Signed,
        GQLListingAgreementStatus.SignedViaNext,
        GQLListingAgreementStatus.Signing,
        GQLListingAgreementStatus.Expired,
      ].includes(status)
    : true
  if (!signersData || isLocked) return null

  return (
    <SendOfferDialog
      estate={estate}
      sellers={signersData}
      listingAgreementBudget={listingAgreementBudget}
      triggerProps={{
        variant: 'outline',
        size: 'sm',
      }}
      triggerText="Del"
    />
  )
}
