'use client'

import { CachedEstate } from '@befaring/lib/cached-estate'
import { CachedLatestEtakstDocMeta } from '@befaring/lib/cached-latest-etakst-doc-meta'
import { useTrackListingAgreement } from '@befaring/lib/track-listing-agreement'
import { EtakstFileBanner } from '@befaring/verdivurdering/[estateId]/din-bolig/components/valuation-header/etakst-file-banner'

export function EtakstBannerStart({
  estate,
  etakst,
  getEtakstBlob,
  logInteractionWithDocument,
}: {
  estate: CachedEstate
  etakst: CachedLatestEtakstDocMeta
  getEtakstBlob: () => Promise<Blob | null>
  logInteractionWithDocument: () => Promise<void>
}) {
  const trackEvent = useTrackListingAgreement(estate)
  return (
    <div className="text-left w-full">
      <EtakstFileBanner
        lastChanged={etakst.lastChanged}
        getDocumentURL={async () => {
          const blob = await getEtakstBlob()
          if (!blob) return null
          return URL.createObjectURL(blob)
        }}
        logInteractionWithDocument={async () => {
          await logInteractionWithDocument()
          trackEvent('listing_agreement_etakst_clicked')
        }}
      />
    </div>
  )
}
