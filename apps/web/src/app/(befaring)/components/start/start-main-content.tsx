import { getEtakstBlob } from '@befaring/actions/get-etakst-blob'
import { cachedEstate } from '@befaring/lib/cached-estate'
import { cachedLatestEtakstDocMeta } from '@befaring/lib/cached-latest-etakst-doc-meta'
import { ArrowRightIcon } from 'lucide-react'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { TextButton } from '@nordvik/ui/text-button'

import { logListingAgreementInteraction } from '@/actions/log-listing-agreement-interaction'
import { riskCompletionCheck } from '@/actions/risk-check-completed'
import {
  GQLListingAgreementInteractionType,
  GQLListingAgreementStatus,
} from '@/api/generated-client'
import { Logo } from '@/components/logo'
import prisma from '@/db/prisma'
import { getCurrentUser } from '@/lib/session'

import { EtakstBannerStart } from './etakst-banner-start'

export async function StartMainContent({ estateId }: { estateId: string }) {
  const [estate, inspection, user, etakst, isRiskCheckComplete, cookieStore] =
    await Promise.all([
      cachedEstate(estateId),
      prisma.inspection_folders.findUnique({
        where: { estate_id: estateId },
        select: { listing_agreement_active: true },
      }),
      getCurrentUser(),
      cachedLatestEtakstDocMeta(estateId),
      riskCompletionCheck(estateId).then(({ isComplete }) => isComplete),
      cookies(),
    ])

  const cidCookie = cookieStore.get('cid')?.value
  const ecidCookie = cookieStore.get('ecid')?.value

  if (!estate) {
    return notFound()
  }

  const getEtakstBlobAction = async () => {
    'use server'
    return getEtakstBlob(estateId)
  }

  const logInteractionWithDocument = async () => {
    'use server'
    if (!user && estate.listingAgreement?.id) {
      await logListingAgreementInteraction({
        event_type: GQLListingAgreementInteractionType.EtakstClicked,
        listing_agreements_id: estate.listingAgreement.id,
        seller_id: cidCookie || ecidCookie,
      })
    }
  }

  const linkToAgreement = inspection?.listing_agreement_active && !user
  const broker = estate.mainBroker
  const showPrivacyDisclaimer = !user

  const etakstAvailable = etakst?.lastChanged && isRiskCheckComplete

  const isOnlyVerdivurdering =
    estate.listingAgreement?.status !== GQLListingAgreementStatus.Signed &&
    etakstAvailable

  return (
    <main
      className={cn(
        'relative md:bg-root rounded-md overflow-clip text-center w-[44rem] max-w-full flex flex-col items-center gap-4 md:gap-8 mb-[--footer-height] md:mt-10',
        showPrivacyDisclaimer ? 'pt-16' : 'py-16',
      )}
    >
      <div className="flex flex-col gap-3 items-center px-[--container-padding]">
        <h1 className="typo-detail-md ink-gold text-pretty">
          et godt boligsalg
        </h1>
        <h2 className="typo-display-lg md:typo-display-xl text-balance">
          {estate.address?.streetAddress}
        </h2>
        <p className="typo-body-md ink-subtle text-balance max-w-[28rem]">
          Her finner du alt om salgsprosessen. Sammen legger vi en plan for å få
          best pris for boligen din.
        </p>
      </div>
      <div className="flex flex-col max-w-[17rem] w-full mx-auto px-[--container-padding]">
        {broker && broker.name && (
          <div className="flex flex-col items-center">
            <Avatar
              imageUrl={broker.image?.medium}
              className="size-20 md:size-24 mb-3"
            />
            <div className="typo-body-md">{broker.name}</div>
            <div className="typo-body-sm ink-subtle">{broker.title}</div>
          </div>
        )}
      </div>
      <div className="flex flex-col self-stretch px-[--container-padding] max-w-[28rem] mx-auto w-full">
        {etakstAvailable && (
          <EtakstBannerStart
            estate={estate}
            etakst={etakst}
            getEtakstBlob={getEtakstBlobAction}
            logInteractionWithDocument={logInteractionWithDocument}
          />
        )}
        {!isOnlyVerdivurdering &&
          (linkToAgreement ? (
            <div className="flex flex-col gap-4 items-center mt-8 border-t border-muted pt-3 md:pt-4 self-stretch">
              <div className="typo-body-md">Se oppdragsavtale</div>
              <Button
                href={`/oppdragsavtale/${estateId}`}
                iconOnly={<ArrowRightIcon />}
                variant="unstyled"
                className="ink-[black] bg-gold-emphasis"
                size="lg"
              >
                Til oppdragsavtalen
              </Button>
            </div>
          ) : (
            <div className="flex flex-col gap-4 items-center mt-8">
              <Button
                href="./salgsprosess"
                iconOnly={<ArrowRightIcon />}
                size="lg"
              >
                Gå videre
              </Button>
            </div>
          ))}
      </div>
      {showPrivacyDisclaimer && (
        <p className="ink-muted px-[--container-padding]  typo-body-sm text-balance mt-10 md:bg-[#00262A] self-stretch py-2.5">
          Ved å gå videre godtar du våre{' '}
          <TextButton
            href="https://www.nordvikbolig.no/personvern"
            className="inline-block"
            target="_blank"
          >
            vilkår for bruk
          </TextButton>
        </p>
      )}
    </main>
  )
}

function Avatar({
  imageUrl,
  className,
}: {
  imageUrl?: string | null
  className?: string
}) {
  const usePlaceholder = !imageUrl || imageUrl.includes('placeholder')
  return (
    <div
      className={cn(
        'w-full aspect-square rounded-full overflow-clip',
        className,
      )}
    >
      {usePlaceholder ? (
        <PlaceholderImage />
      ) : (
        <img src={imageUrl} alt="" className="size-full object-cover" />
      )}
    </div>
  )
}

function PlaceholderImage() {
  return (
    <div className="size-full aspect-square flex bg-brand-muted fill-gold-emphasis">
      <div className="m-[30%]">
        <Logo className="size-full" />
      </div>
    </div>
  )
}
