'use client'

import { BrokerRole } from '@befaring/lib/broker-constants'
import isObject from 'lodash/isObject'
import Image from 'next/image'

import { Avatar, AvatarFallback } from '@nordvik/ui/avatar'

import type { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { Logo } from '@/components/logo'
import { formatPhoneNumber } from '@/lib/format-phone-number'
import { getBrokerInitials } from '@/lib/getBrokerInitials'
import { sortBySpecificOrder } from '@/lib/sort-by-specific-order'

import { useBudget } from '../context/budget-context'

export function BrokersInfoSection({
  estate,
}: {
  estate: GQLGetBrokerEstateQuery['estate']
}) {
  const { brokers } = useBudget()

  if (!estate) return null
  const { department } = estate

  const reorderedBrokers = sortBySpecificOrder(
    brokers ?? [],
    [
      BrokerRole.Main,
      BrokerRole.Responsible,
      BrokerRole.Assistant,
      BrokerRole.SecondaryAssistant,
      BrokerRole.Settlement,
    ],
    'role',
  )
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-5 gap-y-8">
      {isObject(department) && (
        <ContactCard name={department.name} office>
          <span>Organisasjonsnummer: {department.organisationNumber}</span>
          {department.phone && <span>Telefon: {department.phone}</span>}
        </ContactCard>
      )}
      {reorderedBrokers.map((broker) => (
        <ContactCard
          key={`broker-${broker.name}-${broker.role}`}
          name={broker.name}
          imageSrc={broker.imageSrc}
          label={broker.roleName}
        >
          <span className="line-clamp-1">{broker.title}</span>
          <span>Telefon: {formatPhoneNumber(broker.mobilePhone)}</span>
          <span>E-post: {broker.email}</span>
        </ContactCard>
      ))}
    </div>
  )
}

function ContactCard({
  name,
  label,
  office,
  imageSrc,
  children,
}: {
  name: string | null | undefined
  label?: string
  office?: boolean
  imageSrc?: string | null
  children?: React.ReactNode
}) {
  const initials = getBrokerInitials(name ?? '')
  return (
    <div className="flex gap-4">
      <Avatar className="size-10 bg-brand-subtle flex-center">
        {office !== undefined && (
          <Logo className="size-3.5 [&>*]:fill-[white]" />
        )}
        {!office &&
          (imageSrc ? (
            <Image src={imageSrc} alt="logo" width={40} height={40} />
          ) : (
            <AvatarFallback className="typo-label-md">
              {initials}
            </AvatarFallback>
          ))}
      </Avatar>

      <div className="typo-body-md flex flex-col">
        <span className="typo-label-md uppercase ink-subtle">
          {office ? 'Kontor' : label}
        </span>
        <span className="font-medium">{name}</span>
        {children}
      </div>
    </div>
  )
}
