import React from 'react'

import { cn } from '@nordvik/theme/cn'

export function Section({
  children,
  className,
  title,
}: {
  children: React.ReactNode
  className?: string
  title?: string
}) {
  return (
    <div
      id={title}
      className={cn(
        'text-pretty sm:rounded-lg bg-root py-6 px-[--padding-x]',
        className,
      )}
    >
      {title !== undefined && <h2 className="typo-display-sm mb-6">{title}</h2>}
      {children}
    </div>
  )
}
