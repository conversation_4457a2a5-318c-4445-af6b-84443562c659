import { cva } from 'class-variance-authority'

import { formatCurrency } from '@/lib/formatCurrency'

export default function SectionTotalRow({
  total,
  title,
  description,
  disabled = false,
}: {
  total: number
  title: string
  description?: string
  disabled?: boolean
}) {
  const wrapperVariants = cva(
    'flex items-center justify-between gap-2 flex-wrap rounded-b-lg px-[--padding-x] py-3',
    {
      variants: {
        disabled: {
          false: 'bg-success-muted border-t border-t-[transparent]',
          true: 'bg-root border-t border-muted',
        },
      },
      defaultVariants: {
        disabled: false,
      },
    },
  )

  const textVariants = cva('typo-body-md', {
    variants: {
      disabled: {
        false: 'ink-default',
        true: 'ink-disabled',
      },
      size: {
        md: 'typo-body-sm',
        lg: 'typo-label-lg',
      },
    },
    defaultVariants: {
      disabled: false,
      size: 'md',
    },
  })

  const currencyVariants = cva(
    'typo-body-md font-medium whitespace-nowrap ml-auto',
    {
      variants: {
        disabled: {
          false: 'ink-on-success-muted',
          true: 'ink-muted',
        },
      },
      defaultVariants: {
        disabled: false,
      },
    },
  )

  return (
    <div className={wrapperVariants({ disabled })}>
      <div className="flex flex-col">
        <span
          className={textVariants({
            disabled,
            size: 'lg',
            className: 'font-medium',
          })}
        >
          {title}
        </span>
        <span className={textVariants({ disabled, size: 'lg' })}>
          {description}
        </span>
      </div>
      <span className={currencyVariants({ disabled })}>
        {formatCurrency(total)}
      </span>
    </div>
  )
}
