import { cn } from '@nordvik/theme/cn'

export function SubSection({
  title,
  children,
  className,
  loading,
}: {
  title: string
  children: React.ReactNode
  className?: string
  loading?: boolean
}) {
  return (
    <div className={cn(className)}>
      <h3
        className={cn(
          'typo-title-xs mb-4',
          loading && 'masked-placeholder-text',
        )}
      >
        {title}
      </h3>
      {children}
    </div>
  )
}
