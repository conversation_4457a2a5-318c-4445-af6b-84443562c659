import React from 'react'

const getFullscreenState = () => !!document.fullscreenElement

export function useFullscreen() {
  const [isFullscreen, setIsFullscreen] = React.useState(() =>
    typeof document !== 'undefined' ? getFullscreenState() : false,
  )

  React.useEffect(() => {
    const handleFullscreenChange = () => setIsFullscreen(getFullscreenState)
    if (document.fullscreenEnabled) handleFullscreenChange()

    document.addEventListener('fullscreenchange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  function toggleFullscreen() {
    const canFullscreen = !!document.fullscreenEnabled
    const isRealFullscreen = !!document.fullscreenElement
    const shouldNotFullscreen = getDeviceType() !== 'desktop'

    if (canFullscreen && (!shouldNotFullscreen || isRealFullscreen)) {
      if (isFullscreen) document.exitFullscreen()
      else document.documentElement.requestFullscreen()
    } else {
      setIsFullscreen(!isFullscreen)
    }
  }

  return {
    isFullscreen,
    toggleFullscreen,
  }
}

const getDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
  const ua = navigator.userAgent
  const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  const isTablet = /iPad|Android(?!.*Mobile)/i.test(ua)
  const isMobile = /iPhone|Android.*Mobile/i.test(ua)

  // Extended tablet detection (including large tablets like iPad Pro)
  if (isTablet || (isTouch && screen.width >= 768 && screen.width <= 1366)) {
    return 'tablet'
  }

  if (isMobile || (isTouch && screen.width < 768)) {
    return 'mobile'
  }

  return 'desktop'
}
