'use client'

import { formatSecuredSocialSecurity } from '@befaring/actions/utils'
import { validateSellerPhoneNumber } from '@befaring/lib/check-valid-fields'
import { AlertTriangle } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { TextButton } from '@nordvik/ui/text-button'

import { getContact } from '@/actions/next/contact-get'
import type { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import { UserAvatar } from '@/components/user-avatar'

import { useUpdateSeller } from '../../hooks/use-update-seller'

import EditSellerDialog, { EditSellersFormValues } from './edit-seller-dialog'

export default function SellerInfoCard({
  seller: initialSeller,
  canEdit,
}: {
  seller: NextPrivateContactWithProxy
  canEdit: boolean
}) {
  const [seller, setSeller] = React.useState(initialSeller)

  const {
    firstName,
    lastName,
    mobilePhone,
    email,
    postalAddress,
    postalCode,
    city,
    socialSecurity,
  } = seller

  const { handleSubmit } = useUpdateSeller(seller)

  const onSubmit = async (data: EditSellersFormValues) => {
    await handleSubmit(data)
    setSeller((prev) => ({ ...prev, ...data.seller }))
    setTimeout(async () => {
      try {
        const updatedContact = await getContact(seller.contactId)
        if (updatedContact) {
          setSeller(updatedContact)
        }
      } catch (error) {
        console.error(error)
      }
    })
  }

  const missingDataTag = <span className="ink-danger"> Mangler</span>

  const hasValidPhoneNumber = validateSellerPhoneNumber(seller.mobilePhone)

  return (
    <div className="flex gap-4">
      <UserAvatar user={{ name: `${firstName} ${lastName}` }} />
      <div className="flex flex-col items-start gap-2">
        <div className="typo-body-md flex flex-col gap-1">
          <span className="typo-body-md font-medium">
            {firstName && lastName
              ? `${firstName} ${lastName}`
              : missingDataTag}
          </span>
          <span>
            Mobil:{' '}
            <span
              className={cn(
                'inline-flex items-center gap-1',
                hasValidPhoneNumber ? '' : 'ink-danger',
              )}
            >
              {mobilePhone ?? missingDataTag}{' '}
              {hasValidPhoneNumber ? null : (
                <AlertTriangle className="size-4" />
              )}
            </span>
          </span>
          <span>E-post: {email ?? missingDataTag}</span>
          <span>
            Adresse:{' '}
            {postalAddress && postalCode && city
              ? `${postalAddress}, ${postalCode} ${city}`
              : missingDataTag}
          </span>
          <span>
            Fødselsnummer:{' '}
            {socialSecurity
              ? formatSecuredSocialSecurity(socialSecurity)
              : missingDataTag}
          </span>
          {seller.proxy && (
            <div className="border-y border-muted py-3.5 mt-1.5 ">
              <span className="font-medium">Fullmektig:</span>{' '}
              {seller.proxy.firstName} {seller.proxy.lastName}
            </div>
          )}
        </div>
        {canEdit && (
          <EditSellerDialog
            seller={seller}
            handleSubmit={onSubmit}
            buttonComponent={<TextButton>Rediger</TextButton>}
          />
        )}
      </div>
    </div>
  )
}
