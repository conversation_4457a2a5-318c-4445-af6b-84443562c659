'use client'

import { yupResolver } from '@hookform/resolvers/yup'
import isEmpty from 'lodash/isEmpty'
import React from 'react'
import { UseControllerProps, useController, useForm } from 'react-hook-form'
import * as yup from 'yup'

import { But<PERSON> } from '@nordvik/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { Input } from '@nordvik/ui/input'
import { useToast } from '@nordvik/ui/toaster'

import { OfferContact } from '@/components/send-offer-dialog/types'
import { useUserContext } from '@/lib/UserContext'

import { DisabledFieldsInfo } from './disabled-fields-info-alert'

export type EditSellersFormValues = {
  seller: typeof sellerSchema.__outputType
  proxy?: typeof sellerSchema.__outputType
}

const sellerSchema = yup.object().shape({
  socialSecurity: yup.string().required('Mangler fødselsnummer'),
  email: yup.string().required('Mangler e-post'),
  mobilePhone: yup
    .string()
    .matches(/^\+?[1-9]\d{0,14}(?:\s\d{1,14})*$/, 'Ugyldig telefonnummer')
    .required('Mangler telefonnummer'),
  firstName: yup.string().required('Mangler fornavn'),
  lastName: yup.string().required('Mangler etternavn'),
  postalAddress: yup.string().required('Mangler adresse'),
  postalCode: yup.string().required('Mangler postnummer'),
  city: yup.string().required('Mangler poststed'),
})

export const onlySellerSchema = yup.object().shape({
  seller: sellerSchema,
})

const sellerWithProxySchema = yup.object().shape({
  seller: sellerSchema,
  proxy: sellerSchema,
})

const getDefaultValues = (seller: OfferContact): EditSellersFormValues => {
  return {
    seller: {
      firstName: seller.firstName ?? '',
      lastName: seller.lastName ?? '',
      mobilePhone: seller.mobilePhone ?? '',
      email: seller.email ?? '',
      postalAddress: seller.postalAddress ?? '',
      postalCode: seller.postalCode ?? '',
      socialSecurity: seller.socialSecurity ?? '',
      city: seller.city ?? '',
    },
    proxy: seller.proxy
      ? {
          firstName: seller.proxy.firstName ?? '',
          lastName: seller.proxy.lastName ?? '',
          mobilePhone: seller.proxy.mobilePhone ?? '',
          email: seller.proxy.email ?? '',
          postalAddress: seller.proxy.postalAddress ?? '',
          postalCode: seller.proxy.postalCode ?? '',
          socialSecurity: seller.proxy.socialSecurity ?? '',
          city: seller.proxy.city ?? '',
        }
      : undefined,
  }
}

export default function EditSellerDialog({
  seller,
  buttonComponent,
  title = 'Rediger informasjon',
  handleSubmit,
}: {
  seller: OfferContact
  buttonComponent: React.ReactNode
  title?: string
  handleSubmit: (formData: EditSellersFormValues) => void
}) {
  const { user } = useUserContext()
  const {
    handleSubmit: handleSubmitHookForm,
    formState,
    reset,
    control,
  } = useForm<EditSellersFormValues>({
    resolver: yupResolver(
      seller.proxy ? sellerWithProxySchema : onlySellerSchema,
    ),
    defaultValues: getDefaultValues(seller),
  })

  const [open, setOpen] = React.useState(false)
  const { toast } = useToast()

  async function onSubmit(formData: EditSellersFormValues) {
    try {
      if (!isEmpty(formState.errors)) {
        throw new Error()
      }

      await handleSubmit(formData)
      setTimeout(() => {
        setOpen(false)
      })
    } catch (error) {
      toast({
        title: 'Kunne ikke oppdatere selger',
        variant: 'destructive',
      })
    }
  }

  const handleOpenModal = (boolean: boolean) => {
    reset(getDefaultValues(seller))
    setOpen(boolean)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenModal}>
      <DialogTrigger asChild onClick={() => setOpen(true)}>
        {buttonComponent}
      </DialogTrigger>
      <DialogContent
        title={title}
        size="lg"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        {user && <DisabledFieldsInfo />}
        <form
          onSubmit={handleSubmitHookForm(onSubmit)}
          className="flex flex-col gap-6"
        >
          <DialogDescription divider className="flex flex-col gap-4">
            <EditSellerField
              disabled
              label="Fornavn"
              name="seller.firstName"
              control={control}
            />
            <EditSellerField
              disabled
              label="Etternavn"
              name="seller.lastName"
              control={control}
            />
            <EditSellerField
              label="Mobil"
              name="seller.mobilePhone"
              control={control}
            />
            <EditSellerField
              label="E-post"
              name="seller.email"
              control={control}
            />
            <EditSellerField
              label="Adresse"
              name="seller.postalAddress"
              control={control}
            />
            <EditSellerField
              label="Postnummer"
              name="seller.postalCode"
              control={control}
            />
            <EditSellerField
              label="Poststed"
              name="seller.city"
              control={control}
            />

            <EditSellerField
              label="Fødselsnummer"
              name="seller.socialSecurity"
              disabled={!isEmpty(seller.socialSecurity)}
              control={control}
            />

            {seller.proxy && (
              <>
                <div className="border-t border-muted pt-4">
                  <p className="typo-body-md font-medium">Fullmektig</p>
                  <p className="typo-body-md ink-subtle">
                    Personen vil signere på vegne av selgeren.
                  </p>
                </div>

                <EditSellerField
                  disabled
                  label="Fornavn"
                  name="proxy.firstName"
                  control={control}
                />
                <EditSellerField
                  disabled
                  label="Etternavn"
                  name="proxy.lastName"
                  control={control}
                />
                <EditSellerField
                  label="Mobil"
                  name="proxy.mobilePhone"
                  control={control}
                />
                <EditSellerField
                  label="E-post"
                  name="proxy.email"
                  control={control}
                />
                <EditSellerField
                  label="Adresse"
                  name="proxy.postalAddress"
                  control={control}
                />
                <EditSellerField
                  label="Postnummer"
                  name="proxy.postalCode"
                  control={control}
                />
                <EditSellerField
                  label="Poststed"
                  name="proxy.city"
                  control={control}
                />

                <EditSellerField
                  disabled={!isEmpty(seller.proxy?.socialSecurity)}
                  label="Fødselsnummer"
                  name="proxy.socialSecurity"
                  control={control}
                />
              </>
            )}
          </DialogDescription>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              size="md"
              onClick={() => {
                reset()
                setOpen(false)
              }}
            >
              Avbryt
            </Button>
            <Button type="submit" size="md" loading={formState.isSubmitting}>
              Lagre
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

function EditSellerField({
  disabled,
  ...props
}: UseControllerProps<EditSellersFormValues> & {
  label: string
  type?: string
}) {
  const { field, fieldState } = useController(props)

  return (
    <Input
      {...field}
      label={props.label}
      id={props.name}
      value={typeof field.value === 'object' ? '' : field.value}
      type={props.type ?? 'text'}
      errorMessage={fieldState.error?.message}
      disabled={disabled}
    />
  )
}
