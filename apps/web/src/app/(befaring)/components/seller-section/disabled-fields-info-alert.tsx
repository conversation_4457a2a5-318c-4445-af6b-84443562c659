import { AlertCircle } from 'lucide-react'

import { Alert, AlertDescription } from '@nordvik/ui/alert'
import { TextButton } from '@nordvik/ui/text-button'

import { getNextStakeholdersUrl } from '@/actions/get-next-overview-url'
import { useEstateId } from '@/hooks/use-estateid'

export function DisabledFieldsInfo() {
  const estateId = useEstateId()

  return (
    <Alert className="mb-3">
      <div className="flex gap-2 items-center">
        <AlertCircle size={16} />
        <AlertDescription>
          Deaktiverte felt kan oppdateres i{' '}
          <TextButton
            className="inline p-0"
            onClick={async () => {
              if (!estateId) {
                console.error('Failed to fetch estateId')
                return
              }
              const link = await getNextStakeholdersUrl(estateId)
              if (!link) {
                console.error('Failed to fetch Vitec URL')
                return
              }
              window.open(link, '_blank')
            }}
          >
            Next
          </TextButton>
        </AlertDescription>
      </div>
    </Alert>
  )
}
