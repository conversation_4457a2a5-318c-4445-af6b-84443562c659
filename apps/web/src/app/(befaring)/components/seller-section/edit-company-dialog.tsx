'use client'

import { yupResolver } from '@hookform/resolvers/yup'
import isEmpty from 'lodash/isEmpty'
import { AlertCircle } from 'lucide-react'
import React, { useCallback, useMemo, useState } from 'react'
import { UseControllerProps, useController, useForm } from 'react-hook-form'
import * as yup from 'yup'

import { Alert, AlertDescription } from '@nordvik/ui/alert'
import { Button } from '@nordvik/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { Input } from '@nordvik/ui/input'
import { Separator } from '@nordvik/ui/separator'
import { TextButton } from '@nordvik/ui/text-button'
import { useToast } from '@nordvik/ui/toaster'

import { getNextOverviewUrl } from '@/actions/get-next-overview-url'
import type { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import { useEstateId } from '@/hooks/use-estateid'
import { getLinkToObjectTabInNext } from '@/utils/get-link-to-object-tab-in-next'

import { useUpdateCompany } from '../../hooks/use-update-company'

const companySchema = yup.object({
  companyName: yup.string().optional(),
  postalAddress: yup.string().required('Mangler adresse'),
  postalCode: yup.string().required('Mangler postnummer'),
  city: yup.string().required('Mangler poststed'),
  organisationNumber: yup.string().required('Mangler organisasjonsnummer'),
})

export type EditCompanyFormValues = yup.InferType<typeof companySchema>

const getDefaultCompanyValues = (
  company: NextPrivateContactWithProxy,
): EditCompanyFormValues => ({
  companyName: company.companyName ?? '',
  postalAddress: company.postalAddress ?? '',
  postalCode: company.postalCode ?? '',
  city: company.city ?? '',
  organisationNumber: company.organisationNumber ?? '',
})

export default function EditCompanyDialog({
  company,
  children,
  title = 'Firma',
}: {
  company: NextPrivateContactWithProxy
  title?: string
  children?: React.ReactNode
}) {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()

  const { handleSubmit } = useUpdateCompany(company)

  const defaultValues = useMemo(
    () => getDefaultCompanyValues(company),
    [company],
  )

  const {
    handleSubmit: handleSubmitHookForm,
    formState,
    reset,
    control,
  } = useForm<EditCompanyFormValues>({
    resolver: yupResolver(companySchema),
    defaultValues,
  })

  const onSubmit = useCallback(
    async (formData: EditCompanyFormValues) => {
      try {
        if (!isEmpty(formState.errors)) throw new Error()

        await handleSubmit({ ...company, ...formData })
        setOpen(false)
      } catch (error) {
        toast({
          title: 'Kunne ikke oppdatere selskap',
          variant: 'destructive',
        })
      }
    },
    [formState.errors, handleSubmit, toast, company],
  )

  const estateId = useEstateId()

  const handleOpenModal = useCallback(
    (isOpen: boolean) => {
      if (!isOpen) reset(defaultValues) // Reset values when closing
      setOpen(isOpen)
    },
    [reset, defaultValues],
  )

  return (
    <Dialog open={open} onOpenChange={handleOpenModal}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        title={title}
        size="lg"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogDescription>
          <Alert className="mb-3">
            <div className="flex gap-2 items-center">
              <AlertCircle size={16} />
              <AlertDescription>
                Kontaktinformasjon for et firma må oppdateres i{' '}
                <TextButton
                  className="inline p-0"
                  onClick={async () => {
                    if (!estateId) {
                      console.error('Failed to fetch estateId')
                      return
                    }
                    const nextUrl = await getNextOverviewUrl(estateId)
                    if (!nextUrl) {
                      console.error('Failed to fetch Vitec URL')
                      return
                    }
                    const link = getLinkToObjectTabInNext(
                      nextUrl,
                      'relatedparties',
                    )

                    window.open(link, '_blank')
                  }}
                >
                  Next
                </TextButton>
                .
              </AlertDescription>
            </div>
          </Alert>
          <form
            onSubmit={handleSubmitHookForm(onSubmit)}
            className="flex flex-col gap-6"
          >
            <EditCompanyField
              disabled
              label="Selskapsnavn"
              name="companyName"
              control={control}
            />
            <EditCompanyField
              disabled
              label="Org. nummer"
              name="organisationNumber"
              control={control}
            />
            <EditCompanyField
              disabled
              label="Adresse"
              name="postalAddress"
              control={control}
            />
            <div className="flex gap-4">
              <EditCompanyField
                disabled
                label="Postnummer"
                name="postalCode"
                control={control}
                className="flex-1"
              />
              <EditCompanyField
                disabled
                label="Sted"
                name="city"
                control={control}
                className="flex-1"
              />
            </div>
            <Separator />
            <div className="flex gap-2 self-end">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setOpen(false)}
              >
                Avbryt
              </Button>
              <Button
                type="submit"
                size="sm"
                disabled
                loading={formState.isSubmitting}
              >
                Lagre
              </Button>
            </div>
          </form>
        </DialogDescription>
      </DialogContent>
    </Dialog>
  )
}

function EditCompanyField({
  disabled,
  label,
  className,
  ...props
}: UseControllerProps<EditCompanyFormValues> & {
  label: string
  type?: string
  className?: string
}) {
  const { field, fieldState } = useController(props)

  return (
    <label
      htmlFor={props.name}
      className={`flex flex-col gap-1.5 ${className}`}
    >
      <span className="typo-label-md">{label}</span>
      <Input
        {...field}
        id={props.name}
        value={field.value || ''}
        type={props.type ?? 'text'}
        errorMessage={fieldState.error?.message}
        disabled={disabled}
      />
    </label>
  )
}
