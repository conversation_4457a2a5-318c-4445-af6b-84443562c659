'use server'

import { cache } from 'react'

import { getCompanyContacts } from '@/actions/next/company-contacts'
import { getEstateContactRole } from '@/actions/next/estate-contact-role'
import { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import prisma from '@/db/prisma'

import { SellerCompanyInfoCardContent } from './seller-company-info-card-content'

export const cachedCompanyContacts = cache(getCompanyContacts)

export async function SellerCompanyInfoCard({
  company,
  estateId,
  canEdit,
}: {
  company: NextPrivateContactWithProxy
  estateId: string
  canEdit: boolean
}) {
  const [contacts, contactRoles, signRights] = await Promise.all([
    (await cachedCompanyContacts(company.contactId)) ?? [],
    company.contactId
      ? getEstateContactRole(estateId, company.contactId)
      : null,
    getSignRights({ company, estateId }),
  ])

  return (
    <SellerCompanyInfoCardContent
      estateId={estateId}
      company={company}
      contacts={contacts}
      contactRoles={contactRoles ?? []}
      signRights={signRights}
      canEdit={canEdit}
    />
  )
}

export async function getSignRights({
  company,
  estateId,
}: {
  company: Pick<NextPrivateContactWithProxy, 'contactId' | 'proxy'>
  estateId: string
}) {
  if (!company.contactId) return null
  const signRights = await prisma.company_sign_rights.findFirst({
    where: {
      company_contact_id: company.contactId,
      listing_agreements: { estate_id: estateId, deleted_at: null },
    },
    select: {
      signers: true,
      id: true,
    },
  })

  if (company.proxy) {
    // If there's a proxy, make sure the proxy is the only signer
    const correctPrxySigner =
      signRights?.signers.includes(company.proxy.contactId) &&
      signRights.signers.length === 1

    if (!correctPrxySigner) {
      if (signRights) {
        return prisma.company_sign_rights.update({
          where: {
            id: signRights.id,
          },
          data: {
            signers: {
              set: [company.proxy.contactId],
            },
          },
        })
      } else {
        return prisma.company_sign_rights.create({
          data: {
            company_contact_id: company.contactId,
            listing_agreements: { connect: { estate_id: estateId } },
            signers: [company.proxy.contactId],
          },
        })
      }
    }
  }

  return signRights
}
