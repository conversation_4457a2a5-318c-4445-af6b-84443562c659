'use client'

import { TriangleAlert } from 'lucide-react'
import React, { useEffect } from 'react'

import { TextButton } from '@nordvik/ui/text-button'

import { ContactRole } from '@/actions/next/types'
import { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import { LoadingIndicator } from '@/components/loading-indicator'
import { UserAvatar } from '@/components/user-avatar'

import { ChooseSignersDialog } from '../../oppdragsavtale/[estateId]/(agreement)/parter/components/choose-signers-dialog'

const missing = (msg) => (
  <div className="inline-flex gap-2 items-center">
    <span className="ink-danger">{msg}</span>
    <TriangleAlert className="size-4 ink-danger" />
  </div>
)

const renderAddress = (seller: NextPrivateContactWithProxy) =>
  seller.postalAddress && seller.postalCode && seller.city
    ? `${seller.postalAddress}, ${seller.postalCode} ${seller.city}`
    : missing('Mangler')

export function SellerCompanyInfoCardContent({
  estateId,
  company,
  contacts,
  contactRoles,
  signRights,
  canEdit,
}: {
  estateId: string
  company: NextPrivateContactWithProxy
  contacts: NextPrivateContactWithProxy[]
  contactRoles: ContactRole[]
  signRights: { signers: string[]; id: string } | null
  canEdit: boolean
}) {
  const [open, setOpen] = React.useState(false)
  const [isDirty, setIsDirty] = React.useState(false)

  const handleOnClose = (open: boolean, hasChanges: boolean) => {
    setIsDirty(hasChanges)
    setOpen(open)
  }

  useEffect(() => {
    setIsDirty(false)
  }, [signRights?.signers])

  const mainContact = contactRoles
    ? contacts.find(
        (contact) => contact.contactId === contactRoles[0]?.childContactId,
      )
    : undefined

  const signers = company.proxy
    ? [company.proxy]
    : contacts.filter((contact) =>
        signRights?.signers.includes(contact.contactId),
      )

  const renderSigners = () => {
    if (!signers || signers.length === 0) {
      return missing('Ikke angitt')
    }

    const firstSignerName = `${signers[0]?.firstName} ${signers[0]?.lastName}`

    if (signers.length === 1) {
      return firstSignerName
    }

    return (
      <div className="inline-flex gap-2">
        {firstSignerName}{' '}
        <TextButton onClick={() => setOpen(true)}>
          +{signers.length - 1}
        </TextButton>
      </div>
    )
  }

  const renderMainContact = () =>
    mainContact && (
      <div className="inline-flex gap-2">
        {mainContact.firstName} {mainContact.lastName}{' '}
        {contacts.length > 1 && (
          <TextButton onClick={() => setOpen(true)}>
            +{contacts.length - 1}
          </TextButton>
        )}
      </div>
    )

  const renderProxy = () =>
    company.proxy?.firstName && company.proxy?.lastName
      ? `${company.proxy.firstName} ${company.proxy.lastName}`
      : missing('Ikke angitt')

  return (
    <div className="flex gap-4">
      <UserAvatar user={{ name: company.companyName ?? '' }} />
      <div className="flex flex-col items-start gap-2">
        <div className="typo-body-md flex flex-col gap-1">
          <span className="typo-body-md font-medium">
            {company.companyName}
          </span>
          <span>
            Org.nr: {company.organisationNumber ?? missing('Mangler')}
          </span>
          <span>Adresse: {renderAddress(company)}</span>
          {mainContact && <span>Hovedkontakt: {renderMainContact()}</span>}
          {company.proxy && <span>Fullmektig: {renderProxy()}</span>}
          <span>
            Signeres av:{' '}
            {isDirty ? (
              <LoadingIndicator
                className="inline-flex translate-y-[0.200rem]"
                hasDelay={false}
              />
            ) : (
              renderSigners()
            )}
          </span>
          {canEdit && (
            <TextButton onClick={() => setOpen(true)}>Rediger</TextButton>
          )}
        </div>
      </div>
      {canEdit && (
        <ChooseSignersDialog
          estateId={estateId}
          open={open}
          onOpenChange={handleOnClose}
          company={company}
          signers={signers}
          contacts={contacts.map((contact) => ({
            ...contact,
            main: contact.contactId === mainContact?.contactId,
            roleName: contactRoles?.find(
              (role) => role.childContactId === contact.contactId,
            )?.contactRoleName,
          }))}
        />
      )}
    </div>
  )
}

export function SellerCompanyInfoCardContentSkeleton() {
  return (
    <div className="flex gap-4">
      <UserAvatar
        className="masked-placeholder-text"
        user={{ name: 'Unfold AS' }}
      />
      <div className="flex flex-col items-start gap-2">
        <div className="typo-body-md flex flex-col gap-1">
          <span className="masked-placeholder-text">Unfold AS</span>
          <span className="masked-placeholder-text">Org.nr: *********</span>
          <span className="masked-placeholder-text">Adresse: Loading Gate</span>
          <span className="masked-placeholder-text">Hovedkontakt: Main</span>
          <TextButton className="masked-placeholder-text no-underline">
            Rediger
          </TextButton>
        </div>
      </div>
    </div>
  )
}
