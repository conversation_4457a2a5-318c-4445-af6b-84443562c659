'use client'

import {
  Collapsible,
  CollapsibleContent,
  type CollapsibleContentProps,
  CollapsibleTrigger,
} from '@radix-ui/react-collapsible'
import { ChevronDown, ChevronUp, Loader2, Pencil } from 'lucide-react'
import React, { useState } from 'react'

import { cn } from '@nordvik/theme/cn'

export function CollapsibleSection({
  title,
  action,
  loading,
  editMode,
  className,
  children,
  defaultOpen = true,
}: {
  title: string
  action?: React.ReactNode
  loading?: boolean
  editMode?: boolean
  className?: string
  children: React.ReactNode
  defaultOpen?: boolean
}) {
  const [open, setOpen] = useState(defaultOpen)
  return (
    <Collapsible
      open={open}
      onOpenChange={setOpen}
      className={cn(
        'relative flex flex-col sm:rounded-lg bg-root [overflow:clip]',
        className,
      )}
    >
      {Boolean(editMode) && (
        <div className="sticky top-0 z-10 flex w-full items-center gap-2 bg-success-subtle px-6 py-2">
          <Pencil size={14} />
          <span className="typo-body-sm">Redigeringsmodus</span>
        </div>
      )}
      <CollapsibleTrigger asChild>
        <div className="flex items-center justify-between gap-6 px-[--padding-x] py-5 md:py-6 cursor-pointer">
          <h2 className="typo-display-sm">{title}</h2>
          <div className="flex items-center gap-2">
            {action}

            {open ? <ChevronUp /> : <ChevronDown />}
          </div>
          {Boolean(loading) && (
            <Loader2 className="absolute right-16 top-6 animate-spin ink-muted" />
          )}
        </div>
      </CollapsibleTrigger>
      {children}
    </Collapsible>
  )
}

export function SectionContent({
  children,
  className,
  ...props
}: CollapsibleContentProps) {
  return (
    <CollapsibleContent asChild {...props}>
      <div
        className={cn(
          'data-[state=open]:animate-collapsible-slide-down data-[state=closed]:animate-collapsible-slide-up',
          className,
        )}
      >
        {children}
      </div>
    </CollapsibleContent>
  )
}
