import { ChevronRightIcon } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'

import { SalesProcessData } from '../sales-process-data'

export function SlideFooter({
  allArticles,
  setOpenSlide,
  currentArticleId,
  category,
}: {
  allArticles?: NonNullable<SalesProcessData['content']>[number][]
  currentArticleId?: string
  setOpenSlide: (slide: string | null) => void
  category?: Pick<SalesProcessData, 'title'>
}) {
  if (!allArticles || allArticles?.length === 0) return null

  return (
    <div className="mx-[var(--content-padding-x)] px-[var(--container-padding-x)] pb-10 mt-16">
      <p className="typo-body-lg font-medium mb-4">
        Mer om <span className="lowercase">{category?.title}</span>
      </p>
      <div className="border border-muted rounded-sm overflow-hidden">
        {allArticles.map((article) => (
          <button
            key={article.id}
            onClick={() => setOpenSlide(article.slug)}
            className={cn(
              'group/article p-4 flex items-center justify-between gap-4 w-full not-last:border-b border-muted py-4',
              article.id === currentArticleId &&
                'bg-interactive-muted cursor-default',
            )}
          >
            <div className="flex gap-3">
              <div className="size-2 rounded-full bg-gold-emphasis mt-[7px] md:mt-2.5 shrink-0" />
              <div className="space-y-1 text-left">
                <p className="typo-display-xs">{article.title}</p>
                <p className="typo-body-sm ink-muted">{article.description}</p>
              </div>
            </div>
            <ChevronRightIcon
              size={16}
              data-disabled={article.id === currentArticleId}
              className={cn(
                'xs:opacity-0 group-hover/article:opacity-100 transition-opacity',
                article.id === currentArticleId &&
                  'group-hover/article:opacity-0',
              )}
            />
          </button>
        ))}
      </div>
    </div>
  )
}
