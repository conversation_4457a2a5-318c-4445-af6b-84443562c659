import { Transition, motion } from 'framer-motion'
import Image from 'next/image'

import { SalesProcessData } from '../sales-process-data'

export function CategoryList({
  categories,
  setActiveCategory,
  expandTransition,
}: {
  categories: SalesProcessData[]
  setActiveCategory: (category: string | null) => void
  expandTransition: Transition
}) {
  return (
    <div
      className="
      grid grow gap-5 group/categories px-[--container-padding]
      max-lg:flex max-lg:flex-col max-lg:gap-4
    "
      style={{ gridTemplateColumns: `repeat(${categories.length}, 1fr)` }}
    >
      {categories.map((category, index, list) => (
        <div
          key={category.id}
          data-category
          onClick={() => setActiveCategory(category.slug)}
          className="
           flex flex-col gap-4 lg:gap-2 group/category hover-supported:group-has-[[data-category]:hover]/categories:opacity-50 hover:!opacity-100 transition-opacity duration-300
           max-lg:flex-row-reverse max-lg:justify-end
          "
          data-last={index === list.length - 1}
        >
          <div className="flex flex-col max-lg:flex-row gap-4 lg:gap-2 typo-display-xs items-start max-lg:translate-y-[calc(50%-1rem)]">
            <div className="flex items-center max-lg:flex-col gap-2 lg:gap-4 self-stretch">
              <motion.div
                transition={expandTransition}
                className="ink-gold"
                layoutId={`category-number-${category.id}`}
              >
                {`${index + 1}`.padStart(2, '0')}
              </motion.div>
              <motion.div
                transition={expandTransition}
                layoutId={`category-divider-${category.id}`}
                className="grow max-lg:border-l lg:border-t border-dashed border-subtle group-data-[last=true]/category:hidden"
              />
            </div>
            <motion.h2
              transition={expandTransition}
              className="truncate"
              layoutId={`category-title-${category.id}`}
            >
              {category.title}
            </motion.h2>
          </div>
          <motion.div
            transition={expandTransition}
            layoutId={`category-image-${category.id}`}
            className="lg:grow flex relative rounded-md overflow-clip lg:max-h-[24rem] bg-root-muted max-lg:aspect-square max-lg:w-[20vw]"
          >
            <Image
              fill
              src={category.image.medium}
              alt=""
              style={{ objectFit: 'cover' }}
            />
            <motion.div layoutId={`category-content-${category.id}`} />
          </motion.div>
        </div>
      ))}
    </div>
  )
}
