import { AnimatePresence, Transition, motion } from 'framer-motion'
import { ChevronLeftIcon } from 'lucide-react'
import React from 'react'

import { Button } from '@nordvik/ui/button'

import { TrackingPageId } from '@/components/track-page-visit'

import { SalesProcessData } from '../sales-process-data'

import { CategoryList } from './category-list'
import { ExpandedCategoryList } from './expanded-category-list'

export function Overview({
  categories,
  pageId,
}: {
  categories: SalesProcessData[]
  pageId: TrackingPageId
}) {
  const [activeCategory, setActiveCategory] = React.useState<string | null>(
    null,
  )
  const scrollToRef = React.useRef<(categorySlug: string) => void>(null)
  return (
    <div className="flex flex-col grow">
      <AnimatePresence mode="sync" initial={false}>
        {activeCategory ? (
          <CategoryNavigation
            categories={categories}
            activeCategory={activeCategory}
            setActiveCategory={setActiveCategory}
            scrollToRef={scrollToRef}
          />
        ) : (
          <motion.header
            key="header"
            transition={{ type: 'spring', stiffness: 200, damping: 30 }}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <div className="typo-display-xl py-16 flex [@media(max-height:50rem)]:py-12 [@media(max-height:50rem)]:typo-display-lg flex-col justify-center px-[--container-padding]">
              <h1 className="ink-gold">Salgsprosess</h1>
              <p>Veien til den beste prisen</p>
            </div>
          </motion.header>
        )}
      </AnimatePresence>
      <div className="grow-[5] flex flex-col pb-[--footer-height,8rem]">
        {activeCategory ? (
          <ExpandedCategoryList
            scrollToRef={scrollToRef}
            categories={categories}
            activeCategory={activeCategory}
            setActiveCategory={setActiveCategory}
            expandTransition={expandTransition}
            pageId={pageId}
          />
        ) : (
          <CategoryList
            categories={categories}
            setActiveCategory={setActiveCategory}
            expandTransition={expandTransition}
          />
        )}
      </div>
    </div>
  )
}

function CategoryNavigation({
  categories,
  activeCategory,
  setActiveCategory,
  scrollToRef,
}: {
  categories: SalesProcessData[]
  activeCategory: string
  setActiveCategory: (categorySlug: string | null) => void
  scrollToRef: React.RefObject<((categorySlug: string) => void) | null>
}) {
  const closeButton = (
    <>
      <Button
        variant="ghost"
        iconOnly={<ChevronLeftIcon />}
        size="sm"
        className="lg:hidden"
        onClick={() => setActiveCategory(null)}
      >
        Oversikt
      </Button>
      <div className="flex items-center max-lg:hidden">
        <button
          onClick={() => setActiveCategory(null)}
          className="ink-disabled transition-colors hover:ink-default typo-body-sm whitespace-nowrap"
        >
          Oversikt
        </button>
        <div className="border-l max-xl:hidden border-muted h-5 mx-5" />
      </div>
    </>
  )

  return (
    <motion.nav
      key="nav"
      className="max-xl:sticky inset-x-0 top-0  z-10"
      transition={{ type: 'spring', stiffness: 200, damping: 30 }}
      initial={{ opacity: 0, height: 0, y: '-50%' }}
      animate={{ opacity: 1, height: 'auto', y: '0%' }}
      exit={{ opacity: 0, height: 0, y: '0%' }}
    >
      <div className="px-[--container-padding] max-lg:gap-4 py-2 xl:py-6 flex items-center bg-root max-xl:border-b max-xl:border-muted">
        {closeButton}
        <div className="flex items-center max-xl:mx-auto gap-2">
          {categories.map((category, index) => (
            <React.Fragment key={category.id}>
              {index > 0 && (
                <div className="w-3 max-xl:w-5 border-t border-dashed border-muted" />
              )}
              <button
                aria-current={
                  activeCategory === category.slug ? 'step' : 'false'
                }
                onClick={() => scrollToRef.current?.(category.slug)}
                className="aria-[current=step]:ink-gold ink-disabled transition-colors hover:ink-default typo-body-sm whitespace-nowrap"
              >
                <span className="mr-1">{`${index + 1}`.padStart(2, '0')}</span>{' '}
                <span className="max-md:hidden">{category.title}</span>
              </button>
            </React.Fragment>
          ))}
        </div>
        <div aria-hidden className="invisible">
          {closeButton}
        </div>
      </div>
    </motion.nav>
  )
}

const expandTransition: Transition = {
  type: 'spring',
  stiffness: 110,
  damping: 20,
  mass: 0.3,
}
