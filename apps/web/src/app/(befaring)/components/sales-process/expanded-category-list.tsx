import { DialogTitle } from '@radix-ui/react-dialog'
import { Transition, motion } from 'framer-motion'
import Image from 'next/image'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import * as Sheet from '@nordvik/ui/sheet'
import { VisuallyHidden } from '@nordvik/ui/visually-hidden'

import { TrackingPageId } from '@/components/track-page-visit'

import { SalesProcessData } from '../sales-process-data'

import SlideContent from './slide-content'

export function ExpandedCategoryList({
  categories,
  activeCategory,
  setActiveCategory,
  expandTransition,
  scrollToRef,
  pageId,
}: {
  categories: SalesProcessData[]
  activeCategory: string
  setActiveCategory: (category: string | null) => void
  expandTransition: Transition
  scrollToRef: React.RefObject<
    ((slug: string, behavior?: 'instant' | 'smooth') => void) | null
  > | null
  pageId: TrackingPageId
}) {
  const [initialCategory] = React.useState(activeCategory)
  const [openedSlide, setOpenedSlide] = React.useState<string | null>(null)
  const scrollRef = React.useRef<HTMLDivElement>(null)

  const scrollToCategory = React.useCallback(
    (slug: string, behavior: 'instant' | 'smooth' = 'smooth') => {
      const node = scrollRef.current
      if (!node) return
      node.scrollTo({
        top: 0,
        behavior: behavior,
        left:
          window.innerWidth *
          categories.findIndex((category) => category.slug === slug),
      })
    },
    [categories],
  )

  // If arrow left or right is pressed, we want to scroll to the next or previous category
  React.useEffect(() => {
    const keyUpListener = (event: KeyboardEvent) => {
      if (openedSlide) return
      if (event.key === 'ArrowRight' || event.key === 'ArrowLeft') {
        const direction = event.key === 'ArrowRight' ? 'right' : 'left'
        const index = categories.findIndex(
          (category) => category.slug === activeCategory,
        )
        const nextIndex = Math.max(
          0,
          Math.min(
            direction === 'right' ? index + 1 : index - 1,
            categories.length - 1,
          ),
        )
        const nextCategory = categories[nextIndex]
        setActiveCategory(nextCategory.slug)
        scrollToCategory(nextCategory.slug)
      }
    }
    window.addEventListener('keyup', keyUpListener)
    return () => window.removeEventListener('keyup', keyUpListener)
  }, [
    openedSlide,
    activeCategory,
    categories,
    setActiveCategory,
    scrollToCategory,
  ])

  React.useImperativeHandle(scrollToRef, () => scrollToCategory, [
    scrollToCategory,
  ])

  // On mount, we want to scroll the the initial active category
  React.useEffect(() => {
    scrollToCategory(initialCategory, 'instant')
  }, [initialCategory, scrollToCategory])

  return (
    <div
      ref={scrollRef}
      className={cn(
        'grid [grid-auto-flow:column] overflow-x-auto',
        'gap-y-20',
        'pt-6 md:pt-16 [@media(min-height:1000px)_and_(min-width:1200px)]:pt-[10vh]',
        // Add sroll snapping, animation of scroll change and  scroll-padd so the images align with parent container
        'scroll-smooth snap-x snap-mandatory scroll-px-[--overflow]',
        // Please hide it
        'hide-scrollbar',
      )}
    >
      {categories.map((category, index) => (
        <motion.div
          key={category.id}
          data-category-slug={category.slug}
          className="
          w-screen snap-start grow flex px-[--container-padding]
          max-lg:scroll-my-20
          "
        >
          <div className="flex flex-col 2xl:max-w-[1200px] mx-auto grow">
            <div className="typo-display-lg flex gap-1.5 items-start mb-6">
              <motion.div
                transition={expandTransition}
                className="ink-gold"
                layoutId={`category-number-${category.id}`}
                onViewportEnter={() => {
                  setActiveCategory(category.slug)
                }}
                viewport={{
                  root: scrollRef,
                  amount: 'all',
                }}
              >
                {`${index + 1}`.padStart(2, '0')}
              </motion.div>
              <motion.div
                transition={expandTransition}
                layoutId={`category-divider-${category.id}`}
              />
              <motion.h2
                transition={expandTransition}
                layoutId={`category-title-${category.id}`}
              >
                {category.title}
              </motion.h2>
            </div>
            <div className="flex max-lg:flex-col grow gap-6 lg:gap-20">
              <motion.div
                transition={expandTransition}
                layoutId={`category-image-${category.id}`}
                className="relative rounded-md overflow-clip h-auto lg:grow max-lg:aspect-video"
                onClick={() =>
                  activeCategory === category.slug
                    ? setActiveCategory(null)
                    : scrollToCategory(category.slug)
                }
              >
                <Image
                  fill
                  src={category.image.medium}
                  alt=""
                  style={{ objectFit: 'cover' }}
                />
              </motion.div>
              <motion.div
                layoutId={`category-content-${category.id}`}
                layout="preserve-aspect"
                className="grow basis-0 flex flex-col max-lg:pl-6"
                transition={{ ...expandTransition }}
              >
                <div className="flex flex-col">
                  {category.content?.map((entry, index) => (
                    <motion.div
                      key={entry.id}
                      onClick={() =>
                        setOpenedSlide(`${category.slug}/${entry.slug}`)
                      }
                      className="py-3 border-b border-muted cursor-pointer group/entry"
                      initial={{
                        x: -14,
                        opacity: 0,
                      }}
                      animate={{
                        x: 0,
                        opacity: 1,
                      }}
                      transition={{
                        type: 'spring',
                        stiffness: 200,
                        damping: 30,
                        delay: 0.2 + 0.05 * index,
                      }}
                    >
                      <h3 className="relative typo-display-xs group-hover/entry:underline underline-offset-2 decoration-muted">
                        {entry.title}
                        <div className="absolute top-1/2 -mt-1 -left-6 size-2 rounded-full bg-gold-emphasis" />
                      </h3>
                      <p className="ink-subtle typo-body-md">
                        {entry.description}
                      </p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      ))}

      <Sheet.Sheet
        open={!!openedSlide}
        onOpenChange={(open) => !open && setOpenedSlide(null)}
      >
        <Sheet.SheetPortal>
          <Sheet.SheetOverlay className="bg-overlay animate-in fade-in-0" />
          <Sheet.SheetContent
            className="@container/slide w-screen md:w-[80vw] max-w-[1152px] p-0 scroll-smooth"
            closeButton={false}
            data-id="slide-content"
          >
            <VisuallyHidden>
              <DialogTitle>My Dialog Title</DialogTitle>
            </VisuallyHidden>
            <SlideContent
              slug={openedSlide}
              setOpenSlide={setOpenedSlide}
              pageId={pageId}
            />
          </Sheet.SheetContent>
        </Sheet.SheetPortal>
      </Sheet.Sheet>
    </div>
  )
}
