'use client'

import { useQuery } from '@tanstack/react-query'
import { useParams } from 'next/navigation'

import { TrackPageVisit, TrackingPageId } from '@/components/track-page-visit'

import { salesProcessData } from '../sales-process-data'

import { SlideArticle } from './slide-article'
import { SlideFooter } from './slide-footer'
import { SlideHeader } from './slide-header'
import { SlideLabels } from './slide-labels'

function getSlideContent(slug: string | null) {
  if (!slug) return
  const [categorySlug, slideSlug] = slug.split('/')
  const category = salesProcessData.find((entry) => entry.slug === categorySlug)
  if (!category) return
  const current = category.content.find((item) => item.slug === slideSlug)

  return {
    category,
    categoryIndex: salesProcessData.indexOf(category),
    allArticles: category.content,
    current,
  }
}

function SlideContent({
  slug,
  setOpenSlide,
  pageId,
}: {
  slug: string | null
  setOpenSlide: (slide: string | null) => void
  pageId: TrackingPageId
}) {
  const params = useParams<{ estateId: string }>()
  const { data } = useQuery({
    queryKey: ['salesProcessData', slug],
    queryFn: () => getSlideContent(slug),
  })

  if (!data) {
    return null
  }

  return (
    <>
      {data.category && data.current && (
        <TrackPageVisit
          estateId={params.estateId}
          pageId={`${pageId}/${data.category.slug}/${data.current.slug}`}
        />
      )}
      <div className="flex flex-col min-h-full [--container-padding-x:1rem] @lg/slide:[--container-padding-x:2rem] @lg/slide:[--content-padding-x:1rem] @4xl/slide:[--content-padding-x:7.25rem]">
        <SlideHeader
          category={data.category}
          current={data.current}
          categoryIndex={data.categoryIndex}
        />
        <SlideLabels labels={data.current?.labels} />
        <SlideArticle article={data.current} />
        <SlideFooter
          allArticles={data.allArticles}
          currentArticleId={data.current?.id}
          category={data.category}
          setOpenSlide={(slideSlug) =>
            setOpenSlide(`${data.category?.slug}/${slideSlug}`)
          }
        />
      </div>
    </>
  )
}

export default SlideContent
