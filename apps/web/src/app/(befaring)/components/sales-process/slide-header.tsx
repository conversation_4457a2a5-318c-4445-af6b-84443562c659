import { XIcon } from 'lucide-react'

import { Button } from '@nordvik/ui/button'
import * as Sheet from '@nordvik/ui/sheet'

import { SalesProcessData } from '../sales-process-data'

export function SlideHeader({
  category,
  current,
  categoryIndex,
}: {
  category?: Pick<SalesProcessData, 'title' | 'slug'>
  current?: Pick<NonNullable<SalesProcessData['content']>[number], 'title'>
  categoryIndex: number
}) {
  if (!category || !current) return null

  return (
    <div className="bg-root px-[var(--container-padding-x)]">
      <Sheet.SheetClose asChild>
        <Button
          variant="unstyled"
          className="my-4 ml-auto md:ml-0"
          iconOnly={<XIcon size={24} />}
          size="lg"
        />
      </Sheet.SheetClose>
      <div className="pt-4 px-[var(--content-padding-x)]">
        <p className="typo-detail-md">
          {String((categoryIndex ?? 0) + 1).padStart(2, '0')}{' '}
          <span className="ink-muted">{category.title}</span>
        </p>
        <h1 className="typo-display-lg ink-gold">{current.title}</h1>
      </div>
    </div>
  )
}
