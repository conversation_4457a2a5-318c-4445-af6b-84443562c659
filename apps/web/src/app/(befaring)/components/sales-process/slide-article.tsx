import Image from 'next/image'

import { cn } from '@nordvik/theme/cn'
import { RichTextModule } from '@nordvik/ui/rich-text-module'

import { renderModule } from '@/components/articles/article-content'
import { HorizontalScrollContainer } from '@/components/broker-profile/presentation/components/scroll-container'
import { ImageComparison } from '@/components/image-comparison/image-comaprison'

import { SalesProcessData } from '../sales-process-data'

export function SlideArticle({
  article,
}: {
  article?: NonNullable<SalesProcessData['content']>[number]
}) {
  if (!article) return null

  const firstComparisonModule = article.modules?.findIndex(
    (module) => module.type === 'comparisonModule',
  )

  return (
    <div className="p-4 px-[var(--container-padding-x)] grow">
      {article.excerpt ? (
        <div className="mx-[var(--content-padding-x)] mb-10">
          <RichTextModule
            text={article.excerpt}
            className="[&_p]:typo-body-xl"
          />
        </div>
      ) : null}
      {article.modules?.map((module, index) => {
        switch (module.type) {
          case 'imageModule':
            return <SlideImage key={`${module.id}-${index}`} module={module} />
          case 'richTextModule':
            return (
              <SlideTextModule key={`${module.id}-${index}`} module={module} />
            )
          case 'financingFormModule':
            return (
              <SlideTextModule key={`${module.id}-${index}`} module={module} />
            )
          case 'comparisonModule':
            if (!module.beforeImage || !module.afterImage) return null

            return (
              <div className="mt-10" key={`${module.id}-${index}`}>
                <ImageComparison
                  beforeImage={{
                    href: module.beforeImage,
                  }}
                  afterImage={{
                    href: module.afterImage,
                  }}
                  animate={index === firstComparisonModule}
                />
              </div>
            )
          default:
            return null
        }
      })}
    </div>
  )
}

function SlideImage({
  module,
}: {
  module: NonNullable<SalesProcessData['content']>[number]['modules'][number]
}) {
  if (!module.image) return null

  if (module.image.length === 1) {
    return (
      <div className="w-full h-auto  mt-10 overflow-hidden rounded-md flex justify-center">
        <Image
          src={module.image[0].url}
          alt={module.image[0].alt}
          width={1088}
          height={1088}
          className="w-full h-max object-contain"
        />
      </div>
    )
  }

  if (module.image.length > 2) {
    return (
      <div className="mt-10">
        <p className="typo-label-lg mb-4">{module.title}</p>
        <HorizontalScrollContainer
          alignChildrenTop
          className="gap-4"
          listClassName="
            [--gap-x:1rem] gap-x-[--gap-x] auto-cols-[calc((100%-var(--gap-x))/1.2)]
            md:auto-cols-[calc((100%-var(--gap-x))/2)]
            "
        >
          {module.image &&
            module.image.map((image) => (
              <div
                key={image.url}
                className="w-full aspect-[6/9] @md/slide:aspect-[11/9] overflow-hidden rounded-md"
              >
                <Image
                  src={image.url}
                  alt={image.alt}
                  width={1088}
                  height={725}
                  className="size-full object-cover"
                />
              </div>
            ))}
        </HorizontalScrollContainer>
      </div>
    )
  }

  return null
}

const matchModuleTypes = ['richTextModule', 'financingFormModule']

function SlideTextModule({
  module,
}: {
  module: NonNullable<SalesProcessData['content']>[number]['modules'][number]
}) {
  return (
    <article className={'mx-[var(--content-padding-x)] my-10 first:mt-0 '}>
      {renderModule(
        module,
        {},
        cn(
          matchModuleTypes.includes(module.type) &&
            '[&_p]:!leading-[1.9] [&_li]:!leading-[1.9] [&_p]:not-first:!mt-4',
          '[&_table]:w-full [&_table]:!border-collapse',
          '[&_tr]:border-0',
          '[&_tr_td]:border-0',
          '[&_tbody_tr]:!border-b [&_tbody_tr]:!border-muted',
          '[&_thead_tr]:!border-b [&_thead_tr]:!border-muted',
          '[&_th]:py-6 [&_th]:px-4 [&_th]:text-left',
          '[&_td]:py-6 [&_td]:px-4 [&_td]:text-left',
          '[&_*[id]]:ink-default font-normal [&_*[id]]:typo-display-sm [&_*[id]]:!mt-20',
        ),
      )}
    </article>
  )
}
