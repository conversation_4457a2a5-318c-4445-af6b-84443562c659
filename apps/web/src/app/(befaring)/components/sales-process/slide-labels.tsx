import { useEffect, useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'

import { SalesProcessData } from '../sales-process-data'

export function SlideLabels({
  labels,
}: {
  labels: NonNullable<SalesProcessData['content']>[number]['labels']
}) {
  const [isScrolling, setIsScrolling] = useState(false)
  const labelIds = labels?.map((label) => label.id) || []
  const { activeId, setActiveId } = useIntersectionObserver(
    labelIds,
    '[data-id="slide-content"]',
    isScrolling,
  )

  const handleScrollTo = (id: string) => {
    const element = document.getElementById(id)
    const container = document.querySelector('[data-id="slide-content"]')
    if (!element || !container) return

    setIsScrolling(true)

    container.scrollTo({
      top: element.offsetTop - 100,
      behavior: 'smooth',
    })
    setActiveId(id)
    setTimeout(() => {
      setIsScrolling(false)
      setActiveId(id)
    }, 1000)
  }
  return (
    <div
      className={cn(
        'bg-root px-[var(--container-padding-x)] py-2.5 flex overflow-x-auto hide-scrollbar top-0 gap-4',
        labels?.length && 'sticky pt-10 pb-6',
      )}
    >
      {labels?.map((label) => (
        <Button
          id={`label-${label.id}`}
          key={label.id}
          onClick={() => handleScrollTo(label.id)}
          variant="unstyled-for-real"
          className={cn(
            'typo-label-md ink-muted hover:ink-default whitespace-nowrap first:ml-[var(--content-padding-x)] last:mr-[var(--content-padding-x)]',
            'rounded-none px-0 h-min',
            'border-b-2 border-b-[transparent] hover:border-b-gold-emphasis pb-2',
            activeId === label.id && 'ink-default border-b-gold-emphasis',
          )}
          size="lg"
        >
          {label.title}
        </Button>
      ))}
    </div>
  )
}

export function useIntersectionObserver(
  ids: string[],
  rootSelector: string,
  isScrolling: boolean,
) {
  const [activeId, setActiveId] = useState<string | null>(null)

  useEffect(() => {
    if (isScrolling) return
    const rootElement = document.querySelector(rootSelector)
    if (!rootElement) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id)
            const labelElement = document.getElementById(
              `label-${entry.target.id}`,
            )
            if (labelElement) {
              labelElement.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center',
              })
            }
          }
        })
      },
      {
        root: rootElement,
        rootMargin: `-20% 0px -70% 0px`,
        threshold: 0.5,
      },
    )

    ids.forEach((id) => {
      const element = document.getElementById(id)
      if (element) {
        observer.observe(element)
      }
    })

    return () => {
      ids.forEach((id) => {
        const element = document.getElementById(id)
        if (element) {
          observer.unobserve(element)
        }
      })
    }
  }, [ids, rootSelector, isScrolling])

  return { activeId, setActiveId }
}
