'use client'

import Image from 'next/image'

import { cn } from '@nordvik/theme/cn'

import { BrandLoopVideoPlayer } from '@/components/looping-brand-videos/video-player'

export default function NotFound() {
  return (
    <div data-theme="dark" className="absolute inset-0 flex-center bg-root">
      <BrandLoopVideoPlayer startIndex={0} className="bg-root" />
      <div className="absolute inset-0 md:hidden bg-[black] opacity-80" />
      <main className={cn('relative md:bg-root rounded-md p-16 text-center')}>
        <div className="flex flex-col gap-1 items-center px-[--container-padding]">
          <div data-theme="dark" className="mb-11">
            <Image src="/logo.svg" alt="" width={200} height={65} />
          </div>
          <h3 className="typo-title-sm"><PERSON><PERSON><PERSON>, her er det ikke noe</h3>
          <p className="typo-body-lg ink-subtle max-w-[24rem]">
            Det kan være fordi lenken er utdatert eller du ikke har tilgang til
            innholdet.
          </p>
        </div>
      </main>
    </div>
  )
}
