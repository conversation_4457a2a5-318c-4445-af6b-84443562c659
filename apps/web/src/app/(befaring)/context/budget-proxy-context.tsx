'use client'

import React from 'react'

type BudgetPriceProxy = {
  budgetTotalReduction: number | null
  budgetGrandTotal: number | null
  setBudgetTotalReduction: (value: number | null) => void
  setBudgetGrandTotal: (value: number | null) => void
}

export const BudgetPriceProxyContext = React.createContext<BudgetPriceProxy>({
  budgetTotalReduction: null,
  budgetGrandTotal: null,
  setBudgetTotalReduction: () => void 0,
  setBudgetGrandTotal: () => void 0,
})

export const useBudgetPriceProxy = () => {
  const context = React.useContext(BudgetPriceProxyContext)
  return context
}

export const BudgetPriceProxyProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [budgetTotalReduction, setBudgetTotalReduction] = React.useState<
    number | null
  >(null)
  const [budgetGrandTotal, setBudgetGrandTotal] = React.useState<number | null>(
    null,
  )

  const value = React.useMemo(
    () => ({
      budgetTotalReduction,
      budgetGrandTotal,
      setBudgetTotalReduction,
      setBudgetGrandTotal,
    }),
    [
      budgetTotalReduction,
      budgetGrandTotal,
      setBudgetTotalReduction,
      setBudgetGrandTotal,
    ],
  )

  return (
    <BudgetPriceProxyContext.Provider value={value}>
      {children}
    </BudgetPriceProxyContext.Provider>
  )
}
