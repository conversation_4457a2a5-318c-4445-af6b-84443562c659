'use client'

import { updateSuggestedPrice } from '@befaring/actions/listing-agreement'
import { ListingAgreementResponse } from '@befaring/actions/types'
import {
  type ListingAgreement,
  calculateBudgetSection,
  calculateBudgetTotalReduction,
} from '@befaring/lib/budget-helpers'
import { calculateCommission } from '@befaring/lib/calculate-commission'
import {
  listingAgreementCompanySchema,
  listingAgreementSchema,
  validatedFieldsTranslation,
} from '@befaring/lib/check-valid-fields'
import {
  type ListingAgreementBroker,
  formatBrokersForContract,
} from '@befaring/lib/format-brokers'
import {
  type SellerInsurance,
  getEstateInsuranceData,
} from '@befaring/lib/prices'
import isEqual from 'lodash/isEqual'
import pick from 'lodash/pick'
import React, { createContext, useContext, useOptimistic } from 'react'

import { listing_agreements } from '@nordvik/database'

import type {
  MergedBudgetPost,
  NextAssignmentBudget,
} from '@/actions/next/types-next'
import {
  GQLGetBrokerEstateQuery,
  GQLMarketingPackage,
  useMarketingPackagesQuery,
} from '@/api/generated-client'

import { ValidationErrorItem, useValidateForm } from '../hooks/useValidateForm'

import { useBudgetPriceProxy } from './budget-proxy-context'

export type CustomerInfo = Pick<
  listing_agreements,
  | 'married_or_in_partnership'
  | 'is_common_estate'
  | 'owner_is_seller'
  | 'waive_withdrawal_right'
  | 'no_previous_brokers'
  | 'previous_brokers'
  | 'receive_loan_offer'
  | 'recipient_loan_offer'
  | 'seller_is_shareholder'
  | 'loan_offer_comment'
>

interface BudgetContextProps {
  budget: NextAssignmentBudget
  listingAgreement: ListingAgreement
  brokers: ListingAgreementBroker[]
  locked: boolean
  errorFields: ValidationErrorItem[] | null
  setErrorFields: React.Dispatch<
    React.SetStateAction<ValidationErrorItem[] | null>
  >

  documentId: string | null
  setDocumentId: React.Dispatch<React.SetStateAction<string | null | undefined>>

  isUpdating: boolean
  setIsUpdating: React.Dispatch<React.SetStateAction<boolean>>
  editMode: boolean
  setEditMode(editMode: boolean): void

  suggestedPrice: number | null
  setSuggestedPrice: React.Dispatch<React.SetStateAction<number>>

  feePercentage: number | null
  setFeePercentage: React.Dispatch<React.SetStateAction<number | null>>
  setFixedCommission: React.Dispatch<React.SetStateAction<number | null>>

  budgetSum: number
  budgetGrandTotal: number

  outlay: { posts: MergedBudgetPost[]; sum: number | null | undefined }
  setOutlayPosts: React.Dispatch<React.SetStateAction<MergedBudgetPost[]>>

  income: { posts: MergedBudgetPost[]; sum: number | null | undefined }
  setIncomePosts: React.Dispatch<React.SetStateAction<MergedBudgetPost[]>>

  budgetDiscount: MergedBudgetPost | undefined
  setBudgetDiscount: (post?: MergedBudgetPost) => void

  budgetTotalReduction: number

  commission: number
  initialCommission: number
  commissionIsPercentageBased: boolean

  selectedMarketingKey: string | null
  setSelectedMarketingKey: React.Dispatch<React.SetStateAction<string | null>>
  marketingPackage: GQLMarketingPackage | undefined

  hasInsurance: boolean
  setHasInsurance: React.Dispatch<React.SetStateAction<boolean>>
  sellerInsurance: SellerInsurance | null

  validateListingAgreement: () => Promise<{
    errors: ValidationErrorItem[] | null
    data: Partial<ListingAgreement>
  }>

  validateCustomerInfo: () => Promise<{
    errors: ValidationErrorItem[] | null
    data: Partial<ListingAgreement>
  }>

  customerInfo: CustomerInfo
  setCustomerInfo: React.Dispatch<React.SetStateAction<CustomerInfo>>

  valuation: {
    post?: MergedBudgetPost
  }
}

const BudgetContext = createContext<BudgetContextProps | undefined>(undefined)

export function BudgetProvider({
  children,
  listingAgreementData: {
    budget,
    listingAgreement,
    mergedDiscountPost,
    mergedIncomePosts,
    mergedOutlayPosts,
    derived,
  },
  estate,
  readOnly,
}: {
  children: React.ReactNode
  listingAgreementData: ListingAgreementResponse
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>
  readOnly?: boolean
}) {
  const [customerInfo, setCustomerInfo] = useOptimistic<CustomerInfo>(
    pick(listingAgreement, [
      'married_or_in_partnership',
      'is_common_estate',
      'owner_is_seller',
      'waive_withdrawal_right',
      'no_previous_brokers',
      'previous_brokers',
      'receive_loan_offer',
      'recipient_loan_offer',
      'seller_is_shareholder',
      'loan_offer_comment',
    ]),
  )

  const [listingAgreementErrors, setListingAgreementErrors] = React.useState<
    ValidationErrorItem[] | null
  >(null)

  const { validate } = useValidateForm<ListingAgreement>(
    estate,
    estate.hasCompanySeller
      ? listingAgreementCompanySchema
      : listingAgreementSchema,
    {
      fieldsTranslation: validatedFieldsTranslation,
    },
  )

  const { setBudgetTotalReduction, setBudgetGrandTotal } = useBudgetPriceProxy()
  const [documentId, setDocumentId] = useOptimistic(
    listingAgreement.signicat_document_id,
  )
  const estateId = listingAgreement.estate_id
  const [isUpdating, setIsUpdating] = React.useState(false)
  const [editMode, setEditMode] = React.useState(false)

  // STATIC DATA
  const locked = Boolean(documentId) || Boolean(readOnly)
  const brokers = formatBrokersForContract(estate)

  // VALIDATION
  const validateListingAgreement = React.useCallback(async () => {
    setIsUpdating(true)
    const { errors, data } = await validate(listingAgreement)
    setIsUpdating(false)
    return { errors, data }
  }, [validate, listingAgreement])

  const handleValidateListingAgreement = async () => {
    setIsUpdating(true)
    const customerErrors = await handleValidateCustomerInfo()

    if (customerErrors.errors?.length) {
      // If customerInfo has errors, we don't need to validate listingAgreement
      setIsUpdating(false)
      setListingAgreementErrors(customerErrors.errors)
      return customerErrors
    }

    const { errors, data } = await validateListingAgreement()

    setIsUpdating(false)
    setListingAgreementErrors(errors)
    return { errors, data }
  }

  // Has optimistically updated customerInfo
  const handleValidateCustomerInfo = async () => {
    const { errors, data } = await validate({
      ...customerInfo,
      suggested_price: suggestedPrice,
    })
    setListingAgreementErrors(errors)
    return { errors, data }
  }

  // On each revalidatePath we can assume that the data is refetched.
  React.useEffect(() => {
    setIsUpdating(false)
  }, [listingAgreement])

  // Validating customerInfo if it has changed and has been validated before with errors
  const customerInfoRef = React.useRef(customerInfo)
  React.useEffect(() => {
    if (
      listingAgreementErrors?.length &&
      !isEqual(customerInfo, customerInfoRef.current)
    ) {
      handleValidateCustomerInfo()
      customerInfoRef.current = customerInfo
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- we want to run this on every change of listingAgreement
  }, [customerInfo])

  // COMMISSION
  const { commissionIsPercentageBased } = derived

  const [initialCommission, setInitialCommission] = useOptimistic(
    derived.initialCommission,
  )

  const [feePercentage, setFeePercentage] = useOptimistic(derived.feePercentage)
  const [suggestedPrice, setSuggestedPrice] = useOptimistic(
    derived.suggestedPrice,
  )
  const [fixedCommission, setFixedCommission] = useOptimistic<number | null>(
    listingAgreement.commission,
  )
  const commission = calculateCommission({
    commissionIsPercentageBased,
    feePercentage,
    suggestedPrice,
    fixedCommission,
  })

  async function handleSetSuggestedPrice(price: number) {
    setSuggestedPrice(price)
    if (commissionIsPercentageBased) {
      setInitialCommission(
        calculateCommission({
          commissionIsPercentageBased,
          feePercentage: derived.initialFeePercentage,
          suggestedPrice: price,
          fixedCommission,
        }),
      )
    }
    return await updateSuggestedPrice(estateId, price)
  }

  // DISCOUNT
  const [budgetDiscount, setBudgetDiscount] = useOptimistic(mergedDiscountPost)

  // INSURANCE
  const [hasInsurance, setHasInsurance] = useOptimistic(
    listingAgreement.seller_insurance ?? false,
  )

  const sellerInsurance = getEstateInsuranceData({
    estateType: estate?.estateType,
    ownershipType: estate?.ownershipType,
    collectiveDebt: estate.estatePrice?.collectiveDebt,
    suggestedPrice,
    hasInsurance,
  })

  // MARKETING PACKAGE
  const [selectedMarketingKey, setSelectedMarketingKey] = useOptimistic(
    listingAgreement.marketing_package,
  )

  const { data } = useMarketingPackagesQuery({
    active: true,
    publicVisible: true,
    type: 'nordvik',
  })

  const marketingPackage = data?.marketingPackages.find(
    (mp) => mp.productTag === selectedMarketingKey,
  )

  // BUDGET
  const [incomePosts, setIncomePosts] = useOptimistic(mergedIncomePosts)
  const [outlayPosts, setOutlayPosts] = useOptimistic(mergedOutlayPosts)

  const budgetTotalReduction = calculateBudgetTotalReduction(
    incomePosts,
    outlayPosts,
    budgetDiscount?.amountWithTaxIncluded,
    initialCommission,
    commission,
  )

  const incomeSum = calculateBudgetSection(incomePosts)
  const outlaySum = calculateBudgetSection(outlayPosts)
  const discountSum = budgetDiscount?.amountWithTaxIncluded ?? 0 // Negative value
  const marketingSum = marketingPackage?.price ?? 0
  const insuranceSum = sellerInsurance?.sum ?? 0

  const budgetSum = commission + incomeSum + outlaySum + discountSum
  const budgetGrandTotal = budgetSum + marketingSum + insuranceSum

  React.useEffect(() => {
    setBudgetTotalReduction?.(budgetTotalReduction)
    setBudgetGrandTotal?.(budgetGrandTotal)
  }, [
    budgetTotalReduction,
    budgetGrandTotal,
    setBudgetTotalReduction,
    setBudgetGrandTotal,
  ])

  let valuationPost = incomePosts.find((post) =>
    post.name.toLowerCase().includes('verdivurdering'),
  )

  // mock valuation if vitec hub doesn't return a post
  if (!valuationPost && estate.isValuation) {
    valuationPost = {
      ...MOCK_VALUATION_BUDGET_POST,
    }
  }

  return (
    <BudgetContext.Provider
      value={{
        budget,
        listingAgreement,
        locked,
        brokers,
        errorFields: listingAgreementErrors,
        setErrorFields: setListingAgreementErrors,
        documentId,
        setDocumentId,

        isUpdating,
        setIsUpdating,
        editMode,
        setEditMode,

        suggestedPrice,
        setSuggestedPrice: handleSetSuggestedPrice,

        budgetSum,
        budgetGrandTotal,
        budgetTotalReduction,

        outlay: {
          posts: outlayPosts,
          sum: outlaySum,
        },
        setOutlayPosts,

        income: {
          posts: incomePosts,
          sum: incomeSum,
        },

        valuation: {
          post: valuationPost,
        },

        setIncomePosts,

        budgetDiscount,
        setBudgetDiscount,

        commission,
        initialCommission,
        commissionIsPercentageBased,

        selectedMarketingKey,
        setSelectedMarketingKey,
        marketingPackage,

        hasInsurance,
        setHasInsurance,
        sellerInsurance,
        feePercentage,
        setFeePercentage,
        setFixedCommission,

        validateListingAgreement: handleValidateListingAgreement,

        // Has optimistically updated customerInfo
        validateCustomerInfo: handleValidateCustomerInfo,

        customerInfo,
        setCustomerInfo,
      }}
    >
      {children}
    </BudgetContext.Provider>
  )
}

export const useBudget = (): BudgetContextProps => {
  const context = useContext(BudgetContext)
  if (!context) {
    throw new Error('useBudget must be used within a BudgetProvider')
  }
  return context
}

export const MOCK_VALUATION_BUDGET_POST: MergedBudgetPost = {
  budgetpostId: -1, // use a negative id to indicate it's a mock post
  name: 'Verdivurdering',
  userId: '',
  status: 1,
  lastChanged: new Date().toISOString(),
  changedBy: '',
  amountWithTaxExcluded: 0,
  amountWithTaxIncluded: 0,
  id: '',
  listing_agreement_id: '',
  next_budget_post_id: BigInt(-1),
  initial_price: 3900,
  description: 'Verdivurdering av bolig',
  created_at: new Date(),
  updated_at: new Date(),
  deleted_at: null,
}
