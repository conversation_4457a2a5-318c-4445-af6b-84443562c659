import type { Metada<PERSON> } from 'next'
import { redirect } from 'next/navigation'
import { Suspense } from 'react'

import { EmptyState } from '@/components/empty-state'
import { Main } from '@/components/main'
import { getCurrentUserOrThrow } from '@/lib/session'

import { AverageCommissionSection } from './components/average-commission-section'
import DashboardTabs from './components/dashboard-tabs'
// import ExpectedCommissionSection from './components/expected-commission-section'
import { ImportantTasks } from './components/important-tasks'
import { KeyFiguresSection } from './components/key-figures-section'
import { KtiSection } from './components/kti-section'
import { Q4Progress } from './components/milliard-o-meter/q4-progress'
import Q4ProgressServer from './components/milliard-o-meter/q4-progress.server'
import { RevenueSection } from './components/revenue-section'
import { DashboardContainer, Divider, Row } from './components/section'
import { ToplistSection } from './components/toplist-section'
// import { UpcomingActivities } from './components/upcoming-activities'
import { DashboardBanner } from './dashboard-banner'

export const metadata: Metadata = {
  title: 'Dashboard',
}

export default async function Page() {
  let user
  try {
    user = await getCurrentUserOrThrow()
  } catch (e) {
    return redirect('/login')
  }

  if (!user.department || !user.employeeId)
    return (
      <Root>
        <EmptyState
          className="mt-[max(10vw,4rem)]"
          illustration="no-data"
          title="Kan ikke vise dashboard"
          description="Du tilhører ikke en avdeling og derfor kan vi ikke innhente tall for deg."
        />
      </Root>
    )

  return (
    <Root>
      <div className="flex flex-col gap-6 pb-20 container pt-8 md:pt-10">
        <DashboardBanner />
        <Suspense fallback={<Q4Progress total={0} />}>
          <Q4ProgressServer />
        </Suspense>
        <ImportantTasks />
        <DashboardTabs department={user.department?.name} />
        <DashboardContainer>
          <Row>
            <Suspense>
              <RevenueSection size="large" />
            </Suspense>
            <Divider />
            <Suspense>
              <AverageCommissionSection size="small" />
            </Suspense>
          </Row>
          {/* {hasActivitiesFeatureFlag && (
            <div className="contents [&:has(>:only-child)]:hidden">
              <Divider />
              <UpcomingActivities />
            </div>
          )} */}
          {/* <Divider />
            <ExpectedCommissionSection size="full" /> */}
          <Divider />
          <Suspense>
            <KeyFiguresSection size="full" />
          </Suspense>
          <Divider />
          <Row>
            <Suspense>
              <ToplistSection size="large" department={user.department} />
            </Suspense>
            <Divider />
            <Suspense>
              <KtiSection size="large" />
            </Suspense>
          </Row>
        </DashboardContainer>
      </div>
    </Root>
  )
}

function Root({ children }: { children: React.ReactNode }) {
  return (
    <Main
      asContainer={false}
      className="bg-root"
      navBarProps={{ color: 'dark' }}
      data-theme="dark"
    >
      {children}
    </Main>
  )
}
