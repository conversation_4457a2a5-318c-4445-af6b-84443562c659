import { motion } from 'framer-motion'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import { MilestoneBadge } from './milestone-badge'
import { CONFIG } from './q4-progress'

export type RewardMilestone = {
  id: string
  value: number
  label: string
}

export type ProgressIndicatorProps = {
  progress: number
  totalSold: number
  rewardMilestones?: RewardMilestone[]
  onMilestoneHover: (milestoneId: string | null) => void
}

export function ProgressIndicator({
  progress,
  totalSold,
  rewardMilestones = [],
  onMilestoneHover,
}: ProgressIndicatorProps) {
  const animationDuration = (totalSold / 1000) * 0.7
  return (
    <div className="relative h-6 w-full rounded-full bg-chart-fill-muted-background overflow-hidden">
      {rewardMilestones.length > 0 && (
        <MilestoneBadge
          rewardMilestones={rewardMilestones}
          totalSold={totalSold}
          onMilestoneHover={onMilestoneHover}
        />
      )}

      <TotalText />
      <CurrentValueText
        progress={progress}
        totalSold={totalSold}
        animationDuration={animationDuration}
      />
    </div>
  )
}

function TotalText() {
  return (
    <div className="absolute top-1/2 -translate-y-1/2 right-2 flex items-center justify-center z-10 max-md:hidden">
      <span className="typo-body-xs text-ink-muted text-right whitespace-nowrap">
        {Number(CONFIG.MAX_VALUE).toLocaleString('no-NO')} salg
      </span>
    </div>
  )
}

function CurrentValueText({
  progress,
  totalSold,
  animationDuration,
}: {
  progress: number
  totalSold: number
  animationDuration: number
}) {
  return (
    <motion.div
      className="h-full rounded-full bg-chart-fill-gold z-[11] absolute"
      variants={{
        hidden: {
          width: '0%',
        },
        visible: {
          width: `${progress}%`,
          transition: {
            duration: animationDuration,
            ease: 'easeOut',
          },
        },
      }}
      initial="hidden"
      animate="visible"
    >
      {/* Total text positioned at the end of progress fill */}
      {totalSold > 100 && (
        <motion.div
          className={cn(
            'absolute top-1/2 -translate-y-1/2 flex items-center justify-center z-[11]',
            totalSold < 300 && 'max-md:hidden',
          )}
          variants={{
            hidden: {
              opacity: 0,
              right: 16,
            },
            visible: {
              opacity: 1,
              right: 16,
              transition: {
                duration: animationDuration * 0.3,
                ease: 'easeOut',
                delay: animationDuration * 0.7,
              },
            },
          }}
          initial="hidden"
          animate="visible"
        >
          <span className="typo-body-xs text-ink-on-success text-right whitespace-nowrap">
            {Number(totalSold).toLocaleString('no-NO')}
          </span>
        </motion.div>
      )}
    </motion.div>
  )
}
