import type { Decorator } from '@storybook/nextjs'
import type { PostHog } from 'posthog-js'
import { PostHogProvider } from 'posthog-js/react'
import React from 'react'
import { userEvent, within } from 'storybook/test'

import {
  type GQLDashboardImportantTasksQuery,
  type GQLDashboardImportantTasksQueryVariables,
  GQLImportantTaskType,
  useDashboardImportantTasksQuery,
} from '@/api/generated-client'
import { getQueryClient } from '@/lib/get-query-client'

import { ImportantTasks } from './important-tasks'

type Task = GQLDashboardImportantTasksQuery['dashboardImportantTasks'][number]

// Minimal PostHog stub so useFeatureFlag works in stories
const fakePosthog = {
  isFeatureEnabled: () => true,
  onFeatureFlags: (
    cb: (flags: string[], variants: Record<string, string | boolean>) => void,
  ) => {
    cb([], { 'important-tasks': true })
    return () => {}
  },
} as unknown

const withPosthog: Decorator = (Story) => (
  <PostHogProvider client={fakePosthog as PostHog}>
    <Story />
  </PostHogProvider>
)

export default {
  title: 'Pages / Dashboard / Important tasks',
  parameters: {
    theme: 'dark',
  },
  decorators: [withPosthog],
}

function seedTasks(tasks: Task[]) {
  const client = getQueryClient()
  const variables: GQLDashboardImportantTasksQueryVariables = {}
  const key = useDashboardImportantTasksQuery.getKey(variables)
  client.setQueryDefaults(key, { staleTime: Infinity })
  client.setQueryData(key, { dashboardImportantTasks: tasks })
}

const oneTaskData: Task[] = [
  {
    type: GQLImportantTaskType.SignListingAgreement,
    estateId: 'estate-1',
    estateAddress: 'Parkveien 12, Oslo',
    mainBrokerId: 'broker-1',
    signUrl: '/sign/estate-1',
  },
]

const multipleTasksData: Task[] = [
  {
    type: GQLImportantTaskType.EtakstChecksIncomplete,
    estateId: 'estate-2',
    estateAddress: 'Nedre gate 5, Oslo',
    mainBrokerId: 'broker-2',
    eiendomsverdiUrl: '/ev/estate-2',
  },
  {
    type: GQLImportantTaskType.AmlCheckIncomplete,
    estateId: 'estate-3',
    estateAddress: 'Kirkeveien 23, Oslo',
    mainBrokerId: 'broker-3',
    amlUrl: '/aml/estate-3',
  },
  {
    type: GQLImportantTaskType.SignListingAgreement,
    estateId: 'estate-4',
    estateAddress: 'Solli plass 1, Oslo',
    mainBrokerId: 'broker-4',
    signUrl: '/sign/estate-4',
  },
]

export const OneTask = () => {
  seedTasks(oneTaskData)
  return (
    <div className="container max-w-3xl my-8">
      <ImportantTasks />
    </div>
  )
}

export const MultipleTasks = () => {
  seedTasks(multipleTasksData)
  return (
    <div className="container max-w-3xl my-8">
      <ImportantTasks />
    </div>
  )
}

export const NoTasks = () => {
  seedTasks([])
  return (
    <div className="container max-w-3xl my-8">
      {/* Renders nothing when there are no tasks */}
      <ImportantTasks />
    </div>
  )
}

export const MultipleTasksOpen = {
  render: () => {
    seedTasks(multipleTasksData)
    return (
      <div className="container max-w-3xl my-8">
        <ImportantTasks />
      </div>
    )
  },
  async play({ canvasElement }: { canvasElement: HTMLElement }) {
    const canvas = within(canvasElement)
    // Click the dropdown toggle button using test id (avoid matching feedback button)
    const toggle = await canvas.findByTestId('important-tasks-toggle')
    await userEvent.click(toggle)
  },
}
