import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { TextButton } from '@nordvik/ui/text-button'

import { Hexagon } from './hexagon'
import { CONFIG } from './q4-progress'

export function RulesDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <TextButton className="inline typo-body-sm">
          Slik fungerer det
        </TextButton>
      </DialogTrigger>

      <DialogContent
        title={false}
        data-theme="dark"
        className="p-6 pb-10 max-w-lg"
      >
        <div className="flex items-center flex-col">
          <Hexagon
            id="q4-progress-rules"
            isCompleted={true}
            delay={0}
            winnerAvatar={null}
            size="md"
            className="mb-4"
          />
          <DialogTitle className="typo-title-md mb-1 text-center">
            Slik fungerer det
          </DialogTitle>
          <p className="typo-body-md text-center">
            Vi skal avslutte året med et brak og feirer kvartalets salgsmål med
            premier!
          </p>
        </div>
        <ul className="typo-body-md pl-[1em] list-disc [&_li]:text-pretty mt-4 space-y-1">
          <li>Barometeret er live og hvert eneste salg telles fortløpende</li>
          <li>
            Hvert 500. salg gir en premie til megleren som lander akkurat det
            salget ({CONFIG.THRESHOLDS[0]}, {CONFIG.THRESHOLDS[1]},{' '}
            {CONFIG.THRESHOLDS[2]} …)
          </li>
          <li>
            Når vi når målet på 2329 salg vinner alle - og da blir det tidenes
            AFTERPARTY på Nordvikdagene!
          </li>
        </ul>

        <p className="typo-body-md-bold mt-4 mb-2">Salgsmål og premier:</p>
        <ul className="typo-body-md pl-[1em] list-disc [&_li]:text-pretty space-y-1">
          <li>
            Salgsmål {CONFIG.THRESHOLDS[0]}: Martin Kiligitto stiller til
            tjeneste
          </li>
          <li>
            Salgsmål {CONFIG.THRESHOLDS[1]}: Oppgradert rom under Nordvikdagene
          </li>
          <li>
            Salgsmål {CONFIG.THRESHOLDS[2]}: Oppgradert rom under Nordvikdagene
            + Kjedeledelsen lager lunsj til kontoret ditt
          </li>
          <li>
            Salgsmål {CONFIG.THRESHOLDS[3]}: Meglerannonsering på Nordvik Ads,
            til en verdi av 10.000,- kroner
          </li>
          <li>
            Salgsmål {CONFIG.THRESHOLDS[4]}: Tidenes afterparty på
            Nordvikdagene!
          </li>
        </ul>

        {/* Extra internal close button always visible even if the top bar (iOS Smart App Banner) covers the usual close control */}
        <div className="mt-8 flex justify-center">
          <DialogClose asChild>
            <TextButton className="typo-body-sm">Lukk</TextButton>
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  )
}
