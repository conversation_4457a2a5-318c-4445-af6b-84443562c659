'use client'

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import { Section } from '../section'

import { DesktopHexagon } from './desktop-hexagon'
import { MobileHexagon } from './mobile-hexagon'
import { ProgressIndicator, RewardMilestone } from './progress-indicator'
import { RulesDialog } from './rules-dialog'

export type Q4ProgressProps = {
  total: number
}

export type Milestone = {
  id: string
  reward: string
  threshold: number
  isCompleted: boolean
  isMainReward: boolean
  winnerAvatar: string | null
  delay: number
}
const MAX_VALUE = 2329
export const CONFIG = {
  MAX_VALUE: MAX_VALUE,
  THRESHOLDS: [500, 1000, 1500, 2000, MAX_VALUE],
  ANIMATION_DELAY: 0.5,
}
export function Q4Progress({ total: rawTotal }: Q4ProgressProps) {
  const total = Math.max(0, rawTotal)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const milestoneRefs = useRef<Map<string, HTMLDivElement>>(new Map())
  const [hoveredMilestone, setHoveredMilestone] = useState<string | null>(null)

  // Memoize milestone completion status separately
  const milestoneCompletions = useMemo(
    () => CONFIG.THRESHOLDS.map((threshold) => total >= threshold),
    [total],
  )

  // Static milestone data (never changes)
  const staticMilestones = useMemo(
    () => [
      {
        id: 'martin-kiligitto',
        reward: 'Martin Kiligitto stiller til tjeneste',
        threshold: CONFIG.THRESHOLDS[0],
        isMainReward: false,
        winnerAvatar: null,
        delay: (CONFIG.THRESHOLDS[0] / 1000) * CONFIG.ANIMATION_DELAY,
      },
      {
        id: 'beste-rommet',
        reward: 'Oppgradert rom under Nordvikdagene',
        threshold: CONFIG.THRESHOLDS[1],
        isMainReward: false,
        winnerAvatar: null,
        delay: (CONFIG.THRESHOLDS[1] / 1000) * CONFIG.ANIMATION_DELAY,
      },
      {
        id: 'kjedeledelsen',
        reward: 'Oppgradert rom + lunsj til kontoret fra kjedeledelsen',
        threshold: CONFIG.THRESHOLDS[2],
        isMainReward: false,
        winnerAvatar: null,
        delay: (CONFIG.THRESHOLDS[2] / 1000) * CONFIG.ANIMATION_DELAY,
      },
      {
        id: 'gratis-meglerannonsering',
        reward: 'Meglerannonsering til en verdi av 10.000,- kroner',
        threshold: CONFIG.THRESHOLDS[3],
        isMainReward: false,
        winnerAvatar: null,
        delay: (CONFIG.THRESHOLDS[3] / 1000) * CONFIG.ANIMATION_DELAY,
      },
      {
        id: 'afterparty',
        reward: 'Tidenes afterparty på Nordvikdagene',
        threshold: CONFIG.THRESHOLDS[4],
        isMainReward: true,
        winnerAvatar: null,
        delay: (CONFIG.THRESHOLDS[4] / 1000) * CONFIG.ANIMATION_DELAY,
      },
    ],
    [],
  )

  // Combine static data with dynamic completion status
  const milestones: Milestone[] = useMemo(
    () =>
      staticMilestones.map((milestone, index) => ({
        ...milestone,
        isCompleted: milestoneCompletions[index],
      })),
    [staticMilestones, milestoneCompletions],
  )

  const percentage = useMemo(
    () => Math.min((total / CONFIG.MAX_VALUE) * 100, 100),
    [total],
  )

  const rewardMilestones: RewardMilestone[] = useMemo(
    () =>
      milestones
        .filter((m) => m.threshold < CONFIG.MAX_VALUE)
        .map((milestone) => ({
          id: milestone.id,
          value: milestone.threshold,
          label: `Salgsmål: ${milestone.threshold}`,
        })),
    [milestones],
  )

  useEffect(() => {
    const refs = milestoneRefs.current
    return () => {
      refs.clear()
    }
  }, [])

  // Memoize scroll function to prevent recreation
  const scrollToTarget = useCallback(() => {
    if (!scrollContainerRef.current) return

    const targetMilestone =
      [...milestones].reverse().find((m) => m.isCompleted) || milestones[0]

    if (targetMilestone) {
      const targetElement = milestoneRefs.current.get(targetMilestone.id)
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'start',
        })
      }
    }
  }, [milestones])

  // Debounce scroll updates
  useEffect(() => {
    const timeoutId = setTimeout(scrollToTarget, 1200)
    return () => clearTimeout(timeoutId)
  }, [scrollToTarget])

  return (
    <Section
      size="full"
      title={
        <div className="">
          <p className="typo-display-sm">Salg-o-meter</p>
          <span className="typo-body-sm ink-subtle">
            Vi skal avslutte året med et brak og feirer kvartalets salgsmål med
            premier! <RulesDialog />
          </span>
        </div>
      }
    >
      <div className="space-y-4 mb-8" id="q4-progress">
        <div
          className="overflow-x-auto hide-scrollbar max-md:-mx-4 max-md:px-4 md:overflow-visible"
          ref={scrollContainerRef}
        >
          {/* Desktop Layout */}
          <div className="grid grid-cols-5 w-full content-center max-md:hidden">
            {milestones.map((milestone) => (
              <DesktopHexagon
                key={milestone.id}
                milestone={milestone}
                hoveredMilestone={hoveredMilestone}
                milestoneRefs={milestoneRefs}
              />
            ))}
          </div>

          {/* Mobile Layout */}
          <div className="flex gap-2 w-max md:hidden">
            {milestones.map((milestone) => (
              <MobileHexagon
                key={milestone.id}
                milestone={milestone}
                hoveredMilestone={hoveredMilestone}
                milestoneRefs={milestoneRefs}
              />
            ))}
          </div>
        </div>

        <ProgressIndicator
          progress={percentage}
          totalSold={total}
          rewardMilestones={rewardMilestones}
          onMilestoneHover={setHoveredMilestone}
        />
      </div>
    </Section>
  )
}
