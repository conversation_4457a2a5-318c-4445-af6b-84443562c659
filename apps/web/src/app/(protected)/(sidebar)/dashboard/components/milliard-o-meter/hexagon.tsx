'use client'

import { motion, useSpring, useTransform } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'

import { cn } from '@nordvik/theme/cn'

// Utility to detect Safari browser
const isSafari = () => {
  if (typeof window === 'undefined') return false
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
}

export type HexagonProps = {
  id: string
  isCompleted?: boolean
  delay?: number
  winnerAvatar?: string | null
  size?: 'sm' | 'md' | 'xs'
  className?: string
  isExternallyHovered?: boolean
}

export function Hexagon({
  id,
  isCompleted = false,
  delay = 0,
  winnerAvatar = null,
  size = 'md',
  className,
  isExternallyHovered = false,
}: HexagonProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [showAvatar, setShowAvatar] = useState(false)

  // Simplified animations for better performance - only use when needed
  const glowEffect = useSpring(0, { stiffness: 200, damping: 25 })
  const hoverTransform = useSpring(0, { stiffness: 200, damping: 25 })

  // Transform values for animations - reduced complexity for performance
  const glowSpread = useTransform(glowEffect, [0, 1], [0, isCompleted ? 3 : 0])
  const glowOpacity = useTransform(
    glowEffect,
    [0, 1],
    [0, isCompleted ? 0.2 : 0],
  )
  const diamondY = useTransform(
    hoverTransform,
    [0, 1],
    [0, isCompleted ? -3 : 0],
  )
  const diamondScale = useTransform(
    hoverTransform,
    [0, 1],
    [1, isCompleted ? 1.005 : 1],
  )

  // Handle external hover state
  useEffect(() => {
    if (isExternallyHovered && isCompleted) {
      if (!isSafari()) {
        glowEffect.set(1)
      }
      hoverTransform.set(1)
    } else if (!isExternallyHovered) {
      glowEffect.set(0)
      hoverTransform.set(0)
    }
  }, [isExternallyHovered, isCompleted, glowEffect, hoverTransform])

  useEffect(() => {
    const currentContainer = containerRef.current
    if (!currentContainer || !isCompleted) return // Only add listeners for completed items

    const handleMouseEnter = () => {
      if (!isSafari()) {
        glowEffect.set(1)
      }
      hoverTransform.set(1)
    }

    const handleMouseLeave = () => {
      if (!isExternallyHovered) {
        glowEffect.set(0)
        hoverTransform.set(0)
      }
    }

    currentContainer.addEventListener('mouseenter', handleMouseEnter, {
      passive: true,
    })
    currentContainer.addEventListener('mouseleave', handleMouseLeave, {
      passive: true,
    })

    return () => {
      currentContainer.removeEventListener('mouseenter', handleMouseEnter)
      currentContainer.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [isCompleted, glowEffect, hoverTransform, isExternallyHovered])

  // Handle avatar transition timing
  useEffect(() => {
    if (!winnerAvatar) return

    const timer = setTimeout(
      () => {
        setShowAvatar(true)
      },
      (delay + 1) * 200,
    ) // Add 1 second after the initial delay

    return () => clearTimeout(timer)
  }, [winnerAvatar, delay])

  const sizeClasses = {
    xs: {
      svg: 32,
      circle: 'size-6',
    },
    sm: {
      svg: 72,
      circle: 'size-8',
    },
    md: {
      svg: 100,
      circle: 'size-8',
    },
  }

  return (
    <div ref={containerRef} className={cn('relative select-none', className)}>
      <motion.svg
        key={`${isCompleted}-${id}`}
        width={sizeClasses[size].svg}
        height={sizeClasses[size].svg}
        viewBox="0 0 91 90"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        initial={{ opacity: 0.5, scale: 0.8 }}
        animate={{
          opacity: isCompleted ? 1 : size === 'xs' ? 1 : 0.5,
          scale: 1,
        }}
        transition={{
          duration: 0.5,
          delay,
        }}
        style={{
          filter: isCompleted ? `url(#goldGlow_${id})` : 'none',
        }}
        className="max-md:!filter-none group/hexagon"
      >
        <g filter={`url(#filter0_f_${id})`}>
          <path
            d="M8.5 23.5759L45.5 8L82.5 23.5759V66.4241L45.5 82L8.5 66.4241V23.5759Z"
            fill="#193D41"
          />
        </g>
        {/* Border */}
        <motion.path
          key={`path-${isCompleted}-${id}`}
          d="M43.5342 7.49121C45.4137 6.61209 47.5863 6.61209 49.4658 7.49121L77.4658 20.5879C79.9273 21.7392 81.5 24.2113 81.5 26.9287V63.0713C81.5 65.7887 79.9273 68.2608 77.4658 69.4121L49.4658 82.5088C47.5863 83.3879 45.4137 83.3879 43.5342 82.5088L15.5342 69.4121C13.0727 68.2608 11.5 65.7887 11.5 63.0713V26.9287C11.5 24.2113 13.0727 21.7392 15.5342 20.5879L43.5342 7.49121Z"
          fill="#193D41"
          strokeWidth="2"
          initial={{ stroke: '#47686B' }}
          animate={{
            stroke: isCompleted ? `url(#sparkGradient_${id})` : '#47686B',
          }}
          transition={{ duration: 0.5, delay }}
        />

        <motion.g
          opacity="0.4"
          filter={`url(#filter1_f_${id})`}
          animate={{
            scale: isCompleted ? [1, 1.2, 1] : 1,
          }}
          transition={{
            duration: 0.3,
            ease: 'easeOut',
            scale: {
              duration: isCompleted ? 0.9 : 0.3,
              ease: 'easeInOut',
              delay: delay + 0.1,
            },
          }}
        >
          <circle cx="46.125" cy="47.875" r="16.875" fill="white" />
        </motion.g>

        <mask
          id={`mask0_${id}`}
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="24"
          y="30"
          width="45"
          height="35"
        >
          <path
            d="M25.3672 44.4935C23.7204 42.7733 23.5839 40.1057 25.0465 38.2263L30.0035 31.8569C30.9129 30.6883 32.3108 30.0049 33.7915 30.0049H59.3093C60.8016 30.0049 62.2091 30.699 63.1175 31.883L67.9952 38.2403C69.4343 40.116 69.2934 42.7602 67.663 44.4723L50.0276 62.9919C48.1393 64.9748 44.9776 64.9789 43.0842 63.001L25.3672 44.4935Z"
            fill="white"
          />
        </mask>
        {/* Diamond */}
        <motion.g
          mask={`url(#mask0_${id})`}
          style={{
            transformOrigin: 'center',
            y: diamondY,
            scale: diamondScale,
          }}
          animate={{
            opacity: showAvatar ? 0 : 1,
            scale: showAvatar ? 0.8 : 1,
          }}
          transition={{
            duration: 0.6,
            ease: 'easeInOut',
          }}
        >
          <g filter={`url(#filter2_n_${id})`}>
            <path
              d="M22.4999 41.4984L31.4446 30.0049H61.6764L70.4948 41.4984L46.5605 66.6326L22.4999 41.4984Z"
              fill={`url(#paint0_linear_${id})`}
            />
          </g>
          <path
            d="M22.5 41.4985L33.5767 45.1563L46.5606 66.6327L22.5 41.4985Z"
            fill="black"
            fillOpacity="0.1"
          />
          <path
            d="M70.4341 41.4985L59.173 45.1563L46.5604 66.6327L70.4341 41.4985Z"
            fill="black"
            fillOpacity="0.1"
          />
          <g style={{ mixBlendMode: 'overlay' }}>
            <path
              d="M46.3766 32.5185L31.3466 30L33.5771 45.1563H46.3134H46.4397H59.1735L61.404 30L46.3766 32.5185Z"
              fill="white"
            />
          </g>
          <g style={{ mixBlendMode: 'soft-light' }}>
            <path
              d="M31.3466 30L46.561 35.0521L61.6768 30.0051L31.3466 30Z"
              fill="white"
            />
          </g>
          <g style={{ mixBlendMode: 'overlay' }}>
            <path
              d="M46.3759 32.5181L33.5764 45.1559H59.1729L46.3759 32.5181Z"
              fill="white"
            />
          </g>
          <g style={{ mixBlendMode: 'soft-light' }}>
            <path
              d="M46.5603 35.0518L33.5764 45.156H59.1729L46.5603 35.0518Z"
              fill="white"
            />
          </g>
          <g style={{ mixBlendMode: 'soft-light' }}>
            <path
              d="M33.5767 45.1563L22.5 41.4986L31.3462 30H33.5767V45.1563Z"
              fill="white"
            />
          </g>
          <g style={{ mixBlendMode: 'soft-light' }}>
            <path
              d="M59.1731 45.1563L70.4999 41.4986L61.6537 30H59.4232L59.1731 45.1563Z"
              fill="white"
            />
          </g>
          <g style={{ mixBlendMode: 'soft-light' }} opacity="0.5">
            <path
              d="M33.5764 45.1562L46.4972 66.6L59.1729 45.1562H33.5764Z"
              fill="white"
            />
          </g>
          <g
            style={{ mixBlendMode: 'soft-light' }}
            opacity="0.3"
            filter={`url(#filter3_i_${id})`}
          >
            <path
              d="M33.5764 45.1562L46.4972 66.6L59.1729 45.1562H33.5764Z"
              fill="black"
            />
          </g>
        </motion.g>

        {/* Avatar */}
        {winnerAvatar && (
          <motion.g
            initial={{ opacity: 0 }}
            animate={{
              opacity: showAvatar ? 0.75 : 0,
            }}
            transition={{
              duration: 0.2,
              delay,
            }}
            className={cn(
              showAvatar &&
                'group-hover/hexagon:opacity-100 transition-opacity duration-300',
              isExternallyHovered && 'opacity-100',
            )}
          >
            <image
              href={winnerAvatar}
              x="5.5"
              y="5"
              width="80"
              height="80"
              clipPath={`url(#hexagonClip_${id})`}
              preserveAspectRatio="xMidYMid slice"
            />
          </motion.g>
        )}

        {/* Sprinkles */}
        {!showAvatar && (
          <motion.g
            style={{
              mixBlendMode: 'plus-lighter',
              transformOrigin: 'center',
              y: diamondY,
              scale: diamondScale,
            }}
          >
            <circle
              cx="58.5189"
              cy="28.1003"
              r="0.644015"
              fill="white"
              data-completed={isCompleted}
              className="group-hover/hexagon:animate-[pulse_0.5s_0.1s_ease-in-out_infinite] data-[completed=true]:animate-[pulse_0.8s_ease-in-out_2s]"
            />
            <circle
              cx="49.6303"
              cy="24.8775"
              r="0.515212"
              fill="white"
              data-completed={isCompleted}
              className="group-hover/hexagon:animate-[pulse_0.5s_0.2s_ease-in-out_infinite] data-[completed=true]:animate-[pulse_0.8s_ease-in-out_2s]"
            />
            <circle
              opacity="0.5"
              cx="37.2652"
              cy="26.5792"
              r="0.515212"
              fill="white"
              data-completed={isCompleted}
              className="group-hover/hexagon:animate-[pulse_0.5s_0.3s_ease-in-out_infinite] data-[completed=true]:animate-[pulse_0.8s_ease-in-out_2s]"
            />
            <circle
              cx="44.2194"
              cy="22.8562"
              r="0.257606"
              fill="white"
              data-completed={isCompleted}
              className="group-hover/hexagon:animate-[pulse_0.5s_0.4s_ease-in-out_infinite] data-[completed=true]:animate-[pulse_0.8s_ease-in-out_2s]"
            />
            <circle
              opacity="0.3"
              cx="51.9486"
              cy="20.7942"
              r="0.257606"
              fill="white"
              data-completed={isCompleted}
              className="group-hover/hexagon:animate-[pulse_0.5s_0.5s_ease-in-out_infinite] data-[completed=true]:animate-[pulse_0.8s_ease-in-out_2s]"
            />
            <circle
              opacity="0.5"
              cx="48.8571"
              cy="15.1292"
              r="0.257606"
              fill="white"
              data-completed={isCompleted}
              className="group-hover/hexagon:animate-[pulse_0.5s_0.6s_ease-in-out_infinite] data-[completed=true]:animate-[pulse_0.8s_ease-in-out_2s]"
            />
            <circle
              opacity="0.5"
              cx="41.258"
              cy="18.6068"
              r="0.128803"
              fill="white"
              data-completed={isCompleted}
              className="group-hover/hexagon:animate-[pulse_0.5s_0.7s_ease-in-out_infinite] data-[completed=true]:animate-[pulse_0.8s_ease-in-out_2s]"
            />
            <circle
              opacity="0.2"
              cx="45.1222"
              cy="10.8788"
              r="0.128803"
              fill="white"
              data-completed={isCompleted}
              className="group-hover/hexagon:animate-[pulse_0.5s_0.8s_ease-in-out_infinite] data-[completed=true]:animate-[pulse_0.8s_ease-in-out_2s]"
            />
          </motion.g>
        )}

        <defs>
          {/* Hexagon clipPath for avatar */}
          <clipPath id={`hexagonClip_${id}`}>
            <path d="M43.5342 7.49121C45.4137 6.61209 47.5863 6.61209 49.4658 7.49121L77.4658 20.5879C79.9273 21.7392 81.5 24.2113 81.5 26.9287V63.0713C81.5 65.7887 79.9273 68.2608 77.4658 69.4121L49.4658 82.5088C47.5863 83.3879 45.4137 83.3879 43.5342 82.5088L15.5342 69.4121C13.0727 68.2608 11.5 65.7887 11.5 63.0713V26.9287C11.5 24.2113 13.0727 21.7392 15.5342 20.5879L43.5342 7.49121Z" />
          </clipPath>

          {/* Diamond clipPath (legacy) */}
          <clipPath id={`avatarClip_${id}`}>
            <path d="M25.3672 44.4935C23.7204 42.7733 23.5839 40.1057 25.0465 38.2263L30.0035 31.8569C30.9129 30.6883 32.3108 30.0049 33.7915 30.0049H59.3093C60.8016 30.0049 62.2091 30.699 63.1175 31.883L67.9952 38.2403C69.4343 40.116 69.2934 42.7602 67.663 44.4723L50.0276 62.9919C48.1393 64.9748 44.9776 64.9789 43.0842 63.001L25.3672 44.4935Z" />
          </clipPath>

          {/* Circular mask with gradient fade */}
          <mask id={`avatarMask_${id}`}>
            <circle cx="45.5" cy="45" r="28" fill="url(#avatarFade_${id})" />
          </mask>

          {/* Radial gradient for smooth fade to edges */}
          <radialGradient id={`avatarFade_${id}`} cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="white" stopOpacity="1" />
            <stop offset="50%" stopColor="white" stopOpacity="1" />
            <stop offset="70%" stopColor="white" stopOpacity="0.8" />
            <stop offset="85%" stopColor="white" stopOpacity="0.4" />
            <stop offset="95%" stopColor="white" stopOpacity="0.1" />
            <stop offset="100%" stopColor="white" stopOpacity="0" />
          </radialGradient>

          <filter id={`avatarBlend_${id}`} colorInterpolationFilters="sRGB">
            {/* Convert to grayscale first */}
            <feColorMatrix
              type="matrix"
              values="0.299 0.587 0.114 0 0 0.299 0.587 0.114 0 0 0.299 0.587 0.114 0 0 0 0 0 1 0"
              result="grayscale"
            />
            {/* Apply theme colors based on completion state */}
            <feColorMatrix
              type="matrix"
              values={
                isCompleted
                  ? '0.79 0.69 0.57 0 0.05  0.78 0.54 0.23 0 0.05  0.67 0.61 0.55 0 0.05  0 0 0 1 0'
                  : '0.41 0.67 0.69 0 0.05  0.20 0.26 0.27 0 0.05  0.40 0.66 0.67 0 0.05  0 0 0 1 0'
              }
              result="colorized"
            />
            {/* Enhance contrast */}
            <feComponentTransfer result="contrast">
              <feFuncA type="discrete" tableValues="0 .5 .5 .7 .7 .8 .9 1" />
            </feComponentTransfer>
          </filter>

          <filter
            id={`goldGlow_${id}`}
            x="-80%"
            y="-80%"
            width="260%"
            height="260%"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <motion.feDropShadow
              dx="0"
              dy="0"
              floodColor={isCompleted ? '#D7B180' : '#69AAB1'}
              stdDeviation={glowSpread}
              floodOpacity={glowOpacity}
            />
          </filter>
          <filter
            id={`filter0_f_${id}`}
            x="0.5"
            y="0"
            width="90"
            height="90"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur" />
          </filter>
          <filter
            id={`filter1_f_${id}`}
            x="13.7812"
            y="15.5312"
            width="64.6875"
            height="64.6875"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feGaussianBlur
              stdDeviation="7.73438"
              result="effect1_foregroundBlur"
            />
          </filter>
          <filter
            id={`filter2_n_${id}`}
            x="22.4999"
            y="30.0049"
            width="47.9949"
            height="36.6277"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feTurbulence
              type="fractalNoise"
              baseFrequency="2.6666667461395264 2.6666667461395264"
              stitchTiles="stitch"
              numOctaves="3"
              result="noise"
              seed="4614"
            />
            <feColorMatrix
              in="noise"
              type="luminanceToAlpha"
              result="alphaNoise"
            />
            <feComponentTransfer in="alphaNoise" result="coloredNoise1">
              <feFuncA
                type="discrete"
                tableValues="1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "
              />
            </feComponentTransfer>
            <feComposite
              operator="in"
              in2="shape"
              in="coloredNoise1"
              result="noise1Clipped"
            />
            <feFlood floodColor="rgba(0, 0, 0, 0.06)" result="color1Flood" />
            <feComposite
              operator="in"
              in2="noise1Clipped"
              in="color1Flood"
              result="color1"
            />
            <feMerge result="effect1_noise">
              <feMergeNode in="shape" />
              <feMergeNode in="color1" />
            </feMerge>
          </filter>
          <filter
            id={`filter3_i_${id}`}
            x="33.5764"
            y="42.1562"
            width="25.5964"
            height="24.4436"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="-3" />
            <feGaussianBlur stdDeviation="11.25" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
            />
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
          </filter>
          <linearGradient
            id={`paint0_linear_${id}`}
            x1="46.4974"
            y1="30.0049"
            x2="46.4974"
            y2="66.6326"
            gradientUnits="userSpaceOnUse"
          >
            <motion.stop
              key={`stop1-${isCompleted}-${id}`}
              initial={{ stopColor: '#69AAB1' }}
              animate={{
                stopColor: isCompleted ? '#CAB192' : '#69AAB1',
              }}
              transition={{ duration: 0.5, delay }}
            />
            <motion.stop
              key={`stop2-${isCompleted}-${id}`}
              offset="0.5"
              initial={{ stopColor: '#324244' }}
              animate={{
                stopColor: isCompleted ? '#C6893A' : '#324244',
              }}
              transition={{ duration: 0.5, delay }}
            />
            <motion.stop
              key={`stop3-${isCompleted}-${id}`}
              offset="1"
              initial={{ stopColor: '#65A8AB' }}
              animate={{
                stopColor: isCompleted ? '#AA9C8B' : '#65A8AB',
              }}
              transition={{ duration: 0.5, delay }}
            />
          </linearGradient>

          <linearGradient
            id={`sparkGradient_${id}`}
            x1="0"
            y1="0"
            x2="100"
            y2="0"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0%" stopColor="#D7B180">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#D7B180;#D7B180;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="10%" stopColor="#D7B180">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#D7B180;#D7B180;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="15%" stopColor="#FFE57F">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#FFE57F;#FFE57F;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="20%" stopColor="#FFFFFF">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#FFFFFF;#FFFFFF;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="25%" stopColor="#FFE57F">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#FFE57F;#FFE57F;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="30%" stopColor="#D7B180">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#D7B180;#D7B180;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="70%" stopColor="#D7B180">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#D7B180;#D7B180;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="75%" stopColor="#FFE57F">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#FFE57F;#FFE57F;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="80%" stopColor="#FFFFFF">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#FFFFFF;#FFFFFF;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="85%" stopColor="#FFE57F">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#FFE57F;#FFE57F;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="90%" stopColor="#D7B180">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#D7B180;#D7B180;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            <stop offset="100%" stopColor="#D7B180">
              {isCompleted && (
                <animate
                  attributeName="stop-color"
                  values="#D7B180;#D7B180;#C6893A"
                  dur="8s"
                  fill="freeze"
                />
              )}
            </stop>
            {isCompleted && (
              <animateTransform
                attributeName="gradientTransform"
                type="rotate"
                values="0 46 45;360 46 45"
                dur="3s"
                repeatCount="3"
              />
            )}
          </linearGradient>
        </defs>
      </motion.svg>
    </div>
  )
}
