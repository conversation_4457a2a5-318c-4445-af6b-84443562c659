import { motion } from 'framer-motion'

import { cn } from '@nordvik/theme/cn'
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from '@nordvik/ui/tooltip'

import { Hexagon } from './hexagon'
import { RewardMilestone } from './progress-indicator'
import { CONFIG } from './q4-progress'

export function MilestoneBadge({
  rewardMilestones,
  totalSold,
  onMilestoneHover,
}: {
  rewardMilestones: RewardMilestone[]
  totalSold: number
  onMilestoneHover: (milestoneId: string | null) => void
}) {
  return rewardMilestones.map((milestone, index) => {
    const milestonePercentage = (milestone.value / CONFIG.MAX_VALUE) * 100
    const isReached = totalSold >= milestone.value
    // Calculate when this milestone should appear during the animation
    const milestoneDelay = (milestone.value / 1000) * 0.5

    return (
      <motion.div
        key={milestone.value + index}
        className="absolute z-20 h-full cursor-pointer"
        style={{ left: `${milestonePercentage}%` }}
        initial={{ opacity: 0, scale: 0 }}
        animate={{
          opacity: 1,
          scale: 1,
          transition: { delay: milestoneDelay, duration: 0.3 },
        }}
        onMouseEnter={() => {
          onMilestoneHover(milestone.id)
        }}
        onMouseLeave={() => {
          onMilestoneHover(null)
        }}
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn(
                'absolute top-1/2 rounded-full size-6 hover:bg-overlay -translate-x-1/2 -translate-y-1/2 flex-center',
                isReached && 'text-[#6E624B] hover:ink-gold-muted',
                !isReached && 'text-[rgba(0,0,0,0.3)] hover:text-[#6E624B]',
              )}
            >
              <svg
                width="11"
                height="10"
                viewBox="0 0 10 9"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.7093 9H5H5.34884L10 3.15328V2.89051L7.96512 0H5H2.03488L0 2.89051V3.15328L4.7093 9Z"
                  fill="currentColor"
                />
              </svg>
            </div>
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent
              variant="dark"
              side="bottom"
              className="flex items-center gap-2 "
            >
              {milestone.label}{' '}
              <Hexagon id={milestone.id} size="xs" isCompleted={isReached} />
            </TooltipContent>
          </TooltipPortal>
        </Tooltip>
      </motion.div>
    )
  })
}
