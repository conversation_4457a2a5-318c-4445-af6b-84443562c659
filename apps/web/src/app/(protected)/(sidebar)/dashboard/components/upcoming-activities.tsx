'use client'

import { keepPreviousData } from '@tanstack/react-query'
import { parseISO } from 'date-fns'
import { AnimatePresence, motion } from 'framer-motion'
import { CalendarIcon, ClockIcon } from 'lucide-react'
import Link from 'next/link'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import {
  GQLDashboardType,
  GQLDashboardUpcomingActivitiesQuery,
  useDashboardUpcomingActivitiesQuery,
} from '@/api/generated-client'
import { HorizontalScrollContainer } from '@/components/broker-profile/presentation/components/scroll-container'
import { LoadingIndicator } from '@/components/loading-indicator'
import { formatDate } from '@/lib/dates'

type Activity =
  GQLDashboardUpcomingActivitiesQuery['dashboardUpcomingActivities'][number]

export function UpcomingActivities() {
  const { data, isLoading, error } = useDashboardUpcomingActivitiesQuery(
    {
      type: 'personal' as GQLDashboardType,
    },
    { placeholderData: keepPreviousData },
  )

  if (error) {
    return null
  }

  const activities = data?.dashboardUpcomingActivities
  if (!activities?.length && !isLoading) return null

  return (
    <div className="flex flex-col gap-2.5" aria-busy={isLoading}>
      <div className="flex items-center gap-2">
        <h2 className="typo-label-lg">Kommende aktiviteter</h2>
        {isLoading && <LoadingIndicator className="translate-y-px" />}
      </div>
      <HorizontalScrollContainer
        alignChildrenTop
        loading={isLoading}
        listClassName={cn(`
          [--overflow:--container-padding] xl:[--overflow:0]
          [--gap-x:1rem] gap-x-[--gap-x]
          auto-cols-[70%]
          md:auto-cols-[calc((100%-var(--gap-x)*2)/3)]
          subtle-scrollbar
          items-start
          pb-4
        `)}
        beforeNavigation={
          <button
            data-feedback="upcoming-events"
            className="typo-label-sm max-md:hidden bg-root-muted ink-subtle hover:ink-default  rounded-sm px-2 py-1 self-center mr-2"
          >
            Gi tilbakemelding
          </button>
        }
      >
        <AnimatePresence mode="popLayout" initial={false}>
          {isLoading
            ? Array.from({ length: 3 }).map((_, index) => (
                <ActivityCardSkeleton key={index} />
              ))
            : activities
                ?.filter((activity) => !activity.done)
                .map((activity) => (
                  <ActivityCard key={activity.id} activity={activity} />
                ))}
        </AnimatePresence>
      </HorizontalScrollContainer>
    </div>
  )
}

const MotionLink = motion.create(Link)

const ActivityCard = React.forwardRef<
  HTMLAnchorElement,
  { activity: Activity }
>(function ActivityCard({ activity }, ref) {
  const startDate = activity.start ? parseISO(activity.start) : null
  const endDate = activity.end ? parseISO(activity.end) : null
  const followUpTime = activity.name?.match(/(\d+\s*mnd)\b/)?.[0]

  return (
    <MotionLink
      ref={ref}
      href={`/oppdrag/detaljer/${activity.estateId}`}
      className="border px-4 py-3 border-muted rounded-md hover:border-subtle transition-colors"
      title={activity.name}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <h2 className="typo-body-sm-bold">
        {activity.typeName} {followUpTime ? followUpTime : ''}
        <span className="ink-muted">{' – '}</span>
        <span className="ink-subtle">{activity.estateAddress}</span>
      </h2>
      <div className="typo-body-sm ink-subtle">
        {startDate && (
          <div className="flex items-center gap-x-2 flex-wrap">
            <div className="flex items-center gap-1">
              <CalendarIcon className="size-3" />
              <span>{formatDate(startDate, 'EEEE d. MMMM')}</span>
            </div>
            {startDate && (
              <div className="flex items-center gap-1">
                <ClockIcon className="size-3" />
                <span>
                  {formatDate(startDate, 'HH:mm')}
                  {endDate && ` - ${formatDate(endDate, 'HH:mm')}`}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </MotionLink>
  )
})

const ActivityCardSkeleton = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<'div'>
>(function ActivityCardSkeleton(_, ref) {
  return (
    <motion.div
      aria-hidden
      ref={ref}
      className="border px-4 py-3 border-muted rounded-md hover:border-subtle transition-colors"
    >
      <h2 className="typo-body-sm-bold space-x-2">
        <span className="masked-placeholder-text">Aktivitetstype</span>
        <span className="masked-placeholder-text">Relevant adresse</span>
      </h2>
      <div className="typo-body-sm ink-subtle">
        <div className="flex-wrap space-x-2">
          <span className="masked-placeholder-text">Dato og måned</span>
          <span className="masked-placeholder-text">Tidspunk</span>
        </div>
      </div>
    </motion.div>
  )
})
