import { motion } from 'framer-motion'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import { Hexagon } from './hexagon'
import { Milestone } from './q4-progress'

export const DesktopHexagon = React.memo(function DesktopHexagon({
  milestone,
  hoveredMilestone,
  milestoneRefs,
}: {
  milestone: Milestone
  hoveredMilestone: string | null
  milestoneRefs: React.RefObject<Map<string, HTMLDivElement>>
}) {
  return (
    <div
      className="flex-center group/hexagon flex-col mx-auto max-w-[140px] relative max-md:min-w-[140px] max-md:mx-0 max-md:hidden"
      ref={(el) => {
        if (el && !milestoneRefs.current.has(milestone.id)) {
          milestoneRefs.current.set(milestone.id, el)
        }
      }}
    >
      <div className="relative">
        <Hexagon
          id={milestone.id + '-md'}
          isCompleted={milestone.isCompleted}
          delay={milestone.delay}
          winnerAvatar={milestone.winnerAvatar}
          size="md"
          isExternallyHovered={hoveredMilestone === milestone.id}
        />
        <motion.div
          initial={{
            opacity: 0,
            backgroundColor:
              'var(--nordvik-fill-interactive-emphasis, #ccd5d6ff)',
            color: 'var(--nordvik-ink-on-brand, #ccd5d6ff)',
          }}
          animate={{
            opacity: 1,
            backgroundColor: milestone.isCompleted
              ? 'var(--nordvik-chart-fill-gold, #d7b180ff)'
              : 'var(--nordvik-fill-interactive-emphasis, #ccd5d6ff)',

            color: milestone.isCompleted
              ? 'var(--nordvik-ink-on-gold, #d7b180ff)'
              : 'var(--nordvik-ink-on-brand, #ccd5d6ff)',
          }}
          transition={{ duration: 0.3, delay: milestone.delay }}
          className="absolute bottom-1 left-1/2 -translate-x-1/2 w-max py-0.5 px-1.5 rounded-md bg-fill-interactive-emphasis typo-label-sm text-[10px]"
        >
          {Number(milestone.threshold).toLocaleString('no-NO')} salg
        </motion.div>
      </div>
      <p
        className={cn(
          'typo-body-xs leading-relaxed ink-muted text-center transition-colors ',
          milestone.isCompleted && 'group-hover/hexagon:ink-default',
          hoveredMilestone === milestone.id && 'ink-default',
        )}
      >
        {milestone.reward}
      </p>
    </div>
  )
})
