import React from 'react'

import { getFeatureFlag } from '@/lib/analytics/feature-flag.server'
import { getQ4SalesCount } from '@/server/model/Dashboard/helpers/q4-sales-count'

import { Q4Progress } from './q4-progress'

export default async function Q4ProgressServer() {
  const isFeatureEnabled = await getFeatureFlag('milliard-o-meter')
  if (!isFeatureEnabled) return null

  const data = await getQ4SalesCount()

  return <Q4Progress total={data?.total || 0} />
}
