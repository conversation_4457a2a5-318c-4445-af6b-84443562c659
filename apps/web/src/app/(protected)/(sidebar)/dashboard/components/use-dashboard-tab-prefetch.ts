import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

import { GQLDashboardType } from '@/api/generated-client'

export function useDashboardTabPrefetch(currentTab: GQLDashboardType) {
  const router = useRouter()

  useEffect(() => {
    const otherTab =
      currentTab === GQLDashboardType.Personal
        ? GQLDashboardType.Department
        : GQLDashboardType.Personal

    router.prefetch(`/dashboard?tab=${otherTab}`)
  }, [currentTab, router])
}
