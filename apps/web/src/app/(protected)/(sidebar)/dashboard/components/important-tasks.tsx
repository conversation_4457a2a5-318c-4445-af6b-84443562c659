'use client'

import { keepPreviousData } from '@tanstack/react-query'
import { ChevronDown } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@nordvik/ui/dropdown-menu'

import {
  type GQLDashboardImportantTasksQuery,
  GQLImportantTaskType,
  useDashboardImportantTasksQuery,
} from '@/api/generated-client'
import { useFeatureFlag } from '@/lib/analytics/feature-flag'

import { TaskCTA } from './task-cta'
import { TaskMessage } from './task-message'
import type { TaskMeta } from './task-types'

type TaskWithAml =
  GQLDashboardImportantTasksQuery['dashboardImportantTasks'][number]

function getTaskMeta(task: TaskWithAml): TaskMeta {
  const href = `/oppdrag/detaljer/${task.estateId}`
  const type = task.type
  const meta = {
    href,
    address: task.estateAddress,
    signUrl: task.signUrl ?? href,
    evUrl: task.eiendomsverdiUrl ?? href,
    amlUrl: task.amlUrl ?? href,
    isSign: type === GQLImportantTaskType.SignListingAgreement,
    isEtakst: type === GQLImportantTaskType.EtakstChecksIncomplete,
    isAml: type === GQLImportantTaskType.AmlCheckIncomplete,
  }
  return meta
}

function TaskItemDot() {
  return (
    <span className="mt-[7px] size-2 rounded-full bg-success-bold shrink-0 hidden md:inline" />
  )
}

export const ImportantTasks = React.memo(function ImportantTasks() {
  const enabledFlag = useFeatureFlag('important-tasks')
  const enabled = !!enabledFlag
  const { data, isLoading, error } = useDashboardImportantTasksQuery(
    {},
    { placeholderData: keepPreviousData, enabled },
  )
  const [open, setOpen] = React.useState(false)
  const handleOpenChange = React.useCallback((v: boolean) => setOpen(v), [])

  if (!enabled || error) return null

  const tasks = data?.dashboardImportantTasks

  if (!tasks?.length || isLoading) return null

  // If there is only one task, render it directly without the dropdown
  if (tasks.length === 1) {
    const meta = getTaskMeta(tasks[0])

    return (
      <div className="flex flex-col gap-3 typo-body-xs md:typo-body-md">
        <div className="w-full">
          <div
            className={cn(
              'w-full rounded-md border px-3 py-3 md:px-4 border-stroke-subtle bg-interactive-top',
            )}
            data-theme="dark"
          >
            <div className="flex w-full flex-col items-start gap-3 md:flex-row md:items-center md:gap-4">
              <div className="flex min-w-0 grow items-start gap-3">
                <TaskItemDot />
                <div className="space-y-0.5 min-w-0">
                  <TaskMessage meta={meta} />
                </div>
              </div>
              <TaskCTA meta={meta} />
            </div>
          </div>
        </div>
      </div>
    )
  }

  const countLabel = `${tasks.length - 1} til`

  const firstMeta = getTaskMeta(tasks[0])

  return (
    <div className="flex flex-col gap-3">
      <div className="w-full">
        <DropdownMenu modal={false} open={open} onOpenChange={handleOpenChange}>
          <DropdownMenuTrigger asChild>
            <div
              className={cn(
                'w-full rounded-md border px-3 md:px-4 py-3 border-stroke-subtle bg-interactive-top cursor-default',
                open && 'rounded-b-none',
              )}
            >
              <div className="flex w-full flex-col items-start gap-3 md:flex-row md:items-center md:gap-4">
                <div
                  className="flex min-w-0 grow items-start gap-3"
                  onClick={(e) => e.stopPropagation()}
                  onPointerDown={(e) => e.stopPropagation()}
                  onKeyDown={(e) => e.stopPropagation()}
                >
                  <TaskItemDot />
                  <div className="space-y-0.5 min-w-0">
                    <TaskMessage meta={firstMeta} />
                  </div>
                </div>

                {/* CTA + count/chevron should sit next to each other on mobile */}
                <div className="flex w-full items-center justify-between gap-2 md:w-auto md:ml-auto md:gap-4 md:justify-end">
                  {/* Action button for the first task; stop propagation so it doesn't toggle the menu */}
                  <div
                    onClick={(e) => e.stopPropagation()}
                    onPointerDown={(e) => e.stopPropagation()}
                    onKeyDown={(e) => e.stopPropagation()}
                  >
                    <TaskCTA meta={firstMeta} />
                  </div>

                  {/* This area toggles the dropdown and shows pointer */}
                  <button
                    type="button"
                    data-testid="important-tasks-toggle"
                    className="flex items-center gap-2 ml-2 md:ml-0 self-end md:self-center cursor-pointer"
                    aria-expanded={open}
                    aria-haspopup="menu"
                  >
                    <span className="typo-body-md ink-subtle whitespace-nowrap">
                      {countLabel}
                    </span>
                    <ChevronDown
                      className={cn(
                        'size-4 ink-subtle transition-transform duration-200',
                        open && 'rotate-180',
                      )}
                      aria-hidden
                    />
                  </button>
                </div>
              </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="start"
            sideOffset={0}
            className={cn(
              'p-0 w-[--radix-popper-anchor-width] max-w-[--radix-popper-anchor-width] overflow-hidden rounded-md rounded-t-none bg-interactive-top',
            )}
          >
            <div
              className="bg-interactive-top border border-t-0 border-stroke-subtle"
              data-theme="dark"
            >
              {tasks.slice(1).map((task, index) => {
                const meta = getTaskMeta(task)

                return (
                  <DropdownMenuItem
                    key={`${task.type}-${task.estateId}-${index}`}
                    onSelect={(e) => e.preventDefault()}
                    className={cn(
                      'px-3 md:px-4 py-3',
                      index > 0 && 'border-t border-muted',
                      'cursor-default hover:bg-interactive-top typo-body-xs md:typo-body-md',
                    )}
                  >
                    <div className="flex w-full flex-col items-start gap-3 md:flex-row md:items-center md:gap-4">
                      <div className="flex min-w-0 grow items-start gap-3">
                        <TaskItemDot />
                        <div className="space-y-0.5 min-w-0">
                          <TaskMessage meta={meta} />
                        </div>
                      </div>
                      <TaskCTA meta={meta} />
                    </div>
                  </DropdownMenuItem>
                )
              })}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
})
