import Link from 'next/link'

import { cn } from '@nordvik/theme/cn'

import type { TaskMeta } from './task-types'

const linkClass = cn(
  // Make underline only on hover
  'typo-body-md inline max-w-full truncate md:whitespace-nowrap hover:underline underline-offset-2 underline md:no-underline',
)

export function TaskMessage({ meta }: { meta: TaskMeta }) {
  const { isSign, isEtakst, isAml, href, address } = meta

  let description: string

  switch (true) {
    case isSign:
      description = 'mangler bare din signatur'
      break
    case isEtakst:
      description = 'mangler bare e-takst før utsendelse'
      break
    case isAml:
      description = 'mangler bare risikovurdering før utsendelse'
      break
    default:
      description = 'oppgave'
  }

  return (
    <p>
      <Link href={href} className={linkClass}>
        {address}
      </Link>
      <span className="ink-muted block md:inline typo-body-md">
        <span className="hidden md:inline" aria-hidden>
          {' - '}
        </span>
        {description}
      </span>
    </p>
  )
}
