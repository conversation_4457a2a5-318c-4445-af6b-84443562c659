import React from 'react'

import { Hexagon } from './hexagon'
import { Milestone } from './q4-progress'

export const MobileHexagon = React.memo(function MobileHexagon({
  milestone,
  hoveredMilestone,
  milestoneRefs,
}: {
  milestone: Milestone
  hoveredMilestone: string | null
  milestoneRefs: React.RefObject<Map<string, HTMLDivElement>>
}) {
  return (
    <div
      className="flex-center relative md:hidden gap-2"
      ref={(el) => {
        if (el) {
          milestoneRefs.current.set(milestone.id, el)
        } else {
          milestoneRefs.current.delete(milestone.id)
        }
      }}
    >
      <Hexagon
        id={milestone.id + '-sm'}
        isCompleted={milestone.isCompleted}
        delay={milestone.delay}
        winnerAvatar={milestone.winnerAvatar}
        size="sm"
        isExternallyHovered={hoveredMilestone === milestone.id}
      />

      <div className="flex flex-col">
        <p className="typo-body-xs">
          {Number(milestone.threshold).toLocaleString('no-NO')} salg
        </p>
        <p className="typo-body-xs ink-muted max-w-[20ch]">
          {milestone.reward}
        </p>
      </div>
    </div>
  )
})
