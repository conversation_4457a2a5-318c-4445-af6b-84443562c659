import { LayoutGroup, Transition, motion } from 'framer-motion'
import { ChevronDown, InfoIcon } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import * as ButtonTabs from '@nordvik/ui/button-tabs'
import * as Select from '@nordvik/ui/select'
import { Separator } from '@nordvik/ui/separator'
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from '@nordvik/ui/tooltip'

import {
  GQLDashboardKeyFiguresQuery,
  GQLKeyFigureEntry,
} from '@/api/generated-client'
import {
  AnimateNumber,
  AnimateNumberFormat,
  AnimateNumberGroup,
} from '@/components/animate-number'
import { LoadingIndicator } from '@/components/loading-indicator'

import type { KeyFigurePeriod } from '../../types'

import { StorebrandLeadsChart } from './storebrand-leads-chart'

export function KeyFiguresContainer({
  children,
  loading,
  period,
  setPeriod,
  currentMonth = new Date().getMonth(),
}: {
  children: React.ReactNode
  loading?: boolean
  period: KeyFigurePeriod
  setPeriod: (period: KeyFigurePeriod) => void
  currentMonth?: number
}) {
  const dateFormatter = new Intl.DateTimeFormat('nb-NO', { month: 'long' })
  const months = Array.from({ length: currentMonth + 1 }, (_, i) => i)
  const hiddenMonths = months.slice(0, -2)
  const visibleMonths = months.slice(-2)

  return (
    <ButtonTabs.Root
      value={String(period)}
      onValueChange={(val) => setPeriod(val as KeyFigurePeriod)}
    >
      <div className="inline-flex items-center gap-2">
        <LayoutGroup>
          <MonthDropdown
            value={period}
            options={hiddenMonths.map((g) => ({
              label: dateFormatter.format(new Date(2022, g)),
              value: `month:${g}`,
            }))}
            onSelect={(month) => setPeriod(month as KeyFigurePeriod)}
          />
          <ButtonTabs.List asChild>
            <motion.div layout="position" transition={transition}>
              {visibleMonths.map((month) => (
                <ButtonTabs.Trigger key={month} value={`month:${month}`}>
                  <span className="capitalize">
                    {dateFormatter.format(new Date(2022, month))}
                  </span>
                </ButtonTabs.Trigger>
              ))}
              <ButtonTabs.Trigger value="all">I år</ButtonTabs.Trigger>
            </motion.div>
          </ButtonTabs.List>
          {loading && <LoadingIndicator />}
        </LayoutGroup>
      </div>
      <ButtonTabs.Content value={period} aria-busy={loading}>
        {children}
      </ButtonTabs.Content>
    </ButtonTabs.Root>
  )
}

const AnimatedAddonButton = motion.create(ButtonTabs.AddonButton)
const transition: Transition = {
  type: 'spring',
  stiffness: 350,
  damping: 30,
}

function MonthDropdown({
  options,
  onSelect,
  value,
}: {
  options: { value: string; label: string }[]
  onSelect: (month: string) => void
  value?: string
}) {
  if (options.length === 0) return null
  const hasValue = options.some((o) => o.value === value)
  return (
    <Select.Select value={value} onValueChange={onSelect}>
      <Select.Trigger asChild>
        <AnimatedAddonButton
          layout
          transition={transition}
          className={cn(
            'flex items-center capitalize',
            hasValue && 'bg-interactive-subtle gap-1.5',
          )}
        >
          <motion.span layout transition={transition}>
            <Select.SelectValue />
          </motion.span>

          <motion.div
            layout
            transition={transition}
            className="h-[1lh] flex items-center"
          >
            <ChevronDown className="size-[1rem] -mb-[0.1em]" />
          </motion.div>
        </AnimatedAddonButton>
      </Select.Trigger>
      <Select.SelectPortal data-theme="dark">
        <Select.SelectContent>
          {options.map((option) => (
            <Select.SelectItem
              key={option.value}
              value={option.value}
              className="capitalize"
            >
              {option.label}
            </Select.SelectItem>
          ))}
        </Select.SelectContent>
      </Select.SelectPortal>
    </Select.Select>
  )
}

function getFigureFormat(
  figure: GQLKeyFigureEntry,
): AnimateNumberFormat | undefined {
  if (figure.format === 'percentage') {
    return {
      style: 'percent',
      maximumFractionDigits: 0,
      trailingZeroDisplay: 'stripIfInteger',
    }
  }
  if (figure.format === 'currency') return 'currency'
  return undefined
}

export function KeyFigures({
  figures,
  leads,
  skeleton,
  department,
}: {
  figures: GQLKeyFigureEntry[]
  leads?: GQLDashboardKeyFiguresQuery['dashboardLeads']
  skeleton?: boolean
  department: boolean
}) {
  const processedLeads = React.useMemo(() => {
    if (!leads) return undefined
    if (department) return leads
    // derive a copy with adjusted names/counts to avoid mutating props
    return {
      ...leads,
      entries: leads.entries.map((entry) => {
        if (entry.current) {
          return { ...entry, name: 'Deg', employeeCount: 0 }
        }
        return { ...entry, employeeCount: 0 }
      }),
    }
  }, [leads, department])

  const leadEntry = processedLeads?.entries.find((entry) => entry.current)
  const hitBudget = leadEntry && leadEntry.actual >= leadEntry.budget
  const missingLeads = leadEntry ? leadEntry.budget - leadEntry.actual : 0

  return (
    <div className="flex gap-y-6 pt-6 gap-x-12 max-md:flex-col">
      <div className="flex flex-col col-span-4 @[26rem]/section:basis-1/2 @[50rem]/section:basis-1/3">
        <div className="mb-2">
          <div className="flex items-center gap-x-2 relative">
            <h2 className="typo-label-md whitespace-nowrap">
              Storebrand leads
            </h2>
            <Tooltip delayDuration={100}>
              <TooltipTrigger>
                <InfoIcon className="ink-muted size-4" />
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent>
                  <div className="typo-body-sm">
                    Grafen viser hvordan du ligger an målt mot budsjett og mot
                    de tre på kontoret med flest leads. Målet er minst to leads
                    per megler/fullmektig hver måned.
                  </div>
                </TooltipContent>
              </TooltipPortal>
            </Tooltip>
          </div>
          {!skeleton && leadEntry && (
            <div
              className={cn(
                'typo-body-sm whitespace-nowrap',
                hitBudget ? 'ink-success' : 'ink-gold',
              )}
            >
              {hitBudget
                ? `${department ? leadEntry.name : 'Du'} har nådd målet på ${leadEntry.budget} leads`
                : `${department ? leadEntry.name : 'Du'} mangler ${missingLeads} ${missingLeads > 1 ? 'leads' : 'lead'} for å nå målet`}
            </div>
          )}
          {skeleton && (
            <div className="typo-body-xs whitespace-nowrap masked-placeholder-text inline-block">
              Du mangler 2 leads for å nå målet
            </div>
          )}
        </div>
        <StorebrandLeadsChart
          className="group-aria-[busy=true]:opacity-30 mb-2"
          data={processedLeads?.entries ?? []}
          budget={processedLeads?.budget ?? 2}
          skeleton={skeleton}
          labelFormatter={
            // show first part of name (if not department view)
            department ? undefined : (label) => label.split(' ')[0]
          }
        />
      </div>
      <div className="flex items-center justify-center">
        <Separator orientation="vertical" />
      </div>
      <div
        aria-busy={skeleton}
        className="basis-2/3 group grid grid-rows-[repeat(auto-fit,min-content)] content-between @[26rem]/section:grid-cols-2 @[50rem]/section:grid-cols-3 gap-x-12 gap-y-12"
      >
        {figures.map((figure) => {
          return (
            <div key={figure.label} className="flex flex-col items-start">
              <h2 className="typo-label-md whitespace-nowrap mb-2 group-aria-[busy=true]:masked-placeholder-text">
                {figure.label}
              </h2>
              <div className="flex flex-wrap items-baseline gap-x-2 group-aria-[busy=true]:masked-placeholder-text">
                <AnimateNumberGroup>
                  <AnimateNumber
                    format={getFigureFormat(figure)}
                    value={figure.value || 0}
                    className="typo-display-lg"
                  />
                  <AnimateNumber
                    className="whitespace-nowrap ink-muted"
                    format={getFigureFormat(figure)}
                    value={figure.lastValue || 0}
                    isPending={skeleton}
                    suffix=" i fjor"
                  />
                </AnimateNumberGroup>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
