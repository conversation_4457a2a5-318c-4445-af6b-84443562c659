import { ArrowUpRight } from 'lucide-react'

import { Button } from '@nordvik/ui/button'

import { BankIdButton } from '@/components/bank-id-button'

import type { TaskMeta } from './task-types'

export function TaskCTA({ meta }: { meta: TaskMeta }) {
  const { isSign, isEtakst, isAml, signUrl, evUrl, amlUrl, href } = meta

  if (isSign) {
    return (
      <BankIdButton
        href={signUrl ?? href}
        size="sm"
        theme="light"
        className="shrink-0 w-full md:w-auto mt-2 md:mt-0"
      />
    )
  }

  if (isEtakst) {
    return (
      <Button
        href={evUrl ?? href}
        target={evUrl ? '_blank' : undefined}
        rel={evUrl ? 'noopener noreferrer' : undefined}
        variant="tertiary"
        size="md"
        iconEnd={<ArrowUpRight />}
        className="shrink-0 w-full md:w-auto mt-2 md:mt-0"
      >
        Lag e-takst
      </Button>
    )
  }

  if (isAml) {
    return (
      <Button
        href={amlUrl}
        variant="tertiary"
        size="md"
        className="shrink-0 w-full md:w-auto mt-2 md:mt-0"
      >
        Fullfør risikovurdering
      </Button>
    )
  }

  return null
}
