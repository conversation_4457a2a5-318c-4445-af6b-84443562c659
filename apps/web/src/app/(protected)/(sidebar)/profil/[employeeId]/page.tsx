import {
  GQLProfilePreviewByEmployeeIdQuery,
  ProfilePreviewByEmployeeIdDocument,
} from '@/api/generated-client'
import { gqlServerFetch } from '@/api/gqlServerFetch'
import { BrokerPresentation } from '@/components/broker-profile/presentation/broker-presentation'
import { EmptyState } from '@/components/empty-state'

export default async function Page({
  params,
}: {
  params: Promise<{ employeeId: string }>
}) {
  const { employeeId } = await params

  const decodedEmployeeId = decodeURIComponent(employeeId)

  const { data, error } =
    await gqlServerFetch<GQLProfilePreviewByEmployeeIdQuery>(
      ProfilePreviewByEmployeeIdDocument,
      {
        employeeId: decodedEmployeeId,
      },
    )

  if (!data?.brokerByEmployeeId || error) {
    return <EmptyState illustration="error" title="Fant ikke profilen" />
  }

  return <BrokerPresentation broker={data.brokerByEmployeeId} />
}
