'use client'

import { Loader2 } from 'lucide-react'
import { useQueryState } from 'nuqs'
import React from 'react'

import {
  useCmsChangelogsCategoriesQuery,
  useInfiniteCmsChangelogsQuery,
} from '@/api/generated-client'
import InfinityScroll from '@/components/infinity-scroll'

import { Categories } from './categories'
import { ChangelogsTimeline } from './changelog-timeline'

export function Changelogs() {
  const [selectedCategory] = useQueryState('kategori')

  const { data: categories, isLoading: categoriesLoading } =
    useCmsChangelogsCategoriesQuery()

  const {
    data,
    isLoading: loadingInitialData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteCmsChangelogsQuery(
    {
      limit: 10,
      page: 1,
      categorySlug: selectedCategory,
    },
    {
      getNextPageParam: (lastPage) => {
        if (
          lastPage.cmsChangelogs.meta.currentPage >=
          lastPage.cmsChangelogs.meta.totalPages
        ) {
          return undefined
        }

        return {
          page: lastPage.cmsChangelogs.meta.currentPage + 1,
        }
      },
      initialPageParam: 1,
    },
  )

  const changelogs = React.useMemo(
    () => data?.pages.flatMap((page) => page.cmsChangelogs.items) ?? [],
    [data],
  )

  return (
    <div>
      <Categories categories={categories} isLoading={categoriesLoading} />

      <InfinityScroll
        fetchNextPage={fetchNextPage}
        isLoading={isFetchingNextPage || loadingInitialData}
        lastPage={!hasNextPage}
      >
        {loadingInitialData || changelogs.length ? (
          <ChangelogsTimeline
            changelogs={loadingInitialData ? placeholderChangelogs : changelogs}
            isLoading={loadingInitialData && !data}
          />
        ) : null}

        {isFetchingNextPage && (
          <div className="mt-16 flex items-center gap-2 py-2 px-4 bg-brand-muted w-max mx-auto rounded-md">
            <p className="typo-body-sm">Henter mer</p>
            <Loader2 className="size-4 animate-spin" />
          </div>
        )}
      </InfinityScroll>
    </div>
  )
}

const placeholderChangelogs = Array.from({ length: 3 }, (_, index) => ({
  id: `245215-${index}`,
  slug: `add-custom-contacts-now-${index}`,
  title: `Ferdigstill den nye meglerprofilen din under profil ${index}`,
  postDate: '2025-04-02T14:36:00+02:00',
  externalUrl: undefined,
  author: undefined,
  categories: [],
  departments: undefined,
  targetRoles: [],
  image: {
    small: undefined,
    medium: undefined,
    large: undefined,
  },
  modules: [
    {
      type: 'richTextModule',
      body: `
        <p>Nå kan du oppdatere din Meglerprofil under Min Profil. Profilen vil snart bli synlig som en del av oppdragsavtalen, og vil være en sentral del av den nye befaringsmappen som lanseres i vinter.</p>
\n
<p>Slik gjør du det:</p>
\n
<ol>
   <li>Fyll inn din instagramkoto om du har en</li>
   <li>Legg til eller rediger innsalgspunktne dine. Vi har begrenset det til tre stk. Legg gjerne inn en liten setning som beskriver det også</li>
   <li>Sjekk hvilke kundeanmeldelser som er synlige på profilen. Du kan enkelt skru de av eller på ved å trykke rediger</li>
   <li>Har du vunnet noen priser eller mottatt heder og ære for noe? De som kommer fra Nordvik skal ligge inne. Legg til andre, og skjul de du ikke ønsker skal være offentlige</li>
   <li>Få frem teamet ditt? Legg til fotograf, stylist eller bygningssakkyndige du samarbeider med.</li>
</ol>
\n
<p>Klikk her for å se endringene</p>
      `,
      accordion: undefined,
    },
  ],
}))
