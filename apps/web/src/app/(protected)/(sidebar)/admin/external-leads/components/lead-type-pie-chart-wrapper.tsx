import { Prisma } from '@nordvik/database'

import prisma from '@/db/prisma'

import { LeadTypePieChart } from './lead-type-pie-chart'

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface LeadTypePieChartWrapperProps {
  filters?: FilterState
}

function buildWhereClause(
  filters?: FilterState,
): Prisma.ExternalLeadAuditWhereInput {
  const where: Prisma.ExternalLeadAuditWhereInput = {}

  if (filters?.leadType) {
    where.leadType = filters.leadType
  }

  if (filters?.department) {
    where.departmentOfBroker = filters.department
  }

  if (filters?.successStatus && filters.successStatus !== 'all') {
    where.isSuccessful = filters.successStatus === 'successful'
  }

  // Always use YTD data for this chart
  const currentYear = new Date().getFullYear()
  const ytdStart = new Date(currentYear, 0, 1) // January 1st of current year
  where.createdAt = { gte: ytdStart }

  // Only include successful leads
  where.isSuccessful = true

  return where
}

export async function LeadTypePieChartWrapper({
  filters,
}: LeadTypePieChartWrapperProps) {
  const whereClause = buildWhereClause(filters)

  // Get YTD leads by type
  const ytdLeads = await prisma.externalLeadAudit.findMany({
    where: whereClause,
    select: { leadType: true },
  })

  // Calculate lead type distribution
  const leadTypeCount = ytdLeads
    .filter((lead) => lead.leadType)
    .reduce(
      (acc, lead) => {
        const type = lead.leadType!
        acc[type] = (acc[type] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
    )

  const total = Object.values(leadTypeCount).reduce(
    (sum, count) => sum + count,
    0,
  )

  // Format data for pie chart
  const chartData = Object.entries(leadTypeCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 8) // Show top 8 lead types to avoid overcrowding
    .map(([name, value]) => ({
      name,
      value,
      percentage: Math.round((value / total) * 100),
    }))

  return (
    <div className="bg-root border border-stroke-muted rounded-lg p-6">
      <div className="mb-4">
        <h3 className="typo-body-lg font-medium">
          YTD Successful Lead Distribution
        </h3>
        <p className="typo-detail-sm ink-muted mt-1">
          Breakdown of {total.toLocaleString()} successful leads by type (Year
          to Date)
        </p>
      </div>
      <LeadTypePieChart data={chartData} />
    </div>
  )
}
