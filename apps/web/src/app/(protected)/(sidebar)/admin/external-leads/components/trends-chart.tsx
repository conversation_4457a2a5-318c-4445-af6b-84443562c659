'use client'

import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'

interface TrendsData {
  date: string
  total: number
  lastYearTotal?: number
  twoYearsAgoTotal?: number
}

interface TrendsChartProps {
  data: TrendsData[]
}

const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
]

export function TrendsChart({ data }: TrendsChartProps) {
  const hasLastYearData = data.some(
    (item) => item.lastYearTotal !== undefined && item.lastYearTotal !== null,
  )

  const hasTwoYearsAgoData = data.some(
    (item) =>
      item.twoYearsAgoTotal !== undefined && item.twoYearsAgoTotal !== null,
  )

  return (
    <div className="bg-root rounded-lg border border-stroke-muted p-6">
      <div className="mb-4">
        <h3 className="typo-display-sm">
          Successful Lead Trends Comparison - Weekly (Year to Date)
        </h3>
        {!hasLastYearData && !hasTwoYearsAgoData && (
          <p className="text-sm ink-muted mt-1">
            Historical data not available for comparison
          </p>
        )}
        {(hasLastYearData || hasTwoYearsAgoData) && (
          <p className="text-sm ink-muted mt-1">
            Comparing current year with {hasLastYearData ? '2024' : ''}
            {hasLastYearData && hasTwoYearsAgoData ? ' and ' : ''}
            {hasTwoYearsAgoData ? '2023' : ''}
          </p>
        )}
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" tick={{ fontSize: 12 }} />
          <YAxis />
          <Tooltip
            formatter={(value, name) => [value || 'No data', name]}
            labelFormatter={(label) => `Week of: ${label}`}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="total"
            stroke={COLORS[4]}
            strokeWidth={2}
            name="2025 Leads"
            connectNulls={false}
            dot={false}
            activeDot={{ r: 4 }}
          />
          {hasLastYearData && (
            <Line
              type="monotone"
              dataKey="lastYearTotal"
              stroke={COLORS[1]}
              strokeWidth={2}
              name="2024 Leads"
              connectNulls={false}
              dot={false}
              activeDot={{ r: 4 }}
            />
          )}
          {hasTwoYearsAgoData && (
            <Line
              type="monotone"
              dataKey="twoYearsAgoTotal"
              stroke={COLORS[2]}
              strokeWidth={2}
              name="2023 Leads"
              connectNulls={false}
              dot={false}
              activeDot={{ r: 4 }}
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
