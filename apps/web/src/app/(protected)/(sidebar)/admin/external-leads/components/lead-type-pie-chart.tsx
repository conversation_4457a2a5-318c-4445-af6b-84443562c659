'use client'

import { useState } from 'react'
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  Toolt<PERSON>,
} from 'recharts'

interface LeadTypePieChartProps {
  data: { name: string; value: number; percentage: number }[]
}

const COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // emerald-500
  '#F59E0B', // amber-500
  '#EF4444', // red-500
  '#8B5CF6', // violet-500
  '#06B6D4', // cyan-500
  '#F97316', // orange-500
  '#84CC16', // lime-500
  '#EC4899', // pink-500
  '#6B7280', // gray-500
]

interface TooltipPayload {
  payload: { name: string; value: number; percentage: number }
}

const CustomTooltip = ({
  active,
  payload,
}: {
  active?: boolean
  payload?: TooltipPayload[]
}) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    return (
      <div className="bg-root border border-stroke-muted rounded-lg p-3 shadow-lg">
        <p className="typo-body-sm font-medium">{data.name}</p>
        <p className="typo-detail-sm ink-muted">
          {data.value} leads ({data.percentage}%)
        </p>
      </div>
    )
  }
  return null
}

interface LegendEntry {
  value: string
  color: string
}

interface LegendProps {
  payload?: LegendEntry[]
}

export function LeadTypePieChart({ data }: LeadTypePieChartProps) {
  const [hiddenEntries, setHiddenEntries] = useState<Set<string>>(new Set())

  if (!data.length) {
    return (
      <div className="h-64 flex items-center justify-center">
        <p className="typo-body-sm ink-muted">No lead type data available</p>
      </div>
    )
  }

  // Filter out hidden entries
  const visibleData = data.filter((entry) => !hiddenEntries.has(entry.name))

  // Handle legend click to toggle visibility
  const handleLegendClick = (entry: { value: string }) => {
    const newHiddenEntries = new Set(hiddenEntries)
    if (hiddenEntries.has(entry.value)) {
      newHiddenEntries.delete(entry.value)
    } else {
      newHiddenEntries.add(entry.value)
    }
    setHiddenEntries(newHiddenEntries)
  }

  // Custom legend component with click handling
  const renderCustomLegend = (props: LegendProps) => {
    const { payload } = props
    if (!payload) return null

    return (
      <ul className="flex flex-wrap justify-center gap-2 mt-4">
        {payload.map((entry: LegendEntry, index: number) => {
          const isHidden = hiddenEntries.has(entry.value)
          return (
            <li
              key={`legend-${index}`}
              className={`flex items-center gap-1 cursor-pointer px-2 py-1 rounded transition-opacity ${
                isHidden ? 'opacity-50' : 'opacity-100'
              }`}
              onClick={() => handleLegendClick(entry)}
            >
              <span
                className="w-3 h-3 rounded-sm"
                style={{
                  backgroundColor: isHidden ? '#d1d5db' : entry.color,
                  transition: 'background-color 0.2s',
                }}
              />
              <span
                className={`typo-detail-sm ${isHidden ? 'line-through ink-muted' : ''}`}
              >
                {entry.value}
              </span>
            </li>
          )
        })}
      </ul>
    )
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={visibleData}
            cx="50%"
            cy="50%"
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ percentage }) => `${percentage}%`}
            labelLine={false}
          >
            {visibleData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend
            content={renderCustomLegend}
            verticalAlign="bottom"
            height={60}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
