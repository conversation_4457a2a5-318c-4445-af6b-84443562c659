import { Suspense } from 'react'

import { ExternalLeadsChartsWrapper } from './external-leads-charts-wrapper'
import { ExternalLeadsFilters } from './external-leads-filters'
import { ExternalLeadsOverview } from './external-leads-overview'
import { ExternalLeadsTable } from './external-leads-table'
import { LeadTypePieChartFilteredWrapper } from './lead-type-pie-chart-filtered-wrapper'
import { LeadTypePieChartWrapper } from './lead-type-pie-chart-wrapper'
import { TrendsWithTakeoverWrapper } from './trends-with-takeover-wrapper'

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface ExternalLeadsPageContentProps {
  leadTypes: string[]
  departments: string[]
  filters: FilterState
}

export function ExternalLeadsPageContent({
  leadTypes,
  departments,
  filters,
}: ExternalLeadsPageContentProps) {
  return (
    <div className="flex flex-col gap-8">
      {/* Header with filters */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="typo-display-md">Eksterne Tips</h2>
          <p className="typo-body-sm ink-muted mt-1">
            Oversikt over eksterne tips og deres behandling
          </p>
        </div>
        <ExternalLeadsFilters
          leadTypes={leadTypes}
          departments={departments}
          initialFilters={filters}
        />
      </div>

      {/* Overview Cards */}
      <div>
        <Suspense
          fallback={
            <div className="h-32 bg-stroke-muted animate-pulse rounded-lg" />
          }
        >
          <ExternalLeadsOverview filters={filters} />
        </Suspense>
      </div>

      {/* YTD Lead Types Pie Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Suspense
          fallback={
            <div className="h-80 bg-stroke-muted animate-pulse rounded-lg" />
          }
        >
          <LeadTypePieChartWrapper filters={filters} />
        </Suspense>

        <Suspense
          fallback={
            <div className="h-80 bg-stroke-muted animate-pulse rounded-lg" />
          }
        >
          <LeadTypePieChartFilteredWrapper filters={filters} />
        </Suspense>
      </div>

      {/* Trends Chart with Takeover Card */}
      <Suspense
        fallback={
          <div className="h-96 bg-stroke-muted animate-pulse rounded-lg" />
        }
      >
        <TrendsWithTakeoverWrapper filters={filters} />
      </Suspense>

      {/* Department Charts */}
      <Suspense
        fallback={
          <div className="h-96 bg-stroke-muted animate-pulse rounded-lg" />
        }
      >
        <ExternalLeadsChartsWrapper filters={filters} />
      </Suspense>

      {/* Detailed Table */}
      <Suspense
        fallback={
          <div className="h-96 bg-stroke-muted animate-pulse rounded-lg" />
        }
      >
        <ExternalLeadsTable filters={filters} />
      </Suspense>
    </div>
  )
}
