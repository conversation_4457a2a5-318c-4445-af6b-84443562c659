import { addDays, format, startOfDay } from 'date-fns'
import { fromZonedTime, toZonedTime } from 'date-fns-tz'
import { CalendarIcon } from 'lucide-react'

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@nordvik/ui/card'

import mongo from '@/db/mongo'

const timeZone = 'Europe/Oslo'

interface UpcomingTakeoverCardProps {
  days?: number // Number of days to look ahead (default: 7)
}

interface TakeoverEstate {
  takeOverDate: string
}

async function getUpcomingTakeovers(
  days: number = 7,
): Promise<TakeoverEstate[]> {
  try {
    await mongo.connect()
  } catch (error) {
    console.warn('MongoDB connection error:', error)
    return []
  }

  // Get today in Oslo timezone, then convert to UTC for MongoDB query
  const todayInOslo = startOfDay(toZonedTime(new Date(), timeZone))
  const endDateInOslo = addDays(todayInOslo, days)

  // Convert to UTC for consistent database queries
  const todayUtc = fromZonedTime(todayInOslo, timeZone)
  const endDateUtc = fromZonedTime(endDateInOslo, timeZone)

  try {
    const estates = await mongo
      .db('nordvik')
      .collection('estates')
      .find({
        takeOverDate: {
          $gte: todayUtc,
          $lte: endDateUtc,
        },
      })
      .project({
        takeOverDate: 1,
      })
      .toArray()

    return estates.map((estate) => ({
      takeOverDate: estate.takeOverDate,
    }))
  } catch (error) {
    console.error('Error fetching upcoming takeovers:', error)
    return []
  }
}

function getDaysUntilTakeover(takeOverDate: string): number {
  // Convert current time to Oslo timezone for consistent day calculations
  const todayInOslo = startOfDay(toZonedTime(new Date(), timeZone))

  // Convert the takeover date to Oslo timezone
  const takeoverInOslo = startOfDay(
    toZonedTime(new Date(takeOverDate), timeZone),
  )

  return Math.ceil(
    (takeoverInOslo.getTime() - todayInOslo.getTime()) / (1000 * 60 * 60 * 24),
  )
}

export async function UpcomingTakeoverCard({
  days = 7,
}: UpcomingTakeoverCardProps) {
  const upcomingTakeovers = await getUpcomingTakeovers(days)

  const totalCount = upcomingTakeovers.length

  // Count estates for each of the next 5 days
  const dailyCounts = Array.from({ length: 5 }, (_, index) => {
    const dayCount = upcomingTakeovers.filter(
      (estate) => getDaysUntilTakeover(estate.takeOverDate) === index,
    ).length

    // Calculate date in Oslo timezone for consistent labeling
    const todayInOslo = startOfDay(toZonedTime(new Date(), timeZone))
    const date = addDays(todayInOslo, index)
    let label = ''
    if (index === 0) label = 'I dag'
    else if (index === 1) label = 'I morgen'
    else {
      const dayNames = ['Søn', 'Man', 'Tir', 'Ons', 'Tor', 'Fre', 'Lør']
      label = dayNames[date.getDay()]
    }

    return {
      count: dayCount,
      label,
      date: format(date, 'dd.MM'),
    }
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarIcon className="w-5 h-5" />
          Kommende Overtakelser
        </CardTitle>
        <CardDescription>
          Fordeling av overtakelser de neste 5 dagene
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Summary stats for next 5 days */}
          <div className="grid grid-cols-5 gap-2 p-4 bg-root-muted rounded-lg border border-stroke-muted">
            {dailyCounts.map((day, index) => (
              <div key={index} className="text-center">
                <div className="typo-display-xs">{day.count}</div>
                <div className="typo-detail-sm ink-muted">{day.label}</div>
                <div className="typo-detail-sm ink-muted text-xs">
                  {day.date}
                </div>
              </div>
            ))}
          </div>

          {/* Total count */}
          <div className="text-center p-3 bg-root rounded-lg border border-stroke-muted">
            <div className="typo-display-sm">{totalCount}</div>
            <div className="typo-body-sm ink-muted">
              Totalt de neste {days} dagene
            </div>
          </div>

          {/* Empty state message */}
          {totalCount === 0 && (
            <div className="text-center py-8 ink-muted">
              <CalendarIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>Ingen planlagte overtakelser de neste {days} dagene</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
