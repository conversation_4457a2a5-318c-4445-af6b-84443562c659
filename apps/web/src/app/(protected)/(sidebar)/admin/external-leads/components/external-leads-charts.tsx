'use client'

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianGrid,
  LabelList,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts'

interface ChartData {
  leadsByDepartment: {
    department: string
    count: number
    lastYearCount: number
    percentageChange: number
  }[]
  trendsData: {
    date: string
    total: number
    lastYearTotal?: number
    twoYearsAgoTotal?: number
  }[]
}

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface ExternalLeadsChartsProps {
  data: ChartData
  filters?: FilterState
}

const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
]

export function ExternalLeadsCharts({
  data,
  filters,
}: ExternalLeadsChartsProps) {
  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        {filters && Object.keys(filters).length > 0 && (
          <div className="text-sm ink-muted">Filtered results</div>
        )}
      </div>
      <ChartsContent data={data} />
    </div>
  )
}

function ChartsContent({ data }: { data: ChartData }) {
  return (
    <div className="grid grid-cols-1 gap-8">
      {/* Department Performance */}
      <div className="bg-root rounded-lg border border-stroke-muted p-6">
        <h3 className="typo-display-sm mb-4">Leads by Department</h3>
        <ResponsiveContainer width="100%" height={750}>
          <BarChart
            layout="vertical"
            data={data.leadsByDepartment}
            margin={{ top: 10, right: 0, left: 0, bottom: 0 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis
              type="category"
              dataKey="department"
              tick={{ fontSize: 12 }}
              interval={0}
              width={200}
              tickFormatter={(value) =>
                value
                  ?.toString()
                  .replace('Eiendomsmegling', '')
                  .replace('Eiendomsmegler', '')
                  .replace('Eigedomsmekling', '')
                  .replace('Nordvik & Partners', '')
                  .replace('Meglerhuset', '')
                  .replace('AS', '')
                  .replace('  ', ' ')
              }
            />
            <Tooltip
              formatter={(value, name) => [
                value,
                name === 'count'
                  ? `${new Date().getFullYear()} Leads`
                  : `${new Date().getFullYear() - 1} Leads`,
              ]}
              labelFormatter={(label) => `Department: ${label}`}
            />
            <Legend />
            <Bar
              dataKey="count"
              fill={COLORS[2]}
              name={`${new Date().getFullYear()} Leads`}
            >
              <LabelList
                dataKey="percentageChange"
                position="right"
                formatter={(value: number) => {
                  if (value === 0) return ''
                  const sign = value > 0 ? '+' : ''
                  return `${sign}${value}%`
                }}
                style={{ fontSize: '11px', fontWeight: 'bold' }}
              />
            </Bar>
            <Bar
              dataKey="lastYearCount"
              fill={COLORS[1]}
              name={`${new Date().getFullYear() - 1} Leads`}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
