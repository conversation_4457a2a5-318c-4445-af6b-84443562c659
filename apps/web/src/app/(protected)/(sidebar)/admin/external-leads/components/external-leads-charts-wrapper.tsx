import { startOfWeek } from 'date-fns'

import prisma from '@/db/prisma'

import { ExternalLeadsCharts } from './external-leads-charts'

interface ChartData {
  leadsByDepartment: {
    department: string
    count: number
    lastYearCount: number
    percentageChange: number
  }[]
  trendsData: {
    date: string
    total: number
    lastYearTotal?: number
    twoYearsAgoTotal?: number
  }[]
}

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface ExternalLeadsChartsWrapperProps {
  filters?: FilterState
}

function getDateRangeFilter(dateRange?: string) {
  if (!dateRange || dateRange === 'all') return undefined

  const now = new Date()
  switch (dateRange) {
    case 'last7days':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    case 'last30days':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    case 'last90days':
      return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    default:
      return undefined
  }
}

function buildWhereClause(filters?: FilterState) {
  const where: Record<string, unknown> = {}

  if (filters?.leadType) {
    where.leadType = filters.leadType
  }

  if (filters?.department) {
    where.departmentOfBroker = filters.department
  }

  if (filters?.successStatus && filters.successStatus !== 'all') {
    where.isSuccessful = filters.successStatus === 'successful'
  }

  const dateFilter = getDateRangeFilter(filters?.dateRange)
  if (dateFilter) {
    where.createdAt = { gte: dateFilter }
  }

  return where
}

async function getChartData(filters?: FilterState): Promise<ChartData> {
  const whereClause = buildWhereClause(filters)

  // Get current year and last year leads for department comparison
  const currentYear = new Date().getFullYear()
  const lastYear = currentYear - 1
  const ytdStart = new Date(currentYear, 0, 1) // January 1st of current year
  const lastYearStart = new Date(lastYear, 0, 1) // January 1st of last year
  const lastYearYtdEnd = new Date(
    lastYear,
    new Date().getMonth(),
    new Date().getDate(),
  ) // Same date last year

  const [allLeads, lastYearLeadsForDepartments] = await Promise.all([
    // Current year leads (only successful leads)
    prisma.externalLeadAudit.findMany({
      select: {
        departmentOfBroker: true,
        createdAt: true,
      },
      where: {
        ...whereClause,
        isSuccessful: true,
        createdAt: { gte: ytdStart },
      },
    }),
    // Last year leads for department comparison (only successful leads)
    prisma.externalLeadAudit.findMany({
      select: {
        departmentOfBroker: true,
      },
      where: {
        isSuccessful: true,
        createdAt: {
          gte: lastYearStart,
          lte: lastYearYtdEnd,
        },
      },
    }),
  ])

  // Group current year leads by department
  const departmentMap = new Map<string, { count: number }>()
  allLeads.forEach((lead) => {
    if (lead.departmentOfBroker) {
      const existing = departmentMap.get(lead.departmentOfBroker) || {
        count: 0,
      }
      departmentMap.set(lead.departmentOfBroker, {
        count: existing.count + 1,
      })
    }
  })

  // Group last year leads by department
  const lastYearDepartmentMap = new Map<string, number>()
  lastYearLeadsForDepartments.forEach((lead) => {
    if (lead.departmentOfBroker) {
      const existing = lastYearDepartmentMap.get(lead.departmentOfBroker) || 0
      lastYearDepartmentMap.set(lead.departmentOfBroker, existing + 1)
    }
  })

  // Get all unique departments from both years
  const allDepartments = new Set([
    ...departmentMap.keys(),
    ...lastYearDepartmentMap.keys(),
  ])

  const leadsByDepartment = Array.from(allDepartments)
    .map((department) => {
      const currentCount = departmentMap.get(department)?.count || 0
      const lastYearCount = lastYearDepartmentMap.get(department) || 0

      // Calculate percentage change
      const percentageChange =
        lastYearCount > 0
          ? Math.round(((currentCount - lastYearCount) / lastYearCount) * 100)
          : currentCount > 0
            ? 100
            : 0 // If no last year data but current data exists, show 100%

      return {
        department: department, // Keep full department name
        count: currentCount,
        lastYearCount: lastYearCount,
        percentageChange: percentageChange,
      }
    })
    .sort((a, b) => b.count + b.lastYearCount - (a.count + a.lastYearCount)) // Sort by total activity across both years

  // Process trend data - Year to Date (reuse variables from above)
  const twoYearsAgo = currentYear - 2
  const lastYearEnd = new Date(lastYear, 11, 31, 23, 59, 59) // December 31st of last year
  const twoYearsAgoStart = new Date(twoYearsAgo, 0, 1) // January 1st of two years ago
  const twoYearsAgoEnd = new Date(twoYearsAgo, 11, 31, 23, 59, 59) // December 31st of two years ago

  // Current year data is already filtered in allLeads
  const ytdLeads = allLeads

  // Get last year's data separately (only successful leads for comparison)
  const [lastYearLeads, twoYearsAgoLeads] = await Promise.all([
    prisma.externalLeadAudit.findMany({
      select: {
        createdAt: true,
      },
      where: {
        createdAt: {
          gte: lastYearStart,
          lte: lastYearEnd,
        },
        isSuccessful: true,
      },
    }),
    prisma.externalLeadAudit.findMany({
      select: {
        createdAt: true,
      },
      where: {
        createdAt: {
          gte: twoYearsAgoStart,
          lte: twoYearsAgoEnd,
        },
        isSuccessful: true,
      },
    }),
  ])

  // Build current year trend map (weekly aggregation)
  const trendMap = new Map<
    string,
    { total: number; lastYearTotal?: number; twoYearsAgoTotal?: number }
  >()
  ytdLeads.forEach((lead) => {
    if (lead.createdAt) {
      // Group by week start date (Monday)
      const weekStart = startOfWeek(lead.createdAt, {
        weekStartsOn: 1, // Monday
      })
      const weekKey = weekStart.toISOString().split('T')[0]

      const existing = trendMap.get(weekKey) || { total: 0 }
      trendMap.set(weekKey, {
        ...existing,
        total: existing.total + 1,
      })
    }
  })

  // Build last year trend map for comparison (weekly aggregation)
  const lastYearTrendMap = new Map<string, number>()
  lastYearLeads.forEach((lead) => {
    if (lead.createdAt) {
      // Convert last year date to this year's equivalent for comparison
      const lastYearDate = new Date(lead.createdAt)
      const thisYearEquivalent = new Date(
        currentYear,
        lastYearDate.getMonth(),
        lastYearDate.getDate(),
      )

      // Group by week start date (Monday)
      const weekStart = startOfWeek(thisYearEquivalent, {
        weekStartsOn: 1, // Monday
      })
      const weekKey = weekStart.toISOString().split('T')[0]

      const existing = lastYearTrendMap.get(weekKey) || 0
      lastYearTrendMap.set(weekKey, existing + 1)
    }
  })

  // Build two years ago trend map for comparison (weekly aggregation)
  const twoYearsAgoTrendMap = new Map<string, number>()
  twoYearsAgoLeads.forEach((lead) => {
    if (lead.createdAt) {
      // Convert two years ago date to this year's equivalent for comparison
      const twoYearsAgoDate = new Date(lead.createdAt)
      const thisYearEquivalent = new Date(
        currentYear,
        twoYearsAgoDate.getMonth(),
        twoYearsAgoDate.getDate(),
      )

      // Group by week start date (Monday)
      const weekStart = startOfWeek(thisYearEquivalent, {
        weekStartsOn: 1, // Monday
      })
      const weekKey = weekStart.toISOString().split('T')[0]

      const existing = twoYearsAgoTrendMap.get(weekKey) || 0
      twoYearsAgoTrendMap.set(weekKey, existing + 1)
    }
  })

  // Merge last year data into current year trend map
  lastYearTrendMap.forEach((count, dateKey) => {
    const existing = trendMap.get(dateKey) || { total: 0 }
    trendMap.set(dateKey, {
      ...existing,
      lastYearTotal: count,
    })
  })

  // Merge two years ago data into current year trend map
  twoYearsAgoTrendMap.forEach((count, dateKey) => {
    const existing = trendMap.get(dateKey) || { total: 0 }
    trendMap.set(dateKey, {
      ...existing,
      twoYearsAgoTotal: count,
    })
  })

  const trendsData = Array.from(trendMap.entries())
    .sort(
      ([weekStartA], [weekStartB]) =>
        new Date(weekStartA).getTime() - new Date(weekStartB).getTime(),
    )
    .map(([weekStart, stats]) => ({
      date: new Date(weekStart).toLocaleDateString('nb-NO', {
        month: 'short',
        day: 'numeric',
      }),
      total: stats.total,
      lastYearTotal: stats.lastYearTotal,
      twoYearsAgoTotal: stats.twoYearsAgoTotal,
    }))

  return {
    leadsByDepartment,
    trendsData,
  }
}

export async function ExternalLeadsChartsWrapper({
  filters,
}: ExternalLeadsChartsWrapperProps) {
  const data = await getChartData(filters)

  return <ExternalLeadsCharts data={data} filters={filters} />
}
