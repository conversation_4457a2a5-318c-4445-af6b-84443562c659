import { Prisma } from '@nordvik/database'

import prisma from '@/db/prisma'

import { LeadTypePieChart } from './lead-type-pie-chart'

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface LeadTypePieChartFilteredWrapperProps {
  filters?: FilterState
}

function buildWhereClause(
  filters?: FilterState,
): Prisma.ExternalLeadAuditWhereInput {
  const where: Prisma.ExternalLeadAuditWhereInput = {}

  if (filters?.leadType) {
    where.leadType = filters.leadType
  }

  if (filters?.department) {
    where.departmentOfBroker = filters.department
  }

  if (filters?.successStatus && filters.successStatus !== 'all') {
    where.isSuccessful = filters.successStatus === 'successful'
  }

  // Always use YTD data for this chart
  const currentYear = new Date().getFullYear()
  const ytdStart = new Date(currentYear, 0, 1) // January 1st of current year
  where.createdAt = { gte: ytdStart }

  // Only include successful leads
  where.isSuccessful = true

  // Note: We'll filter out OTP and settlement leads after fetching
  // since we need to examine the JSON data column for notes.settlementBuyerOrSellerId and notes.otpId

  return where
}

function isOtpOrSettlementLead(data: Prisma.JsonValue): boolean {
  if (!data || typeof data !== 'object' || Array.isArray(data)) {
    return false
  }

  // Type guard to ensure data is a JsonObject (Record<string, unknown>)
  const dataObj = data as Record<string, unknown>

  // Check if the data has notes with settlementBuyerOrSellerId or otpId
  if (
    dataObj.notes &&
    typeof dataObj.notes === 'object' &&
    !Array.isArray(dataObj.notes)
  ) {
    const notesObj = dataObj.notes as Record<string, unknown>
    return !!(notesObj.settlementBuyerOrSellerId || notesObj.otpId)
  }

  return false
}

export async function LeadTypePieChartFilteredWrapper({
  filters,
}: LeadTypePieChartFilteredWrapperProps) {
  const whereClause = buildWhereClause(filters)

  // Get YTD leads by type (including data column for filtering)
  const ytdLeads = await prisma.externalLeadAudit.findMany({
    where: whereClause,
    select: { leadType: true, data: true },
  })

  // Calculate lead type distribution (excluding OTP and settlement leads)
  const leadTypeCount = ytdLeads
    .filter((lead) => lead.leadType && !isOtpOrSettlementLead(lead.data))
    .reduce(
      (acc, lead) => {
        const type = lead.leadType!
        acc[type] = (acc[type] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
    )

  const total = Object.values(leadTypeCount).reduce(
    (sum, count) => sum + count,
    0,
  )

  // Format data for pie chart
  const chartData = Object.entries(leadTypeCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 8) // Show top 8 lead types to avoid overcrowding
    .map(([name, value]) => ({
      name,
      value,
      percentage: Math.round((value / total) * 100),
    }))

  return (
    <div className="bg-root border border-stroke-muted rounded-lg p-6">
      <div className="mb-4">
        <h3 className="typo-body-lg font-medium">
          YTD Lead Distribution (Excluding OTP & Settlement)
        </h3>
        <p className="typo-detail-sm ink-muted mt-1">
          {total.toLocaleString()} successful leads by type, excluding OTP and
          settlement leads (YTD)
        </p>
      </div>
      <LeadTypePieChart data={chartData} />
    </div>
  )
}
