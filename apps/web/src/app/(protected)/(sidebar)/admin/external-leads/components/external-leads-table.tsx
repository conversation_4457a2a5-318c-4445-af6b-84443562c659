import {
  MailIcon,
  MapPinIcon,
  MessageCircleIcon,
  PhoneIcon,
  UserIcon,
} from 'lucide-react'

import { Badge } from '@nordvik/ui/badge'
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@nordvik/ui/tooltip'

import prisma from '@/db/prisma'
import { formatDate } from '@/lib/dates'

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface ExternalLeadsTableProps {
  filters?: FilterState
}

function getDateRangeFilter(dateRange?: string) {
  if (!dateRange || dateRange === 'all') return undefined

  const now = new Date()
  switch (dateRange) {
    case 'last7days':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    case 'last30days':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    case 'last90days':
      return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    default:
      return undefined
  }
}

function buildWhereClause(filters?: FilterState) {
  const where: Record<string, unknown> = {}

  if (filters?.leadType) {
    where.leadType = filters.leadType
  }

  if (filters?.department) {
    where.departmentOfBroker = filters.department
  }

  if (filters?.successStatus && filters.successStatus !== 'all') {
    where.isSuccessful = filters.successStatus === 'successful'
  }

  const dateFilter = getDateRangeFilter(filters?.dateRange)
  if (dateFilter) {
    where.createdAt = { gte: dateFilter }
  }

  return where
}

export async function ExternalLeadsTable({ filters }: ExternalLeadsTableProps) {
  const whereClause = buildWhereClause(filters)

  const leads = await prisma.externalLeadAudit.findMany({
    where: whereClause,
    orderBy: { createdAt: 'desc' },
    take: 100, // Show last 100 leads
  })

  return (
    <div className="bg-root rounded-lg border border-stroke-muted p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="typo-display-sm">Recent External Leads</h3>
        <Badge variant="grey">{leads.length} leads</Badge>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full [&_:is(th,td):not(:first-child)]:pl-3 [&_:is(th,td):not(:last-child)]:pr-3">
          <thead className="text-left [&_th]:typo-detail-md [&_th]:py-3 sticky top-0 bg-root border-b border-stroke-muted">
            <tr>
              <th>Lead Info</th>
              <th>Type</th>
              <th>Contact</th>
              <th>Location</th>
              <th>Broker/Department</th>
              <th>Status</th>
              <th>Created</th>
            </tr>
          </thead>
          <tbody>
            {leads.map((lead) => (
              <tr
                key={lead.id}
                className="border-b border-stroke-muted [&_td]:py-4 [&_td]:typo-body-sm hover:bg-stroke-muted/20"
              >
                <td>
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      <UserIcon className="w-8 h-8 ink-muted bg-stroke-muted rounded-full p-2" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="typo-body-md font-medium">
                        {lead.name || 'Unknown'}
                      </p>
                      <p className="typo-detail-sm ink-muted">
                        ID: {lead.externalLeadId || 'N/A'}
                      </p>
                    </div>
                  </div>
                </td>

                <td>
                  <Badge
                    variant={getLeadTypeBadgeVariant(lead.leadType)}
                    size="lg"
                  >
                    {formatLeadType(lead.leadType)}
                  </Badge>
                </td>

                <td>
                  <div className="space-y-1">
                    {lead.email && (
                      <div className="flex items-center gap-2 text-sm">
                        <MailIcon className="w-4 h-4 ink-muted" />
                        <span
                          className="truncate max-w-[150px]"
                          title={lead.email}
                        >
                          {lead.email}
                        </span>
                      </div>
                    )}
                    {lead.phone && (
                      <div className="flex items-center gap-2 text-sm">
                        <PhoneIcon className="w-4 h-4 ink-muted" />
                        <span>{lead.phone}</span>
                      </div>
                    )}
                  </div>
                </td>

                <td>
                  <div className="space-y-1">
                    {lead.address && (
                      <div className="flex items-center gap-2 text-sm">
                        <MapPinIcon className="w-4 h-4 ink-muted" />
                        <span
                          className="truncate max-w-[120px]"
                          title={lead.address}
                        >
                          {lead.address}
                        </span>
                      </div>
                    )}
                    {lead.postalCode && (
                      <div className="text-sm ink-muted">{lead.postalCode}</div>
                    )}
                  </div>
                </td>

                <td>
                  <div className="space-y-1">
                    {lead.brokerId && (
                      <div className="text-sm">
                        <span className="font-medium">ID:</span> {lead.brokerId}
                      </div>
                    )}
                    {lead.departmentOfBroker && (
                      <div className="text-sm ink-muted">
                        {lead.departmentOfBroker}
                      </div>
                    )}
                  </div>
                </td>

                <td>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={lead.isSuccessful ? 'bright-green' : 'red'}
                      size="lg"
                    >
                      {lead.isSuccessful ? 'Success' : 'Failed'}
                    </Badge>
                    {lead.data && (
                      <TooltipProvider delayDuration={0}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button className="p-1 hover:bg-interactive-muted rounded">
                              <MessageCircleIcon className="size-4 ink-muted cursor-pointer" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <pre className="text-xs whitespace-pre-wrap">
                              {typeof lead.data === 'string'
                                ? lead.data
                                : JSON.stringify(lead.data, null, 2)}
                            </pre>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </td>

                <td>
                  <div className="text-sm">
                    {lead.createdAt ? formatDate(lead.createdAt) : 'N/A'}
                  </div>
                  {lead.updatedAt && lead.updatedAt !== lead.createdAt && (
                    <div className="text-xs ink-muted">
                      Updated: {formatDate(lead.updatedAt)}
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {leads.length === 0 && (
          <div className="text-center py-12">
            <UserIcon className="w-12 h-12 ink-muted mx-auto mb-4" />
            <p className="typo-body-lg ink-muted">No external leads found</p>
            <p className="typo-body-sm ink-muted">
              External leads will appear here when they are created
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

function formatLeadType(leadType: string) {
  return leadType
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

function getLeadTypeBadgeVariant(
  leadType: string,
):
  | 'beige'
  | 'red'
  | 'dark-green'
  | 'blue'
  | 'grey'
  | 'bright-green'
  | 'light-green' {
  const type = leadType.toLowerCase()
  if (type.includes('financing') || type.includes('mortgage'))
    return 'bright-green'
  if (type.includes('inspection') || type.includes('viewing')) return 'blue'
  if (type.includes('valuation') || type.includes('appraisal')) return 'beige'
  if (type.includes('contact') || type.includes('inquiry')) return 'grey'
  return 'light-green'
}
