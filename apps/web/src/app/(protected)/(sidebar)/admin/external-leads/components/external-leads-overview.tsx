import {
  ClipboardCheckIcon,
  FileTextIcon,
  TrendingUpIcon,
  UsersIcon,
} from 'lucide-react'

import prisma from '@/db/prisma'

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface ExternalLeadsOverviewProps {
  filters?: FilterState
}

function getDateRangeFilter(dateRange?: string) {
  if (!dateRange || dateRange === 'all') return undefined

  const now = new Date()
  switch (dateRange) {
    case 'last7days':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    case 'last30days':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    case 'last90days':
      return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    default:
      return undefined
  }
}

function buildWhereClause(filters?: FilterState) {
  const where: Record<string, unknown> = {}

  if (filters?.leadType) {
    where.leadType = filters.leadType
  }

  if (filters?.department) {
    where.departmentOfBroker = filters.department
  }

  if (filters?.successStatus && filters.successStatus !== 'all') {
    where.isSuccessful = filters.successStatus === 'successful'
  }

  const dateFilter = getDateRangeFilter(filters?.dateRange)
  if (dateFilter) {
    where.createdAt = { gte: dateFilter }
  }

  return where
}

export async function ExternalLeadsOverview({
  filters,
}: ExternalLeadsOverviewProps) {
  const whereClause = buildWhereClause(filters)

  // Calculate YTD dates for current year and historical years
  const currentYear = new Date().getFullYear()
  const lastYear = currentYear - 1
  const twoYearsAgo = currentYear - 2
  const ytdStart = new Date(currentYear, 0, 1) // January 1st of current year
  const lastYearStart = new Date(lastYear, 0, 1) // January 1st of last year
  const lastYearYtdEnd = new Date(
    lastYear,
    new Date().getMonth(),
    new Date().getDate(),
  ) // Same date last year
  const twoYearsAgoStart = new Date(twoYearsAgo, 0, 1) // January 1st of two years ago
  const twoYearsAgoYtdEnd = new Date(
    twoYearsAgo,
    new Date().getMonth(),
    new Date().getDate(),
  ) // Same date two years ago

  const [
    ytdLeads,
    lastYearYtdLeads,
    twoYearsAgoYtdLeads,
    // OTP data
    ytdOtpCompleted,
    ytdOtpLeads,
    // Settlement data
    ytdSettlementCompleted,
    ytdSettlementLeads,
  ] = await Promise.all([
    // YTD total count (current year)
    prisma.externalLeadAudit.count({
      where: {
        ...whereClause,
        createdAt: { gte: ytdStart },
        isSuccessful: true, // Only successful leads
      },
    }),
    // Last year YTD count (same period)
    prisma.externalLeadAudit.count({
      where: {
        createdAt: {
          gte: lastYearStart,
          lte: lastYearYtdEnd,
        },
        isSuccessful: true, // Only successful leads
      },
    }),
    // Two years ago YTD count (same period)
    prisma.externalLeadAudit.count({
      where: {
        createdAt: {
          gte: twoYearsAgoStart,
          lte: twoYearsAgoYtdEnd,
        },
        isSuccessful: true, // Only successful leads
      },
    }),
    // OTP forms completed (current year)
    prisma.overtakeProtocol.count({
      where: {
        signingFinished: {
          not: null,
          gte: ytdStart,
        },
      },
    }),
    // OTP-related leads (current year)
    prisma.$queryRaw<[{ count: bigint }]>`
      SELECT COUNT(*)::bigint as count 
      FROM "ExternalLeadAudit" 
      WHERE "isSuccessful" = true 
      AND "createdAt" >= ${ytdStart}
      AND "data"::jsonb -> 'notes' ->> 'otpId' IS NOT NULL
    `.then((result) => Number(result[0].count)),
    // Settlement forms completed (current year) - both buyer and seller
    Promise.all([
      prisma.settlementBuyer.count({
        where: {
          signingFinished: {
            not: null,
            gte: ytdStart,
          },
        },
      }),
      prisma.settlementSeller.count({
        where: {
          signingFinished: {
            not: null,
            gte: ytdStart,
          },
        },
      }),
    ]).then(([buyer, seller]) => buyer + seller),
    // Settlement-related leads (current year)
    prisma.$queryRaw<[{ count: bigint }]>`
      SELECT COUNT(*)::bigint as count 
      FROM "ExternalLeadAudit" 
      WHERE "isSuccessful" = true 
      AND "createdAt" >= ${ytdStart}
      AND "data"::jsonb -> 'notes' ->> 'settlementBuyerOrSellerId' IS NOT NULL
    `.then((result) => Number(result[0].count)),
  ])

  // Calculate percentage changes for all leads
  const lastYearChange =
    lastYearYtdLeads > 0
      ? Math.round(((ytdLeads - lastYearYtdLeads) / lastYearYtdLeads) * 100)
      : 0
  const twoYearsAgoChange =
    twoYearsAgoYtdLeads > 0
      ? Math.round(
          ((ytdLeads - twoYearsAgoYtdLeads) / twoYearsAgoYtdLeads) * 100,
        )
      : 0

  const formatPercentageChange = (change: number) => {
    const sign = change > 0 ? '+' : ''
    return `${sign}${change}%`
  }

  // Calculate OTP lead ratio (percentage of OTP forms that get a lead)
  const ytdOtpRatio =
    ytdOtpCompleted > 0 ? Math.round((ytdOtpLeads / ytdOtpCompleted) * 100) : 0

  // Calculate Settlement lead ratio (percentage of Settlement forms that get a lead)
  const ytdSettlementRatio =
    ytdSettlementCompleted > 0
      ? Math.round((ytdSettlementLeads / ytdSettlementCompleted) * 100)
      : 0

  const metrics = [
    {
      title: 'YTD Successful Leads',
      value: ytdLeads.toLocaleString(),
      icon: UsersIcon,
      description: `${currentYear} successful leads`,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'YoY Changes',
      value: `${formatPercentageChange(lastYearChange)} / ${formatPercentageChange(twoYearsAgoChange)}`,
      icon: TrendingUpIcon,
      description: `vs ${lastYear} / vs ${twoYearsAgo}`,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'OTP Lead Ratio',
      value: `${ytdOtpRatio}%`,
      icon: FileTextIcon,
      description: `${ytdOtpLeads} / ${ytdOtpCompleted}`,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Settlement Lead Ratio',
      value: `${ytdSettlementRatio}%`,
      icon: ClipboardCheckIcon,
      description: `${ytdSettlementLeads} / ${ytdSettlementCompleted}`,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {metrics.map((metric) => (
        <div
          key={metric.title}
          className="bg-root rounded-lg border border-stroke-muted p-6 hover:shadow-md transition-shadow"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="typo-detail-md ink-muted mb-2">{metric.title}</p>
              <p className="typo-display-sm mb-1">{metric.value}</p>
              <p className="typo-body-sm ink-muted">{metric.description}</p>
            </div>
            <div className={`${metric.bgColor} ${metric.color} p-3 rounded-lg`}>
              <metric.icon className="w-6 h-6" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
