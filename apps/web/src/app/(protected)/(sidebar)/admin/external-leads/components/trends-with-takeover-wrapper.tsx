import { startOfWeek } from 'date-fns'
import { Suspense } from 'react'

import prisma from '@/db/prisma'

import { Trends<PERSON>hart } from './trends-chart'
import { UpcomingTakeoverCard } from './upcoming-takeover-card'

interface TrendsData {
  date: string
  total: number
  lastYearTotal?: number
  twoYearsAgoTotal?: number
}

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface TrendsWithTakeoverWrapperProps {
  filters?: FilterState
}

function getDateRangeFilter(dateRange?: string) {
  if (!dateRange || dateRange === 'all') return undefined

  const now = new Date()
  switch (dateRange) {
    case 'last7days':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    case 'last30days':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    case 'last90days':
      return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
    default:
      return undefined
  }
}

function buildWhereClause(filters?: FilterState) {
  const where: Record<string, unknown> = {}

  if (filters?.leadType) {
    where.leadType = filters.leadType
  }

  if (filters?.department) {
    where.departmentOfBroker = filters.department
  }

  if (filters?.successStatus && filters.successStatus !== 'all') {
    where.isSuccessful = filters.successStatus === 'successful'
  }

  const dateFilter = getDateRangeFilter(filters?.dateRange)
  if (dateFilter) {
    where.createdAt = { gte: dateFilter }
  }

  return where
}

async function getTrendsData(filters?: FilterState): Promise<TrendsData[]> {
  const whereClause = buildWhereClause(filters)

  const currentYear = new Date().getFullYear()
  const lastYear = currentYear - 1
  const twoYearsAgo = currentYear - 2
  const ytdStart = new Date(currentYear, 0, 1)
  const lastYearStart = new Date(lastYear, 0, 1)
  const lastYearEnd = new Date(lastYear, 11, 31, 23, 59, 59)
  const twoYearsAgoStart = new Date(twoYearsAgo, 0, 1)
  const twoYearsAgoEnd = new Date(twoYearsAgo, 11, 31, 23, 59, 59)

  const [currentYearLeads, lastYearLeads, twoYearsAgoLeads] = await Promise.all(
    [
      prisma.externalLeadAudit.findMany({
        select: {
          createdAt: true,
        },
        where: {
          ...whereClause,
          isSuccessful: true,
          createdAt: { gte: ytdStart },
        },
      }),
      prisma.externalLeadAudit.findMany({
        select: {
          createdAt: true,
        },
        where: {
          createdAt: {
            gte: lastYearStart,
            lte: lastYearEnd,
          },
          isSuccessful: true,
        },
      }),
      prisma.externalLeadAudit.findMany({
        select: {
          createdAt: true,
        },
        where: {
          createdAt: {
            gte: twoYearsAgoStart,
            lte: twoYearsAgoEnd,
          },
          isSuccessful: true,
        },
      }),
    ],
  )

  // Build current year trend map (weekly aggregation)
  const trendMap = new Map<
    string,
    { total: number; lastYearTotal?: number; twoYearsAgoTotal?: number }
  >()

  currentYearLeads.forEach((lead) => {
    if (lead.createdAt) {
      const weekStart = startOfWeek(lead.createdAt, { weekStartsOn: 1 })
      const weekKey = weekStart.toISOString().split('T')[0]

      const existing = trendMap.get(weekKey) || { total: 0 }
      trendMap.set(weekKey, {
        ...existing,
        total: existing.total + 1,
      })
    }
  })

  // Build last year trend map for comparison
  const lastYearTrendMap = new Map<string, number>()
  lastYearLeads.forEach((lead) => {
    if (lead.createdAt) {
      const lastYearDate = new Date(lead.createdAt)
      const thisYearEquivalent = new Date(
        currentYear,
        lastYearDate.getMonth(),
        lastYearDate.getDate(),
      )

      const weekStart = startOfWeek(thisYearEquivalent, { weekStartsOn: 1 })
      const weekKey = weekStart.toISOString().split('T')[0]

      const existing = lastYearTrendMap.get(weekKey) || 0
      lastYearTrendMap.set(weekKey, existing + 1)
    }
  })

  // Build two years ago trend map for comparison
  const twoYearsAgoTrendMap = new Map<string, number>()
  twoYearsAgoLeads.forEach((lead) => {
    if (lead.createdAt) {
      const twoYearsAgoDate = new Date(lead.createdAt)
      const thisYearEquivalent = new Date(
        currentYear,
        twoYearsAgoDate.getMonth(),
        twoYearsAgoDate.getDate(),
      )

      const weekStart = startOfWeek(thisYearEquivalent, { weekStartsOn: 1 })
      const weekKey = weekStart.toISOString().split('T')[0]

      const existing = twoYearsAgoTrendMap.get(weekKey) || 0
      twoYearsAgoTrendMap.set(weekKey, existing + 1)
    }
  })

  // Merge data
  lastYearTrendMap.forEach((count, dateKey) => {
    const existing = trendMap.get(dateKey) || { total: 0 }
    trendMap.set(dateKey, {
      ...existing,
      lastYearTotal: count,
    })
  })

  twoYearsAgoTrendMap.forEach((count, dateKey) => {
    const existing = trendMap.get(dateKey) || { total: 0 }
    trendMap.set(dateKey, {
      ...existing,
      twoYearsAgoTotal: count,
    })
  })

  return Array.from(trendMap.entries())
    .sort(
      ([weekStartA], [weekStartB]) =>
        new Date(weekStartA).getTime() - new Date(weekStartB).getTime(),
    )
    .map(([weekStart, stats]) => ({
      date: new Date(weekStart).toLocaleDateString('nb-NO', {
        month: 'short',
        day: 'numeric',
      }),
      total: stats.total,
      lastYearTotal: stats.lastYearTotal,
      twoYearsAgoTotal: stats.twoYearsAgoTotal,
    }))
}

export async function TrendsWithTakeoverWrapper({
  filters,
}: TrendsWithTakeoverWrapperProps) {
  const trendsData = await getTrendsData(filters)

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Trends Chart */}
      <div className="lg:col-span-2">
        <TrendsChart data={trendsData} />
      </div>

      {/* Upcoming Takeovers Card */}
      <div>
        <Suspense
          fallback={
            <div className="h-32 bg-stroke-muted animate-pulse rounded-lg" />
          }
        >
          <UpcomingTakeoverCard days={7} />
        </Suspense>
      </div>
    </div>
  )
}
