'use server'

import { Suspense } from 'react'

import prisma from '@/db/prisma'

import { AdminTabs } from '../components/admin-tabs'

import { ExternalLeadsPageContent } from './components/external-leads-page-content'

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface PageProps {
  searchParams: Promise<{
    leadType?: string
    department?: string
    dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
    successStatus?: 'all' | 'successful' | 'failed'
  }>
}

export default async function Page({ searchParams }: PageProps) {
  // Await searchParams before using
  const resolvedSearchParams = await searchParams

  // Get unique lead types and departments for filter options
  try {
    const [leadTypesData, departmentsData] = await Promise.all([
      prisma.externalLeadAudit.findMany({
        select: { leadType: true },
        distinct: ['leadType'],
      }),
      prisma.externalLeadAudit.findMany({
        select: { departmentOfBroker: true },
        distinct: ['departmentOfBroker'],
      }),
    ])

    const leadTypes = (leadTypesData || [])
      .map((item) => item?.leadType)
      .filter(Boolean) as string[]

    const departments = (departmentsData || [])
      .map((item) => item?.departmentOfBroker)
      .filter(Boolean) as string[]

    const filters: FilterState = {
      leadType: resolvedSearchParams.leadType,
      department: resolvedSearchParams.department,
      dateRange: resolvedSearchParams.dateRange,
      successStatus: resolvedSearchParams.successStatus,
    }

    return (
      <div className="flex flex-col gap-6 py-4">
        <div className="mt-8 mb-2 flex items-baseline justify-between">
          <h1 className="typo-display-lg">Admin</h1>
        </div>
        <AdminTabs currentTab="external-leads" />

        <Suspense
          fallback={
            <div className="h-96 bg-stroke-muted animate-pulse rounded-lg" />
          }
        >
          <ExternalLeadsPageContent
            leadTypes={leadTypes}
            departments={departments}
            filters={filters}
          />
        </Suspense>
      </div>
    )
  } catch (error) {
    console.error('Error loading external leads data:', error)
    return (
      <div className="flex flex-col gap-6 py-4">
        <div className="mt-8 mb-2 flex items-baseline justify-between">
          <h1 className="typo-display-lg">Admin</h1>
        </div>
        <AdminTabs currentTab="external-leads" />
        <div className="p-6 bg-root rounded-lg border border-stroke-muted">
          <h3 className="typo-body-lg">Error loading external leads</h3>
          <p className="ink-muted typo-body-sm mt-1">
            There was an error loading the external leads data. Please try
            again.
          </p>
        </div>
      </div>
    )
  }
}
