'use client'

import { FilterIcon } from 'lucide-react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useState } from 'react'

import { Button } from '@nordvik/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@nordvik/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@nordvik/ui/select'

interface FilterState {
  leadType?: string
  department?: string
  dateRange?: 'last7days' | 'last30days' | 'last90days' | 'all'
  successStatus?: 'all' | 'successful' | 'failed'
}

interface ExternalLeadsFiltersProps {
  leadTypes: string[]
  departments: string[]
  initialFilters?: FilterState
}

export function ExternalLeadsFilters({
  leadTypes,
  departments,
  initialFilters = {},
}: ExternalLeadsFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [filters, setFilters] = useState<FilterState>(initialFilters)
  const [isOpen, setIsOpen] = useState(false)

  const updateFilter = (key: keyof FilterState, value: string | undefined) => {
    const newFilters = {
      ...filters,
      [key]: value === 'all' ? undefined : value,
    }
    setFilters(newFilters)

    // Update URL search params
    const params = new URLSearchParams(searchParams)
    if (value && value !== 'all') {
      params.set(key, value)
    } else {
      params.delete(key)
    }
    router.push(`?${params.toString()}`)
  }

  const clearFilters = () => {
    const emptyFilters = {}
    setFilters(emptyFilters)

    // Clear URL search params
    router.push(window.location.pathname)
  }

  const hasActiveFilters = Object.values(filters).some(
    (value) => value !== undefined,
  )

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={hasActiveFilters ? 'default' : 'outline'}
          className="gap-2"
          size="lg"
        >
          <FilterIcon className="w-4 h-4" />
          Filters
          {hasActiveFilters && (
            <span className="ml-1 px-1.5 py-0.5 text-xs bg-background-root rounded-full">
              {Object.values(filters).filter(Boolean).length}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-4 space-y-4" align="end">
        <div className="flex items-center justify-between">
          <h4 className="typo-body-md font-medium">Filter External Leads</h4>
          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="text-xs"
              size="lg"
            >
              Clear all
            </Button>
          )}
        </div>

        <div className="space-y-3">
          <div>
            <label className="typo-detail-md block mb-2">Lead Type</label>
            <Select
              value={filters.leadType || 'all'}
              onValueChange={(value) => updateFilter('leadType', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All lead types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All lead types</SelectItem>
                {leadTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type
                      .split('_')
                      .map(
                        (word) =>
                          word.charAt(0).toUpperCase() +
                          word.slice(1).toLowerCase(),
                      )
                      .join(' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="typo-detail-md block mb-2">Department</label>
            <Select
              value={filters.department || 'all'}
              onValueChange={(value) => updateFilter('department', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All departments" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All departments</SelectItem>
                {departments.map((dept) => (
                  <SelectItem key={dept} value={dept}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="typo-detail-md block mb-2">Date Range</label>
            <Select
              value={filters.dateRange || 'all'}
              onValueChange={(value) => updateFilter('dateRange', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All time" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All time</SelectItem>
                <SelectItem value="last7days">Last 7 days</SelectItem>
                <SelectItem value="last30days">Last 30 days</SelectItem>
                <SelectItem value="last90days">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="typo-detail-md block mb-2">Success Status</label>
            <Select
              value={filters.successStatus || 'all'}
              onValueChange={(value) => updateFilter('successStatus', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="successful">Successful only</SelectItem>
                <SelectItem value="failed">Failed only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
