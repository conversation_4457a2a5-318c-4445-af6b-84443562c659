'use server'

import {
  endOfYear,
  format,
  startOfTomorrow,
  startOfYear,
  subMonths,
  subYears,
} from 'date-fns'
import { startOfMonth } from 'date-fns/startOfMonth'
import { unstable_cache } from 'next/cache'

import mongo from '@/db/mongo'
import { getAllbrokers } from '@/server/model/Broker/factory'
import { getAllDepartments } from '@/server/model/Department/factory'

// removed extra date-fns sub imports; consolidated above

export type TimeToMarketRow = {
  month: string // ISO month start date
  overallAvgTTMDays: number | null
  overallMedianTTMDays: number | null
  overallP75TTMDays: number | null
  overallCount: number
  perDepartment: {
    departmentId: number
    avgTTMDays: number
    medianTTMDays: number | null
    p75TTMDays: number | null
    count: number
  }[]
}

// A reduced row variant (top 3 shortest per department) shares same shape
export type TimeToMarketRowTop3 = TimeToMarketRow

export const getTimeToMarket = unstable_cache(
  async (from?: string | Date, to?: string | Date) => {
    // Default: last 2 years up to tomorrow (inclusive upper bound)
    const fromDate = from
      ? new Date(from)
      : startOfMonth(subYears(new Date(), 2))
    const toDate = to ? new Date(to) : startOfTomorrow()

    try {
      await mongo.connect()
    } catch (e) {
      console.warn('Mongo connection error', e)
    }

    // Fetch raw estates once, then aggregate in memory for median / percentiles
    const raw = await mongo
      .db('nordvik')
      .collection('estates')
      .find({
        commissionAcceptedDate: { $gte: fromDate, $lte: toDate },
        departmentId: { $ne: 1 },
        assignmentTypeGroup: { $ne: 3 },
        firstPublished: { $type: 'date' },
      })
      .project({
        commissionAcceptedDate: 1,
        firstPublished: 1,
        departmentId: 1,
      })
      .toArray()

    type Row = { month: string; departmentId: number; ttm: number }
    const rows: Row[] = []
    for (const doc of raw) {
      const cad = doc.commissionAcceptedDate as Date | undefined
      const fp = doc.firstPublished as Date | undefined
      if (!cad || !fp) continue
      if (fp < cad) continue
      const month = new Date(cad)
      month.setDate(1)
      month.setHours(0, 0, 0, 0)
      const ttm = (fp.getTime() - cad.getTime()) / (1000 * 60 * 60 * 24)
      rows.push({
        month: month.toISOString(),
        departmentId: doc.departmentId as number,
        ttm,
      })
    }

    function summarize(values: number[]) {
      if (!values.length)
        return {
          avg: null as number | null,
          median: null as number | null,
          p75: null as number | null,
        }
      const sorted = [...values].sort((a, b) => a - b)
      const avg = sorted.reduce((a, b) => a + b, 0) / sorted.length
      const mid = Math.floor(sorted.length / 2)
      const median =
        sorted.length % 2 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2
      const p75Index = Math.floor(0.75 * (sorted.length - 1))
      const p75 = sorted[p75Index]
      return { avg, median, p75 }
    }

    // Group by month then department
    const monthGroups = new Map<string, Row[]>()
    for (const r of rows) {
      if (!monthGroups.has(r.month)) monthGroups.set(r.month, [])
      monthGroups.get(r.month)!.push(r)
    }

    const resultArr: TimeToMarketRow[] = []
    for (const [month, monthRows] of [...monthGroups.entries()].sort((a, b) =>
      a[0].localeCompare(b[0]),
    )) {
      const overallVals = monthRows.map((r) => r.ttm)
      const overall = summarize(overallVals)
      const deptMap = new Map<number, number[]>()
      monthRows.forEach((r) => {
        if (!deptMap.has(r.departmentId)) deptMap.set(r.departmentId, [])
        deptMap.get(r.departmentId)!.push(r.ttm)
      })
      const perDepartment = [...deptMap.entries()]
        .map(([departmentId, vals]) => {
          const s = summarize(vals)
          return {
            departmentId,
            avgTTMDays: Number(s.avg?.toFixed(2)),
            medianTTMDays:
              s.median === null ? null : Number(s.median.toFixed(2)),
            p75TTMDays: s.p75 === null ? null : Number(s.p75.toFixed(2)),
            count: vals.length,
          }
        })
        .sort((a, b) => a.departmentId - b.departmentId)
      resultArr.push({
        month,
        overallAvgTTMDays:
          overall.avg === null ? null : Number(overall.avg.toFixed(2)),
        overallMedianTTMDays:
          overall.median === null ? null : Number(overall.median.toFixed(2)),
        overallP75TTMDays:
          overall.p75 === null ? null : Number(overall.p75.toFixed(2)),
        overallCount: overallVals.length,
        perDepartment: perDepartment,
      })
    }

    let results = resultArr

    // Ensure every month in range exists
    const monthMap = new Map<string, TimeToMarketRow>()
    results.forEach((r) => monthMap.set(r.month, r))
    const filled: TimeToMarketRow[] = []
    const cursor = new Date(fromDate)
    const end = new Date(toDate)
    // Move end to start of month to avoid partial last month duplication
    end.setDate(1)
    end.setHours(0, 0, 0, 0)
    cursor.setDate(1)
    cursor.setHours(0, 0, 0, 0)
    while (cursor <= end) {
      const iso = cursor.toISOString()
      const existing = monthMap.get(iso)
      if (existing) {
        filled.push(existing)
      } else {
        filled.push({
          month: iso,
          overallAvgTTMDays: null,
          overallMedianTTMDays: null,
          overallP75TTMDays: null,
          overallCount: 0,
          perDepartment: [],
        })
      }
      // increment month
      cursor.setMonth(cursor.getMonth() + 1)
    }
    results = filled

    const departments = await getAllDepartments()
    const deptLabels: Record<number, string> = {}
    departments.items.forEach((d) => {
      if (d.departmentId) deptLabels[d.departmentId] = d.name
    })

    // Derive a parallel dataset considering only the 3 shortest TTM per department per month
    const rowsTop3: TimeToMarketRow[] = results.map((r) => {
      // For each department pick up to 3 shortest ttm values from original raw rows
      // We need access to original per-department distributions; reconstruct via monthGroups
      const month = r.month
      const origMonthRows = monthGroups.get(month) || []
      const deptToVals = new Map<number, number[]>()
      origMonthRows.forEach((mr) => {
        if (!deptToVals.has(mr.departmentId))
          deptToVals.set(mr.departmentId, [])
        deptToVals.get(mr.departmentId)!.push(mr.ttm)
      })
      const perDepartment = [...deptToVals.entries()]
        .map(([departmentId, vals]) => {
          const shortest = [...vals].sort((a, b) => a - b).slice(0, 3)
          const s = summarize(shortest)
          return {
            departmentId,
            avgTTMDays: s.avg === null ? 0 : Number(s.avg.toFixed(2)),
            medianTTMDays:
              s.median === null ? null : Number(s.median.toFixed(2)),
            p75TTMDays: s.p75 === null ? null : Number(s.p75.toFixed(2)),
            count: shortest.length, // count of subset considered
          }
        })
        .sort((a, b) => a.departmentId - b.departmentId)
      // Overall computed across all shortest picks concatenated
      const overallVals: number[] = []
      perDepartment.forEach((d) => {
        const orig = deptToVals.get(d.departmentId) || []
        const shortest = [...orig].sort((a, b) => a - b).slice(0, 3)
        overallVals.push(...shortest)
      })
      const overall = summarize(overallVals)
      return {
        month: r.month,
        overallAvgTTMDays:
          overall.avg === null ? null : Number(overall.avg.toFixed(2)),
        overallMedianTTMDays:
          overall.median === null ? null : Number(overall.median.toFixed(2)),
        overallP75TTMDays:
          overall.p75 === null ? null : Number(overall.p75.toFixed(2)),
        overallCount: overallVals.length,
        perDepartment,
      }
    })

    return { rows: results, rowsTop3, deptLabels }
  },
  ['getTimeToMarket'],
  { revalidate: 900 },
)

export type TimeToMarketSummary = {
  ytdAvg: number | null
  ytdMedian: number | null
  ytdLastYearAvg: number | null
  ytdLastYearMedian: number | null
  ytdDelta: number | null // (this year - last year)
  lastMonthAvg: number | null
  lastMonthMedian: number | null
  lastMonthLabel: string
  topBrokers: {
    employeeId: string
    name: string
    departmentId?: number
    departmentName?: string
    avgTTM: number
    count: number
  }[]
}

export const getTimeToMarketSummary = unstable_cache(
  async () => {
    try {
      await mongo.connect()
    } catch {
      /* ignore connection race */
    }
    const now = new Date()
    const startYTD = startOfYear(now)
    const startLastYTD = startOfYear(subYears(now, 1))
    const endLastYTD = endOfYear(subYears(now, 1))
    const lastMonthDate = subMonths(startOfMonth(now), 1)
    const lastMonthStart = new Date(lastMonthDate)
    const lastMonthEnd = startOfMonth(now)

    // Fetch estates for needed ranges in one query (last 13 months + last year span)
    const earliest = startOfYear(subYears(now, 1))
    const raw = await mongo
      .db('nordvik')
      .collection('estates')
      .find({
        commissionAcceptedDate: { $gte: earliest, $lte: now },
        departmentId: { $ne: 1 },
        assignmentTypeGroup: { $ne: 3 },
        firstPublished: { $type: 'date' },
      })
      .project({
        commissionAcceptedDate: 1,
        firstPublished: 1,
        employeeId: 1,
        departmentId: 1,
      })
      .toArray()

    interface BrokerRec {
      employeeId: string
      ttm: number
      departmentId?: number
    }
    const brokerMap: Record<string, BrokerRec[]> = {}
    const ytdVals: number[] = []
    const lastYtdVals: number[] = []
    const lastMonthVals: number[] = []

    for (const doc of raw) {
      const cad = doc.commissionAcceptedDate as Date | undefined
      const fp = doc.firstPublished as Date | undefined
      if (!cad || !fp || fp < cad) continue
      const ttm = (fp.getTime() - cad.getTime()) / (1000 * 60 * 60 * 24)
      // YTD this year
      if (cad >= startYTD) ytdVals.push(ttm)
      // YTD last year
      if (cad >= startLastYTD && cad <= endLastYTD) lastYtdVals.push(ttm)
      // Last month
      if (cad >= lastMonthStart && cad < lastMonthEnd) lastMonthVals.push(ttm)
      // Broker accumulation (last 12 months only)
      if (cad >= subMonths(now, 12)) {
        const emp = doc.employeeId as string | undefined
        if (emp) {
          if (!brokerMap[emp]) brokerMap[emp] = []
          brokerMap[emp].push({
            employeeId: emp,
            ttm,
            departmentId: doc.departmentId as number,
          })
        }
      }
    }

    function avg(arr: number[]) {
      return arr.length
        ? Number((arr.reduce((a, b) => a + b, 0) / arr.length).toFixed(2))
        : null
    }
    function median(arr: number[]) {
      if (!arr.length) return null
      const s = [...arr].sort((a, b) => a - b)
      const mid = Math.floor(s.length / 2)
      const m = s.length % 2 ? s[mid] : (s[mid - 1] + s[mid]) / 2
      return Number(m.toFixed(2))
    }
    const ytdAvg = avg(ytdVals)
    const ytdMedian = median(ytdVals)
    const ytdLastYearAvg = avg(lastYtdVals)
    const ytdLastYearMedian = median(lastYtdVals)
    const ytdDelta =
      ytdAvg != null && ytdLastYearAvg != null
        ? Number((ytdAvg - ytdLastYearAvg).toFixed(2))
        : null
    const lastMonthAvg = avg(lastMonthVals)
    const lastMonthMedian = median(lastMonthVals)
    const lastMonthLabel = format(lastMonthStart, 'yyyy-MM')

    // Enrich broker info
    const brokers = await getAllbrokers()
    const brokerInfo = new Map(brokers.items.map((b) => [b.employeeId, b]))
    const departments = await getAllDepartments()
    const deptInfo = new Map(
      departments.items.map((d) => [d.departmentId, d.name]),
    )
    const topBrokers = Object.entries(brokerMap)
      .map(([employeeId, items]) => {
        const count = items.length
        const average = avg(items.map((i) => i.ttm)) || 0
        const info = brokerInfo.get(employeeId)
        const departmentId =
          info?.department?.departmentId ?? items[0]?.departmentId
        return {
          employeeId,
          name: info?.name || employeeId,
          departmentId,
          departmentName: departmentId ? deptInfo.get(departmentId) : undefined,
          avgTTM: average,
          count,
        }
      })
      .filter((b) => b.count >= 15)
      .sort((a, b) => a.avgTTM - b.avgTTM)
      .slice(0, 20)

    const summary: TimeToMarketSummary = {
      ytdAvg,
      ytdMedian,
      ytdLastYearAvg,
      ytdLastYearMedian,
      ytdDelta,
      lastMonthAvg,
      lastMonthMedian,
      lastMonthLabel,
      topBrokers,
    }
    return summary
  },
  ['getTimeToMarketSummary'],
  { revalidate: 900 },
)
