'use client'

import { useMemo, useState } from 'react'

import { Checkbox } from '@nordvik/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@nordvik/ui/select'

import type { TimeToMarketRow, TimeToMarketSummary } from './get-data'
import { TimeToMarketChart } from './time-to-market-chart'

interface DashboardProps {
  rows: TimeToMarketRow[]
  rowsTop3?: TimeToMarketRow[]
  deptLabels: Record<number, string>
  summary?: TimeToMarketSummary
}

const presets = [
  { key: '6m', label: '6 mnd', months: 6 },
  { key: '12m', label: '12 mnd', months: 12 },
  { key: '24m', label: '24 mnd', months: 24 },
  { key: '36m', label: '36 mnd', months: 36 },
]

export function TimeToMarketDashboard({
  rows,
  rowsTop3,
  deptLabels,
  summary,
}: DashboardProps) {
  const [preset, setPreset] = useState<string>('36m')
  const [showMedian, setShowMedian] = useState(true)
  const [showP75, setShowP75] = useState(false)
  const [movingAvgWindow, setMovingAvgWindow] = useState<number>(0)
  const [selectedDepartments, setSelectedDepartments] = useState<number[]>([])

  const filteredRows = useMemo(() => {
    const months = presets.find((p) => p.key === preset)?.months ?? 36
    const end = rows[rows.length - 1]
    if (!end) return rows
    const endDate = new Date(end.month)
    const startBoundary = new Date(endDate)
    startBoundary.setMonth(startBoundary.getMonth() - (months - 1))
    return rows.filter((r) => new Date(r.month) >= startBoundary)
  }, [rows, preset])

  const filteredRowsTop3 = useMemo(() => {
    if (!rowsTop3) return undefined
    const months = presets.find((p) => p.key === preset)?.months ?? 36
    const end = rowsTop3[rowsTop3.length - 1]
    if (!end) return rowsTop3
    const endDate = new Date(end.month)
    const startBoundary = new Date(endDate)
    startBoundary.setMonth(startBoundary.getMonth() - (months - 1))
    return rowsTop3.filter((r) => new Date(r.month) >= startBoundary)
  }, [rowsTop3, preset])

  const rowsWithMA = useMemo(() => {
    if (!movingAvgWindow || movingAvgWindow < 2) return filteredRows
    const window = movingAvgWindow
    const clone = [...filteredRows]
    // Compute moving average for overallAvgTTMDays
    for (let i = 0; i < clone.length; i++) {
      if (i + 1 < window) continue
      const slice = clone.slice(i + 1 - window, i + 1)
      const vals = slice
        .map((s) => s.overallAvgTTMDays)
        .filter((v): v is number => v != null)
      if (vals.length === window) {
        const ma = vals.reduce((a, b) => a + b, 0) / vals.length
        // store on object (ts ignore field) via type assertion
        ;(
          clone[i] as unknown as { overallAvgTTM_MA?: number }
        ).overallAvgTTM_MA = Number(ma.toFixed(2))
      }
    }
    return clone
  }, [filteredRows, movingAvgWindow])

  const rowsTop3WithMA = useMemo(() => {
    if (!filteredRowsTop3) return undefined
    if (!movingAvgWindow || movingAvgWindow < 2) return filteredRowsTop3
    const window = movingAvgWindow
    const clone = [...filteredRowsTop3]
    for (let i = 0; i < clone.length; i++) {
      if (i + 1 < window) continue
      const slice = clone.slice(i + 1 - window, i + 1)
      const vals = slice
        .map((s) => s.overallAvgTTMDays)
        .filter((v): v is number => v != null)
      if (vals.length === window) {
        const ma = vals.reduce((a, b) => a + b, 0) / vals.length
        ;(
          clone[i] as unknown as { overallAvgTTM_MA?: number }
        ).overallAvgTTM_MA = Number(ma.toFixed(2))
      }
    }
    return clone
  }, [filteredRowsTop3, movingAvgWindow])

  const deptIds = useMemo(
    () =>
      Array.from(
        new Set(
          rows.flatMap((r) => r.perDepartment.map((d) => d.departmentId)),
        ),
      ).sort((a, b) => a - b),
    [rows],
  )

  function toggleDepartment(id: number) {
    setSelectedDepartments((prev) =>
      prev.includes(id) ? prev.filter((p) => p !== id) : [...prev, id],
    )
  }

  //   function exportCSV() {
  //     const headers = [
  //       'month',
  //       'overallAvg',
  //       'overallMedian',
  //       'overallP75',
  //       'overallCount',
  //     ]
  //     const deptCols: string[] = []
  //     deptIds.forEach((id) => {
  //       if (selectedDepartments.length && !selectedDepartments.includes(id))
  //         return
  //       deptCols.push(`dept_${id}_avg`)
  //       deptCols.push(`dept_${id}_median`)
  //       deptCols.push(`dept_${id}_p75`)
  //       deptCols.push(`dept_${id}_count`)
  //     })
  //     const lines = [headers.concat(deptCols).join(',')]
  //     rowsWithMA.forEach((r) => {
  //       const base = [
  //         format(new Date(r.month), 'yyyy-MM'),
  //         r.overallAvgTTMDays ?? '',
  //         r.overallMedianTTMDays ?? '',
  //         r.overallP75TTMDays ?? '',
  //         r.overallCount,
  //       ]
  //       const deptData: (string | number)[] = []
  //       deptIds.forEach((id) => {
  //         if (selectedDepartments.length && !selectedDepartments.includes(id))
  //           return
  //         const d = r.perDepartment.find((x) => x.departmentId === id)
  //         deptData.push(d?.avgTTMDays ?? '')
  //         deptData.push(d?.medianTTMDays ?? '')
  //         deptData.push(d?.p75TTMDays ?? '')
  //         deptData.push(d?.count ?? '')
  //       })
  //       lines.push(base.concat(deptData).join(','))
  //     })
  //     const blob = new Blob([lines.join('\n')], {
  //       type: 'text/csv;charset=utf-8;',
  //     })
  //     const url = URL.createObjectURL(blob)
  //     const a = document.createElement('a')
  //     a.href = url
  //     a.download = 'time_to_market.csv'
  //     a.click()
  //     URL.revokeObjectURL(url)
  //   }

  return (
    <div className="flex flex-col gap-6">
      {summary && (
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <SummaryCard
            title="Snitt TTM YTD"
            value={summary.ytdAvg}
            median={summary.ytdMedian}
            subtitle={`Δ vs i fjor: ${summary.ytdDelta ?? '–'} dager`}
            delta={summary.ytdDelta}
          />
          <SummaryCard
            title={`Snitt TTM YTD i fjor`}
            value={summary.ytdLastYearAvg}
            median={summary.ytdLastYearMedian}
          />
          <SummaryCard
            title={`Snitt sist mnd (${summary.lastMonthLabel})`}
            value={summary.lastMonthAvg}
            median={summary.lastMonthMedian}
          />
          <div className="rounded-md border p-4 flex flex-col">
            <div className="text-xs uppercase tracking-wide mb-2 opacity-60">
              Top 20 meglere (≤ 12 mnd, ≥15 salg)
            </div>
            <ol className="text-xs space-y-1 max-h-40 overflow-auto pr-2">
              {summary.topBrokers.map((b) => (
                <li key={b.employeeId} className="flex justify-between gap-2">
                  <span className="truncate">
                    {b.name}
                    {b.departmentName ? ` (${b.departmentName})` : ''}
                  </span>
                  <span className="tabular-nums">
                    {b.avgTTM}d <span className="opacity-60">({b.count})</span>
                  </span>
                </li>
              ))}
            </ol>
          </div>
        </div>
      )}
      <div className="flex flex-wrap gap-4 items-end">
        <div>
          <label className="block text-xs font-medium mb-1">Periode</label>
          <Select value={preset} onValueChange={setPreset}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Periode" />
            </SelectTrigger>
            <SelectContent>
              {presets.map((p) => (
                <SelectItem key={p.key} value={p.key}>
                  {p.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <Checkbox
            checked={showMedian}
            onCheckedChange={() => setShowMedian((v) => !v)}
            id="median"
          />
          <label htmlFor="median" className="text-sm">
            Median
          </label>
        </div>
        <div className="flex items-center gap-2">
          <Checkbox
            checked={showP75}
            onCheckedChange={() => setShowP75((v) => !v)}
            id="p75"
          />
          <label htmlFor="p75" className="text-sm">
            75-persentil
          </label>
        </div>
        <div>
          <label className="block text-xs font-medium mb-1">
            Glidende snitt
          </label>
          <Select
            value={String(movingAvgWindow)}
            onValueChange={(v) => setMovingAvgWindow(Number(v))}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Ingen" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">Ingen</SelectItem>
              <SelectItem value="3">3 mnd</SelectItem>
              <SelectItem value="6">6 mnd</SelectItem>
              <SelectItem value="12">12 mnd</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="flex flex-wrap gap-3">
        {deptIds.map((id) => {
          const active =
            selectedDepartments.length === 0 || selectedDepartments.includes(id)
          return (
            <button
              key={id}
              onClick={() => toggleDepartment(id)}
              className={`text-xs px-2 py-1 rounded border ${active ? '' : 'opacity-50'}`}
            >
              {deptLabels[id] ?? `Dept ${id}`}
            </button>
          )
        })}
      </div>
      <TimeToMarketChart
        data={rowsWithMA}
        deptLabels={deptLabels}
        title="Average Time to Market (days)"
        options={{
          showMedian,
          showP75,
          movingAvg: movingAvgWindow,
          filterDepartments: selectedDepartments,
        }}
      />
      {rowsTop3WithMA && (
        <TimeToMarketChart
          data={rowsTop3WithMA}
          deptLabels={deptLabels}
          title="Avg TTM (Top 3 Shortest Estates per Dept)"
          options={{
            showMedian,
            showP75,
            movingAvg: movingAvgWindow,
            filterDepartments: selectedDepartments,
          }}
        />
      )}
    </div>
  )
}

function SummaryCard({
  title,
  value,
  median,
  subtitle,
  delta,
}: {
  title: string
  value: number | null | undefined
  median?: number | null
  subtitle?: string
  delta?: number | null
}) {
  const isPositive = delta != null && delta < 0 // lower is better
  let decoratedSubtitle = subtitle
  if (subtitle && delta != null) {
    decoratedSubtitle = `${isPositive ? '↓' : '↑'} ${subtitle}`
  }
  return (
    <div className="rounded-md border p-4 flex flex-col gap-1">
      <div className="text-xs uppercase tracking-wide opacity-60">{title}</div>
      <div className="text-lg tabular-nums flex items-baseline gap-2">
        <span>{value != null ? `${value} d` : '–'}</span>
        {median != null && (
          <span className="text-xs opacity-70">Median {median} d</span>
        )}
      </div>
      {decoratedSubtitle && (
        <div className="text-xs opacity-70">{decoratedSubtitle}</div>
      )}
    </div>
  )
}
