import { startOfTomorrow, subHours } from 'date-fns'
import { Suspense } from 'react'

import { AdminTabs } from '../components/admin-tabs'

import { TimeToMarketDashboard } from './dashboard'
import { getTimeToMarket, getTimeToMarketSummary } from './get-data'

export const revalidate = 900

export default async function Page(props: {
  searchParams: Promise<{ from?: string; to?: string }>
}) {
  const searchParams = await props.searchParams
  return (
    <div className="flex flex-col gap-4">
      <AdminTabs currentTab="ttm" />
      <Suspense key={`${searchParams.from}-${searchParams.to}`}>
        <TTMWrapper from={searchParams.from} to={searchParams.to} />
      </Suspense>
    </div>
  )
}

async function TTMWrapper({ from, to }: { from?: string; to?: string }) {
  // If no from provided, let server function default to 5 years ago.
  const fromDate = from ? subHours(new Date(from), 2) : undefined
  const toDate = subHours(to ? new Date(to) : startOfTomorrow(), 2)

  const [series, summary] = await Promise.all([
    getTimeToMarket(fromDate, toDate),
    getTimeToMarketSummary(),
  ])

  return (
    <TimeToMarketDashboard
      rows={series.rows}
      rowsTop3={series.rowsTop3}
      deptLabels={series.deptLabels}
      summary={summary}
    />
  )
}
