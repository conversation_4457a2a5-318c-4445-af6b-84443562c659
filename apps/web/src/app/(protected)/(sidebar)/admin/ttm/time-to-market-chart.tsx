'use client'

import { format } from 'date-fns'
import {
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  type TooltipProps,
  XAxis,
  YAxis,
} from 'recharts'

import { Card, CardContent, CardHeader, CardTitle } from '@nordvik/ui/card'

import type { TimeToMarketRow } from './get-data'

interface ChartOptions {
  showMedian?: boolean
  showP75?: boolean
  movingAvg?: number
  filterDepartments?: number[] // if empty show all
}

interface ChartRow {
  month: string
  overall: number | null
  overallCount: number
  overallMedian?: number | null
  overallP75?: number | null
  movingAvg?: number
  [key: string]: string | number | null | undefined
}

function toChartData(rows: TimeToMarketRow[]): ChartRow[] {
  return rows.map((r) => {
    const base: ChartRow = {
      month: format(new Date(r.month), 'yyyy-MM'),
      overall: r.overallAvgTTMDays,
      overallCount: r.overallCount,
    }
    for (const d of r.perDepartment) {
      base[`dept_${d.departmentId}_count`] = d.count
      // Only show department value on chart if at least 3 estates that month
      if (d.count >= 3) {
        base[`dept_${d.departmentId}`] = d.avgTTMDays
      } else {
        base[`dept_${d.departmentId}`] = null
      }
    }
    return base
  })
}

export function TimeToMarketChart({
  data,
  deptLabels,
  options,
  title = 'Average Time to Market (days)',
}: {
  data: TimeToMarketRow[]
  deptLabels: Record<number, string>
  options?: ChartOptions
  title?: string
}) {
  const chartData = toChartData(data).map((row, idx) => {
    const src = data[idx]
    const ma = (src as unknown as { overallAvgTTM_MA?: number })
      .overallAvgTTM_MA
    if (ma !== undefined) {
      row.movingAvg = ma
    }
    row.overallMedian = src.overallMedianTTMDays
    row.overallP75 = src.overallP75TTMDays
    return row
  })
  const deptIdsAll = Array.from(
    new Set(data.flatMap((r) => r.perDepartment.map((d) => d.departmentId))),
  ).sort((a, b) => a - b)
  const deptIds = options?.filterDepartments?.length
    ? deptIdsAll.filter((id) => options.filterDepartments!.includes(id))
    : deptIdsAll

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData}>
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip
              content={(props) => (
                <CustomTooltip
                  {...(props as TooltipProps<number, string>)}
                  deptLabels={deptLabels}
                  options={options}
                />
              )}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="overall"
              name="Snitt"
              stroke="#111827"
              strokeWidth={2}
              dot={false}
            />
            {options?.showMedian && (
              <Line
                type="monotone"
                dataKey="overallMedian"
                name="Median"
                stroke="#7c3aed"
                strokeDasharray="4 4"
                strokeWidth={1.5}
                dot={false}
              />
            )}
            {options?.showP75 && (
              <Line
                type="monotone"
                dataKey="overallP75"
                name="75%"
                stroke="#f59e0b"
                strokeDasharray="2 6"
                strokeWidth={1.5}
                dot={false}
              />
            )}
            {options?.movingAvg && options.movingAvg > 1 && (
              <Line
                type="monotone"
                dataKey="movingAvg"
                name={`Glidende (${options.movingAvg})`}
                stroke="#10b981"
                strokeWidth={2}
                dot={false}
              />
            )}
            {deptIds.map((id, i) => (
              <Line
                key={id}
                type="monotone"
                dataKey={`dept_${id}`}
                name={deptLabels[id] ?? `Dept ${id}`}
                stroke={palette[i % palette.length]}
                strokeWidth={1.5}
                dot={false}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

const palette = [
  '#2563eb',
  '#16a34a',
  '#dc2626',
  '#7c3aed',
  '#f59e0b',
  '#0891b2',
  '#ef4444',
  '#10b981',
  '#a855f7',
  '#fb7185',
]

// Custom tooltip to list departments sorted by ascending TTM for that month
function CustomTooltip(
  props:
    | (TooltipProps<number, string> & {
        deptLabels: Record<number, string>
        options?: ChartOptions
      })
    | undefined,
) {
  if (!props) return null
  const { active, payload, label, deptLabels } = props
  if (!active || !payload || !payload.length) return null
  const row = payload[0].payload as ChartRow

  // Collect department metrics (avg) from row keys
  const deptEntries: {
    id: number
    value: number
    count?: number
    color?: string
  }[] = []
  for (const p of payload) {
    const key = p.dataKey as string
    if (!key || !key.startsWith('dept_') || key.endsWith('_count')) continue
    const idStr = key.replace('dept_', '')
    const id = Number(idStr)
    const val = p.value as number | undefined | null
    if (val == null) continue
    // Respect filterDepartments option (already enforced by rendered lines, but double-check)
    if (
      props.options?.filterDepartments?.length &&
      !props.options.filterDepartments.includes(id)
    )
      continue
    deptEntries.push({
      id,
      value: val,
      count: row[`dept_${id}_count`] as number | undefined,
      color: p.color as string | undefined,
    })
  }

  deptEntries.sort((a, b) => a.value - b.value)

  return (
    <div
      className="rounded-md border backdrop-blur-sm px-3 py-2 shadow text-xs max-w-60"
      style={{ background: 'rgba(255,255,255,0.95)' }}
    >
      <div className="font-medium mb-1">{label}</div>
      <div className="space-y-1">
        {typeof row.overall === 'number' && (
          <div className="flex justify-between gap-4">
            <span className="opacity-70">Snitt</span>
            <span className="tabular-nums">{row.overall} d</span>
          </div>
        )}
        {props.options?.showMedian && typeof row.overallMedian === 'number' && (
          <div className="flex justify-between gap-4">
            <span className="opacity-70">Median</span>
            <span className="tabular-nums">{row.overallMedian} d</span>
          </div>
        )}
        {props.options?.showP75 && typeof row.overallP75 === 'number' ? (
          <div className="flex justify-between gap-4">
            <span className="opacity-70">75%</span>
            <span className="tabular-nums">{row.overallP75} d</span>
          </div>
        ) : null}
        {props.options?.movingAvg && typeof row.movingAvg === 'number' ? (
          <div className="flex justify-between gap-4">
            <span className="opacity-70">Glidende</span>
            <span className="tabular-nums">{row.movingAvg} d</span>
          </div>
        ) : null}
        {deptEntries.length > 0 && (
          <div className="mt-1 pt-1 border-t">
            {deptEntries.map((d) => (
              <div
                key={d.id}
                className="flex justify-between items-center gap-2 py-0.5"
              >
                <span className="flex items-center gap-1 truncate">
                  <span
                    className="inline-block w-2 h-2 rounded-full"
                    style={{ background: d.color ?? '#666' }}
                  />
                  {deptLabels[d.id] ?? `Dept ${d.id}`}
                </span>
                <span className="tabular-nums">
                  {d.value} d
                  {typeof d.count === 'number' && (
                    <span className="opacity-50"> ({d.count})</span>
                  )}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
