'use client'

import Link from 'next/link'

import { Tabs, TabsList, TabsTrigger } from '@nordvik/ui/tabs'

export const AdminTabValues = {
  EMPLOYEES: 'employees',
  INSIGHTS: 'insights',
  INSPECTIONS: 'inspections',
  LEADS: 'leads',
  EXTERNAL_LEADS: 'external-leads',
  VALUATIONS: 'valuations',
  VOA: 'voa',
  TTM: 'ttm',
} as const

type AdminTabsProps = {
  currentTab: (typeof AdminTabValues)[keyof typeof AdminTabValues]
}

const tabs = [
  { value: 'employees', label: 'Meg<PERSON><PERSON>', href: '/admin' },
  { value: 'insights', label: 'Innsikt', href: '/admin/insights' },
  { value: 'inspections', label: 'Befaring', href: '/admin/inspections' },
  { value: 'leads', label: 'Tips', href: '/admin/leads' },
  {
    value: 'external-leads',
    label: 'App Tips',
    href: '/admin/external-leads',
  },
  { value: 'valuations', label: 'E-takst', href: '/admin/valuations' },
  { value: 'voa', label: 'VOA', href: '/admin/voa' },
  { value: 'ttm', label: 'TTM', href: '/admin/ttm' },
]

export function AdminTabs({ currentTab }: AdminTabsProps) {
  return (
    <Tabs value={currentTab}>
      <TabsList className="flex w-full">
        {tabs.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value}>
            <Link href={tab.href}>{tab.label}</Link>
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  )
}
