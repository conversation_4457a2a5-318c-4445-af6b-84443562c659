import { AnimatePresence, motion } from 'framer-motion'
import { SearchIcon, XIcon } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

interface SearchInputProps {
  searchQuery?: string
  setSearchQuery: (searchQuery: string) => void
  placeholder?: string
  children: React.ReactNode
}

export const SearchInput = ({
  searchQuery,
  setSearchQuery,
  placeholder = 'Søk',
  children,
}: SearchInputProps) => {
  return (
    <>
      <label
        data-has-value={!!searchQuery}
        className={cn(
          'flex h-9 items-center data-[has-value=true]:grow gap-2 group/search cursor-pointer ink-subtle hover:ink-default',
          'max-md:[&:not(:focus-within)]:w-6 [&:has(:focus-within)]:w-52 duration-300 transition-[width] ease-in-out',
        )}
      >
        <div className="h-9 w-4 shrink-0 flex-center select-none">
          <SearchIcon size={16} />
        </div>

        <div className="sr-only">Søk i oppdrag</div>
        <input
          placeholder={placeholder}
          value={searchQuery || ''}
          onChange={(e) => setSearchQuery(e.target.value)}
          data-has-value={!!searchQuery}
          className={cn(
            'focus:outline-none grow placeholder:ink-subtle group-hover/search:placeholder:fill-ink focus:w-full w-0 overflow-hidden',
          )}
        />
        <XIcon
          size={14}
          onClick={() => setSearchQuery('')}
          className={cn(
            'cursor-pointer ink-muted group-hover/search:ink-default',
            { hidden: !searchQuery },
          )}
        />
      </label>
      <AnimatePresence>
        {!searchQuery && (
          <motion.div
            style={{ overflow: 'clip' }}
            initial={false}
            animate={{ width: 'auto' }}
            transition={{ type: 'spring', damping: 60, stiffness: 600 }}
            exit={{ width: 0 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
