'use client'

import { useQueryClient } from '@tanstack/react-query'
import { AnimatePresence, motion } from 'framer-motion'
import { CheckIcon, Loader2Icon, NotebookPenIcon } from 'lucide-react'
import { Op } from 'quill'
import React from 'react'

import { But<PERSON> } from '@nordvik/ui/button'
import { Dialog, DialogContent, DialogTrigger } from '@nordvik/ui/dialog'
import { useToast } from '@nordvik/ui/toaster'

import { useUpdateNotesMutation } from '@/api/generated-client'
import { QUILL_FORMATS } from '@/components/rich-text-editor/config'
import { RichTextEditor } from '@/components/rich-text-editor/rich-text-editor'
import { useQuill } from '@/components/rich-text-editor/use-quill.hook'
import { convertDeltaToHtml } from '@/components/rich-text-editor/utils'
import { useThrottle } from '@/hooks/use-throttle'
import { useTrackEvent } from '@/lib/analytics/track-event'

import type { EstateForBroker } from '../types'

export function EstateCardNotes({ estate }: { estate: EstateForBroker }) {
  const [isOpen, setIsOpen] = React.useState(false)
  const initialNotes = estate.inspectionFolder?.notes
  const [currentNotes, setCurrentNotes] = React.useState(initialNotes || '')

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          iconOnly={<NotebookPenIcon />}
          data-has-notes={!!initialNotes}
          className="group-hover/estate-card:opacity-100 opacity-0 transition-opacity duration-200 group-hover/estate-card:hover:opacity-100 data-[has-notes=true]:opacity-100 data-[state=open]:opacity-100"
        >
          Notater
        </Button>
      </DialogTrigger>
      <DialogContent
        title="Notater"
        size="md"
        subtitle="Kun synlig for meglerne på oppdraget og ikke for kunden"
        aria-describedby={undefined}
      >
        {isOpen && (
          <NotesEditor
            estateId={estate.estateId}
            notes={currentNotes}
            onNotesChange={setCurrentNotes}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}

function NotesEditor({
  estateId,
  notes,
  onNotesChange,
}: {
  estateId: string
  notes: string
  onNotesChange: (notes: string) => void
}) {
  const [isSaving, setIsSaving] = React.useState(false)
  const [isTyping, setIsTyping] = React.useState(false)
  const { Quill } = useQuill()
  const [value, setValue] = React.useState<{ text: string } | null>({
    text: notes,
  })

  // Update editor value when notes prop changes
  React.useEffect(() => {
    setValue({ text: notes })
  }, [notes])

  const { toast } = useToast()
  const trackEvent = useTrackEvent()
  const queryClient = useQueryClient()

  const { mutate } = useUpdateNotesMutation({
    onMutate(variables) {
      const key = ['notes', { estateId: variables.estateId }]
      const previousNote = queryClient.getQueryData(key)
      queryClient.setQueryData(key, {
        inspectionFolder: {
          id: estateId,
          notes: variables.notes,
        },
      })

      return { previousNote }
    },
    onSettled() {
      queryClient.invalidateQueries({
        queryKey: ['notes', { estateId }],
      })
      trackEvent('notes_updated', { estateId })
      setIsSaving(false)
    },
    onError: (error, variables, ctx) => {
      if (ctx) {
        queryClient.setQueryData(
          ['notes', { estateId: variables.estateId }],
          ctx.previousNote,
        )
        setIsSaving(false)
      }
      console.error('Error updating notes', error)
      toast({
        variant: 'destructive',
        title: 'Kunne ikke lagre notater',
      })
    },
  })

  const saveNotes = useThrottle(
    (ops: Op[]) => {
      setIsTyping(false) // User stopped typing
      setIsSaving(true) // Now show loading
      try {
        const newNotes = convertDeltaToHtml(ops, Quill)
        onNotesChange(newNotes)
        mutate({
          estateId,
          notes: newNotes,
        })
      } catch (error) {
        console.error('Error updating notes', error)
      }
    },
    2000,
    {
      trailing: true,
    },
  )

  return (
    <div className="relative">
      <SaveIndicator isSaving={isSaving} isTyping={isTyping} />

      <RichTextEditor
        className="w-full min-h-[20rem] border-t border-t-muted grow flex [&_.ql-container]:grow flex-col bg-root-muted [&_.quill]:grow [&_.ql-editor]:px-[--dialog-padding-x] [&_.ql-editor]:py-2.5 [&_.ql-blank.ql-editor::before]:left-[--dialog-padding-x] [&_.ql-blank.ql-editor::before]:ink-muted"
        autoFocus
        value={value}
        onChange={(value) => {
          if (value.ops) {
            setIsTyping(true) // User is typing
            setIsSaving(false) // Hide loading while typing
            setValue({ text: convertDeltaToHtml(value.ops, Quill) })
            saveNotes(value.ops)
          }
        }}
        formats={QUILL_FORMATS}
        placeholder="Notater…"
      />
    </div>
  )
}

export function SaveIndicator({
  isSaving,
  isTyping,
}: {
  isSaving: boolean
  isTyping: boolean
}) {
  return (
    <AnimatePresence initial={false}>
      {isSaving ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 1, scale: 0.5 }}
          className="absolute right-3 bottom-3 size-6 flex items-center justify-center bg-root p-1 rounded-full"
        >
          <Loader2Icon className="animate-spin size-full ink-muted opacity-80" />
        </motion.div>
      ) : !isTyping ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{
            opacity: [0, 1, 1, 1, 0],
            scale: [0.5, 1.1, 1, 1, 1],
            transition: {
              duration: 2,
              times: [0, 0.1, 0.4, 0.9, 1],
            },
          }}
          exit={{ opacity: 0, scale: 0.5 }}
          className="absolute right-3 bottom-3 size-6 flex items-center justify-center bg-root p-1 rounded-full"
        >
          <CheckIcon className="size-full ink-success" />
        </motion.div>
      ) : null}
    </AnimatePresence>
  )
}
