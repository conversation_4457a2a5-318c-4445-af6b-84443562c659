'use client'

import React from 'react'

import { cn } from '@nordvik/theme/cn'

import { GQLEstatesOverviewItemFragment } from '@/api/generated-client'

export type ColumnCellProps = {
  children: React.ReactNode
  className?: string
  // Whether the table cell should render a border x. Defaults to true since most cells in the table have borders
  withBorderX?: boolean
}

export const ColumnCell = ({
  children,
  className,
  withBorderX = true,
}: ColumnCellProps) => {
  const base = cn(`py-6 px-3 border-stroke-muted`)
  const border = withBorderX ? `border` : 'border-y'
  return <td className={cn(base, className, border)}>{children}</td>
}

export type EstateColumnProps = {
  estate: GQLEstatesOverviewItemFragment
}
