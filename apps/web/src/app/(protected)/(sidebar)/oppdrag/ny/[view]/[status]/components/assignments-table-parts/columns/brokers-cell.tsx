'use client'

import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Avatar, AvatarFallback, AvatarImage } from '@nordvik/ui/avatar'
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from '@nordvik/ui/tooltip'

import type { GQLEstatesOverviewItemFragment } from '@/api/generated-client'
import { brokerTypes } from '@/app/(befaring)/lib/broker-constants'

export function BrokersCell({
  brokers,
}: {
  brokers: GQLEstatesOverviewItemFragment['brokers']
}) {
  const uniqueBrokers = React.useMemo(() => {
    const list = brokers ?? []
    return Array.from(new Map(list.map((b) => [b?.email, b])).values())
  }, [brokers])

  if (!uniqueBrokers || uniqueBrokers.length === 0) return null

  return (
    <div className="flex -space-x-2">
      {uniqueBrokers.map((b, i) => (
        <Tooltip key={(b?.email || '') + i}>
          <TooltipTrigger asChild>
            <Avatar
              className={cn(
                'size-6 ring-2 ring-transparent ring-offset-2 ring-offset-background-root',
              )}
            >
              {b?.image?.small ? (
                <AvatarImage src={b.image.small} alt={b?.name || 'Megler'} />
              ) : (
                <AvatarFallback>{getInitials(b?.name)}</AvatarFallback>
              )}
            </Avatar>
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent variant="dark">
              <div className="space-y-0.5">
                <div className="font-medium leading-5">
                  {b?.name || b?.email || 'Megler'}
                </div>
                {b?.role && brokerTypes[b.role as 1 | 2 | 3 | 4 | 5]?.label ? (
                  <div className="typo-body-xs ink-muted">
                    {brokerTypes[b.role as 1 | 2 | 3 | 4 | 5]?.label}
                  </div>
                ) : null}
              </div>
            </TooltipContent>
          </TooltipPortal>
        </Tooltip>
      ))}
    </div>
  )
}

function getInitials(name?: string) {
  if (!name) return 'N'
  return name
    .split(' ')
    .filter(Boolean)
    .map((n) => n[0]!)
    .join('')
    .toUpperCase()
}

export const MemoBrokersCell = React.memo(BrokersCell)
