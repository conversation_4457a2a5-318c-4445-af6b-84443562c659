import { uppercaseFirst } from '@befaring/lib/text-formatting'

import { formatDate } from '@/lib/dates'

import EventRow from '../../estate-drawer/estate-drawer-parts/event-row'

import { ColumnCell, EstateColumnProps } from './base'

type InspectionColumnProps = EstateColumnProps

export const InspectionColumn = ({ estate }: InspectionColumnProps) => {
  const inspectionEvents =
    estate.activities?.filter((e) => e.type === 1 && e.start) ?? []

  return (
    <ColumnCell className="py-5">
      {inspectionEvents.length === 0 ? (
        <div className="ink-subtle">Ikke satt</div>
      ) : (
        inspectionEvents.map((event, index) => (
          <EventRow
            key={`${event.start}-${index}`}
            confirmed={true}
            label={
              event.start
                ? uppercaseFirst(formatDate(event.start, 'd. MMM'))
                : 'Ukjent'
            }
            details={event.start && `kl. ${formatDate(event.start, 'HH:mm')}`}
          />
        ))
      )}
    </ColumnCell>
  )
}
