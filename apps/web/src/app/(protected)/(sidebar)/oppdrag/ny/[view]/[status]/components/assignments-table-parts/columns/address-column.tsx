'use client'

import { TextButton } from '@nordvik/ui/text-button'

import EstateThumbnail from '../../estate-thumbnail'

import { ColumnCell, EstateColumnProps } from './base'

export type AddressColumnProps = EstateColumnProps

export const AddressColumn = ({ estate }: AddressColumnProps) => {
  return (
    <ColumnCell withBorderX={false}>
      <div className="flex items-start gap-3">
        <EstateThumbnail imageSource={estate.mainImage?.small} />
        <div className="space-y-1">
          <div className="typo-body-sm-bold leading-5">
            {estate.address?.streetAddress ?? '—'}
          </div>
          <div className="flex gap-3 flex-wrap items-center">
            {estate.assignmentNumber && (
              <span className="ink-muted typo-body-sm">
                {estate.assignmentNumber}
              </span>
            )}
            <TextButton
              size="md"
              href={estate.linkToNext}
              target="_blank"
              iconEnd="external"
              onClick={(e) => e.stopPropagation()}
            >
              Se i Next
            </TextButton>
          </div>
        </div>
      </div>
    </ColumnCell>
  )
}
