'use client'

import { useQueryClient } from '@tanstack/react-query'
import debounce from 'lodash/debounce'
import React from 'react'

import { TableRow } from '@nordvik/ui/table'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
  useEstateDrawerDetailsQuery,
} from '@/api/generated-client'

import { useSidePanel } from '../../client-fetching/use-side-panel'

import { ColumnId, getColumnsForTab } from './columns'
import { AdColumn } from './columns/ad-status'
import { AddressColumn } from './columns/address-column'
import { BrokerTasksColumn } from './columns/broker-tasks-column'
import { BrokersColumn } from './columns/brokers-column'
import { NordvikEkstraColumn } from './columns/nordvik-ekstra-column'
import { PriceSuggestionColumn } from './columns/price-suggestion-column'
import { PublishColumn } from './columns/publish-column'
import { SellerTasksColumn } from './columns/seller-tasks-column'
import { SoldPriceColumn } from './columns/sold-price-column'
import { TakeOverDateColumn } from './columns/take-over-date-column'
import { ViewingColumn } from './columns/viewing-column'

export function Row({
  estate,
  currentTab,
  columns,
}: {
  estate: GQLEstatesOverviewItemFragment
  currentTab?: GQLEstateTabFilter
  columns?: ColumnId[]
}) {
  // Resolve columns only when inputs change
  const cols = React.useMemo(
    () => columns ?? getColumnsForTab(currentTab),
    [columns, currentTab],
  )

  const { openPanel } = useSidePanel()
  const queryClient = useQueryClient()

  // Debounced prefetch so rapid mouse movements don't spam network
  const prefetchEstate = React.useMemo(
    () =>
      debounce((estateId: string) => {
        const variables = { id: estateId }
        const queryKey = useEstateDrawerDetailsQuery.getKey(variables)

        const state = queryClient.getQueryState(queryKey)
        if (state?.status === 'success' || state?.status === 'pending') return

        const fetcher = useEstateDrawerDetailsQuery.fetcher(variables)
        queryClient.prefetchQuery({
          queryKey,
          queryFn: fetcher,
          staleTime: 60 * 1000, // 1 minute freshness window
        })
      }, 200),
    [queryClient],
  )

  // Cancel pending debounce on unmount to avoid invoking after detach
  React.useEffect(() => {
    return () => {
      prefetchEstate.cancel()
    }
  }, [prefetchEstate])

  const handleOpen = React.useCallback(() => {
    openPanel(estate.estateId)
  }, [openPanel, estate.estateId])

  const handlePrefetch = React.useCallback(() => {
    prefetchEstate(estate.estateId)
  }, [prefetchEstate, estate.estateId])

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        handleOpen()
      }
    },
    [handleOpen],
  )

  const renderColumn = React.useCallback(
    (c: ColumnId) => {
      switch (c) {
        case 'address':
          return <AddressColumn key={c} estate={estate} />
        case 'brokerTasks':
          return <BrokerTasksColumn key={c} estate={estate} />
        case 'sellerTasks':
          return (
            <SellerTasksColumn
              key={c}
              estate={estate}
              currentTab={currentTab}
            />
          )
        case 'publish':
          return (
            <PublishColumn key={c} estate={estate} currentTab={currentTab} />
          )
        case 'viewing':
          return <ViewingColumn key={c} estate={estate} />
        case 'brokers':
          return <BrokersColumn key={c} estate={estate} />
        case 'soldPrice':
          return <SoldPriceColumn key={c} estate={estate} />
        case 'priceSuggestion':
          return <PriceSuggestionColumn key={c} estate={estate} />
        case 'takeOverDate':
          return <TakeOverDateColumn key={c} estate={estate} />
        case 'status':
          return <AdColumn key={c} estate={estate} />
        case 'nordvikEkstra':
          return <NordvikEkstraColumn key={c} estate={estate} />
        default:
          return null
      }
    },
    [estate, currentTab],
  )

  return (
    <TableRow
      // provide keyboard & screen reader affordances.
      className="group/estateRow align-top border-stroke-muted cursor-pointer hover:bg-fill-interactive-list-hover focus-within:bg-fill-interactive-list-hover"
      role="button"
      tabIndex={0}
      aria-label="Åpne oppdragsdetaljer"
      onClick={handleOpen}
      onMouseEnter={handlePrefetch}
      onFocus={handlePrefetch}
      onKeyDown={handleKeyDown}
      data-estate-id={estate.estateId}
    >
      {cols.map(renderColumn)}
      {/* Hidden accessible fallback button (kept for screen readers that ignore row role) */}
      <td className="sr-only">
        <button onClick={handleOpen} className="sr-only">
          Se mer
        </button>
      </td>
    </TableRow>
  )
}

export const MemoRow = React.memo(Row)
