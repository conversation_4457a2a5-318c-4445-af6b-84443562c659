'use client'

import React from 'react'

import { Badge } from '@nordvik/ui/badge'

import { ColumnCell, EstateColumnProps } from './base'

export const NordvikEkstraColumn = ({ estate }: EstateColumnProps) => {
  const campaigns = estate.campaigns || []

  const now = Date.now()

  // Filter active campaigns
  const active = campaigns
    .filter((c) => {
      if (!c) return false
      const start = c.orderStartDate ? Date.parse(c.orderStartDate) : undefined
      const end = c.orderEndDate ? Date.parse(c.orderEndDate) : undefined
      if (start && now < start) return false
      if (end && now > end) return false
      // Focus on Nordvik Ekstra packages only if identifiable by marketingPackage or packageName
      const name =
        `${c.packageName ?? ''} ${c.marketingPackage ?? ''}`.toLowerCase()
      return name.includes('ekstra')
    })
    .sort((a, b) => {
      const aEnd = a?.orderEndDate ? Date.parse(a.orderEndDate) : Infinity
      const bEnd = b?.orderEndDate ? Date.parse(b.orderEndDate) : Infinity
      return aEnd - bEnd
    })

  const campaign = active[0]

  // If no active campaign, find the most recently ended Nordvik Ekstra campaign (with an end date in the past)
  let endedCampaign: typeof campaign | undefined
  if (!campaign) {
    endedCampaign = campaigns
      .filter((c) => {
        if (!c) return false
        const end = c.orderEndDate ? Date.parse(c.orderEndDate) : undefined
        if (!end || end > now) return false
        const name =
          `${c.packageName ?? ''} ${c.marketingPackage ?? ''}`.toLowerCase()
        return name.includes('ekstra')
      })
      .sort((a, b) => {
        const aEnd = a?.orderEndDate ? Date.parse(a.orderEndDate) : -Infinity
        const bEnd = b?.orderEndDate ? Date.parse(b.orderEndDate) : -Infinity
        // We want the most recent ended (largest end date first)
        return bEnd - aEnd
      })[0]

    if (!endedCampaign) {
      return (
        <ColumnCell>
          <span className="ink-subtle">—</span>
        </ColumnCell>
      )
    }
  }

  const target = campaign || endedCampaign!

  const start = target.orderStartDate
    ? new Date(target.orderStartDate)
    : undefined
  const end = target.orderEndDate ? new Date(target.orderEndDate) : undefined

  const formatDate = (d?: Date, withYear?: boolean) =>
    d
      ? d.toLocaleDateString('no-NO', {
          day: '2-digit',
          month: 'short',
          year: withYear ? 'numeric' : undefined,
        })
      : ''

  const period =
    start && end
      ? `${formatDate(start)} - ${formatDate(end, start.getFullYear() !== end.getFullYear())}`
      : start && !end
        ? `Fra ${formatDate(start, true)}`
        : end && !start
          ? `Til ${formatDate(end, true)}`
          : ''

  return (
    <ColumnCell>
      <div className="flex flex-col gap-1">
        <div className="typo-body-sm-bold leading-5">
          {target.packageName || 'Nordvik Ekstra'}
        </div>
        {campaign &&
          (period ? (
            <div className="typo-body-xs ink-subtle">
              <Badge variant="bright-green">Aktiv</Badge> {period}
            </div>
          ) : (
            <Badge variant="grey" size="md">
              Aktiv
            </Badge>
          ))}
        {!campaign && end && (
          <div className="typo-body-xs ink-subtle">
            <Badge variant="grey">Ikke aktiv</Badge> stoppet{' '}
            {formatDate(end, end.getFullYear() !== new Date().getFullYear())}
          </div>
        )}
      </div>
    </ColumnCell>
  )
}
