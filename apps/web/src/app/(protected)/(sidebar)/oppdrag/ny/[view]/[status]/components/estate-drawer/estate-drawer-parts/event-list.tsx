import { eventTypes } from '@befaring/oppdragsavtale/[estateId]/fremdriftsplan/timeline-event-types'
import { isSameDay } from 'date-fns'

import { Button } from '@nordvik/ui/button'

import { GQLEstateDrawerDetailsQuery } from '@/api/generated-client'
import { formatDate } from '@/lib/dates'

import EventRow from './event-row'

type Props = {
  events: NonNullable<GQLEstateDrawerDetailsQuery>['events']
  estateId: string
}
const EventList = ({ events, estateId }: Props) => {
  return (
    <div className="bg-background-root rounded-sm p-4 flex flex-col gap-2">
      <div className="flex flex-col gap-1">
        <h3 className="typo-body-sm-bold">Fremdriftsplan</h3>

        {events.length ? (
          <>
            <p className="typo-body-sm text-ink-muted">
              Tentative datoer er hentet fra befaringsmappa
            </p>
            {events.map((event) => (
              <EventRow
                key={event.id}
                confirmed={false}
                label={event.title ?? eventTypes[event.type]?.title ?? 'Ukjent'}
                details={getInterval(event.start, event.end)}
              />
            ))}
          </>
        ) : (
          <p className="typo-body-md text-ink-muted">
            Det er ikke satt opp noen fremdriftsplan
          </p>
        )}
      </div>
      <Button
        size={'sm'}
        variant={'outline'}
        href={`/oppdragsavtale/${estateId}/fremdriftsplan`}
      >
        Gå til befaringsmappe
      </Button>
    </div>
  )
}

function getInterval(start: string, end?: string): string {
  if (!end || isSameDay(start, end)) {
    return formatDate(start, 'eeee dd.MMM')
  }
  return `${formatDate(start, 'eeee dd.MMM')} - ${formatDate(end, 'eeee dd.MMM')}`
}

export default EventList
