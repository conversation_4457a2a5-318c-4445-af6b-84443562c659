'use client'

import React from 'react'

import { Table, TableBody, TableFooter } from '@nordvik/ui/table'

import {
  GQLEstateTabFilter,
  GQLNewAssignmentsOverviewQueryVariables,
  GQLSortEstateBy,
  useInfiniteNewAssignmentsOverviewQuery,
} from '@/api/generated-client'
import InfinityScroll from '@/components/infinity-scroll'
import { useBrokerIdsQuery } from '@/hooks/useBrokerIdsQuery'
import { useSearchQuery } from '@/hooks/useSearchQuery'
import { useUserContext } from '@/lib/UserContext'

import { useCurrentTab } from '../../../../status/[status]/components/estate-list/use-current-tab'
import { estateListStatusMap } from '../../../../util'
import { DocumentStatusesProvider } from '../client-fetching/use-document-statuses-batch-sse'
import { SidePanelProvider } from '../client-fetching/use-side-panel'

import { GroupedBody } from './assignments-table-parts/grouped-body'
import { useAssignmentsDerived } from './assignments-table-parts/hooks'
import { SearchGroupedBody } from './assignments-table-parts/search-grouped-body'
import { SkeletonRows } from './assignments-table-parts/skeleton-rows'
import { TableFooterInfo } from './assignments-table-parts/table-footer-info'
import { TableHeaderControls } from './assignments-table-parts/table-header-controls'
import { UngroupedBody } from './assignments-table-parts/ungrouped-body'
import { ViewType } from './assignments-table-parts/util'
import { BrokersFilter } from './brokers-filter'
import EstateDrawer from './estate-drawer/estate-drawer'
import { PrefetchDebug } from './prefetch-debug'
import { useEstatesFromQuery } from './use-estates-from-query'
import { useGroupedSearchResults } from './use-grouped-search-results'

export type SortKey = 'address' | 'publish' | 'showing' | 'last_changed'
export type SortDir = 'asc' | 'desc'

const PAGE_LIMIT = 15

export function AssignmentsTable({
  status,
  view,
}: {
  status: string
  view: ViewType
}) {
  const officeView = view === 'kontor'

  const [search] = useSearchQuery()
  const { tab, isArchiveTab } = useCurrentTab(status)
  const tabs = estateListStatusMap[tab] ?? [GQLEstateTabFilter.Requested]
  const currentTab = tabs[0] as GQLEstateTabFilter

  const { user } = useUserContext()
  const [selectedBrokerIds] = useBrokerIdsQuery()

  const sortBy: GQLSortEstateBy = GQLSortEstateBy.ChangedDate

  const baseVars = {
    brokerId: user?.employeeId ?? '',
    departmentId: Number(user?.department?.departmentId) || 0,
    tabs,
    limit: PAGE_LIMIT,
    offset: 0,
    archived: isArchiveTab,
    search,
    officeView: officeView || undefined,
    sortBy,
    brokerIds:
      officeView && selectedBrokerIds.length ? selectedBrokerIds : undefined,
    includeCampaigns: currentTab === GQLEstateTabFilter.ForSale,
    includeListingAgreement: currentTab === GQLEstateTabFilter.Requested,
  } satisfies GQLNewAssignmentsOverviewQueryVariables

  const {
    data,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteNewAssignmentsOverviewQuery(baseVars, {
    enabled:
      Boolean(user?.employeeId) && Boolean(user?.department?.departmentId),
    initialPageParam: { offset: 0, limit: PAGE_LIMIT },
    getNextPageParam: (lastPage) => {
      const pag = officeView
        ? lastPage.office?.pagination
        : lastPage.broker?.pagination
      if (!pag) return undefined
      const nextOffset = (pag.offset || 0) + (pag.count || 0)
      if (nextOffset >= (pag.total || 0)) return undefined
      return { offset: nextOffset, limit: pag.limit || PAGE_LIMIT }
    },
    refetchOnWindowFocus: false,
    staleTime: 8000,
  })

  // Debug logging to verify prefetch worked
  React.useEffect(() => {
    if (data?.pages?.length) {
      console.log('🎯 Client: Query data loaded', {
        isLoading,
        pagesCount: data.pages.length,
        firstPageItems: officeView
          ? data.pages[0]?.office?.items?.length || 0
          : data.pages[0]?.broker?.items?.length || 0,
        wasPrefetched: !isLoading && data.pages.length > 0,
      })
    }
  }, [data, isLoading, officeView])

  const { allEstates, totalFromQuery } = useEstatesFromQuery(
    data?.pages ?? [],
    officeView,
  )

  const { groups, withoutPublishDate, visibleCount } = useAssignmentsDerived(
    allEstates,
    currentTab,
  )

  const { hasSearch, grouped } = useGroupedSearchResults(allEstates, search)

  const fallbackBrokers = React.useMemo(
    () =>
      Array.from(
        new Map(
          allEstates
            .flatMap((e) => e.brokers || [])
            .filter((b): b is NonNullable<typeof b> => !!b)
            .map((b) => [b.employeeId, b]),
        ).values(),
      ),
    [allEstates],
  )

  const pagesLength = Array.isArray(data?.pages) ? data!.pages.length : 0
  const initialLoading = isLoading && pagesLength === 0
  const appending = isFetchingNextPage && !initialLoading
  const showEmptyState =
    !initialLoading && !isError && !hasSearch && visibleCount === 0
  return (
    <DocumentStatusesProvider estates={allEstates} status={currentTab!}>
      <SidePanelProvider>
        <div
          className="mt-6 rounded-lg"
          aria-busy={initialLoading || appending || undefined}
        >
          {officeView && !hasSearch && (
            <div className="mb-3 flex gap-2">
              <BrokersFilter
                departmentId={Number(user?.department?.departmentId) || 0}
                fallbackBrokers={fallbackBrokers}
                disabled={initialLoading}
              />
            </div>
          )}
          <InfinityScroll
            fetchNextPage={() => fetchNextPage()}
            isLoading={initialLoading || appending}
            lastPage={!hasNextPage}
          >
            {hasSearch ? (
              <SearchGroupedBody groups={grouped} />
            ) : (
              <Table className="text-sm">
                <caption className="sr-only">Oversikt over oppdrag</caption>
                <TableHeaderControls
                  currentTab={currentTab}
                  currentView={view}
                />
                <TableBody>
                  {isError && !initialLoading && (
                    <tr>
                      <td colSpan={8} className="p-6 text-center">
                        Kunne ikke laste oppdrag nå.
                      </td>
                    </tr>
                  )}
                  {showEmptyState && (
                    <tr>
                      <td colSpan={8} className="p-10 text-center opacity-70">
                        Ingen oppdrag funnet.
                      </td>
                    </tr>
                  )}
                  {officeView &&
                  currentTab === GQLEstateTabFilter.InPreparation ? (
                    <GroupedBody
                      groups={groups}
                      withoutPublishDate={withoutPublishDate}
                      currentTab={currentTab}
                    />
                  ) : (
                    <UngroupedBody
                      estates={allEstates}
                      currentTab={currentTab}
                    />
                  )}
                  {(initialLoading || appending) && (
                    <SkeletonRows colSpan={undefined} currentTab={currentTab} />
                  )}
                </TableBody>
                <TableFooter className="bg-root border-stroke-muted">
                  <TableFooterInfo
                    visibleCount={visibleCount}
                    totalFromQuery={totalFromQuery}
                    isError={isError}
                  />
                </TableFooter>
              </Table>
            )}
          </InfinityScroll>
        </div>
        <EstateDrawer />
        <PrefetchDebug queryVariables={baseVars} />
      </SidePanelProvider>
    </DocumentStatusesProvider>
  )
}
