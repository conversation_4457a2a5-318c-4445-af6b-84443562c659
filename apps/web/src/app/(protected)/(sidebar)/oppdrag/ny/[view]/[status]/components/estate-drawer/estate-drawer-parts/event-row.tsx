import { cn } from '@nordvik/theme/cn'

type Props = {
  confirmed: boolean
  label: string
  details?: string
}

const EventRow = ({ confirmed, label, details }: Props) => {
  return (
    <div className="flex justify-between py-1 border-stroke-muted">
      <div className="flex items-center gap-2">
        <div
          className={cn(
            'h-3.5 rounded-sm w-[3px] shrink-0',
            confirmed ? 'bg-fill-success-bold' : 'bg-fill-gray-subtle',
          )}
        />
        <p className="typo-body-sm">{label}</p>
      </div>
      {details && <p className="typo-body-sm text-ink-muted">{details}</p>}
    </div>
  )
}

export default EventRow
