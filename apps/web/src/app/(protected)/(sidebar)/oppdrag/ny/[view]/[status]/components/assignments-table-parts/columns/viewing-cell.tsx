'use client'

import React from 'react'

import { GQLEstatesOverviewItemFragment } from '@/api/generated-client'
import { formatDate } from '@/lib/dates'

import EventRow from '../../estate-drawer/estate-drawer-parts/event-row'

export function ViewingCell({
  showings,
  inspectionViewings,
}: {
  showings: GQLEstatesOverviewItemFragment['showings']
  inspectionViewings: NonNullable<
    GQLEstatesOverviewItemFragment['inspectionEvents']
  >
}) {
  // Only include future showings, sort ascending and take up to 2 upcoming
  const upcoming = React.useMemo(() => {
    if (!showings || showings.length === 0)
      return [] as NonNullable<typeof showings>
    const now = Date.now()
    return [...showings]
      .filter((s) => s?.start && new Date(s.start!).getTime() >= now)
      .sort(
        (a, b) => new Date(a!.start!).getTime() - new Date(b!.start!).getTime(),
      )
  }, [showings])

  if (upcoming.length === 0) {
    if (inspectionViewings.length > 0) {
      return (
        <div className="space-y-0.5">
          {inspectionViewings.map((viewing, idx) => (
            <EventRow
              key={viewing!.id ?? 'showing' + idx}
              confirmed={false}
              label={formatDate(viewing.start!, 'd. MMM ')}
            />
          ))}
        </div>
      )
    }

    return <span className="ink-muted">Ikke satt</span>
  }

  return (
    <div className="space-y-0.5">
      {upcoming.map((showing, idx) => (
        <EventRow
          key={showing!.showingId ?? 'showing' + idx}
          confirmed={true}
          label={
            showing.start ? formatDate(showing.start, 'dd.MMM') : 'Ukjent dato'
          }
          details={showing.start && formatDate(showing.start, 'HH:mm')}
        />
      ))}
    </div>
  )
}

export const MemoViewingCell = React.memo(ViewingCell)
