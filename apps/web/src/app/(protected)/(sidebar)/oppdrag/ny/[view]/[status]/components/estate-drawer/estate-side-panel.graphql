query estateDrawerDetails($id: String!) {
  estate(id: $id) {
    status
    estateId

    address {
      streetAddress
      city
    }

    assignmentNumber
    noOfBedRooms
    soldDate
    commissionAcceptedDate
    linkToNext
    marketingStart {
      date
      source
    }

    estatePrice {
      totalPrice
      priceSuggestion
      soldPrice
    }

    sumArea {
      braI
      pRom
    }

    links {
      linkType
      url
      text
    }

    mainImage {
      large
    }

    placeholderImage

    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
    }

    mainSeller {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
    }

    showings {
      start
      end
      showingId
    }

    checklist {
      firstTag
      value
    }

    linkToNext
    hjemUrl

    brokers {
      role
      image {
        small
      }
      name
      employeeId
      employeeRoles {
        typeId
      }
    }

    ads {
      source
      link
    }

    projectRelation

    activities {
      type
      start
      end
    }

    finn {
      finnExpireDate
      finnPublishDate
    }
  }

  events: inspectionEvents(estateId: $id) {
    id
    title
    start
    end
    type
    description
  }
}
