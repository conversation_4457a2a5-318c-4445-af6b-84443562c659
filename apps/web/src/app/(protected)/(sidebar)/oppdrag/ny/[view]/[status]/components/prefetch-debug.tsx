'use client'

import { useQueryClient } from '@tanstack/react-query'
import { useEffect, useState } from 'react'

import { useInfiniteNewAssignmentsOverviewQuery } from '@/api/generated-client'

export function PrefetchDebug({ 
  queryVariables 
}: { 
  queryVariables: any 
}) {
  const queryClient = useQueryClient()
  const [debugInfo, setDebugInfo] = useState<any>(null)

  useEffect(() => {
    const queryKey = useInfiniteNewAssignmentsOverviewQuery.getKey(queryVariables)
    const queryState = queryClient.getQueryState(queryKey)
    const queryData = queryClient.getQueryData(queryKey)

    setDebugInfo({
      queryKey: queryKey.join(' | '),
      hasData: !!queryData,
      status: queryState?.status,
      dataUpdatedAt: queryState?.dataUpdatedAt ? new Date(queryState.dataUpdatedAt).toLocaleTimeString() : null,
      fetchStatus: queryState?.fetchStatus,
      isStale: queryState?.isStale,
      pages: (queryData as any)?.pages?.length || 0,
      firstPageItemCount: (queryData as any)?.pages?.[0]?.broker?.items?.length || 
                         (queryData as any)?.pages?.[0]?.office?.items?.length || 0
    })
  }, [queryClient, queryVariables])

  if (process.env.NODE_ENV === 'production') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">🔍 Prefetch Debug</h3>
      <div className="space-y-1">
        <div>Status: <span className="text-green-400">{debugInfo?.status}</span></div>
        <div>Has Data: <span className="text-blue-400">{debugInfo?.hasData ? 'Yes' : 'No'}</span></div>
        <div>Pages: <span className="text-yellow-400">{debugInfo?.pages}</span></div>
        <div>Items: <span className="text-purple-400">{debugInfo?.firstPageItemCount}</span></div>
        <div>Updated: <span className="text-gray-400">{debugInfo?.dataUpdatedAt}</span></div>
        <div>Stale: <span className="text-red-400">{debugInfo?.isStale ? 'Yes' : 'No'}</span></div>
      </div>
    </div>
  )
}
