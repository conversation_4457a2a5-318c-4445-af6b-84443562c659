import { parseAsStringLiteral, useQueryState } from 'nuqs'
import React from 'react'

import type {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'

import type { SortDir, SortKey } from '../assignments-table'

import { ViewType, groupByWeek } from './util'

export function useSortingQueryState() {
  const [sort, setSort] = useQueryState<SortKey>(
    'sort',
    parseAsStringLiteral(['address', 'publish']).withDefault('address'),
  )
  const [dir, setDir] = useQueryState<SortDir>(
    'dir',
    parseAsStringLiteral(['asc', 'desc']).withDefault('asc'),
  )

  const sortKey = sort ?? 'address'
  const dirKey = dir ?? 'asc'

  const onSort = React.useCallback(
    (newSort: SortKey) => {
      if (newSort === sort) {
        setDir(dirKey === 'asc' ? 'desc' : 'asc')
      } else {
        setSort(newSort)
        setDir('asc')
      }
    },
    [setSort, setDir, dirKey, sort],
  )

  const ariaSortFor = (key: SortKey): 'ascending' | 'descending' | 'none' => {
    if (sortKey !== key) return 'none'
    return dirKey === 'asc' ? 'ascending' : 'descending'
  }

  return { sortKey, dirKey, ariaSortFor, onSort }
}

export function useAssignmentsDerived(
  allEstates: GQLEstatesOverviewItemFragment[],
  currentTab?: GQLEstateTabFilter,
) {
  const inPreperation = currentTab === 'InPreparation'

  const groups = React.useMemo(() => {
    if (!inPreperation)
      return [] as {
        key: number
        weekStart: Date
        estates: GQLEstatesOverviewItemFragment[]
      }[]
    return groupByWeek(allEstates)
  }, [inPreperation, allEstates])

  const withoutPublishDate = React.useMemo(
    () => (inPreperation ? allEstates.filter((e) => !e.marketingStart) : []),
    [inPreperation, allEstates],
  )

  const visibleCount = React.useMemo(() => {
    if (!inPreperation) return allEstates.length
    const groupedCount = groups.reduce((sum, g) => sum + g.estates.length, 0)
    return groupedCount + withoutPublishDate.length
  }, [inPreperation, allEstates.length, groups, withoutPublishDate.length])

  const visibleEstateIds = React.useMemo(() => {
    const estates = inPreperation
      ? [...groups.flatMap((g) => g.estates), ...withoutPublishDate]
      : allEstates
    const ids = estates.map((e) => e.estateId).filter(Boolean)
    return Array.from(new Set(ids))
  }, [inPreperation, groups, withoutPublishDate, allEstates])

  return {
    inPreperation,
    allEstates,
    groups,
    withoutPublishDate,
    visibleCount,
    visibleEstateIds,
  }
}

export function useEstatesView() {
  return useQueryState<ViewType>(
    'view',
    parseAsStringLiteral(['kontor', 'mine']).withDefault('mine'),
  )
}
