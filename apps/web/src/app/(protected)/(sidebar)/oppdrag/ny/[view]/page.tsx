import { redirect } from 'next/navigation'

export default async function Page({
  params,
}: {
  params: Promise<{ view: string; status: string }>
}) {
  const { view, status } = await params
  if (!status) {
    if (view === 'mine') {
      return redirect(`/oppdrag/ny/mine/innsalg`)
    } else if (view === 'kontor') {
      return redirect(`/oppdrag/ny/kontor/klargjøring`)
    }
  }

  return redirect(`/oppdrag/ny/mine/innsalg`)
}
