import { GQLEstateTabFilter } from '@/api/generated-client'

type Content =
  | 'publish'
  | 'sellerTasks'
  | 'brokerTasks'
  | 'viewing'
  | 'events'
  | 'inspectionDate'

export function getContentForEstateDrawer(tab: GQLEstateTabFilter): Content[] {
  switch (tab) {
    case 'Valuation':
      return ['inspectionDate', 'sellerTasks', 'brokerTasks', 'events']
    case 'Requested':
      return ['inspectionDate', 'sellerTasks', 'brokerTasks', 'events']
    case 'InPreparation':
      return ['publish', 'viewing', 'sellerTasks', 'brokerTasks', 'events']
    case 'ForSale':
      return ['viewing', 'sellerTasks', 'brokerTasks', 'events']
    case 'Archived':
      return ['viewing', 'events']
    default:
      return []
  }
}
