export const mockEstate = {
  estate: {
    address: 'Abborveien 42',
    image: 'cdn.nordvik.no/abborveien42.jpg',
    assignmentNumber: '13-0401/25',
    nextLink: 'google.com',
    value: '10200000',
    brai: '106',
    roomCount: 3,
  },
  meta: {
    phase: 'Klargj<PERSON>ring',
    broker: { image: 'cdn.nordvik.no/nora.jpeg', name: '<PERSON>' }, // Not sure if this should be an array and add a "type" field for megler1, fullmektig, ansvarlig... or if they should be individual properties
    sellers: [{ name: '<PERSON>' }, { name: '<PERSON>' }],
    note: '...........',
    befaringsmappaUrl: 'www.google.com', // Needs a better name
  },
  sellersTasks: [
    { title: 'task1', status: 'NONE' },
    { title: 'task2', status: 'STARTED' },
    { title: 'task3', status: 'COMPLETE' },
  ],
  brokersTaks: [
    { title: 'task1', status: 'NONE' },
    { title: 'task2', status: 'STARTED' },
    { title: 'task3', status: 'COMPLETE' },
  ],
  inspectionEvents: [
    {
      title: 'Bygningssakkynding',
      date: new Date('1990-10-31'),
      origin: 'next',
    },
    {
      title: 'Visning 1',
      date: new Date('2026-12-31'),
      startTime: new Date(),
      endTime: new Date(),
      origin: 'next',
    },
    {
      title: 'Styling',
      date: new Date('2025-10-20'),
      origin: 'bm',
    },
    {
      title: 'Visning 2',
      date: new Date('2027-12-31'),
      startTime: new Date(),
      endTime: new Date(),
      origin: 'next',
    },
  ],
}
