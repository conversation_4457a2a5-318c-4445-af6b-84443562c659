import React from 'react'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'

import { mapNumericStatusToTab } from '../../../../util'

import {
  type ColumnId,
  getColumnsForTab,
} from './assignments-table-parts/columns'

export const groupedSearchStatusOrder: GQLEstateTabFilter[] = [
  GQLEstateTabFilter.Valuation,
  GQLEstateTabFilter.Requested,
  GQLEstateTabFilter.InPreparation,
  GQLEstateTabFilter.ForSale,
  GQLEstateTabFilter.Sold,
  GQLEstateTabFilter.Archived,
]

export type GroupedSearchResult = {
  status: GQLEstateTabFilter
  estates: GQLEstatesOverviewItemFragment[]
  columns: ColumnId[]
}

export function useGroupedSearchResults(
  estates: GQLEstatesOverviewItemFragment[],
  search: string | undefined,
) {
  const hasSearch = !!search && search.trim().length > 0

  const grouped = React.useMemo<GroupedSearchResult[]>(() => {
    if (!hasSearch) return []
    const byStatus: Record<
      GQLEstateTabFilter,
      GQLEstatesOverviewItemFragment[]
    > = {
      [GQLEstateTabFilter.Valuation]: [],
      [GQLEstateTabFilter.Requested]: [],
      [GQLEstateTabFilter.InPreparation]: [],
      [GQLEstateTabFilter.ForSale]: [],
      [GQLEstateTabFilter.Sold]: [],
      [GQLEstateTabFilter.Archived]: [],
    }
    for (const estate of estates) {
      const tab = mapNumericStatusToTab(estate.status, estate.isValuation)
      if (!tab) continue
      byStatus[tab].push(estate)
    }
    return groupedSearchStatusOrder
      .filter((s) => byStatus[s].length > 0)
      .map((s) => ({
        status: s,
        estates: byStatus[s],
        columns: getColumnsForTab(s),
      }))
  }, [estates, hasSearch])

  return { hasSearch, grouped }
}
