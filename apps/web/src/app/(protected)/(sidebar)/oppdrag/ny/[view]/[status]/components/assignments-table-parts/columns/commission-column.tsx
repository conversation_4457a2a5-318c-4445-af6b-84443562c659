'use client'

import { formatCurrency } from '@/lib/formatCurrency'

import { ColumnCell, EstateColumnProps } from './base'

export type CommissionColumnProps = EstateColumnProps

export const CommissionColumn = ({ estate }: CommissionColumnProps) => {
  return (
    <ColumnCell>
      <div className="text-left flex gap-1 justify-between">
        <span className="whitespace-nowrap">
          {estate.listingAgreement?.commission
            ? formatCurrency(estate.listingAgreement.commission)
            : '—'}
        </span>
        <span className="ml-1 ink-muted">
          {estate.listingAgreement?.feePercentage
            ? ` (${estate.listingAgreement.feePercentage}%)`
            : ''}
        </span>
      </div>
    </ColumnCell>
  )
}
