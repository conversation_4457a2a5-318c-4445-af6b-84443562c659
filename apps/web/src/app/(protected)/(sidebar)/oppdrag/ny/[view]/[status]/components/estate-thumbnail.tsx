import Image from 'next/image'

import { LogoWithBorder } from '@/components/logo'

type Props = {
  imageSource: string | undefined
}

const EstateThumbnail = ({ imageSource }: Props) => {
  if (!imageSource || imageSource?.includes('estates/placeholder/')) {
    return (
      <div className="size-12 shrink-0 rounded-md bg-root-muted flex justify-center items-center">
        <LogoWithBorder className="size-6 p-0.5 text-ink-brand" />
      </div>
    )
  }
  return (
    <Image
      src={imageSource}
      alt={''}
      width={48}
      height={48}
      sizes="48px"
      className="size-12 shrink-0 rounded-md object-cover"
    />
  )
}

export default EstateThumbnail
