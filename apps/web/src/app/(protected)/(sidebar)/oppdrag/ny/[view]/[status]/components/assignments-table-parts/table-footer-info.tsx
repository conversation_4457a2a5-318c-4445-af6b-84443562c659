import React from 'react'

import { TableRow } from '@nordvik/ui/table'

export function TableFooterInfo({
  visibleCount,
  totalFromQuery,
  isError,
}: {
  visibleCount: number
  totalFromQuery?: number
  isError: boolean
}) {
  return (
    <TableRow>
      <td className="py-3 pl-4 typo-body-sm-bold text-ink-muted">
        {!!totalFromQuery && (
          <>
            Viser {visibleCount} av {totalFromQuery}
          </>
        )}
      </td>
      <td className="p-4 text-right space-x-2 flex">
        {isError && (
          <span className="ink-danger">Kunne ikke laste flere oppdrag</span>
        )}
      </td>
      <td />
    </TableRow>
  )
}
