'use client'

import { Send } from 'lucide-react'
import React from 'react'

import { Button } from '@nordvik/ui/button'

import { TaskBadgeState } from '@/components/task-status-badge/task-status-badge'

import {
  FollowUpSellerModal,
  FollowUpSellerModalProps,
} from '../../follow-up-seller-modal'

import TaskList from './task-list'

type Props = {
  tasks: { title: string; status: TaskBadgeState }[]
  estate: FollowUpSellerModalProps['estate']
}
const SellersTasks = ({ tasks, estate }: Props) => {
  const [openFollowUp, setOpenFollowUp] = React.useState(false)

  const hasPending = tasks.some((task) => task.status !== 'COMPLETE')

  return (
    <TaskList
      title="Selgers ansvar"
      description="Status på innhenting av informasjon fra selgere"
      tasks={tasks}
      action={
        hasPending && (
          <>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setOpenFollowUp(true)}
              iconStart={<Send />}
            >
              Følg opp selger
            </Button>
            {openFollowUp && (
              <FollowUpSellerModal
                estate={estate}
                open={openFollowUp}
                onOpenChange={setOpenFollowUp}
              />
            )}
          </>
        )
      }
    />
  )
}

export default SellersTasks
