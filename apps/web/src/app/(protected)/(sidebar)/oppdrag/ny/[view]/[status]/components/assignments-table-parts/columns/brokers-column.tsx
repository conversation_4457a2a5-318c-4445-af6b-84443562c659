'use client'

import React from 'react'

import { GQLEstateTabFilter } from '@/api/generated-client'

import { RowHoverActions } from '../row-hover-actions'

import { ColumnCell, EstateColumnProps } from './base'
import { MemoBrokersCell } from './brokers-cell'

export type BrokersColumnProps = EstateColumnProps & {
  currentTab?: GQLEstateTabFilter
}

export const BrokersColumn = ({ estate, currentTab }: BrokersColumnProps) => {
  return (
    <ColumnCell withBorderX={false} className="relative">
      <div className="relative flex items-center">
        <MemoBrokersCell brokers={estate.brokers} />
      </div>
      <RowHoverActions estate={estate} currentTab={currentTab} />
    </ColumnCell>
  )
}
