'use client'

import { formatCurrency } from '@/lib/formatCurrency'

import { ColumnCell, EstateColumnProps } from './base'

export type SoldPriceColumnProps = EstateColumnProps

export const SoldPriceColumn = ({ estate }: SoldPriceColumnProps) => {
  return (
    <ColumnCell>
      <div className="text-left">
        {estate.estatePriceModel?.soldPrice
          ? formatCurrency(estate.estatePriceModel.soldPrice)
          : '—'}
      </div>
    </ColumnCell>
  )
}
