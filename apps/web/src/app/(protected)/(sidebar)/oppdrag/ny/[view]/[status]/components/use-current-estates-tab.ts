import { useParams } from 'next/navigation'

import { GQLEstateTabFilter } from '@/api/generated-client'

import { estateListStatusMap } from '../../../../util'

export function useCurrentEstatesTab() {
  const params = useParams<{ status: string }>()
  const tabs = estateListStatusMap[decodeURIComponent(params.status)] ?? [
    GQLEstateTabFilter.Requested,
  ]

  return tabs[0] as GQLEstateTabFilter
}
