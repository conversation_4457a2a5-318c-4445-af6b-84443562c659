import { addDays, getISOWeek } from 'date-fns'
import React from 'react'

import { TableRow } from '@nordvik/ui/table'

import type {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'
import { formatDate } from '@/lib/dates'

import { getColumnsForTab } from './columns'
import { MemoRow } from './row'

type Group = {
  key: number
  weekStart: Date
  estates: GQLEstatesOverviewItemFragment[]
}

export function GroupedBody({
  groups,
  withoutPublishDate,
  currentTab,
}: {
  groups: Group[]
  withoutPublishDate: GQLEstatesOverviewItemFragment[]
  currentTab?: GQLEstateTabFilter
}) {
  const colCount = getColumnsForTab(currentTab).length
  return (
    <>
      {groups.map((g) => (
        <React.Fragment key={g.key}>
          <TableRow className="border-none">
            <td
              colSpan={colCount}
              className="py-2 pl-4 rounded-sm font-medium bg-gray-muted"
            >
              <span>{`Uke ${getISOWeek(g.weekStart)}`}</span>
              <span className="ink-muted typo-body-xs ml-2">{`${formatDate(g.weekStart, 'd MMM')} - ${formatDate(
                addDays(g.weekStart, 6),
                'd MMM',
              )}`}</span>
            </td>
          </TableRow>
          {g.estates.map((estate) => (
            <MemoRow
              key={`${estate.estateId}-${estate.marketingStart?.date}`}
              estate={estate}
              currentTab={currentTab}
            />
          ))}
        </React.Fragment>
      ))}

      {withoutPublishDate.length > 0 && (
        <>
          <TableRow className="border-none">
            <td
              colSpan={colCount}
              className="py-2 pl-4 rounded-sm font-medium bg-gray-muted"
            >
              Uten publiseringsdato
            </td>
          </TableRow>
          {withoutPublishDate.map((estate) => (
            <MemoRow
              key={estate.estateId}
              estate={estate}
              currentTab={currentTab}
            />
          ))}
        </>
      )}
    </>
  )
}
