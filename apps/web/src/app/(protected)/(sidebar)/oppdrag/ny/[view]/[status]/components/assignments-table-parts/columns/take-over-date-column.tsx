'use client'

import { formatDate } from '@/lib/dates'

import { ColumnCell, EstateColumnProps } from './base'

export type TakeOverDateColumnProps = EstateColumnProps

export const TakeOverDateColumn = ({ estate }: TakeOverDateColumnProps) => {
  return (
    <ColumnCell>
      <div className="text-left">
        {estate.takeOverDate ? formatDate(estate.takeOverDate) : '—'}
      </div>
    </ColumnCell>
  )
}
