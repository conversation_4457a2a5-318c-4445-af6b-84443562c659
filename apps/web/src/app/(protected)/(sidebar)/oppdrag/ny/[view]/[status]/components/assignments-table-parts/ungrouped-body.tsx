import React from 'react'

import type {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'

import { MemoRow } from './row'

export function UngroupedBody({
  estates,
  currentTab,
}: {
  estates: GQLEstatesOverviewItemFragment[]
  currentTab?: GQLEstateTabFilter
}) {
  return (
    <>
      {estates.map((estate) => (
        <MemoRow
          key={estate.estateId}
          estate={estate}
          currentTab={currentTab}
        />
      ))}
    </>
  )
}
