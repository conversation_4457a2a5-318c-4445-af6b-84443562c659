import { useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@nordvik/ui/accordion'
import { TextButton } from '@nordvik/ui/text-button'

import { GQLEstateDrawerDetailsQuery } from '@/api/generated-client'

type Props = {
  sellers: NonNullable<GQLEstateDrawerDetailsQuery['estate']>['sellers']
}

const Sellers = ({ sellers }: Props) => {
  const [isOpen, setIsOpen] = useState<string[]>([])

  const mainContact = sellers.find((seller) => seller.mainContact) ?? sellers[0]
  if (!mainContact)
    return (
      <>
        <p className="typo-body-sm text-ink-muted">Selgere</p>
        <p className="typo-body-sm">Ingen selgere registrert</p>
      </>
    )

  const otherContacts = sellers.filter(
    (seller) => seller.contactId !== mainContact.contactId,
  )

  return (
    <>
      <p className="typo-body-sm text-ink-muted">Selgere</p>
      <Accordion
        type="multiple"
        className="col-span-2"
        value={isOpen}
        onValueChange={setIsOpen}
      >
        <AccordionItem value="seller" className="border-0">
          <AccordionTrigger className="flex p-0 items-start w-full">
            <p
              className={cn(
                'typo-body-sm text-left max-w-[calc(100%-20px)] w-full',
                !isOpen.includes('seller') &&
                  'overflow-hidden text-ellipsis whitespace-nowrap',
              )}
            >
              {mainContact.firstName} {mainContact.lastName}
            </p>
            {!!otherContacts.length && (
              <p className="typo-body-sm text-ink-muted mr-3 whitespace-nowrap">
                +{otherContacts.length} til
              </p>
            )}
          </AccordionTrigger>
          <AccordionContent className="p-0">
            {mainContact.mainContact && (
              <p className="typo-body-sm">(Hovedkontakt)</p>
            )}
            {mainContact.email && (
              <TextButton size="md" href={`mailto:${mainContact.email}`}>
                {mainContact.email}
              </TextButton>
            )}
            {mainContact.mobilePhone && (
              <TextButton size="md" href={`tel:${mainContact.mobilePhone}`}>
                {mainContact.mobilePhone}
              </TextButton>
            )}
            {!!otherContacts.length &&
              otherContacts.map((contact) => (
                <div key={contact.contactId} className="mt-4">
                  <p>
                    {contact.firstName} {contact.lastName}
                  </p>
                  {contact.email && (
                    <TextButton size="md" href={`mailto:${contact.email}`}>
                      {contact.email}
                    </TextButton>
                  )}
                  {contact.mobilePhone && (
                    <TextButton size="md" href={`tel:${contact.mobilePhone}`}>
                      {contact.mobilePhone}
                    </TextButton>
                  )}
                </div>
              ))}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </>
  )
}

export default Sellers
