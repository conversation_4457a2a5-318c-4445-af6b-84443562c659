'use client'

import React from 'react'

import { GQLEstateTabFilter } from '@/api/generated-client'

import { DocumentStatuses } from '../../../client-fetching/document-statuses'

import { ColumnCell, EstateColumnProps } from './base'

export type SellerTasksColumnProps = EstateColumnProps & {
  currentTab?: GQLEstateTabFilter
}

export const SellerTasksColumn = ({
  estate,
  currentTab,
}: SellerTasksColumnProps) => {
  return (
    <ColumnCell>
      <div className="flex flex-wrap gap-2">
        <DocumentStatuses estateId={estate.estateId} currentTab={currentTab} />
      </div>
    </ColumnCell>
  )
}
