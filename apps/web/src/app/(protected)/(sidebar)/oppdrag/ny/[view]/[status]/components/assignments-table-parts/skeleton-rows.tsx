import { TableRow } from '@nordvik/ui/table'

import type { GQLEstateTabFilter } from '@/api/generated-client'

import { getColumnsForTab } from './columns'
import { ColumnCell } from './columns/base'

type Props = {
  colSpan?: number
  currentTab?: GQLEstateTabFilter
}

export function SkeletonRows({ colSpan, currentTab }: Props) {
  const count = colSpan ?? getColumnsForTab(currentTab).length
  return (
    <>
      {Array.from({ length: 10 }).map((_, i) => (
        <TableRow key={`skeleton-${i}`}>
          {Array.from({ length: count }).map((__, j) =>
            j === 0 ? (
              <AdressSkeleton key={`sk-${i}-${j}`} />
            ) : (
              <ColumnCell
                key={`sk-${i}-${j}`}
                withBorderX={!(j === 0 || j === count - 1)}
              >
                <div className="h-6 mb-10 w-full animate-pulse rounded bg-root-muted self-start" />
              </ColumnCell>
            ),
          )}
        </TableRow>
      ))}
    </>
  )
}

const AdressSkeleton = () => {
  return (
    <ColumnCell withBorderX={false}>
      <div className="flex gap-2">
        <div className="h-12 mb-10 w-12 animate-pulse rounded bg-root-muted" />
        <div className="h-12 mb-10 w-40 animate-pulse rounded bg-root-muted" />
      </div>
    </ColumnCell>
  )
}
