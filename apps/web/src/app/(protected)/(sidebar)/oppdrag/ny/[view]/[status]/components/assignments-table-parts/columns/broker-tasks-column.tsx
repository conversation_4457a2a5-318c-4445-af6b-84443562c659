'use client'

import React from 'react'

import { GQLEstateTabFilter } from '@/api/generated-client'

import { CHECKLIST_ITEMS } from '../../checklist'
import { MiniRing } from '../../mini-ring'

import { ColumnCell, EstateColumnProps } from './base'

export type BrokerTasksColumnProps = EstateColumnProps & {
  currentTab?: GQLEstateTabFilter
}

export const BrokerTasksColumn = ({
  estate,
  currentTab,
}: BrokerTasksColumnProps) => {
  const checklistItems = React.useMemo(
    () => (currentTab ? CHECKLIST_ITEMS[currentTab] : []),
    [currentTab],
  )

  const totalChecks = checklistItems.length
  const completedChecks = estate.checklist
    ? checklistItems.reduce((acc, item) => {
        if (item.check(estate.checklist)) return acc + 1
        return acc
      }, 0)
    : 0
  const progress = totalChecks ? completedChecks / totalChecks : 0

  return (
    <ColumnCell>
      <div className="flex items-center gap-0.5">
        <MiniRing progress={progress} className="size-5" />
        <div className="ink-subtle">
          {completedChecks}/{totalChecks}
        </div>
      </div>
    </ColumnCell>
  )
}
