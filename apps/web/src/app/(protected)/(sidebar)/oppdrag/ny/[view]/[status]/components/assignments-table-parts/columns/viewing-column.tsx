'use client'

import React from 'react'

import { ColumnCell, EstateColumnProps } from './base'
import { MemoViewingCell } from './viewing-cell'

export type ViewingColumnProps = EstateColumnProps

export const ViewingColumn = ({ estate }: ViewingColumnProps) => {
  return (
    <ColumnCell className="pb-9">
      <MemoViewingCell
        showings={estate.showings}
        inspectionViewings={
          estate.inspectionEvents?.filter((e) => e.type === 'viewing') || []
        }
      />
    </ColumnCell>
  )
}
