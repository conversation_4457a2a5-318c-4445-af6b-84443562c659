import { uppercaseFirst } from '@befaring/lib/text-formatting'

import { GQLEstateDrawerDetailsQuery } from '@/api/generated-client'
import { formatDate } from '@/lib/dates'

import EventRow from './event-row'

type Props = {
  estate: NonNullable<GQLEstateDrawerDetailsQuery['estate']>
}

const InspectionDate = ({ estate }: Props) => {
  const inspectionEvents =
    estate.activities?.filter((e) => e.type === 1 && e.start) ?? []
  return (
    <div className="bg-background-root rounded-sm p-4 flex flex-col gap-2">
      <div className="flex justify-between">
        <h3 className="typo-body-sm-bold">
          Befaring{inspectionEvents.length > 1 && 'er'}
        </h3>
      </div>
      <div>
        {inspectionEvents.length ? (
          inspectionEvents.map((inspection) => (
            <EventRow
              key={`${inspection.start}${inspection.end}`}
              confirmed={true}
              label={
                inspection.start
                  ? uppercaseFirst(formatDate(inspection.start, 'eeee dd. MMM'))
                  : 'Ukjent'
              }
              details={
                inspection.start &&
                `kl. ${formatDate(inspection.start, 'HH:mm')}`
              }
            />
          ))
        ) : (
          <p className="typo-body-sm text-ink-muted">
            Ingen befaringsdato satt
          </p>
        )}
      </div>
    </div>
  )
}
export default InspectionDate
