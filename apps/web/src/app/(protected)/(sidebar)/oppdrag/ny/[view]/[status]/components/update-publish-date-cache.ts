import { InfiniteData, QueryClient } from '@tanstack/react-query'

import {
  GQLEstatesOverviewItemFragment,
  GQLNewAssignmentsOverviewQuery,
} from '@/api/generated-client'

const INFINITE_KEY_ROOT = 'NewAssignmentsOverview.infinite'

export function updatePublishDateInInfiniteCache(
  queryClient: QueryClient,
  estateId: string,
  publishDate: string | null,
) {
  const matches = queryClient.getQueriesData<
    InfiniteData<GQLNewAssignmentsOverviewQuery>
  >({
    queryKey: [INFINITE_KEY_ROOT],
  })

  matches.forEach(([key, infiniteData]) => {
    if (!infiniteData) return
    let changed = false
    const pages = infiniteData.pages.map((page) => {
      const mutateItems = (items: GQLEstatesOverviewItemFragment[]) => {
        if (!items) return items
        let localChanged = false
        const next = items.map((it) => {
          if (it?.estateId === estateId) {
            localChanged = true
            changed = true
            return {
              ...it,
              marketingStart: publishDate
                ? {
                    ...(it.marketingStart || {}),
                    date: publishDate,
                    source: 'manual',
                  }
                : null,
            }
          }
          return it
        })
        return localChanged ? next : items
      }
      const office = page.office
      const broker = page.broker
      const nextPage = {
        ...page,
        office: office
          ? { ...office, items: mutateItems(office.items) }
          : office,
        broker: broker
          ? { ...broker, items: mutateItems(broker.items) }
          : broker,
      }
      return nextPage
    })
    if (changed) {
      queryClient.setQueryData(key, { ...infiniteData, pages })
    }
  })
}

export async function optimisticPublishDateMutation(
  queryClient: QueryClient,
  estateId: string,
  publishDate: string | null,
) {
  await queryClient.cancelQueries({ queryKey: [INFINITE_KEY_ROOT] })
  const previous = queryClient.getQueriesData<
    InfiniteData<GQLNewAssignmentsOverviewQuery>
  >({
    queryKey: [INFINITE_KEY_ROOT],
  })
  updatePublishDateInInfiniteCache(queryClient, estateId, publishDate)
  return { previous }
}

export function rollbackPublishDateMutation(
  queryClient: QueryClient,
  ctx:
    | {
        previous?: [
          unknown,
          InfiniteData<GQLNewAssignmentsOverviewQuery> | undefined,
        ][]
      }
    | undefined,
) {
  ctx?.previous?.forEach(([key, data]) => {
    queryClient.setQueryData(key as readonly unknown[], data)
  })
}

export function invalidatePublishDateRelated(queryClient: QueryClient) {
  queryClient.invalidateQueries({ queryKey: [INFINITE_KEY_ROOT] })
}
