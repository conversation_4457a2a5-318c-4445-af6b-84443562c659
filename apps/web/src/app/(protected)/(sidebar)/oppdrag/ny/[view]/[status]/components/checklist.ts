import { GQLEstateTabFilter } from '@/api/generated-client'
import { PHOTO_TAGS } from '@/server/model/AssignmentDocumentStatus/strategies/photos'
import { SURVEY_REPORT_TAGS } from '@/server/model/AssignmentDocumentStatus/strategies/survey-report'

const KOMMUNE_TAGS = [
  'S_KOMMUNEO_MANUELT',
  'S_AMBITA_KOMMUNEI',
  'S_SUPERTAKST_KOMMUNEI',
]

const BUSINESS_MANAGER_TAGS = [
  'S_FF_INFO_PAPIR',
  'S_AMBITA_INFO',
  'S_VISMA_INFO',
]

const INSURANCE_TAGS_STARTWITH = ['S_SIKRING_PAPIR_', 'S_SIKRING_E_']

const AML_TAGS_STARTWITH = ['AML_DONE', 'AMLoppslogiver']

export const CHECKLIST_ITEMS: Record<
  GQLEstateTabFilter,
  {
    name: string
    check: (list: { firstTag?: string; value?: number }[]) => boolean
  }[]
> = {
  [GQLEstateTabFilter.Valuation]: [
    {
      name: 'AML-sjekk',
      check: (list) =>
        list.some(
          (entry) =>
            AML_TAGS_STARTWITH.some((tag) => entry.firstTag?.startsWith(tag)) &&
            entry.value,
        ),
    },
  ],
  [GQLEstateTabFilter.Requested]: [
    {
      name: 'AML-sjekk',
      check: (list) =>
        list.some(
          (entry) =>
            AML_TAGS_STARTWITH.some((tag) => entry.firstTag?.startsWith(tag)) &&
            entry.value,
        ),
    },
  ],
  [GQLEstateTabFilter.InPreparation]: [
    {
      name: 'Bestilt egenerklæringsskjema',
      check: (list) =>
        list.some(
          (entry) => entry.firstTag?.startsWith('S_EES_') && entry.value,
        ),
    },
    {
      name: 'Bestilt foto',
      check: (list) =>
        list.some(
          (entry) => PHOTO_TAGS.includes(entry.firstTag!) && entry.value,
        ),
    },
    {
      name: 'Bestilt tilstandsrapport',
      check: (list) =>
        list.some(
          (entry) =>
            SURVEY_REPORT_TAGS.includes(entry.firstTag!) && entry.value,
        ),
    },
    {
      name: 'Gjennomgått grunnbok',
      check: (list) =>
        list.some((entry) => entry.firstTag === 'grboksjekkok' && entry.value),
    },
    {
      name: 'Bestilt kommuneopplysninger',
      check: (list) =>
        list.some(
          (entry) => KOMMUNE_TAGS.includes(entry.firstTag!) && entry.value,
        ),
    },
    {
      name: 'Bestill formuesverdi',
      check: (list) =>
        list.some(
          (entry) => entry.firstTag === 'S_FORMUESVERDI' && entry.value,
        ),
    },
    {
      name: 'Bestilt informasjon fra forretningsfører',
      check: (list) =>
        list.some(
          (entry) =>
            BUSINESS_MANAGER_TAGS.includes(entry.firstTag!) && entry.value,
        ),
    },
    {
      name: 'Sikring tinglyst',
      check: (list) =>
        list.some(
          (entry) =>
            INSURANCE_TAGS_STARTWITH.some((tag) =>
              entry.firstTag?.startsWith(tag),
            ) && entry.value,
        ),
    },
    {
      name: 'Fått salgsoppgaven godkjent',
      check: (list) =>
        list.some(
          (entry) =>
            entry.firstTag === 'S_PRIS_SALGSOPPGAVE_GODKJENT' && entry.value,
        ),
    },
  ],
  [GQLEstateTabFilter.ForSale]: [],
  [GQLEstateTabFilter.Sold]: [],
  [GQLEstateTabFilter.Archived]: [],
}
