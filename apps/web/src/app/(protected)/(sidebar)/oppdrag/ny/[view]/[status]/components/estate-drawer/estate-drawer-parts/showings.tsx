import { uppercaseFirst } from '@befaring/lib/text-formatting'

import { Badge } from '@nordvik/ui/badge'

import { GQLEstateDrawerDetailsQuery } from '@/api/generated-client'
import { formatDate } from '@/lib/dates'

import EventRow from './event-row'

type Props = {
  showings: NonNullable<GQLEstateDrawerDetailsQuery['estate']>['showings']
}

const Showings = ({ showings }: Props) => {
  return (
    <div className="bg-background-root rounded-sm p-4 flex flex-col gap-2">
      <div className="flex justify-between">
        <h3 className="typo-body-sm-bold">
          Visning{showings.length > 1 && 'er'}
        </h3>
        {!!showings.length && <Badge variant="outline">Satt i Next</Badge>}
      </div>
      <div>
        {showings.length ? (
          showings.map((showing) => (
            <EventRow
              key={`${showing.start}${showing.showingId}`}
              confirmed={true}
              label={
                showing.start
                  ? uppercaseFirst(formatDate(showing.start, 'eeee dd. MMM'))
                  : 'Ukjent'
              }
              details={
                showing.start && `kl. ${formatDate(showing.start, 'HH:mm')}`
              }
            />
          ))
        ) : (
          <p className="typo-body-md text-ink-muted">
            Det er ingen planlagte visninger
          </p>
        )}
      </div>
    </div>
  )
}
export default Showings
