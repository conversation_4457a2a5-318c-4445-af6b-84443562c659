import React from 'react'

import { GQLEstateTabFilter } from '@/api/generated-client'
import TaskStatusBadge, {
  TaskBadgeState,
  taskBadgeState,
} from '@/components/task-status-badge/task-status-badge'
import { providerTypesForStatus } from '@/server/model/AssignmentDocumentStatus/helpers/provider-types'

import { useEstateDocumentStatuses } from './use-document-statuses-batch-sse'

export const DocumentStatuses = React.memo(function DocumentStatuses({
  estateId,
  currentTab,
}: {
  estateId: string
  currentTab?: GQLEstateTabFilter
}) {
  const itemsMap = useEstateDocumentStatuses(estateId)
  const types = currentTab
    ? providerTypesForStatus(currentTab, itemsMap.estate)
    : []

  return (
    <div className="flex flex-wrap gap-2">
      {types.map(({ type, name }) => (
        <TaskStatusBadge
          key={type}
          text={name}
          status={
            (itemsMap?.providers?.[type]?.state as TaskBadgeState) ??
            taskBadgeState.PENDING
          }
        />
      ))}
    </div>
  )
})
