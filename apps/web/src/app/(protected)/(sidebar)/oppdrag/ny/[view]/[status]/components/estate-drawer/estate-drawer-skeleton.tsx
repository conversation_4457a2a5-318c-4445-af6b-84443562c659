const EstateDrawerSkeleton = () => {
  return (
    <div className="flex flex-col gap-4">
      <div className="bg-root p-4 flex flex-col gap-4">
        <div className="animate-pulse bg-root-muted w-48 h-14" />
        <div className="animate-pulse bg-root-muted w-full h-11" />
        <div className="animate-pulse bg-root-muted w-full h-32" />
        <div className="animate-pulse bg-root-muted w-full h-8" />
      </div>
      <div className="flex flex-col gap-4 px-4">
        <div className="rounded-sm bg-root p-4 flex flex-col gap-4">
          <div className="animate-pulse bg-root-muted w-24 h-5" />
          <div className="animate-pulse bg-root-muted w-full h-28" />
        </div>
        <div className="rounded-sm bg-root p-4 flex flex-col gap-4">
          <div className="animate-pulse bg-root-muted w-24 h-5" />
          <div className="animate-pulse bg-root-muted w-full h-28" />
        </div>

        <div className="rounded-sm bg-root p-4 flex flex-col gap-4">
          <div className="animate-pulse bg-root-muted w-24 h-5" />
          <div className="animate-pulse bg-root-muted w-full h-40" />
        </div>
      </div>
    </div>
  )
}

export default EstateDrawerSkeleton
