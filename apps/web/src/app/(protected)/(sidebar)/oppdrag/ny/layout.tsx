import { redirect } from 'next/navigation'

import { getFeatureFlag } from '@/lib/analytics/feature-flag.server'

const FLAG_NAME = 'assignments-overview-v2'

export default async function Layout({ children }: React.PropsWithChildren) {
  const enabled = await getFeatureFlag(FLAG_NAME)
  if (!enabled) {
    // redirect to old assignments overview when flag disabled
    return redirect('/oppdrag/status/innsalg')
  }
  return <>{children}</>
}
