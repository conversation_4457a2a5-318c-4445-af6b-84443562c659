import type { Metadata } from 'next'
import { unstable_cache } from 'next/cache'
import { notFound } from 'next/navigation'
import { Suspense } from 'react'

import { Main } from '@/components/main'
import { getFeatureFlag } from '@/lib/analytics/feature-flag.server'
import { getCurrentUser } from '@/lib/session'
import userHasAdminRole, { isInHQDepartment } from '@/lib/userHasAdminRole'
import { GQLEstateTabFilter } from '@/server/generated-schema'
import {
  estatesForBrokerIdCount,
  estatesForDepartmentCount,
} from '@/server/model/BrokerEstate/factory'

import { AssignmentsTable } from './components/assignments-table'
import { ViewType } from './components/assignments-table-parts/util'
import StatusTabs from './components/status-tabs'

export const metadata: Metadata = {
  title: 'Mine oppdrag',
  description: 'Oppdragsoversikt med tydelige statuser og filtrering',
}

const FLAG_NAME = 'assignments-overview-v2'

export default async function Page(props: {
  params: Promise<{ status: string; view: ViewType }>
}) {
  const enabled = await getFeatureFlag(FLAG_NAME)
  if (!enabled) {
    notFound()
  }

  const params = await props.params

  return (
    <Main title={params.view === 'kontor' ? 'Kontoret' : 'Mine oppdrag'}>
      <Suspense fallback={<StatusTabs view="mine" tabCountPromises={{}} />}>
        <StatusWithCount view={params.view} />
      </Suspense>
      <AssignmentsTable
        status={decodeURIComponent(params.status)}
        view={params.view}
      />
    </Main>
  )
}

async function StatusWithCount({ view }: { view: string }) {
  const user = await getCurrentUser()
  const isHQEmployee = isInHQDepartment(user)

  if ((userHasAdminRole(user) && !isHQEmployee) || !user?.employeeId) {
    return <StatusTabs view={view} tabCountPromises={{}} />
  }

  const brokerId = user.employeeId
  const departmentId = user.department?.departmentId

  // Decide which tabs we want counts for based on view
  const tabsForView: GQLEstateTabFilter[] =
    view === 'kontor'
      ? [
          GQLEstateTabFilter.InPreparation,
          GQLEstateTabFilter.ForSale,
          GQLEstateTabFilter.Sold,
        ]
      : [
          GQLEstateTabFilter.Valuation,
          GQLEstateTabFilter.Requested,
          GQLEstateTabFilter.InPreparation,
          GQLEstateTabFilter.ForSale,
          GQLEstateTabFilter.Sold,
        ]

  // If office view but no department id -> no counts
  if (view === 'kontor' && !departmentId) {
    return <StatusTabs view={view} tabCountPromises={{}} />
  }

  // Build a Record of promises so each tab can independently suspend on the client
  const tabCountPromises = Object.fromEntries(
    tabsForView.map((tab) => [
      tab,
      view === 'kontor'
        ? getDepartmentTabCount(tab, departmentId!)
        : getBrokerTabCount(tab, brokerId),
    ]),
  ) as Record<GQLEstateTabFilter, Promise<number>>

  return <StatusTabs tabCountPromises={tabCountPromises} view={view} />
}

// Single-tab cached count fetchers
const getBrokerTabCount = unstable_cache(
  async (tab: GQLEstateTabFilter, brokerId: string) => {
    const res = await estatesForBrokerIdCount({ brokerId, tabs: [tab] })
    return res[0]?.count ?? 0
  },
  ['broker-tab-count'],
  { revalidate: 900 },
)

const getDepartmentTabCount = unstable_cache(
  async (tab: GQLEstateTabFilter, departmentId: number) => {
    const res = await estatesForDepartmentCount({ departmentId, tabs: [tab] })
    return res[0]?.count ?? 0
  },
  ['department-tab-count'],
  { revalidate: 900 },
)
