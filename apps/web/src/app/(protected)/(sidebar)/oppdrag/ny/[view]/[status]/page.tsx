import { HydrationBoundary, dehydrate } from '@tanstack/react-query'
import type { Metadata } from 'next'
import { unstable_cache } from 'next/cache'
import { notFound } from 'next/navigation'
import { Suspense } from 'react'

import {
  GQLNewAssignmentsOverviewQuery,
  GQLNewAssignmentsOverviewQueryVariables,
  GQLSortEstateBy,
  NewAssignmentsOverviewDocument,
  useInfiniteNewAssignmentsOverviewQuery,
} from '@/api/generated-client'
import { gqlServerFetch } from '@/api/gqlServerFetch'
import { Main } from '@/components/main'
import { getFeatureFlag } from '@/lib/analytics/feature-flag.server'
import { getQueryClient } from '@/lib/get-query-client'
import { getCurrentUser } from '@/lib/session'
import userHasAdminRole, { isInHQDepartment } from '@/lib/userHasAdminRole'
import { GQLEstateTabFilter } from '@/server/generated-schema'
import {
  estatesForBrokerIdCount,
  estatesForDepartmentCount,
} from '@/server/model/BrokerEstate/factory'

import {
  EstateStatus,
  estateListStatusMap,
  getStatusTextLowercase,
} from '../../../util'

import { AssignmentsTable } from './components/assignments-table'
import { ViewType } from './components/assignments-table-parts/util'
import StatusTabs from './components/status-tabs'

export const metadata: Metadata = {
  title: 'Mine oppdrag',
  description: 'Oppdragsoversikt med tydelige statuser og filtrering',
}

const FLAG_NAME = 'assignments-overview-v2'

async function preloadAssignmentsData(
  params: { status: string; view: ViewType },
  user: { employeeId?: string; department?: { departmentId?: number } },
) {
  const queryClient = getQueryClient()
  const PAGE_LIMIT = 15

  const status = decodeURIComponent(params.status)
  const tab = status ? status : getStatusTextLowercase(EstateStatus.Requested)
  const isArchiveTab = tab === getStatusTextLowercase(EstateStatus.Archive)
  const tabs = estateListStatusMap[tab] ?? [GQLEstateTabFilter.Requested]
  const officeView = params.view === 'kontor'
  const currentTab = tabs[0] as GQLEstateTabFilter

  const variables: GQLNewAssignmentsOverviewQueryVariables = {
    brokerId: user.employeeId!,
    departmentId: Number(user.department?.departmentId) || 0,
    tabs,
    limit: PAGE_LIMIT,
    offset: 0,
    archived: isArchiveTab,
    officeView: officeView || undefined,
    sortBy: GQLSortEstateBy.ChangedDate,
    includeCampaigns: currentTab === GQLEstateTabFilter.ForSale,
    includeListingAgreement: currentTab === GQLEstateTabFilter.Requested,
  }

  // Prefetch the first page for the infinite query
  await queryClient.prefetchInfiniteQuery({
    queryKey: useInfiniteNewAssignmentsOverviewQuery.getKey(variables),
    queryFn: async ({ pageParam }) => {
      const queryVariables = {
        ...variables,
        ...(pageParam ?? { offset: 0, limit: PAGE_LIMIT }),
      }

      const { data } = await gqlServerFetch<
        GQLNewAssignmentsOverviewQuery,
        GQLNewAssignmentsOverviewQueryVariables
      >(NewAssignmentsOverviewDocument, queryVariables)

      return data
    },
    initialPageParam: { offset: 0, limit: PAGE_LIMIT },
    getNextPageParam: (lastPage) => {
      const pag = officeView
        ? lastPage?.office?.pagination
        : lastPage?.broker?.pagination
      if (!pag) return undefined
      const nextOffset = (pag.offset || 0) + (pag.count || 0)
      if (nextOffset >= (pag.total || 0)) return undefined
      return { offset: nextOffset, limit: pag.limit || PAGE_LIMIT }
    },
  })

  return queryClient
}

export default async function Page(props: {
  params: Promise<{ status: string; view: ViewType }>
}) {
  const enabled = await getFeatureFlag(FLAG_NAME)
  if (!enabled) {
    notFound()
  }

  const params = await props.params
  const user = await getCurrentUser()

  // Prefetch assignments data if user has required permissions
  let queryClient = getQueryClient()

  if (user?.employeeId && user?.department?.departmentId) {
    try {
      queryClient = await preloadAssignmentsData(params, user)
    } catch (error) {
      console.error('Failed to prefetch assignments data:', error)
      // Continue without prefetching - the client will fetch the data
    }
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Main title={params.view === 'kontor' ? 'Kontoret' : 'Mine oppdrag'}>
        <Suspense fallback={<StatusTabs view="mine" tabCountPromises={{}} />}>
          <StatusWithCount view={params.view} />
        </Suspense>
        <AssignmentsTable
          status={decodeURIComponent(params.status)}
          view={params.view}
        />
      </Main>
    </HydrationBoundary>
  )
}

async function StatusWithCount({ view }: { view: string }) {
  const user = await getCurrentUser()
  const isHQEmployee = isInHQDepartment(user)

  if ((userHasAdminRole(user) && !isHQEmployee) || !user?.employeeId) {
    return <StatusTabs view={view} tabCountPromises={{}} />
  }

  const brokerId = user.employeeId
  const departmentId = user.department?.departmentId

  // Decide which tabs we want counts for based on view
  const tabsForView: GQLEstateTabFilter[] =
    view === 'kontor'
      ? [
          GQLEstateTabFilter.InPreparation,
          GQLEstateTabFilter.ForSale,
          GQLEstateTabFilter.Sold,
        ]
      : [
          GQLEstateTabFilter.Valuation,
          GQLEstateTabFilter.Requested,
          GQLEstateTabFilter.InPreparation,
          GQLEstateTabFilter.ForSale,
          GQLEstateTabFilter.Sold,
        ]

  // If office view but no department id -> no counts
  if (view === 'kontor' && !departmentId) {
    return <StatusTabs view={view} tabCountPromises={{}} />
  }

  // Build a Record of promises so each tab can independently suspend on the client
  const tabCountPromises = Object.fromEntries(
    tabsForView.map((tab) => [
      tab,
      view === 'kontor'
        ? getDepartmentTabCount(tab, departmentId!)
        : getBrokerTabCount(tab, brokerId),
    ]),
  ) as Record<GQLEstateTabFilter, Promise<number>>

  return <StatusTabs tabCountPromises={tabCountPromises} view={view} />
}

// Single-tab cached count fetchers
const getBrokerTabCount = unstable_cache(
  async (tab: GQLEstateTabFilter, brokerId: string) => {
    const res = await estatesForBrokerIdCount({ brokerId, tabs: [tab] })
    return res[0]?.count ?? 0
  },
  ['broker-tab-count'],
  { revalidate: 900 },
)

const getDepartmentTabCount = unstable_cache(
  async (tab: GQLEstateTabFilter, departmentId: number) => {
    const res = await estatesForDepartmentCount({ departmentId, tabs: [tab] })
    return res[0]?.count ?? 0
  },
  ['department-tab-count'],
  { revalidate: 900 },
)
