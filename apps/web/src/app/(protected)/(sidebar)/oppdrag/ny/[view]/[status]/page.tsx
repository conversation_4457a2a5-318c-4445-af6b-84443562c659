import type { Metadata } from 'next'
import { unstable_cache } from 'next/cache'
import { notFound } from 'next/navigation'
import { Suspense } from 'react'

import { Main } from '@/components/main'
import { getFeatureFlag } from '@/lib/analytics/feature-flag.server'
import { getCurrentUser } from '@/lib/session'
import userHasAdminRole, { isInHQDepartment } from '@/lib/userHasAdminRole'
import { GQLEstateTabFilter } from '@/server/generated-schema'
import { estatesForBrokerIdCount } from '@/server/model/BrokerEstate/factory'

import { AssignmentsTable } from './components/assignments-table'
import StatusTabs from './components/status-tabs'

export const metadata: Metadata = {
  title: 'Mine oppdrag',
  description: 'Oppdragsoversikt med tydelige statuser og filtrering',
}

const FLAG_NAME = 'assignments-overview-v2'

export default async function Page(props: {
  params: Promise<{ status: string; view: string }>
}) {
  const enabled = await getFeatureFlag(FLAG_NAME)
  if (!enabled) {
    notFound()
  }

  const params = await props.params

  // Server component orchestrates only the critical estate list fetch.
  // Document statuses are fetched in parallel per estate inside Suspense
  // boundaries defined in child components.
  return (
    <Main title="Mine oppdrag">
      <Suspense fallback={<StatusTabs view="mine" tabCounts={[]} isLoading />}>
        <StatusWithCount view={params.view} />
      </Suspense>
      <AssignmentsTable
        status={decodeURIComponent(params.status)}
        view={params.view}
      />
    </Main>
  )
}

async function StatusWithCount({ view }: { view: string }) {
  const user = await getCurrentUser()
  const isHQEmployee = isInHQDepartment(user)

  if ((userHasAdminRole(user) && !isHQEmployee) || !user?.employeeId) {
    return <StatusTabs view={view} tabCounts={[]} />
  }

  if (view === 'kontor') {
    return <StatusTabs view={view} tabCounts={[]} />
  }

  const data = await getStatusCounts(user.employeeId)

  if (!data) {
    return <StatusTabs view={view} tabCounts={[]} />
  }

  return <StatusTabs tabCounts={data} view={view} />
}

const getStatusCounts = unstable_cache(
  async (brokerId: string) => {
    return estatesForBrokerIdCount({
      brokerId,
      tabs: [
        GQLEstateTabFilter.Valuation,
        GQLEstateTabFilter.Requested,
        GQLEstateTabFilter.InPreparation,
        GQLEstateTabFilter.ForSale,
        GQLEstateTabFilter.Sold,
      ],
    })
  },
  ['status-count'],
  {
    revalidate: 900, // 15 minutes
  },
)
