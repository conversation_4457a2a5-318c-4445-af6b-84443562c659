'use client'

import { formatCurrency } from '@/lib/formatCurrency'

import { ColumnCell, EstateColumnProps } from './base'

export type PriceSuggestionColumnProps = EstateColumnProps

export const PriceSuggestionColumn = ({
  estate,
}: PriceSuggestionColumnProps) => {
  return (
    <ColumnCell>
      <div className="text-left">
        {estate.estatePriceModel?.priceSuggestion
          ? formatCurrency(estate.estatePriceModel.priceSuggestion)
          : '—'}
      </div>
    </ColumnCell>
  )
}
