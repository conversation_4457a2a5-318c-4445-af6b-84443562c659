import { GQLEstateTabFilter } from '@/api/generated-client'

export type ColumnId =
  | 'address'
  | 'brokerTasks'
  | 'sellerTasks'
  | 'publish'
  | 'viewing'
  | 'brokers'
  | 'soldPrice'
  | 'priceSuggestion'
  | 'takeOverDate'
  | 'status'
  | 'nordvikEkstra'
  | 'inspection'
  | 'commission'
  | 'priceSuggestionCommission'

export function getColumnsForTab(tab?: GQLEstateTabFilter): ColumnId[] {
  switch (tab) {
    case 'Valuation':
      return ['address', 'sellerTasks', 'brokerTasks', 'inspection', 'brokers']
    case 'Requested':
      return [
        'address',
        'priceSuggestionCommission',
        'sellerTasks',
        'brokerTasks',
        'inspection',
        'brokers',
      ]
    case 'InPreparation':
      return [
        'address',
        'sellerTasks',
        'brokerTasks',
        'publish',
        'viewing',
        'brokers',
      ]
    case 'ForSale':
      return ['address', 'status', 'nordvikEkstra', 'viewing', 'brokers']
    case 'Sold':
      return [
        'address',
        'priceSuggestion',
        'soldPrice',
        'sellerTasks',
        'takeOverDate',
        'brokers',
      ]
    case 'Archived':
      return ['address', 'brokers']
    default:
      return [
        'address',
        'brokerTasks',
        'sellerTasks',
        'publish',
        'viewing',
        'brokers',
      ]
  }
}

export function headerLabelFor(id: ColumnId, tab?: GQLEstateTabFilter): string {
  switch (id) {
    case 'address':
      return 'Adresse'
    case 'brokerTasks':
      return 'Oppgaver'
    case 'sellerTasks':
      return 'Selgers ansvar'
    case 'publish':
      // In later stages, show relevant date column label
      if (tab === 'ForSale' || tab === 'Archived' || tab === 'Sold')
        return 'Publisert'
      return 'Publiseres'
    case 'soldPrice':
      return 'Solgt for'
    case 'priceSuggestion':
      return 'Prisantydning'
    case 'priceSuggestionCommission':
      return 'Pris / Provisjon'
    case 'takeOverDate':
      return 'Overtakelse'
    case 'viewing':
      return 'Visning'
    case 'brokers':
      return ''
    case 'status':
      return 'Status'
    case 'nordvikEkstra':
      return 'Nordvik Ekstra'
    case 'inspection':
      return 'Befaring'
    case 'commission':
      return 'Provisjon'
  }
}

export function headerTooltipFor(id: ColumnId): string | null {
  switch (id) {
    case 'brokerTasks':
      return 'Viser status på oppgaver i Next som megler har ansvaret for.'
    case 'sellerTasks':
      return 'Viser status på informasjonshenting som selger har ansvaret for.'
    case 'publish':
      return 'Frist for når alt skal være klart til publisering.'
    case 'nordvikEkstra':
      return 'Aktiv Nordvik Ekstra kampanje og periode.'
    default:
      return null
  }
}

export function headerClassFor(id: ColumnId, tab?: GQLEstateTabFilter): string {
  switch (id) {
    case 'address':
      return 'w-[22%] py-3 pl-4 text-left'
    case 'brokerTasks':
      return 'w-[12%] py-3 text-left'
    case 'sellerTasks': {
      if (tab === 'Valuation') {
        return 'w-[18%] py-3 text-left'
      }
      return 'w-[30%] py-3 text-left'
    }
    case 'publish':
      return 'w-[16%] py-3 px-4 text-left'
    case 'viewing':
      return 'w-[14%] py-3 text-left'
    case 'brokers':
      return 'py-3 text-left'
    case 'soldPrice':
      return 'w-[12%] py-3 pr-4 text-left'
    case 'priceSuggestion':
      return 'w-[12%] py-3 pr-4 text-left'
    case 'priceSuggestionCommission':
      return 'w-[12%] py-3 pr-4 text-left'
    case 'takeOverDate':
      return 'w-[12%] py-3 pr-4 text-left'
    case 'status':
      return 'w-[12%] py-3 pl-4 text-left'
    case 'nordvikEkstra':
      return 'w-[18%] py-3 text-left'
    case 'inspection':
      return 'w-[16%] py-3 pl-4 text-left'
    case 'commission':
      return 'w-[12%] py-3 pr-4 text-left'
  }
}
