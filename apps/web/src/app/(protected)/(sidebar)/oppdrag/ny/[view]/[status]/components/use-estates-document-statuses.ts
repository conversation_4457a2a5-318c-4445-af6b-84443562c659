import { GQLEstateTabFilter } from '@/api/generated-client'
import { TaskBadgeState } from '@/components/task-status-badge/task-status-badge'
import { providerTypesForStatus } from '@/server/model/AssignmentDocumentStatus/helpers/provider-types'

import { useEstateDocumentStatuses } from '../client-fetching/use-document-statuses-batch-sse'

export type EstateSellerTasks = { title: string; status: TaskBadgeState }[]

export function useEstateSellerTasks(
  estateId: string,
  currentTab?: GQLEstateTabFilter,
): EstateSellerTasks {
  const itemsMap = useEstateDocumentStatuses(estateId)
  const types = currentTab
    ? providerTypesForStatus(currentTab, itemsMap.estate)
    : []

  return types.map((a) => ({
    title: a.name,
    status: (itemsMap?.providers?.[a.type]?.state || 'NONE') as TaskBadgeState,
  }))
}
