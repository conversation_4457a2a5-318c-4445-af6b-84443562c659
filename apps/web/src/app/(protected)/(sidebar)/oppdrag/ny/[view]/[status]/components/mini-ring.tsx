import { cn } from '@nordvik/theme/cn'

// Small circular progress + fraction like "6/8"
export function MiniRing({
  progress,
  className,
}: {
  progress: number
  className?: string
}) {
  const radius = 9
  const stroke = 3
  const circumference = 2 * Math.PI * radius
  const offset = circumference * (1 - progress)
  return (
    <svg viewBox="0 0 24 24" className={cn('shrink-0', className)}>
      <circle
        cx="12"
        cy="12"
        r={radius}
        strokeWidth={stroke}
        fill="none"
        className="stroke-subtle"
      />
      <circle
        cx="12"
        cy="12"
        r={radius}
        strokeWidth={stroke}
        fill="none"
        className="stroke-fill-success-bold"
        strokeDasharray={circumference}
        strokeDashoffset={offset}
        transform="rotate(-90 12 12)"
        strokeLinecap="round"
      />
    </svg>
  )
}
