'use client'

import { uppercaseFirst } from '@befaring/lib/text-formatting'
import { Calendar as CalendarIcon } from 'lucide-react'
import React from 'react'

import { Calendar } from '@nordvik/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@nordvik/ui/popover'

import { formatDate } from '@/lib/dates'

export function CalendarPopover({
  value,
  onChange,
  size = 'md',
}: {
  value?: string
  onChange: (value: Date | undefined) => void
  size?: 'sm' | 'md'
}) {
  const [date, setDate] = React.useState<Date | undefined>(
    value ? new Date(value) : undefined,
  )

  const handleChange = (val: Date | undefined) => {
    setDate(val)
    onChange(val)
  }

  const format = (date) => {
    if (!date) return 'Sett dato'
    if (size === 'sm') return formatDate(date, 'dd. MMM')
    return uppercaseFirst(formatDate(date, 'eeee dd. MMMM'))
  }

  return (
    <Popover>
      <PopoverTrigger className="w-full">
        <div className="flex items-center rounded-md border border-subtle px-2 py-1.5 text-left text-sm">
          {size === 'md' && <CalendarIcon className="mr-2 size-4" />}
          <span>{format(date)}</span>
        </div>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-auto p-2">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleChange}
          showWeekNumber
          fromDate={new Date()}
        />
      </PopoverContent>
    </Popover>
  )
}
