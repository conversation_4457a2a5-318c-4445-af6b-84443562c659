'use client'

import { Badge } from '@nordvik/ui/badge'

import { formatCurrency } from '@/lib/formatCurrency'

import { ColumnCell, EstateColumnProps } from './base'

export type PriceSuggestionCommissionColumnProps = EstateColumnProps

export const PriceSuggestionCommissionColumn = ({
  estate,
}: PriceSuggestionCommissionColumnProps) => {
  const priceSuggestion = estate.estatePriceModel?.priceSuggestion
  const commission = estate.listingAgreement?.commission
  const feePct = estate.listingAgreement?.feePercentage

  const feePercentageFormatter = new Intl.NumberFormat('no', {
    signDisplay: 'never',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1,
    style: 'percent',
  })

  return (
    <ColumnCell className="relative">
      <div className="flex flex-col gap-1 text-left leading-tight">
        <div className="font-medium">
          {priceSuggestion ? formatCurrency(priceSuggestion) : '—'}
        </div>
        {priceSuggestion ? (
          <div className="flex gap-1 items-center">
            {feePct ? (
              <Badge
                variant="outline"
                size="md"
                className="rounded-sm px-1 py-0.5 font-normal tracking-tight leading-none text-[10px]"
              >
                {feePercentageFormatter.format(feePct / 100)}
              </Badge>
            ) : null}
            <div className="text-xs ink-muted flex gap-1">
              <span>{commission ? formatCurrency(commission) : '—'}</span>
            </div>
          </div>
        ) : null}
      </div>
    </ColumnCell>
  )
}
