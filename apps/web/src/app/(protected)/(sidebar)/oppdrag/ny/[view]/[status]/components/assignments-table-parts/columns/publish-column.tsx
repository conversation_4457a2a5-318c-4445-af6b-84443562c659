'use client'

import React from 'react'

import { Badge } from '@nordvik/ui/badge'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
  useUpsertEstatePublishDateMutation,
} from '@/api/generated-client'

import { CalendarPopover } from '../../calendar-popover'

import { ColumnCell } from './base'

export type PublishColumnProps = {
  estate: GQLEstatesOverviewItemFragment
  currentTab?: GQLEstateTabFilter
}

export const PublishColumn = ({ estate, currentTab }: PublishColumnProps) => {
  const isSold = currentTab === GQLEstateTabFilter.Sold
  const isReadOnly = currentTab === GQLEstateTabFilter.ForSale || isSold
  const date = isSold ? estate.soldDate : estate.marketingStart?.date
  const d = date ? new Date(date) : undefined

  const { mutateAsync } = useUpsertEstatePublishDateMutation({})

  return (
    <ColumnCell className="py-5">
      {isReadOnly ? (
        <Badge variant="grey" size="md">
          {d
            ? d.toLocaleDateString('no-NO', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
              })
            : '—'}
        </Badge>
      ) : (
        <div onClick={(e) => e.stopPropagation()}>
          <CalendarPopover
            value={estate.marketingStart?.date}
            onChange={(data) =>
              mutateAsync({
                estateId: estate.estateId,
                publishDate: data ? new Date(data).toISOString() : null,
              })
            }
            size="sm"
          />
        </div>
      )}
    </ColumnCell>
  )
}
