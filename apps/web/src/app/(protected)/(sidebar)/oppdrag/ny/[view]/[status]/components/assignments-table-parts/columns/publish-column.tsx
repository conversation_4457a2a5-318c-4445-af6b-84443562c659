'use client'

import { useQueryClient } from '@tanstack/react-query'
import React from 'react'

import { Badge } from '@nordvik/ui/badge'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
  useEstateDrawerDetailsQuery,
  useUpsertEstatePublishDateMutation,
} from '@/api/generated-client'
import { formatDate } from '@/lib/dates'

import { CalendarPopover } from '../../calendar-popover'
import {
  invalidatePublishDateRelated,
  optimisticPublishDateMutation,
  rollbackPublishDateMutation,
} from '../../update-publish-date-cache'

import { ColumnCell } from './base'

export type PublishColumnProps = {
  estate: GQLEstatesOverviewItemFragment
  currentTab?: GQLEstateTabFilter
}

export const PublishColumn = ({ estate, currentTab }: PublishColumnProps) => {
  const isSold = currentTab === GQLEstateTabFilter.Sold
  const isReadOnly = currentTab === GQLEstateTabFilter.ForSale || isSold
  const date = isSold ? estate.soldDate : estate.marketingStart?.date
  const d = date ? new Date(date) : undefined

  const queryClient = useQueryClient()
  const { mutateAsync } = useUpsertEstatePublishDateMutation({
    onMutate: async (vars) => {
      if (!vars?.estateId) return
      return optimisticPublishDateMutation(
        queryClient,
        vars.estateId,
        vars.publishDate ?? null,
      )
    },
    onError: (_e, _v, ctx) => rollbackPublishDateMutation(queryClient, ctx),
    onSuccess: (_data, vars) => {
      if (vars?.estateId) {
        const key = useEstateDrawerDetailsQuery.getKey({ id: vars.estateId })
        void queryClient.invalidateQueries({ queryKey: key })
      }
      invalidatePublishDateRelated(queryClient)
    },
  })

  return (
    <ColumnCell className="py-5">
      {isReadOnly ? (
        <Badge variant="grey" size="md">
          {d ? formatDate(d, 'dd. MMM') : '—'}
        </Badge>
      ) : (
        <div onClick={(e) => e.stopPropagation()}>
          <CalendarPopover
            value={estate.marketingStart?.date}
            onChange={(data) =>
              mutateAsync({
                estateId: estate.estateId,
                publishDate: data ? new Date(data).toISOString() : null,
              })
            }
            size="sm"
          />
        </div>
      )}
    </ColumnCell>
  )
}
