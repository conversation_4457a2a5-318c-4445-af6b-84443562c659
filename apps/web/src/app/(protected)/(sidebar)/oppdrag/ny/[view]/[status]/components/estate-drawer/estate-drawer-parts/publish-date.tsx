import { uppercaseFirst } from '@befaring/lib/text-formatting'
import { useQueryClient } from '@tanstack/react-query'

import { Badge } from '@nordvik/ui/badge'
import { Button } from '@nordvik/ui/button'

import {
  GQLEstateAdSource,
  GQLEstateDrawerDetailsQuery,
  useEstateDrawerDetailsQuery,
  useUpsertEstatePublishDateMutation,
} from '@/api/generated-client'
import { formatDate } from '@/lib/dates'

import { CalendarPopover } from '../../calendar-popover'
import {
  invalidatePublishDateRelated,
  optimisticPublishDateMutation,
  rollbackPublishDateMutation,
} from '../../update-publish-date-cache'

import EventRow from './event-row'

type Props = {
  estate: NonNullable<GQLEstateDrawerDetailsQuery['estate']>
}

const PublishDate = ({ estate }: Props) => {
  const queryClient = useQueryClient()
  const upsertPublishDate = useUpsertEstatePublishDateMutation({
    onMutate: async (vars) => {
      if (!vars?.estateId) return
      return optimisticPublishDateMutation(
        queryClient,
        vars.estateId,
        vars.publishDate ?? null,
      )
    },
    onError: (_e, _v, ctx) => rollbackPublishDateMutation(queryClient, ctx),
    onSuccess: () => {
      if (estate.estateId) {
        const key = useEstateDrawerDetailsQuery.getKey({ id: estate.estateId })
        void queryClient.invalidateQueries({ queryKey: key })
      }
      invalidatePublishDateRelated(queryClient)
    },
  })
  const source = estate.marketingStart?.source ?? 'unset'
  const isEditable = !source.startsWith('next:')
  return (
    <div className="bg-background-root rounded-sm p-4 flex flex-col gap-2">
      <div className="flex justify-between">
        <h3 className="typo-body-sm-bold">Publisering</h3>
        <SourceBadge source={source} />
      </div>
      {isEditable || !estate.marketingStart?.date ? (
        <CalendarPopover
          onChange={(date) =>
            estate &&
            upsertPublishDate.mutate({
              estateId: estate.estateId,
              publishDate: date ? date.toISOString() : null,
            })
          }
          value={estate.marketingStart?.date}
        />
      ) : (
        <EventRow
          label={uppercaseFirst(
            formatDate(estate.marketingStart.date, 'eeee dd. MMMM'),
          )}
          confirmed={true}
        />
      )}
      {!!estate.ads.filter((ad) => ad.link && ad.source).length && (
        <div className="flex gap-2">
          {estate.ads.map((ad) => (
            <Button
              key={ad.link}
              href={ad.link}
              variant={'outline'}
              size={'xs'}
              target="_blank"
            >
              {getSourceName(ad.source)}
            </Button>
          ))}
        </div>
      )}
    </div>
  )
}

function getSourceName(source: GQLEstateAdSource | undefined): string {
  switch (source) {
    case GQLEstateAdSource.Finn:
      return 'finn.no'
    case GQLEstateAdSource.Hjem:
      return 'hjem.no'
    case GQLEstateAdSource.Nordvikbolig:
      return 'nordvikbolig.no'
    default:
      return 'Ukjent'
  }
}

const SourceBadge = ({ source }: { source: string }) => {
  switch (source) {
    case 'next:publishStart':
      return <Badge variant="outline">Annonsering bestilt</Badge>
    default:
      return <Badge variant="grey">Tentativ dato</Badge>
  }
}

export default PublishDate
