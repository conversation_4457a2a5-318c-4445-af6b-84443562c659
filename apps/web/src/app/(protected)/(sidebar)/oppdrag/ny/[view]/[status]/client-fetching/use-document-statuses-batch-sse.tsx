'use client'

import { fetchEventSource } from '@microsoft/fetch-event-source'
import React from 'react'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'

export type StatusItem = {
  type: string
  state: string
  updatedAt: string
  message?: string
}

type StatusMapValueType = {
  providers: Record<string, StatusItem>
  estate: GQLEstatesOverviewItemFragment
}

type EstateStatusMap = Record<string, StatusMapValueType>

function dedupeAndSort(estates: GQLEstatesOverviewItemFragment[]) {
  const uniqueIds = [
    ...new Set(estates.map((e) => e.estateId).filter(Boolean)),
  ].sort()
  return uniqueIds
    .map((id) => estates.find((e) => e.estateId === id))
    .filter(Boolean)
}

function useBatchSSE(
  estates: GQLEstatesOverviewItemFragment[],
  status: GQLEstateTabFilter,
) {
  const [map, setMap] = React.useState<EstateStatusMap>({})

  const key = React.useMemo(
    () =>
      JSON.stringify({
        estates: dedupeAndSort(estates).map((e) => e?.estateId),
        status,
      }),
    [estates, status],
  )

  React.useEffect(() => {
    const uniqueEstates = dedupeAndSort(estates)
    if (uniqueEstates.length === 0) return

    const controller = new AbortController()

    // Needs custom fetchEventSource to use POST request, which we need to use the body
    fetchEventSource(
      `/api/assignment-document-statuses/${encodeURIComponent(String(status))}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ estates: uniqueEstates }),
        signal: controller.signal,
        async onmessage(ev) {
          if (!ev.data) return
          try {
            const payload = JSON.parse(ev.data) as StatusItem & {
              estateId: string
            }
            const { estateId, ...item } = payload
            if (!estateId || !item?.type) return
            setMap((prev) => {
              const entry = prev[estateId] ?? {}
              return {
                ...prev,
                [estateId]: {
                  estate: uniqueEstates.find((e) => e?.estateId === estateId)!,
                  providers: {
                    ...entry.providers,
                    [item.type]: item,
                  },
                },
              }
            })
          } catch {
            // ignore parse errors
          }
        },
        onerror() {
          // Stop (do not retry) on any error
          controller.abort()
        },
      },
    )

    return () => {
      controller.abort()
    }
  }, [key, estates, status])

  return map
}

const StatusesCtx = React.createContext<EstateStatusMap | null>(null)

export function DocumentStatusesProvider({
  estates,
  status,
  children,
}: {
  estates: GQLEstatesOverviewItemFragment[]
  status: GQLEstateTabFilter
  children: React.ReactNode
}) {
  const value = useBatchSSE(estates, status)
  return <StatusesCtx.Provider value={value}>{children}</StatusesCtx.Provider>
}

export function useEstateDocumentStatuses(estateId: string) {
  const ctx = React.useContext(StatusesCtx)
  return (ctx?.[estateId] ?? {}) as StatusMapValueType
}
