import { useMemo, useState } from 'react'

import { Button } from '@nordvik/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
} from '@nordvik/ui/dialog'
import { Label } from '@nordvik/ui/label'
import { Switch } from '@nordvik/ui/switch'

import { GQLBrokerEstate, useEstateSellersQuery } from '@/api/generated-client'
import { EmailEditorWrapper } from '@/components/message-editors/message-editors'
import SellerFormsReminderEmail from '@/lib/email-template/dynamic/seller-forms-reminder'

import { useCurrentEstatesTab } from './use-current-estates-tab'
import { useEstateSellerTasks } from './use-estates-document-statuses'

export type FollowUpSellerModalProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
  estate: Pick<GQLBrokerEstate, 'estateId' | 'address' | 'mainBroker'>
}

export function FollowUpSellerModal({
  open,
  onOpenChange,
  estate,
}: FollowUpSellerModalProps) {
  const tab = useCurrentEstatesTab()
  const tasks = useEstateSellerTasks(estate.estateId, tab)
  const { data } = useEstateSellersQuery({
    id: estate.estateId,
  })
  const [sendCopy, setSendCopy] = useState(true)

  const mainContact = useMemo(() => {
    const sellers = data?.estate?.sellers || []
    const mainSeller = data?.estate?.mainSeller

    if (mainSeller?.firstName) return mainSeller
    if (!!sellers.length && sellers[0].firstName) return sellers[0]
    return { contactId: 'unknown', firstName: '' }
  }, [data])

  const initialHtml = useMemo(
    () => SellerFormsReminderEmail.getTemplateText(mainContact.firstName ?? ''),
    [mainContact],
  )
  const [, setHtml] = useState(initialHtml)

  const [subject, setSubject] = useState(
    'Påminnelse om utfylling av informasjon',
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        title={estate.address?.streetAddress || 'Oppfølging selger'}
        onClick={(e) => e.stopPropagation()}
        onKeyDown={(e) => e.stopPropagation()}
        onKeyUp={(e) => e.stopPropagation()}
        className="max-w-2xl"
      >
        <div className="flex flex-col gap-4 px-4">
          <EmailEditorWrapper
            subject={subject}
            changeSubject={setSubject}
            recipient={mainContact.email}
          >
            {
              <SellerFormsReminderEmail.EditablePreview
                onChange={setHtml}
                html={initialHtml}
                tasks={tasks}
                broker={data?.estate?.mainBroker}
              />
            }
          </EmailEditorWrapper>

          <div className="flex items-center space-x-2">
            <Switch
              size="xs"
              id="send-copy-toggle"
              checked={sendCopy}
              onCheckedChange={() => setSendCopy((prev) => !prev)}
            />
            <Label htmlFor="send-copy-toggle">
              Send kopi til alle meglere på oppdraget
            </Label>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button size="md" variant="outline">
                Avbryt
              </Button>
            </DialogClose>
            <Button size="md">Send epost</Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  )
}
