import { ReactNode } from 'react'

import TaskStatusBadge, {
  TaskBadgeState,
} from '@/components/task-status-badge/task-status-badge'

const TaskList = ({
  tasks,
  title,
  description,
  action,
}: {
  tasks: { title: string; status: TaskBadgeState }[]
  title: string
  description: string
  action?: ReactNode
}) => {
  return (
    <div className="bg-background-root rounded-sm p-4 flex flex-col gap-2">
      <div>
        <h3 className="typo-body-sm-bold">{title}</h3>
        <p className="typo-body-sm text-ink-muted">{description}</p>

        {tasks.map((task) => (
          <div
            key={task.title}
            className="flex gap-2 justify-between py-2 border-b last:border-0 border-stroke-muted items-center"
          >
            <div className="flex gap-2 justify-between items-center">
              <TaskStatusBadge status={task.status as TaskBadgeState} />
              <p className="typo-body-sm">{task.title}</p>
            </div>
            <p className="typo-body-sm text-ink-muted">
              {getStatus(task.status)}
            </p>
          </div>
        ))}
      </div>
      {action && action}
    </div>
  )
}

const getStatus = (status: string): string => {
  switch (status) {
    case 'NONE':
      return 'Ikke mottatt'
    case 'STARTED':
      return 'Sendt'
    case 'COMPLETE':
      return 'Utført'
    default:
      return 'Ukjent'
  }
}

export default TaskList
