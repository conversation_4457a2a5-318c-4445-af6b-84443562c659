'use client'

import { ClipboardList, Send } from 'lucide-react'
import React from 'react'

import { Button } from '@nordvik/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@nordvik/ui/popover'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'
import { AssignmentNotes } from '@/components/assignment-notes'

import { FollowUpSellerModal } from '../follow-up-seller-modal'

export function RowHoverActions({
  estate,
  currentTab,
}: {
  estate: GQLEstatesOverviewItemFragment
  currentTab?: GQLEstateTabFilter
}) {
  const [openFollowUp, setOpenFollowUp] = React.useState(false)

  const onFollowUpSeller = React.useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    setOpenFollowUp(true)
  }, [])

  if (currentTab !== GQLEstateTabFilter.InPreparation) {
    return null
  }

  return (
    <div className="pointer-events-none absolute bottom-2 right-0 z-10 hidden items-end gap-2 pr-2 group-hover/estateRow:flex group-focus-within/estateRow:flex">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            size="sm"
            variant="outline"
            className="pointer-events-auto shadow-sm bg-root hover:bg-root"
            iconOnly={<ClipboardList />}
            aria-label="Skriv notat"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          />
        </PopoverTrigger>
        <PopoverContent
          className="pointer-events-auto"
          onClick={(e: React.MouseEvent) => e.stopPropagation()}
          onKeyDown={(e) => e.stopPropagation()}
          onKeyUp={(e) => e.stopPropagation()}
        >
          <div className="w-80 z-20">
            <AssignmentNotes estateId={estate.estateId} />
          </div>
        </PopoverContent>
      </Popover>
      <Button
        size="sm"
        variant="outline"
        className="pointer-events-auto shadow-md bg-root hover:bg-root"
        onClick={onFollowUpSeller}
        iconStart={<Send />}
      >
        Følg opp selger
      </Button>
      {openFollowUp && (
        <FollowUpSellerModal
          open={openFollowUp}
          onOpenChange={setOpenFollowUp}
          estate={estate}
        />
      )}
    </div>
  )
}
