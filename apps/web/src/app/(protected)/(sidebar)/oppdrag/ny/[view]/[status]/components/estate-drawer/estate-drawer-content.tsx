import React from 'react'

import { Badge } from '@nordvik/ui/badge'

import { GQLEstateDrawerDetailsQuery } from '@/api/generated-client'
import {
  getStatusNameByNumber,
  isActiveOnFinn,
} from '@/app/(protected)/(sidebar)/oppdrag/util'
import { AssignmentNotes } from '@/components/assignment-notes'
import type { TaskBadgeState } from '@/components/task-status-badge/task-status-badge'

import { useSidePanel } from '../../client-fetching/use-side-panel'
import { CHECKLIST_ITEMS } from '../checklist'
import { useCurrentEstatesTab } from '../use-current-estates-tab'
import { useEstateSellerTasks } from '../use-estates-document-statuses'

import { getContentForEstateDrawer } from './drawer-content'
import Brokers from './estate-drawer-parts/brokers'
import EstateDetails from './estate-drawer-parts/estate-details'
import EventList from './estate-drawer-parts/event-list'
import InspectionDate from './estate-drawer-parts/inspection-date'
import PublishDate from './estate-drawer-parts/publish-date'
import Sellers from './estate-drawer-parts/sellers'
import SellersTasks from './estate-drawer-parts/sellers-tasks'
import Showings from './estate-drawer-parts/showings'
import TaskList from './estate-drawer-parts/task-list'

type Props = {
  estateData: GQLEstateDrawerDetailsQuery
}

const EstateDrawerContent = ({ estateData }: Props) => {
  const { estateId } = useSidePanel()

  const phaseLabel = useCurrentEstatesTab()

  const sellersTasks = useEstateSellerTasks(estateId!, phaseLabel)

  const drawerContent = getContentForEstateDrawer(phaseLabel)

  const checklistItems = React.useMemo(
    () => (phaseLabel ? CHECKLIST_ITEMS[phaseLabel] : []),
    [phaseLabel],
  )

  const brokersTasks: { title: string; status: TaskBadgeState }[] =
    checklistItems.map((a) => ({
      title: a.name,
      status: a.check(estateData.estate?.checklist ?? []) ? 'COMPLETE' : 'NONE',
    }))

  if (!estateId || !estateData.estate) return null

  const { estate, events } = estateData
  return (
    <>
      <div className="flex flex-col gap-4 bg-background-root px-6 py-4 border-b border-stroke-muted">
        <EstateDetails estate={estate} />
        <hr className="border-stroke-muted" />
        <div className="grid grid-cols-3 gap-2">
          <p className="typo-body-sm text-ink-muted">Fase</p>{' '}
          <Badge variant="beige" className="col-span-2 w-fit">
            {getStatusNameByNumber(estate.status, isActiveOnFinn(estate.finn))}
          </Badge>
          <Brokers brokers={estate.brokers} />
          <Sellers sellers={estate.sellers} />
          <div className="col-span-2"></div>
          <div className="col-span-3">
            <AssignmentNotes estateId={estateId} />
          </div>
        </div>
      </div>
      <div className="p-4 flex flex-col gap-4">
        {drawerContent.map((content) => {
          switch (content) {
            case 'brokerTasks':
              return (
                <TaskList
                  title="Oppgaver"
                  description="Status på viktige oppgaver i Next"
                  tasks={brokersTasks}
                />
              )
            case 'inspectionDate':
              return <InspectionDate estate={estate} />
            case 'events':
              return <EventList estateId={estateId} events={events} />
            case 'publish':
              return <PublishDate estate={estate} />
            case 'sellerTasks':
              return <SellersTasks tasks={sellersTasks} estate={estate} />
            case 'viewing':
              return <Showings showings={estate.showings} />
          }
        })}
      </div>
    </>
  )
}

export default EstateDrawerContent
