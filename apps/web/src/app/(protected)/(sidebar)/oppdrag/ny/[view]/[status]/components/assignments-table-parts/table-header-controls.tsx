import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { TableHeader, TableRow } from '@nordvik/ui/table'
import { Tooltip, TooltipContent, TooltipTrigger } from '@nordvik/ui/tooltip'

import type { GQLEstateTabFilter } from '@/api/generated-client'

import { SortKey } from '../assignments-table'

import {
  getColumnsForTab,
  headerClassFor,
  headerLabelFor,
  headerTooltipFor,
} from './columns'

export function TableHeaderControls({
  ariaSortFor,
  onSortAddress,
  onSortPublish,
  currentTab,
}: {
  ariaSortFor: (key: SortKey) => 'ascending' | 'descending' | 'none'
  onSortAddress: () => void
  onSortPublish: () => void
  currentTab?: GQLEstateTabFilter
}) {
  const cols = getColumnsForTab(currentTab)

  const isSortableCol = (colName: string): colName is SortKey =>
    colName === 'address' || colName === 'publish'

  const getSortProps = (
    colName: string,
  ): React.ThHTMLAttributes<HTMLTableCellElement> => {
    if (!isSortableCol(colName)) return {}
    const onClick = colName === 'address' ? onSortAddress : onSortPublish
    return {
      onClick,
      'aria-sort': ariaSortFor(colName),
      role: 'columnheader',
    }
  }

  return (
    <TableHeader>
      <TableRow>
        {cols.map((colName) => {
          const label = headerLabelFor(colName, currentTab)
          const baseClass = headerClassFor(colName)
          const sortable = isSortableCol(colName)
          const className = `${baseClass}${sortable ? ' cursor-pointer select-none' : ''}`
          const tooltip = headerTooltipFor(colName)
          return (
            <th
              key={colName}
              className={cn(className, 'ink-subtle font-normal p-3')}
              {...getSortProps(colName)}
            >
              {tooltip ? (
                <Tooltip>
                  <TooltipTrigger>{label}</TooltipTrigger>
                  <TooltipContent>{tooltip}</TooltipContent>
                </Tooltip>
              ) : (
                label
              )}
            </th>
          )
        })}
      </TableRow>
    </TableHeader>
  )
}
