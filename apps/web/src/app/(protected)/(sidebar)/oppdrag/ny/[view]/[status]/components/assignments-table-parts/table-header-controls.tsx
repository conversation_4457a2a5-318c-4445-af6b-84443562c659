import { <PERSON>D<PERSON>, ArrowUp } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { TableHeader, TableRow } from '@nordvik/ui/table'
import { Tooltip, TooltipContent, TooltipTrigger } from '@nordvik/ui/tooltip'

import type { GQLEstateTabFilter } from '@/api/generated-client'

import { SortKey } from '../assignments-table'

import {
  getColumnsForTab,
  headerClassFor,
  headerLabelFor,
  headerTooltipFor,
} from './columns'
import { useSortingQueryState } from './hooks'
import { ViewType } from './util'

export function TableHeaderControls({
  currentTab,
  currentView,
}: {
  currentTab?: GQLEstateTabFilter
  currentView: ViewType
}) {
  const cols = getColumnsForTab(currentTab)

  const { ariaSortFor, sortKey, dirKey, onSort } = useSortingQueryState()

  const isSortableCol = (colName: string): colName is SortKey =>
    currentView === 'kontor'
      ? ['publish'].includes(colName)
      : ['publish', 'viewing'].includes(colName)

  const getSortProps = (
    colName: string,
  ): React.ThHTMLAttributes<HTMLTableCellElement> => {
    if (!isSortableCol(colName)) return {}
    return {
      onClick: () => onSort(colName),
      'aria-sort': ariaSortFor(colName),
      role: 'columnheader',
    }
  }

  return (
    <TableHeader className="[&_tr]:border-b-0">
      <TableRow className="border-b-0">
        {cols.map((colName) => {
          const label = headerLabelFor(colName, currentTab)
          const baseClass = headerClassFor(colName, currentTab)
          const sortable = isSortableCol(colName)
          const className = `${baseClass}${sortable ? ' cursor-pointer select-none' : ''}`
          const tooltip = headerTooltipFor(colName)
          return (
            <th
              key={colName}
              className={cn(className, 'ink-subtle font-normal p-3')}
              {...getSortProps(colName)}
            >
              <div className="flex gap-1 items-center">
                {tooltip ? (
                  <Tooltip>
                    <TooltipTrigger>{label}</TooltipTrigger>
                    <TooltipContent>{tooltip}</TooltipContent>
                  </Tooltip>
                ) : (
                  label
                )}
                {sortKey === colName &&
                  (dirKey === 'asc' ? (
                    <ArrowUp className="size-3" />
                  ) : (
                    <ArrowDown className="size-3" />
                  ))}
              </div>
            </th>
          )
        })}
      </TableRow>
    </TableHeader>
  )
}
