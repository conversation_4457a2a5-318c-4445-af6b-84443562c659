'use client'

import { Badge } from '@nordvik/ui/badge'

import { isActiveOnFinn } from '@/app/(protected)/(sidebar)/oppdrag/util'

import { ColumnCell, EstateColumnProps } from './base'

export type AdColumnProps = EstateColumnProps

export const AdColumn = ({ estate }: AdColumnProps) => {
  return (
    <ColumnCell>
      <div className="text-left">
        {isActiveOnFinn(estate.finn) ? (
          <Badge variant="bright-green">Til salgs</Badge>
        ) : (
          <Badge variant="grey">Ikke publisert</Badge>
        )}
      </div>
    </ColumnCell>
  )
}
