'use client'

import isNil from 'lodash/isNil'
import { useParams } from 'next/navigation'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Link } from '@nordvik/ui/global-navigation-progress/link'
import { Separator } from '@nordvik/ui/separator'
import { Tabs, TabsList, TabsTrigger } from '@nordvik/ui/tabs'

import { GQLEstateTabFilter } from '@/api/generated-client'
import { useDebouncedQueryState } from '@/hooks/use-debounced-query-state'

import { SearchInput } from '../../../../status/[status]/components/saerch-input'
import { SyncEstates } from '../../../../status/[status]/components/sync-estates'
import {
  EstateStatus,
  getStatusText,
  getStatusTextLowercase,
} from '../../../../util'

const tabs = [
  {
    tab: GQLEstateTabFilter.Valuation,
    value: getStatusTextLowercase(EstateStatus.Valuation),
    label: getStatusText(EstateStatus.Valuation),
    enabledForOffice: false,
  },
  {
    tab: GQLEstateTabFilter.Requested,
    value: getStatusTextLowercase(EstateStatus.Requested),
    label: getStatusText(EstateStatus.Requested),
    enabledForOffice: false,
  },
  {
    tab: GQLEstateTabFilter.InPreparation,
    value: getStatusTextLowercase(EstateStatus.InPreperation),
    label: getStatusText(EstateStatus.InPreperation),
    enabledForOffice: true,
  },
  {
    tab: GQLEstateTabFilter.ForSale,
    value: getStatusTextLowercase(EstateStatus.ForSale),
    label: getStatusText(EstateStatus.ForSale),
    enabledForOffice: true,
  },
  {
    tab: GQLEstateTabFilter.Sold,
    value: getStatusTextLowercase(EstateStatus.Sold),
    label: getStatusText(EstateStatus.Sold),
    enabledForOffice: true,
  },
  {
    tab: GQLEstateTabFilter.Archived,
    value: getStatusTextLowercase(EstateStatus.Archive),
    label: getStatusText(EstateStatus.Archive),
    enabledForOffice: true,
  },
]

export default function StatusTabs({
  tabCounts,
  isLoading,
  view,
}: {
  tabCounts: { tab: GQLEstateTabFilter; count: number }[]
  isLoading?: boolean
  view: string
}) {
  const { value: searchQuery, setValue } = useDebouncedQueryState({
    key: 'search',
    wait: 500,
  })
  const params = useParams<{ status: string }>()

  const status = decodeURIComponent(params.status)

  const enabledTabs = tabs.filter((t) =>
    view === 'kontor' ? t.enabledForOffice : true,
  )

  return (
    <>
      <div className="-mx-4 box-content flex w-full max-w-full overflow-x-auto px-4 hide-scrollbar items-baseline">
        <Tabs
          defaultValue={status}
          className={cn(
            'flex-1 flex sm:justify-end sm:flex-row-reverse max-md:gap-2',
          )}
        >
          <SearchInput searchQuery={searchQuery} setSearchQuery={setValue}>
            <div className="flex items-center gap-1.5">
              <TabsList border={false} className={cn('sm:mr-5')}>
                {enabledTabs.map((entry) => (
                  <TabItemWithCount
                    key={entry.label}
                    tabCounts={tabCounts}
                    tab={entry}
                    isLoading={isLoading}
                    view={view}
                  />
                ))}
              </TabsList>
            </div>
          </SearchInput>
        </Tabs>
        {!searchQuery && <SyncEstates />}
      </div>
      <Separator />
    </>
  )
}

const statusMap = {
  [getStatusText(EstateStatus.Requested)]: GQLEstateTabFilter.Requested,
  [getStatusText(EstateStatus.InPreperation)]: GQLEstateTabFilter.InPreparation,
  [getStatusText(EstateStatus.ForSale)]: GQLEstateTabFilter.ForSale,
  [getStatusText(EstateStatus.Valuation)]: GQLEstateTabFilter.Valuation,
  [getStatusText(EstateStatus.Sold)]: GQLEstateTabFilter.Sold,
}

function TabItemWithCount({
  tabCounts,
  tab,
  isLoading,
  view,
}: {
  tabCounts: { tab: GQLEstateTabFilter; count: number }[]
  tab: { tab: GQLEstateTabFilter; value: string; label: string }
  isLoading?: boolean
  view: string
}) {
  const statusCount = tabCounts.find((s) => s.tab === tab.tab)

  return (
    <TabsTrigger key={tab.label} value={tab.value} asChild>
      <Link prefetch href={`/oppdrag/ny/${view}/${tab.value}`}>
        {tab.label}{' '}
        <Count
          count={statusCount?.count}
          isLoading={!isNil(statusMap[tab.label]) ? isLoading : false}
        />
      </Link>
    </TabsTrigger>
  )
}

function Count({
  count,
  isLoading,
}: {
  count?: string | number
  isLoading?: boolean
}) {
  if (!count && !isLoading) {
    return null
  }
  return (
    <span
      aria-busy={isLoading}
      className={cn(
        'typo-label-sm ml-1 mt-[0.15em] shrink-0 rounded-sm bg-root-muted px-1.5 py-0.5 ink-default',
        isLoading && 'masked-placeholder-text',
      )}
    >
      {count || '10'}
    </span>
  )
}
