import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Table, TableBody, TableHeader, TableRow } from '@nordvik/ui/table'
import { Tooltip, TooltipContent, TooltipTrigger } from '@nordvik/ui/tooltip'

import { getStatusText } from '@/app/(protected)/(sidebar)/oppdrag/util'

import type { GroupedSearchResult } from '../use-grouped-search-results'

import { headerClassFor, headerLabelFor, headerTooltipFor } from './columns'
import { MemoRow } from './row'

export function SearchGroupedBody({
  groups,
}: {
  groups: GroupedSearchResult[]
}) {
  if (!groups.length) return null

  // We render one separate table per status group (different columns per group) but
  // the status header itself adopts the same row styling as the week headers in klargjøring tab.
  return (
    <div className="flex flex-col gap-8">
      {groups.map((group) => {
        const { status, estates, columns } = group
        const colSpan = columns.length
        return (
          <Table
            key={status}
            className="text-sm"
            data-status-group={status}
            aria-label={`Oppdrag i status ${getStatusText(status)}`}
          >
            <TableHeader>
              <TableRow className="border-none">
                <td
                  colSpan={colSpan}
                  className="py-2 pl-4 rounded-sm font-medium bg-gray-muted"
                >
                  <span>{getStatusText(status)}</span>
                  <span className="ink-muted typo-body-xs ml-2">
                    ({estates.length})
                  </span>
                </td>
              </TableRow>
              <TableRow>
                {columns.map((colName) => {
                  const label = headerLabelFor(colName, status)
                  const baseClass = headerClassFor(colName, status)
                  const tooltip = headerTooltipFor(colName)
                  const className = cn(baseClass, 'ink-subtle font-normal p-3')
                  return (
                    <th key={colName} className={className} role="columnheader">
                      {tooltip ? (
                        <Tooltip>
                          <TooltipTrigger>{label}</TooltipTrigger>
                          <TooltipContent>{tooltip}</TooltipContent>
                        </Tooltip>
                      ) : (
                        label
                      )}
                    </th>
                  )
                })}
              </TableRow>
            </TableHeader>
            <TableBody alwaysLastBorder>
              {estates.map((estate) => (
                <MemoRow
                  key={estate.estateId}
                  estate={estate}
                  currentTab={status}
                />
              ))}
            </TableBody>
          </Table>
        )
      })}
    </div>
  )
}
