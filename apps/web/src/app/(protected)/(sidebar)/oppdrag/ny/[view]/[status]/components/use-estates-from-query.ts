import React from 'react'

import {
  GQLEstatesOverviewItemFragment,
  GQLNewAssignmentsOverviewQuery,
} from '@/api/generated-client'

export function useEstatesFromQuery(
  pages: GQLNewAssignmentsOverviewQuery[] | undefined,
  officeView: boolean,
) {
  const allEstates = React.useMemo(() => {
    const list: GQLEstatesOverviewItemFragment[] = []
    for (const p of pages ?? []) {
      const items = officeView
        ? (p.office?.items ?? [])
        : (p.broker?.items ?? [])

      list.push(...items)
    }
    return list
  }, [pages, officeView])

  const totalFromQuery = React.useMemo(() => {
    const first = pages?.[0]
    const pag = officeView
      ? first?.office?.pagination
      : first?.broker?.pagination

    return pag?.total
  }, [pages, officeView])

  return { allEstates, totalFromQuery }
}
