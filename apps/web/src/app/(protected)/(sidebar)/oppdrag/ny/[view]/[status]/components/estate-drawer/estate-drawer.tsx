import { Maximize2, X } from 'lucide-react'

import { Button } from '@nordvik/ui/button'
import { Sheet, SheetContent, SheetTitle } from '@nordvik/ui/sheet'

import { useEstateDrawerDetailsQuery } from '@/api/generated-client'

import { useEstateDocumentStatuses } from '../../client-fetching/use-document-statuses-batch-sse'
import { useSidePanel } from '../../client-fetching/use-side-panel'

import EstateDrawerContent from './estate-drawer-content'
import EstateDrawerSkeleton from './estate-drawer-skeleton'

const EstateDrawer = () => {
  const { isOpen, estateId, closePanel } = useSidePanel()
  const { data, isPending } = useEstateDrawerDetailsQuery(
    { id: estateId! },
    { enabled: isOpen && !!estateId },
  )

  const itemsMap = useEstateDocumentStatuses(estateId!)

  if (!isOpen) return null

  return (
    <div>
      <Sheet open={isOpen} onOpenChange={closePanel}>
        <SheetTitle className="sr-only">
          Detaljer for {data?.estate?.address?.streetAddress}
        </SheetTitle>
        <SheetContent
          className="w-full max-w-xl bg-root-muted p-0"
          closeButton={false}
        >
          <div className="flex justify-between items-center bg-background-root px-4 py-3 border-b border-stroke-muted h-[60px]">
            <div className="flex flex-row items-center gap-2">
              <Button
                iconOnly={<X />}
                size="md"
                variant="ghost"
                onClick={closePanel}
              />
              <h2 className="typo-body-md-bold">Detaljer</h2>
            </div>
            <Button
              href={`/oppdrag/detaljer/${estateId}`}
              size={'sm'}
              variant={'outline'}
              iconStart={<Maximize2 />}
            >
              Åpne side
            </Button>
          </div>
          {isPending ? (
            <EstateDrawerSkeleton />
          ) : (
            <>
              {data ? (
                <EstateDrawerContent
                  estateData={data}
                  documentStatuses={itemsMap.providers}
                />
              ) : (
                <div className="rounded-sm bg-root p-4 flex flex-col gap-4">
                  <p>Kunne ikke laste data</p>
                </div>
              )}
            </>
          )}
        </SheetContent>
      </Sheet>
    </div>
  )
}

export default EstateDrawer
