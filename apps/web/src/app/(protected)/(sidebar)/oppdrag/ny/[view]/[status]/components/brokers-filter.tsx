'use client'

import { Search } from 'lucide-react'
import React, { useEffect } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { Checkbox } from '@nordvik/ui/checkbox'
import { Input } from '@nordvik/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@nordvik/ui/popover'
import { ScrollArea } from '@nordvik/ui/scroll-area'

import { useDepartmentEmployeesQuery } from '@/api/generated-client'
import FilterButton from '@/components/filter-button'
import { useBrokerIdsQuery } from '@/hooks/useBrokerIdsQuery'

export function BrokersFilter({
  departmentId,
  disabled,
  fallbackBrokers = [],
}: {
  departmentId?: number
  disabled?: boolean
  // Fallback list (from loaded estates) used until query returns
  fallbackBrokers?: { employeeId?: string | null; name?: string | null }[]
}) {
  const [selected, setSelected] = useBrokerIdsQuery()
  const [open, setOpen] = React.useState(false)
  const [search, setSearch] = React.useState('')
  const { data } = useDepartmentEmployeesQuery(
    { departmentId: departmentId ?? 0 },
    {
      enabled: typeof departmentId === 'number' && departmentId > 0,
      select: (d) =>
        d.department?.employees?.filter(Boolean).map((e) => ({
          employeeId: e?.employeeId ?? '',
          name: e?.name ?? 'Ukjent',
        })) ?? [],
      staleTime: 5 * 60 * 1000,
    },
  )

  const uniqueBrokers = React.useMemo(() => {
    const source = data && data.length > 0 ? data : fallbackBrokers
    const map = new Map<string, string>()
    source.forEach((b) => {
      if (b.employeeId) {
        if (!map.has(b.employeeId)) map.set(b.employeeId, b.name || 'Ukjent')
      }
    })
    return Array.from(map.entries())
      .map(([employeeId, name]) => ({ employeeId, name }))
      .sort((a, b) => a.name.localeCompare(b.name))
  }, [data, fallbackBrokers])

  const filteredBrokers = React.useMemo(() => {
    const searchString = search.toLowerCase()
    return uniqueBrokers.filter((broker) =>
      broker.name.toLowerCase().includes(searchString),
    )
  }, [uniqueBrokers, search])

  useEffect(() => {
    if (!open) {
      setSearch('')
    }
  }, [open])

  const toggle = (id: string) => {
    if (selected.includes(id)) {
      setSelected(selected.filter((s) => s !== id))
    } else {
      setSelected([...selected, id])
    }
  }

  const clearAll = () => setSelected([])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <FilterButton
          selectedCount={selected.length}
          disabled={disabled || uniqueBrokers.length === 0}
        />
      </PopoverTrigger>
      <PopoverContent align="start" className="w-64 p-0">
        <label className="flex justify-center items-center p-1 border-b border-stroke-muted">
          <Search className="size-5 shrink-0 text-ink-muted ml-2" />
          <span className="sr-only">Søk etter megler</span>
          <Input
            variant="ghost"
            placeholder="Søk"
            onChange={(e) => setSearch(e.currentTarget.value)}
          />
        </label>
        <ScrollArea className="max-h-72 py-1">
          <ul>
            {filteredBrokers.map((b) => {
              const checked = selected.includes(b.employeeId)
              return (
                <li
                  key={b.employeeId}
                  onClick={() => toggle(b.employeeId)}
                  className={cn(
                    'flex w-full items-center gap-2 px-3 py-2 text-left hover:bg-root-muted focus:outline-none cursor-pointer',
                  )}
                >
                  <Checkbox
                    checked={checked}
                    onCheckedChange={() => toggle(b.employeeId)}
                    className="pointer-events-none"
                  />
                  <span className="typo-body-sm flex-1 truncate">{b.name}</span>
                </li>
              )
            })}
            {filteredBrokers.length === 0 && (
              <li className="px-3 py-2 typo-body-sm ink-subtle">Ingen treff</li>
            )}
          </ul>
        </ScrollArea>
        {!search && selected.length > 0 && (
          <div className="flex justify-center p-1 border-t border-stroke-muted">
            <Button onClick={clearAll} size="sm" variant="ghost">
              Nullstill
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  )
}
