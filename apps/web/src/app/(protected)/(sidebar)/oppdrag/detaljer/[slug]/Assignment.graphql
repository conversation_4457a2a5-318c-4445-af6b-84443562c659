query estateById($id: String!) {
  estate(id: $id) {
    status
    id
    estateId

    address {
      streetAddress
      city
      zipCode
      municipality
    }

    latitude
    longitude

    assignmentNumber
    assignmentType
    isValuation
    isEtakstPublished
    ownAssignmentType
    noOfBedRooms
    noOfRooms
    soldDate
    commissionAcceptedDate
    linkToNext

    finn {
      finnExpireDate
      finnPublishDate
    }

    estatePrice {
      totalPrice
      priceSuggestion
      soldPrice
    }

    sumArea {
      braI
      pRom
    }
    areaSize {
      BRAItotal
    }

    mainImage {
      large
    }

    placeholderImage

    links {
      linkType
      url
      text
    }

    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
    }

    showings {
      start
      end
      showingId
    }

    activities {
      start
      end
      type
      typeName
      name
      performedById
      done
      id
      value
    }

    upcomingEvents {
      start
      end
      type
      typeName
      name
      performedById
      done
      id
    }

    linkToNext
    hjemUrl

    inspectionFolder {
      id
      publishedAt
    }

    listingAgreement {
      id
      updatedAt
      createdAt
      signicatDocumentId
      feePercentage
      suggestedPrice
      deadline
      signedAt
      initiatedSigningAt
      sentToClientAt
      status
      signers {
        id
        externalSignerId
        url
        signedAt
        title
        email
        firstName
        lastName
      }
      deadlineHasBeenExceeded
      commission
    }
    brokers {
      employeeId
      name
      mobilePhone
      email
      slug
      role
      title
      employeeRoles {
        source
        typeId
        name
      }
      image {
        small
      }
    }

    brokersIdWithRoles {
      employeeId
      brokerRole
      employee {
        title
        name
        email
        mobilePhone
        image {
          small
        }
      }
    }

    isPublished
  }
}

query estateCampaignsById($id: String!) {
  estate(id: $id) {
    campaigns {
      packageName
      marketingPackage
      dateOrdered
      orderStartDate
      orderEndDate
      externalId
    }
  }
}

query estateExtrasById($id: String!, $estateProps: EstateProps) {
  forms: estateFormsByEstateId(estateId: $id, estateProps: $estateProps) {
    type
    name
    link
    status {
      signingFinished
      isNotificationSent
    }
    relevantForEstateWithProps {
      status
      projectRelation
    }
  }

  oa: listingAgreementByEstateId(estateId: $id) {
    id
    createdAt
    status
    signicatDocumentId
    signedAt
    sentToClientAt
    initiatedSigningAt
    deadline

    brokerSigners {
      id
      externalSignerId
      url
      signedAt
      title
      email
      phone
      firstName
      lastName
    }

    sellerSigners {
      id
      externalSignerId
      url
      signedAt
      title
      email
      phone
      firstName
      lastName
    }
    accessTokens {
      id
      createdAt
    }
  }
}

query estateFormsById($id: String!) {
  estate(id: $id) {
    forms {
      type
      name
      link
      status {
        signingFinished
        isNotificationSent
      }
      relevantForEstateWithProps {
        status
        projectRelation
      }
    }
  }
}

query estateInspectionById($id: String!) {
  estate(id: $id) {
    hasInspection
    inspection {
      success
      entries {
        id
        title
        postDate {
          date
          timezone_type
          timezone
        }
        url
      }
    }
  }
}

query syncEstateWithVitec($estateId: String!) {
  syncEstateWithVitec(estateId: $estateId)
}

mutation estateResetForm($estateId: String!, $formType: String!) {
  resetForm(estateId: $estateId, formType: $formType)
}

query agreementAndInspection($estateId: String!) {
  estate(id: $estateId) {
    matrikkel {
      gnr
      bnr
      snr
      ownPart
    }
    ownershipType
    estateId
    hasCompanySeller
    inspectionDate
    estateTypeExternal
    isValuation
    ownership
    isValuation
    landIdentificationMatrix {
      gnr
      bnr
      knr
      snr
      ownPart
    }
    partOwnership {
      partName
      partNumber
      partOrgNumber
      estateHousingCooperativeStockHousingUnitNumber
      estateHousingCooperativeStockNumber
    }
    estateType
    estateTypeId

    estatePrice {
      priceSuggestion
      collectiveDebt
    }

    estatePriceModel {
      collectiveDebt
      estimatedValue
      priceSuggestion
      totalPrice
    }

    department {
      departmentId
      departmentNumber
      name
      legalName
      organisationNumber
      phone
      streetAddress
      postalCode
      city
      email
      employees {
        employeeId
        name
        email
        mobilePhone
        title
        slug
      }
    }
    address {
      streetAddress
      city
      zipCode
      municipality
    }
    activities {
      start
      end
      type
      typeName
      name
      performedById
      done
      id
      value
    }
    status
    linkToNext
    inspectionFolder {
      id
      sentAt
      publishedAt
      listingAgreementSentAt
      listingAgreementActive
      audit {
        id
        sentAt
        listingAgreementActive
      }
    }
    listingAgreement {
      id
      updatedAt
      createdAt
      signicatDocumentId
      feePercentage
      suggestedPrice
      deadline
      signedAt
      initiatedSigningAt
      sentToClientAt
      status
      canStartSigning
      offerSellerLink
      signers {
        id
        externalSignerId
        url
        signedAt
        title
        email
        firstName
        lastName
      }
      deadlineHasBeenExceeded
      commission
      interactions {
        id
        eventType
        eventTimestamp
      }
      hasStorebrandLead
    }

    mainSeller {
      contactId
      firstName
      lastName
    }

    sellers {
      firstName
      lastName
      email
      mobilePhone
      mainContact
      contactId
      contactType
      proxyId
    }
    companyContacts {
      contactId
      departmentId
      contactType
      companyName
      organisationNumber
      firstName
      lastName
      mobilePhone
      privatePhone
      workPhone
      email
      address
      postalAddress
      postalCode
      city
      deletedAt
      relationName
      roleName
      relationType
    }

    extraContacts(source: Next) {
      contactId
      departmentId
      contactType
      companyName
      organisationNumber
      firstName
      lastName
      mobilePhone
      privatePhone
      workPhone
      email
      address
      postalAddress
      postalCode
      city
      deletedAt
      relationName
      relationType
    }

    broker {
      id
      employeeId
      name
      email
    }
    brokers {
      employeeId
      name
      mobilePhone
      email
      slug
      role
      title
      employeeRoles {
        source
        typeId
        name
      }
      image {
        small
      }
    }

    brokersIdWithRoles {
      employeeId
      brokerRole
      employee {
        title
        name
        email
        mobilePhone
        image {
          small
        }
      }
    }
  }
}
