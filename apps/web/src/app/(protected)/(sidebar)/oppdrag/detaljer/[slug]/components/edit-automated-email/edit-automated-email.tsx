'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@befaring/lib/broker-constants'
import { getBrokerByRole } from '@befaring/lib/format-brokers'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useEffect, useMemo, useRef, useState } from 'react'

import { Alert, AlertDescription } from '@nordvik/ui/alert'
import { Button } from '@nordvik/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
} from '@nordvik/ui/dialog'
import { TextButton } from '@nordvik/ui/text-button'
import { toast } from '@nordvik/ui/toaster'

import { employeeMailSignature } from '@/actions/mail/employee-signature'
import { GQLAgreementAndInspectionQuery } from '@/api/generated-client'
import { EmailEditor } from '@/components/message-editors/message-editors'
import { useQuill } from '@/components/rich-text-editor/use-quill.hook'
import { TemplateReplacements } from '@/components/send-offer-dialog/types'
import { useSimpleTemplates } from '@/components/send-offer-dialog/use-simple-templates.hook'
import {
  createStandardReplacements,
  getEmailContent,
} from '@/components/send-offer-dialog/utils'
import {
  TemplateContent,
  TemplateIds,
} from '@/components/template-renderer/types'
import { useUserContext } from '@/lib/UserContext'
import { useTrackEvent } from '@/lib/analytics/track-event'
import { formatDate } from '@/lib/dates'

import { LoadingEditor } from './loading-editor'
import {
  getMessageTemplateOverride,
  resetMessageTemplateOverride,
  saveMessageTemplateOverride,
} from './message-template-override-handlers'
import {
  GetMessageTemplateOverride,
  MessageTemplateOverridePayload,
} from './types'

interface EditAutomatedEmailProps {
  templateId?: TemplateIds
  contacts?: { firstName: string }[]
  additionalReplacements?: Partial<Record<TemplateReplacements[number], string>>
  linkToOffer?: {
    url: string
    text: string
  }
  estate?: NonNullable<GQLAgreementAndInspectionQuery['estate']>
}

const saveAutomatedEmail = async (payload: MessageTemplateOverridePayload) => {
  await saveMessageTemplateOverride(payload)
}

const getAutomatedEmail = async (payload: GetMessageTemplateOverride) => {
  const template = await getMessageTemplateOverride(payload)

  const content =
    typeof template?.emailContent === 'string'
      ? JSON.parse(template.emailContent)
      : template?.emailContent

  return { ...template, emailContent: content as TemplateContent[] }
}

export function EditAutomatedEmail({
  templateId = TemplateIds.EMPTY_TEMPLATE,
  contacts = [],
  additionalReplacements = {},
  linkToOffer,
  estate,
}: EditAutomatedEmailProps) {
  const { Quill } = useQuill()
  const { user } = useUserContext()
  const { slug } = useParams<{ slug: string }>()
  const [isOpen, setIsOpen] = useState(false)
  const [isResetting, setIsResetting] = useState(false)
  const [draftContent, setDraftContent] = useState<TemplateContent[]>([])
  const [forceRegenerate, setForceRegenerate] = useState(0)
  const prevAutomatedEmailRef = useRef<boolean>(false)
  const {
    data: automatedEmail,
    isLoading,
    isRefetching,
    refetch,
  } = useQuery({
    queryKey: ['automated-e-takst-email', templateId, slug],
    queryFn: () =>
      getAutomatedEmail({
        templateId,
        employeeId: user?.employeeId,
        estateId: slug,
      }),
    enabled: isOpen,
  })

  const { mutate: saveAutomatedEmailMutation, isPending } = useMutation({
    mutationFn: saveAutomatedEmail,
    onError: () => {
      toast({
        title: 'Klarte ikke å lagre utsendelse',
        variant: 'destructive',
      })
    },
    onSuccess: () => {
      toast({
        title: 'Tekst til automatisk e-postutsendelse er endret',
        variant: 'success',
      })
      setIsOpen(false)
      refetch()
    },
  })

  const trackEvent = useTrackEvent()

  const mainBroker = getBrokerByRole(BrokerRole.Main, {
    brokers: estate?.brokers,
    brokersIdWithRoles: estate?.brokersIdWithRoles,
  })

  const signature = employeeMailSignature({
    name: mainBroker?.employee.name,
    title: mainBroker?.employee.title,
    email: mainBroker?.employee.email,
    mobilePhone: mainBroker?.employee.mobilePhone,
    image: mainBroker?.employee.image?.small,
    slug: mainBroker?.employeeId,
  })

  const allReplacements = useMemo(() => {
    return {
      ...createStandardReplacements({ contacts }),
      ...additionalReplacements,
    }
  }, [contacts, additionalReplacements])

  const { draft, handleOnChangeTemplateContent } = useSimpleTemplates(
    templateId,
    allReplacements,
    forceRegenerate,
  )
  const lastChanged = automatedEmail?.lastChanged

  useEffect(() => {
    const hadAutomatedEmail = prevAutomatedEmailRef.current
    const hasAutomatedEmail = !!automatedEmail?.emailContent?.length

    if (hadAutomatedEmail && !hasAutomatedEmail && !isRefetching) {
      setForceRegenerate((prev) => prev + 1)
    }

    prevAutomatedEmailRef.current = hasAutomatedEmail
  }, [automatedEmail?.emailContent, isRefetching])

  useEffect(() => {
    if (automatedEmail?.emailContent?.length) {
      setDraftContent(automatedEmail.emailContent)
    } else if (draft?.emailContent?.length) {
      setDraftContent(draft.emailContent)
    }
  }, [
    automatedEmail?.emailContent,
    draft?.emailContent,
    automatedEmail?.lastChanged,
  ])

  const draftSubject = useMemo(() => {
    if (automatedEmail?.subject) {
      return automatedEmail.subject
    }
    return draft?.subject
  }, [automatedEmail, draft])

  if (!draft) {
    return (
      <Button variant="outline" size="sm" disabled className="text-[12px] h-6">
        Tilpass utsendelse
      </Button>
    )
  }

  const handleSave = () => {
    if (!user?.employeeId) {
      return
    }
    const emailHtml = draft.emailContent
      .map((content) => getEmailContent(content, Quill))
      .map((content) => content.html)
      .join('')
    const payload: MessageTemplateOverridePayload = {
      userId: user.id,
      templateId: draft.id,
      subject: draft.subject,
      emailContent: JSON.stringify(draft.emailContent),
      emailHtml,
      employeeId: user.employeeId,
      estateId: slug,
    }
    saveAutomatedEmailMutation(payload)
    trackEvent('automated-email-saved', {
      templateId: draft.id,
      estateId: slug,
      employeeId: user.employeeId,
    })
  }

  const handleReset = async () => {
    if (!user?.employeeId) {
      return
    }
    const payload: Pick<
      MessageTemplateOverridePayload,
      'employeeId' | 'estateId' | 'templateId'
    > = {
      employeeId: user.employeeId,
      estateId: slug,
      templateId: draft.id,
    }
    try {
      setIsResetting(true)
      await resetMessageTemplateOverride(payload)
    } catch (error) {
      console.error(error)
      toast({
        title: 'Klarte ikke å tilbakestille utsendelse',
        variant: 'destructive',
      })
    } finally {
      toast({
        title: 'Utsendelse tilbakestilt',
        variant: 'success',
      })
      await refetch()
      setForceRegenerate((prev) => prev + 1)
      setIsResetting(false)
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        disabled={isPending}
        className="text-[12px] h-6"
      >
        Tilpass utsendelse
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent title="Tilpass innholdet i e-posten">
          <DialogDescription>
            {lastChanged ? (
              <Alert>
                <div className="flex justify-between items-center">
                  <AlertDescription>
                    Sist endret {formatDate(lastChanged, 'd.MM HH:mm')}
                  </AlertDescription>
                  <TextButton onClick={handleReset} disabled={isResetting}>
                    {isResetting && <Loader2 className="size-4 animate-spin" />}{' '}
                    Tilbakestill
                  </TextButton>
                </div>
              </Alert>
            ) : (
              <Alert>
                <AlertDescription>
                  Her kan du tilpasse innholdet i e-posten som blir automatisk
                  utsendt når e-taksten er klar
                </AlertDescription>
              </Alert>
            )}
            <div>
              {isLoading ? (
                <LoadingEditor />
              ) : (
                <EmailEditor
                  key={lastChanged?.toString() ?? draft.id}
                  draftContent={draftContent}
                  onChange={(value, id, type) =>
                    handleOnChangeTemplateContent(value, id, type)
                  }
                  linkToOffer={linkToOffer}
                  subject={draftSubject ?? ''}
                  options={{
                    richText: true,
                  }}
                  signature={signature}
                  className="pb-0"
                />
              )}
            </div>
          </DialogDescription>
          <DialogFooter>
            <Button
              variant="outline"
              size="md"
              onClick={() => setIsOpen(false)}
              disabled={isPending}
            >
              Avbryt
            </Button>
            <Button onClick={handleSave} loading={isPending} size="md">
              Lagre
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
