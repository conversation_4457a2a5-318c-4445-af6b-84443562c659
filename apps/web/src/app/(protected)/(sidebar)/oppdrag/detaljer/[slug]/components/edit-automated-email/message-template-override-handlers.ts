'use server'

import { sendSlackMessage } from '@/app/api/vercel-webhook/send-slack-message'
import prisma from '@/db/prisma'

import {
  GetMessageTemplateOverride,
  MessageTemplateOverridePayload,
} from './types'

export async function saveMessageTemplateOverride(
  payload: MessageTemplateOverridePayload,
) {
  try {
    const existingOverride = await prisma.message_template_override.findFirst({
      where: {
        employee_id: payload.employeeId,
        estate_id: payload.estateId,
        template_id: payload.templateId,
      },
    })

    if (existingOverride) {
      await prisma.message_template_override.update({
        where: { id: existingOverride.id },
        data: {
          email_subject: payload.subject,
          email_content: payload.emailContent,
          email_html: payload.emailHtml,
          magic_link_users_id: payload.userId,
        },
      })
    } else {
      await prisma.message_template_override.create({
        data: {
          magic_link_users_id: payload.userId,
          estate_id: payload.estateId,
          employee_id: payload.employeeId,
          template_id: payload.templateId,
          email_subject: payload.subject,
          email_content: payload.emailContent,
          email_html: payload.emailHtml,
        },
      })
    }

    return true
  } catch (error) {
    console.error(error)
    sendSlackMessage([
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `Error saving message template override
            employeeId: ${payload.employeeId}
            estateId: ${payload.estateId}
            templateId: ${payload.templateId}
            error: ${error}`,
        },
      },
    ])
    throw error
  }
}

export async function getMessageTemplateOverride({
  templateId,
  employeeId,
  estateId,
}: GetMessageTemplateOverride) {
  try {
    const existingOverride = await prisma.message_template_override.findFirst({
      where: {
        employee_id: employeeId,
        estate_id: estateId,
        template_id: templateId,
      },
      select: {
        email_subject: true,
        email_content: true,
        email_html: true,
        template_id: true,
        updated_at: true,
      },
    })

    return {
      subject: existingOverride?.email_subject,
      emailContent: existingOverride?.email_content,
      emailHtml: existingOverride?.email_html,
      templateId: existingOverride?.template_id,
      lastChanged: existingOverride?.updated_at,
    }
  } catch (error) {
    console.error(error)
    throw error
  }
}

export async function resetMessageTemplateOverride(
  payload: Pick<
    MessageTemplateOverridePayload,
    'employeeId' | 'estateId' | 'templateId'
  >,
) {
  try {
    const existingOverride = await prisma.message_template_override.findFirst({
      where: {
        employee_id: payload.employeeId,
        estate_id: payload.estateId,
        template_id: payload.templateId,
      },
    })

    if (existingOverride) {
      await prisma.message_template_override.delete({
        where: { id: existingOverride.id },
      })
    }

    return true
  } catch (error) {
    console.error(error)
    sendSlackMessage(
      [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `Error resetting message template override
            employeeId: ${payload.employeeId}
            estateId: ${payload.estateId}
            templateId: ${payload.templateId}
            error: ${error}`,
          },
        },
      ],
      'NORDVIK_ERRORS',
    )
    throw error
  }
}
