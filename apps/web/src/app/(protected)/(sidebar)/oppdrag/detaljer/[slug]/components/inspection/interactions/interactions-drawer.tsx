'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTit<PERSON> } from '@nordvik/ui/sheet'

import { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import {
  GQLCombinedInteractionsQuery,
  useCombinedInteractionsQuery,
} from '@/api/generated-client'
import { Visit } from '@/lib/analytics/group-page-visits'

import { AgreementStatistics } from './agreement-statistics'
import { InspectionStatistics } from './inspection-statistics/inspection-statistics'
import { InteractionsSellers } from './interactions-sellers'

export type InteractionPageVisits = GQLCombinedInteractionsQuery['pageVisits']

export function InteractionsDrawer({
  estateId,
  signersData,
  open,
  pageVisits,
}: {
  estateId: string
  signersData?: NextPrivateContactWithProxy[]
  open: boolean
  pageVisits?: Visit[]
}) {
  const { data: inspectionData, isLoading: inspectionLoading } =
    useCombinedInteractionsQuery(
      {
        estateId,
        includeSubPages: true,
      },
      {
        enabled: open,
        refetchInterval: 5000,
      },
    )

  const data = inspectionData
  const isLoading = inspectionLoading

  return (
    <SheetContent className="@container/interactions p-0 max-w-[49rem] w-screen bg-root">
      <SheetHeader>
        <h3 className="typo-title-sm">Statistikk og aktivitet</h3>
        <SheetTitle className="sr-only">Statistikk og aktivitet</SheetTitle>
      </SheetHeader>

      <div className="flex flex-col gap-8 py-6">
        <InteractionsSellers
          estate={data?.estate}
          interactions={data?.listingAgreementInteractionsForEstate}
          loading={isLoading}
          pageVisits={pageVisits}
        />
        <InspectionStatistics
          pageVisits={data?.pageVisits}
          isLoading={isLoading}
          isValuation={data?.estate?.isValuation ?? false}
        />
        <AgreementStatistics
          estate={data?.estate}
          listingAgreementInteractionsForEstate={
            data?.listingAgreementInteractionsForEstate
          }
          emailInteractions={data?.emailInteractionsForEstate}
          signersData={signersData}
          audits={data?.estate?.inspectionFolder?.audit}
          pageVisits={data?.pageVisits}
          isLoading={isLoading}
        />
      </div>
    </SheetContent>
  )
}
