import { notFound } from 'next/navigation'
import React, { Suspense } from 'react'

import { AssignmentNotes } from '@/components/assignment-notes'
import ErrorBoundary from '@/components/error-boundary'

import { EstateStatusNumber } from '../../../util'
import { addInspectionEvent } from '../actions/add-inspection-event'

import AssignmentActivities from './assignment-activities'
import AssignmentForms from './assignment-forms'
import { EstateHeader } from './assignment-header/estate-header'
import AssignmentLinks from './assignment-links'
import AssignmentMarketing from './assignment-marketing'
import { cachedEstateDetail, getEstateDetail } from './cached-estate-detail'
import { Inspection } from './inspection/inspection'
import { InspectionLoading } from './inspection/inspection-loading'
import { MissingEvent, UpcomingEvent } from './upcoming-event'

async function AssignmentPageContent({ estateId }: { estateId: string }) {
  let estate = await cachedEstateDetail(estateId)

  if (!estate) {
    notFound()
  }

  let shouldThrow = estate.status === 0 && !estate.inspectionFolder?.id

  if (shouldThrow) {
    estate = await getEstateDetail(estateId)
    shouldThrow = estate?.status === 0 && !estate.inspectionFolder?.id
  }

  if (!estate || shouldThrow) {
    console.error('Error creating inspection folder')
    return notFound()
  }

  const addInspectionHandler = async (from: Date, to: Date) => {
    'use server'
    await addInspectionEvent(from, to, estateId)
  }

  const missingInspectionEvent =
    typeof estate?.status === 'number' &&
    estate.status < EstateStatusNumber.InPreperation &&
    !estate.activities?.some((e) => e.type === 1)

  const showMarketing = estate.status > 0 && !estate.isValuation
  const showForms = !estate.isValuation

  return (
    <div className="w-full">
      <div data-theme="dark" className="w-full bg-root">
        <div className="container pb-6 pt-8 xl:py-10">
          <EstateHeader estate={estate} />
        </div>
      </div>
      <div className="container @container/content py-6">
        <div className="flex flex-col gap-2 empty:hidden">
          {estate.upcomingEvents.map((event) => (
            <UpcomingEvent key={event.id} event={event} />
          ))}
          {missingInspectionEvent && (
            <MissingEvent
              eventType="Befaring"
              addEvent={addInspectionHandler}
            />
          )}
        </div>

        <div className="grid gap-6 @[52rem]/content:gap-8 @[52rem]/content:grid-cols-[1fr,minmax(auto,340px)]">
          <div className="flex flex-col gap-6 @[52rem]/content:gap-8">
            <Suspense fallback={<InspectionLoading estate={estate} />}>
              <Inspection
                estateId={estateId}
                estatePriceSuggestion={estate.estatePrice?.priceSuggestion}
                isValuation={estate.isValuation}
              />
            </Suspense>
            {showMarketing && (
              <ErrorBoundary>
                <div className="@[52rem]/content:max-w-[340px] w-full @[52rem]/content:hidden">
                  <Suspense>
                    <AssignmentMarketing estate={estate} />
                  </Suspense>
                </div>
              </ErrorBoundary>
            )}

            {showForms && (
              <ErrorBoundary>
                <Suspense>
                  <AssignmentForms estate={estate} />
                </Suspense>
              </ErrorBoundary>
            )}

            <AssignmentActivities activities={estate.activities} />
          </div>

          <div className="flex flex-col gap-6 @[52rem]/content:gap-8">
            {showMarketing && (
              <ErrorBoundary>
                <div className="@[52rem]/content:max-w-[340px] w-full hidden @[52rem]/content:block">
                  <Suspense>
                    <AssignmentMarketing estate={estate} />
                  </Suspense>
                </div>
              </ErrorBoundary>
            )}

            <AssignmentLinks estate={estate} />

            <AssignmentNotes estateId={estateId} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default AssignmentPageContent
