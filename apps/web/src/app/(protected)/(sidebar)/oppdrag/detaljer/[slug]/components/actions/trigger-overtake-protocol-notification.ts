'use server'

import { sendSlackMessage } from '@/app/api/vercel-webhook/send-slack-message'
import { simpleFetch } from '@/utils/fetching'

const APP_SYNC_API_URL = process.env.NORDVIK_APP_SYNC_API_URL
const APP_SYNC_API_KEY = process.env.NORDVIK_APP_SYNC_API_KEY

async function fetchAppSyncApi<ResponseType>(
  endPoint: string,
  options?: RequestInit,
) {
  if (!APP_SYNC_API_URL || !APP_SYNC_API_KEY) {
    throw new Error('Missing Nordvik App Sync API environment variables')
  }
  const headers = new Headers({
    'Content-Type': 'application/json',
    'x-api-key': APP_SYNC_API_KEY,
    ...options?.headers,
  })

  const url = endPoint.startsWith('http')
    ? endPoint
    : `${APP_SYNC_API_URL}/${endPoint}`

  return simpleFetch<ResponseType>(url, {
    ...options,
    headers,
  })
}

export async function triggerOvertakeProtocolNotification(estateId: string) {
  if (!estateId) {
    console.error('Missing estateId')
    throw new Error('Missing estateId')
  }

  try {
    await fetchAppSyncApi(
      `notification/trigger/overtake-protocol/${estateId}`,
      {
        method: 'POST',
        body: JSON.stringify({}),
      },
    )

    console.info(
      `Overtake protocol notification triggered for estate ${estateId}`,
    )
  } catch (error) {
    console.error(
      `Failed to trigger overtake protocol notification for estate ${estateId}`,
      error,
    )
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error'

    sendSlackMessage(
      [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `Failed to trigger overtake protocol notification for estate ${estateId}: ${errorMessage}`,
          },
        },
      ],
      'NORDVIK_ERRORS',
    )
    throw new Error(
      `Failed to trigger overtake protocol notification: ${errorMessage}`,
    )
  }
}
