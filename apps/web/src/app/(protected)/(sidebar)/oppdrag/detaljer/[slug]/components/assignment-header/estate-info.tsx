import React from 'react'

import type { GQLEstateByIdQuery } from '@/api/generated-client'
import getEstateArea from '@/utils/get-estate-area'

export function EstateInfo({
  estate,
}: {
  estate: NonNullable<GQLEstateByIdQuery['estate']>
}) {
  const estatePriceSuggestion = estate.estatePrice?.priceSuggestion
  const sizeInfo = getEstateArea(estate.sumArea)

  return (
    <div
      className="flex justify-between sm:grid sm:grid-cols-3 w-full gap-4 border-b border-muted py-4 @3xl/header:pt-0"
      data-theme="dark"
    >
      <EstateInfoItem
        name="Prisantydning"
        value={
          estatePriceSuggestion
            ? `${estatePriceSuggestion.toLocaleString('no-NO')} kr`
            : 'Ikke satt'
        }
      />
      <EstateInfoItem
        name={sizeInfo.label}
        value={
          <>
            {sizeInfo.value}{' '}
            {typeof sizeInfo.value === 'number' && (
              <>
                m<sup>2</sup>
              </>
            )}
          </>
        }
      />
      <EstateInfoItem
        name="Antall rom"
        value={estate.noOfRooms ? estate.noOfRooms : 'Ukjent'}
      />
    </div>
  )
}
function EstateInfoItem({
  name,
  value,
}: {
  name: string
  value: string | number | undefined | React.ReactNode
}) {
  return (
    <div>
      <p className="typo-label-lg whitespace-nowrap">{name}</p>
      <p className="typo-body-md ink-subtle">{value}</p>
    </div>
  )
}
