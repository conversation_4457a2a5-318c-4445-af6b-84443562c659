'use client'

import { isPast } from 'date-fns'
import { AlertCircleIcon } from 'lucide-react'

import { Alert, AlertTitle } from '@nordvik/ui/alert'

import {
  GQLAgreementAndInspectionQuery,
  GQLListingAgreementStatus,
} from '@/api/generated-client'
import { DATE_TIME_FORMAT } from '@/lib/constants'
import { formatDate } from '@/lib/dates'

type PastDeadlineWarningProps = {
  estate: GQLAgreementAndInspectionQuery['estate']
}

export function PastDeadlineWarning({ estate }: PastDeadlineWarningProps) {
  const deadline = estate?.listingAgreement?.deadline
  const isPastDeadline = deadline ? isPast(new Date(deadline)) : false

  if (
    !estate ||
    !isPastDeadline ||
    estate?.listingAgreement?.status === GQLListingAgreementStatus.Signed
  )
    return

  return (
    <Alert variant="destructive" Icon={AlertCircleIcon} className="mt-2">
      <AlertTitle>
        Signeringsfrist utgikk {formatDate(new Date(), DATE_TIME_FORMAT)}
      </AlertTitle>
    </Alert>
  )
}
