import { ValidationResult } from '@befaring/hooks/useValidateForm'
import { isWithinInterval } from 'date-fns/isWithinInterval'
import uniqBy from 'lodash/uniqBy'

import { BadgeProps } from '@nordvik/ui/badge'

import { GQLEstateTabFilter } from '@/api/generated-client'
import { GQLFinnData } from '@/server/generated-schema'

import type { EstateForBroker } from './status/[status]/components/estate-list/types'

export enum EstateStatus {
  Requested = 'Requested',
  InPreperation = 'InPreperation',
  ForSale = 'ForSale',
  Sold = 'Sold',
  Valuation = 'Valuation',
  Archive = 'Archive',
  Lost = 'Lost',
}

export enum EstateStatusNumber {
  Requested = 0,
  InPreperation = 1,
  ForSale = 2,
  Sold = 3,
  Archive = 4,
  Lost = 5,
}

export const getStatusNameByNumber = (
  status: EstateStatusNumber,
  isPublished?: boolean,
  isValuation?: boolean,
) => {
  switch (status) {
    case EstateStatusNumber.Requested:
      if (isValuation) {
        return 'Verdivurdering'
      }

      return 'Innsalg'
    case EstateStatusNumber.InPreperation:
      if (isValuation) {
        return 'E-takst levert'
      }
      return 'Klargjøring'
    case EstateStatusNumber.ForSale:
      return isPublished ? 'Til salgs' : 'Ikke publisert'
    case EstateStatusNumber.Sold:
      return 'Solgt'
    case EstateStatusNumber.Archive:
      return 'Arkiv'
    case EstateStatusNumber.Lost:
      return 'Tapt'
    default:
      return 'Annet'
  }
}

export function getStatusTextLowercase(status: EstateStatus) {
  return getStatusText(status).toLowerCase().replace(/ /g, '-')
}

export const getStatusText = (status: EstateStatus | GQLEstateTabFilter) => {
  switch (status) {
    case EstateStatus.Requested:
      return 'Innsalg'
    case EstateStatus.Valuation:
      return 'Verdivurdering'
    case EstateStatus.InPreperation:
    case GQLEstateTabFilter.InPreparation:
      return 'Klargjøring'
    case EstateStatus.ForSale:
      return 'Til salgs'
    case EstateStatus.Sold:
      return 'Solgt'
    case EstateStatus.Archive:
      return 'Arkiv'
    case EstateStatus.Lost:
      return 'Tapt'
    default:
      return 'Annet'
  }
}

export const getBadgeVariantForStatus = (
  status: number,
  isPublished?: boolean,
  isValuation?: boolean,
  isEtakstPublished?: boolean,
): BadgeProps['variant'] => {
  switch (status) {
    case 0:
      if (isEtakstPublished) {
        return 'bright-green'
      }
      if (isValuation) {
        return 'light-green'
      }
      return 'blue'
    case 1:
      if (isValuation && isEtakstPublished) {
        return 'bright-green'
      }
      return 'beige'
    case 2:
      return isPublished ? 'bright-green' : 'light-green'
    case 3:
      return 'dark-green'
  }
}

export const estateListStatusMap = {
  [getStatusTextLowercase(EstateStatus.Requested)]: [
    GQLEstateTabFilter.Requested,
  ],
  [getStatusTextLowercase(EstateStatus.Valuation)]: [
    GQLEstateTabFilter.Valuation,
  ],
  [getStatusTextLowercase(EstateStatus.InPreperation)]: [
    GQLEstateTabFilter.InPreparation,
  ],
  [getStatusTextLowercase(EstateStatus.ForSale)]: [GQLEstateTabFilter.ForSale],
  [getStatusTextLowercase(EstateStatus.Sold)]: [GQLEstateTabFilter.Sold],
  [getStatusTextLowercase(EstateStatus.Archive)]: [
    GQLEstateTabFilter.Sold,
    GQLEstateTabFilter.Archived,
  ],
}

export const ESTATE_LIST_DEFAULT_LIMIT = 12

export const getSellersString = (estate: EstateForBroker) => {
  const mainSeller = estate.mainSeller

  if (mainSeller?.firstName && mainSeller.lastName) {
    return `${mainSeller.firstName} ${mainSeller.lastName}`
  }

  return estate.sellers
    .map((seller) => {
      if (!seller.firstName) {
        return seller.lastName
      }

      if (!seller.lastName) {
        return seller.firstName
      }

      return `${seller.firstName} ${seller.lastName}`
    })
    .join(', ')
}

export const isActiveOnFinn = (
  finn?: Pick<GQLFinnData, 'finnExpireDate' | 'finnPublishDate'> | null,
) => {
  if (!finn || !finn.finnExpireDate || !finn.finnPublishDate) {
    return false
  }

  return isWithinInterval(new Date(), {
    start: new Date(finn.finnPublishDate),
    end: new Date(finn.finnExpireDate),
  })
}

export const flattenValidationErrors = (
  validation: ValidationResult<{
    mobilePhone: unknown
    email: unknown
  }>[],
) => {
  const errors = uniqBy(
    validation.flatMap((seller) => seller.errors),
    'path',
  ).filter((err) => err !== null)
  return errors
}

export function mapNumericStatusToTab(
  status: number,
  isValuation?: boolean | null,
): GQLEstateTabFilter | undefined {
  // Requested / Valuation (disambiguated by isValuation)
  switch (status) {
    case 0:
      return isValuation
        ? GQLEstateTabFilter.Valuation
        : GQLEstateTabFilter.Requested
    case 1:
      return GQLEstateTabFilter.InPreparation
    case 2:
      return GQLEstateTabFilter.ForSale
    case 3:
      return GQLEstateTabFilter.Sold
    case 4:
      return GQLEstateTabFilter.Archived
    default:
      return undefined
  }
}
