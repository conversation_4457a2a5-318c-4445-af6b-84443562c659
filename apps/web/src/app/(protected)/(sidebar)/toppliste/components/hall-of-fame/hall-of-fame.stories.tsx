import React from 'react'

import { GQLHallOfFameQuery } from '@/api/generated-client'

import { Empty, HallOfFameList } from './hall-of-fame'

export default {
  title: 'Pages / Hall of Fame',
  parameters: {
    theme: 'dark',
  },
  decorators: [
    (Story) => (
      <div className="@container/section">
        <Story />
      </div>
    ),
  ],
}

export const Example = () => {
  return <HallOfFameList hallOfFame={data.hallOfFame} />
}

export const Loading = () => {
  return <HallOfFameList hallOfFame={data.hallOfFame} isLoading />
}

export const EmptyState = () => {
  return <Empty />
}

const data: GQLHallOfFameQuery = {
  hallOfFame: [
    {
      year: 2024,
      entries: [
        {
          awardId: 300148,
          name: '<PERSON><PERSON> megler (antall solgte)',
          employee: {
            id: '5cf8abfca19104b10f7318e4',
            employeeId: '5cf8abfca19104b10f7318e4',
            name: '<PERSON><PERSON>',
            department: {
              name: '<PERSON><PERSON>',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/stian-poehner-medium.jpg',
            },
          },
        },
        {
          awardId: 300159,
          name: 'Årets megler (omsetning)',
          employee: {
            id: '5cf8abfca19104b10f7318e4',
            employeeId: '5cf8abfca19104b10f7318e4',
            name: 'Stian Pøhner',
            department: {
              name: 'Frogner',
            },
            image: {},
          },
        },
        {
          awardId: 300144,
          name: 'Årets fagansvarlig',
          employee: {
            id: '5e9d5443ef871d00082b9131',
            employeeId: '5e9d5443ef871d00082b9131',
            name: 'Dan-Christian Jensen',
            department: {
              name: 'Frogner',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/dan-christian-jensen-medium.jpg',
            },
          },
        },
        {
          awardId: 300155,
          name: 'Årets daglig leder',
          employee: {
            id: '5f43c02c8638d70007371959',
            employeeId: '5f43c02c8638d70007371959',
            department: {
              name: 'Frogner',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/cecilie-rosenquist-medium.jpg',
            },
          },
        },
        {
          awardId: 300167,
          name: 'Årets medarbeider',
          employee: {
            id: '5f686640601aaf0007443ef1',
            employeeId: '5f686640601aaf0007443ef1',
            name: 'Camilla Pitz Jacobsen',
            department: {
              name: 'Nydalen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/camilla-pitz-jacobsen-652835424038-medium.jpg',
            },
          },
        },
        {
          awardId: 300170,
          name: 'Årets progresjon (megler)',
          employee: {
            id: '60a773cb7b2f5b00080e8e9a',
            employeeId: '60a773cb7b2f5b00080e8e9a',
            name: 'Benjamin Ådna',
            department: {
              name: 'Majorstuen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/benjamin-adna-639950443651-medium.jpg',
            },
          },
        },
        {
          awardId: 300161,
          name: 'Årets fullmektig',
          employee: {
            id: '614838042a756b0008aee003',
            employeeId: '614838042a756b0008aee003',
            name: 'Celina Olsen',
            department: {
              name: 'Østensjø',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/celina-olsen-652835399068-medium.jpg',
            },
          },
        },
        {
          awardId: 300157,
          name: 'Årets salgsleder',
          employee: {
            id: '619f5aadc71ee60008d466b2',
            employeeId: '619f5aadc71ee60008d466b2',
            name: 'Lilliann Tronshaug',
            department: {
              name: 'Torshov',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/lilliann-tronshaug-639950457742-medium.jpg',
            },
          },
        },
        {
          awardId: 300163,
          name: 'Årets Nordviker',
          employee: {
            id: '656de737f66c4142df506bcd',
            employeeId: '656de737f66c4142df506bcd',
            name: 'Simen Smith Ringdal',
            department: {
              name: 'Sandefjord',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/simen-smith-ringdal-648540430762-medium.jpg',
            },
          },
        },
        {
          awardId: 300142,
          name: 'Årets nykommer',
          employee: {
            id: '6578724922ac1bace097f738',
            employeeId: '6578724922ac1bace097f738',
            name: 'Vilde Nadden',
            department: {
              name: 'Torshov',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/vilde-nadden-648540430902-medium.jpg',
            },
          },
        },
        {
          awardId: 300174,
          name: 'Årets kundetilfredshet (megler)',
          employee: {
            id: '6578724922ac1bace097f738',
            employeeId: '6578724922ac1bace097f738',
            name: 'Vilde Nadden',
            department: {
              name: 'Torshov',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/vilde-nadden-648540430902-medium.jpg',
            },
          },
        },
      ],
    },
    {
      year: 2023,
      entries: [
        {
          awardId: 300155,
          name: 'Årets daglig leder',
          employee: {
            id: '5cf8abfca19104d9337318fc',
            employeeId: '5cf8abfca19104d9337318fc',
            name: 'Magnus Moestue',
            department: {
              name: 'Bislett',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/magnus-moestue-medium.jpg',
            },
          },
        },
        {
          awardId: 300157,
          name: 'Årets salgsleder',
          employee: {
            id: '5cf8abfca191047662731961',
            employeeId: '5cf8abfca191047662731961',
            name: 'Sverre Ivar Skaarberg Wiborg',
            department: {
              name: 'Bærum',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/sverre-ivar-skaarberg-wiborg-652835423725-medium.jpg',
            },
          },
        },
        {
          awardId: 300174,
          name: 'Årets kundetilfredshet (megler)',
          employee: {
            id: '5cf8abfca191040b74731951',
            employeeId: '5cf8abfca191040b74731951',
            name: 'Kasper Hillås',
            department: {
              name: 'St. Hanshaugen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/kasper-hillas-644245433698-medium.jpg',
            },
          },
        },
        {
          awardId: 300159,
          name: 'Årets megler (omsetning)',
          employee: {
            id: '5cf8abfca1910402267318ed',
            employeeId: '5cf8abfca1910402267318ed',
            name: 'Frederick Horntvedt',
            department: {
              name: 'Frogner',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/frederick-horntvedt-medium.jpg',
            },
          },
        },
      ],
    },
    {
      year: 2022,
      entries: [
        {
          awardId: 300174,
          name: 'Årets kundetilfredshet (megler)',
          employee: {
            id: '5cf8abfba19104394c7318c4',
            employeeId: '5cf8abfba19104394c7318c4',
            name: 'Isan Kvamme Alim',
            department: {
              name: 'Nydalen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/isan-kvamme-alim-652835418288-medium.jpg',
            },
          },
        },
        {
          awardId: 300159,
          name: 'Årets megler (omsetning)',
          employee: {
            id: '5cf8abfca19104b10f7318e4',
            employeeId: '5cf8abfca19104b10f7318e4',
            name: 'Stian Pøhner',
            department: {
              name: 'Frogner',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/stian-poehner-medium.jpg',
            },
          },
        },
      ],
    },
    {
      year: 2021,
      entries: [],
    },
    {
      year: 2020,
      entries: [],
    },
    {
      year: 2019,
      entries: [
        {
          awardId: 300142,
          name: 'Årets nykommer',
          employee: {
            id: '5cf8abfca1910460ca73194e',
            employeeId: '5cf8abfca1910460ca73194e',
            name: 'Johanna Finnby',
            department: {
              name: 'Drammen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/johanna-finnby-657130401265-medium.jpg',
            },
          },
        },
        {
          awardId: 300144,
          name: 'Årets fagansvarlig',
          employee: {
            id: '5cf8abfca19104e7b373194d',
            employeeId: '5cf8abfca19104e7b373194d',
            name: 'Ida Soleim Abrahamsen',
            department: {
              name: 'Drammen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/ida-wang-soleim-medium.jpg',
            },
          },
        },
        {
          awardId: 300163,
          name: 'Årets Nordviker',
          employee: {
            id: '5cf8abfca19104592c731922',
            employeeId: '5cf8abfca19104592c731922',
            name: 'Christian Gjesdal Udgaard',
            department: {
              name: 'Drammen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/christian-gjesdal-udgaard-medium.jpg',
            },
          },
        },
        {
          awardId: 300161,
          name: 'Årets fullmektig',
          employee: {
            id: '5cf8abfca191045c45731925',
            employeeId: '5cf8abfca191045c45731925',
            name: 'Eren Karaca',
            department: {
              name: 'Bislett',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/eren-karaca-medium.jpg',
            },
          },
        },
        {
          awardId: 300148,
          name: 'Årets megler (antall solgte)',
          employee: {
            id: '5cf8abfca1910402267318ed',
            employeeId: '5cf8abfca1910402267318ed',
            name: 'Frederick Horntvedt',
            department: {
              name: 'Frogner',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/frederick-horntvedt-medium.jpg',
            },
          },
        },
        {
          awardId: 300155,
          name: 'Årets daglig leder',
          employee: {
            id: '5cf8abfca191046c41731936',
            employeeId: '5cf8abfca191046c41731936',
            name: 'Morten Styrmoe Hannestad',
            department: {
              name: 'St. Hanshaugen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/morten-styrmoe-hannestad-2847563457074-medium.jpg',
            },
          },
        },
        {
          awardId: 300167,
          name: 'Årets medarbeider',
          employee: {
            id: '5cf8abfca1910474797318dd',
            employeeId: '5cf8abfca1910474797318dd',
            name: 'Maren Tostrup',
            department: {
              name: 'St. Hanshaugen',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/maren-tostrup-medium.jpg',
            },
          },
        },
      ],
    },
    {
      year: 2018,
      entries: [
        {
          awardId: 300142,
          name: 'Årets nykommer',
          employee: {
            id: '5cf8abfca1910438d9731950',
            employeeId: '5cf8abfca1910438d9731950',
            name: 'Kamilla Dagsland',
            department: {
              name: 'Ullevål',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/kamilla-dagsland-medium.jpg',
            },
          },
        },
        {
          awardId: 300144,
          name: 'Årets fagansvarlig',
          employee: {
            id: '5cf8abfca191040d22731919',
            employeeId: '5cf8abfca191040d22731919',
            name: 'Suzanne Linde Hansen',
            department: {
              name: 'Bærum',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/suzanne-linde-hansen-652835423727-medium.jpg',
            },
          },
        },
        {
          awardId: 300148,
          name: 'Årets megler (antall solgte)',
          employee: {
            id: '5cf8abfca1910402267318ed',
            employeeId: '5cf8abfca1910402267318ed',
            name: 'Frederick Horntvedt',
            department: {
              name: 'Frogner',
            },
            image: {
              medium:
                'https://d1j4wdkidt72cf.cloudfront.net/employees/frederick-horntvedt-medium.jpg',
            },
          },
        },
      ],
    },
  ],
}
