import { GQLHallOfFameQuery, useHallOfFameQuery } from '@/api/generated-client'
import { DecorativeDivider } from '@/components/broker-profile/presentation/components/decorative-divider'
import { EmptyState } from '@/components/empty-state'

import { PodiumAward } from '../filters/leader-board/podium-avatar'

export function HallOfFame() {
  const { data, isLoading, isError } = useHallOfFameQuery()

  if (isError) {
    throw (
      <EmptyState
        illustration="error"
        title="Kunne ikke hente Hall of Fame"
        description="Prøv igjen senere"
      />
    )
  }

  if (isLoading) {
    return <HallOfFameList hallOfFame={getHallOfFameLoadingData()} isLoading />
  }

  if (!data?.hallOfFame) {
    return <Empty />
  }

  return <HallOfFameList hallOfFame={data.hallOfFame} />
}

export function HallOfFameList({
  hallOfFame,
  isLoading,
}: {
  hallOfFame: GQLHallOfFameQuery['hallOfFame']
  isLoading?: boolean
}) {
  const premiumId = [300148, 300159]

  return (
    <div className="h-full flex flex-1 flex-col gap-4 @container/section ">
      {hallOfFame.map((item) => {
        const premiumAwards = item.entries
          ?.filter(
            (entry) => entry?.awardId && premiumId.includes(entry.awardId),
          )
          .sort((a, b) => (b.awardId ?? 0) - (a.awardId ?? 0))
        const entriesWithoutPremium = item.entries
          ?.filter(
            (entry) => entry?.awardId && !premiumId.includes(entry.awardId),
          )
          .sort((a, b) => (a.name ?? '').localeCompare(b.name ?? ''))

        return (
          <div key={item.year} className="w-full not-first:mt-16 first:mt-8">
            {(item.entries?.length ?? 0) > 0 && (
              <AwardsLayout
                year={item.year}
                key={item.year}
                top={premiumAwards?.map((entry, idx) => (
                  <PodiumAward
                    key={`${entry.awardId}-${entry.employee?.employeeId}-${idx}`}
                    award={entry}
                    isPremium
                    loading={isLoading}
                  />
                ))}
                bottom={entriesWithoutPremium?.map((entry, idx) => (
                  <PodiumAward
                    key={`${entry.awardId}-${entry.employee?.employeeId}-${idx}`}
                    award={entry}
                    loading={isLoading}
                  />
                ))}
              />
            )}
          </div>
        )
      })}
    </div>
  )
}

export function Empty() {
  return (
    <div className="my-8">
      <EmptyState
        illustration="no-toplist"
        title="Kunne ikke hente Hall of Fame"
        description="Prøv igjen senere"
      />
    </div>
  )
}

function AwardsLayout({
  year,
  top,
  bottom,
}: {
  year?: number
  top?: React.ReactNode
  bottom?: React.ReactNode
}) {
  return (
    <div className="max-w-[1000px] mx-auto">
      <div className="flex flex-col w-full text-center mb-4">
        <p className="typo-body-sm ink-gold">Hall of Fame</p>
        <h2 className="typo-display-xl">{year}</h2>
      </div>
      <DecorativeDivider className="w-full max-w-[500px] mx-auto" />
      <ul className="flex flex-wrap justify-center gap-x-4 gap-y-8 md:gap-y-12 mt-8 pb-8 [&>*]:w-[calc(50%-0.5rem)] @xl/section:[&>*]:w-[calc(33.333%-0.667rem)]">
        {top}
      </ul>
      <ul className="flex flex-wrap justify-center gap-x-4 gap-y-8 md:gap-y-12 pb-8 [&>*]:w-[calc(50%-0.5rem)] @xl/section:[&>*]:w-[calc(33.333%-0.667rem)]">
        {bottom}
      </ul>
    </div>
  )
}

const getHallOfFameLoadingData = (): GQLHallOfFameQuery['hallOfFame'] => {
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 3 }, (_, i) => currentYear - i - 1)

  return years.map((year) => ({
    year,
    entries: [
      {
        awardId: 300148,
        name: 'Broker of the Year',
        employee: {
          employeeId: 'AAAA',
          id: '5cf8abfca19104b10f7318e4',
          name: 'Firstname Lastname',
          department: {
            name: 'Department',
          },
          image: {
            medium: '',
          },
        },
      },
      {
        awardId: 300159,
        name: 'Broker of the Year',
        employee: {
          employeeId: 'BBBB',
          id: '5cf8abfca19104b10f7318e4',
          name: 'Firstname Lastname',
          department: {
            name: 'Department',
          },
          image: {
            medium: '',
          },
        },
      },

      ...Array.from({ length: 6 }, (_, i) => ({
        awardId: i + 1,
        name: 'Årets Ansatte',
        employee: {
          employeeId: 'CCCC',
          id: '5cf8abfca19104b10f7318e4',
          name: 'Firstname Lastname',
          department: {
            name: 'Bolig',
          },
          image: {
            medium: '',
          },
        },
      })),
    ],
  }))
}
