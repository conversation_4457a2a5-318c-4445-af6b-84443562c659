import type { Metadata } from 'next'
import { redirect } from 'next/navigation'

import Resources from '@/app/(protected)/(sidebar)/hjelpesenter/components/resources'
import { ResourcesFilter } from '@/app/(protected)/(sidebar)/hjelpesenter/components/resources-filter'

interface Author {
  name: string
  email: string
}

interface Category {
  id: number
  slug: string
  title: string
  targetRoles: string[]
}

interface AccordionItem {
  header: string
  text: string
}

interface RichTextModule {
  type: 'richTextModule'
  body: string
}

interface AccordionModuleType {
  type: 'accordionModule'
  accordion: AccordionItem[]
}

type Module = RichTextModule | AccordionModuleType

export interface Article {
  id: number
  slug: string
  title: string
  section: string
  type: string
  important: boolean
  eventDate: boolean
  startDate: string | null
  endDate: string | null
  externalUrl: string | null
  excerpt: string
  image: string | null
  postDate: string
  author: Author
  categories: Category[]
  departmentIds: number[]
  targetRoles: string[]
  channels: string[]
  modules: Module[]
}

export const metadata: Metadata = {
  title: 'Hjelpesenter - Nordvik Megler',
  description:
    'Hjelpesenter og kunnskapsbase for Nordvik Megler. Finn svar på vanlige spørsmål om våre tjenester og appen.',
  robots: 'index, follow',
  keywords: 'hjelpesenter, nordvik, megler, app, spørsmål, svar, kunnskapsbase',
  authors: [{ name: 'Nordvik Megler' }],
  creator: 'Nordvik Megler',
  publisher: 'Nordvik Megler',
  applicationName: 'Nordvik Megler',

  openGraph: {
    title: 'Hjelpesenter - Nordvik Megler',
    description:
      'Hjelpesenter og kunnskapsbase for Nordvik Megler. Finn svar på vanlige spørsmål om våre tjenester og appen.',
    type: 'website',
    locale: 'nb_NO',
    siteName: 'Nordvik Megler',
  },
  alternates: {
    canonical: '/hjelpesenter-intercom',
  },
}

export default async function HelpCenter(props: {
  searchParams?: Promise<{
    secret?: string
  }>
}) {
  const searchParams = await props.searchParams
  const secret = searchParams?.secret || ''

  if (secret !== process.env.INTERCOM_CMS_SECRET) {
    redirect('/')
  }

  return (
    <main className="container">
      <div className="flex flex-col gap-8">
        <ResourcesFilter />
        <div className="mt-6 flex flex-col md:mt-10">
          <Resources
            source={`/hjelpesenter-intercom`}
            crawlerAccessToken={secret}
          />
        </div>
      </div>
    </main>
  )
}
