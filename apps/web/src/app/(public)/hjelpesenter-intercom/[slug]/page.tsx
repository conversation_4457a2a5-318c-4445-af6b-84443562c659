import { redirect } from 'next/navigation'
import { Suspense } from 'react'

import { GQLCmsArticleType } from '@/api/generated-client'
import ArticlePage, {
  ArticlePageSkeleton,
} from '@/components/articles/article-page'

export default async function Page(props: {
  params: Promise<{ slug: string }>
  searchParams?: Promise<{
    secret?: string
  }>
}) {
  const params = await props.params
  const searchParams = await props.searchParams
  const secret = searchParams?.secret || ''

  if (secret !== process.env.INTERCOM_CMS_SECRET) {
    redirect('/')
  }

  return (
    <Suspense fallback={<ArticlePageSkeleton />}>
      <ArticlePage slug={params.slug} type={GQLCmsArticleType.Resource} />
    </Suspense>
  )
}
