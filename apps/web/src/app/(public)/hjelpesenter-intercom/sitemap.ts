import type { MetadataRoute } from 'next'

import { getBaseUrl } from '@/lib/getBaseUrl'

import { Article } from './page'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = getBaseUrl()
  const secret = process.env.INTERCOM_CMS_SECRET

  let articles: Article[] = []
  try {
    if (secret) {
      const response = await fetch(
        `${baseUrl}/api/intercom-cms?secret=${secret}`,
        { next: { revalidate: 86400 } }, // 24 hours
      )
      if (response.ok) {
        articles = await response.json()
      }
    }
  } catch (error) {
    console.error('Failed to fetch articles for sitemap:', error)
    articles = []
  }

  const lastModified =
    articles.length > 0
      ? articles.sort(
          (a, b) =>
            new Date(b.postDate).getTime() - new Date(a.postDate).getTime(),
        )[0].postDate
      : new Date()

  const sitemapEntries: MetadataRoute.Sitemap = [
    {
      url: `${baseUrl}/hjelpesenter-intercom${secret ? `?secret=${secret}` : ''}`,
      lastModified,
      changeFrequency: 'weekly',
      priority: 1.0,
    },
  ]

  if (articles && Array.isArray(articles)) {
    // Generate URLs for individual article pages using slug
    const articleEntries = articles
      .filter((article: Article) => article.slug) // Only include articles with slugs
      .map((article: Article) => ({
        url: `${baseUrl}/hjelpesenter-intercom/${article.slug}${secret ? `?secret=${secret}` : ''}`,
        lastModified: article.postDate
          ? new Date(article.postDate)
          : lastModified,
        changeFrequency: 'monthly' as const,
        priority: 0.8,
      }))

    sitemapEntries.push(...articleEntries)
  }

  return sitemapEntries
}
