'use client'

import { motion } from 'framer-motion'

import { Button } from '@nordvik/ui/button'

export function Header() {
  return (
    <header data-theme="dark" className="bg-root">
      <motion.div
        className="mx-auto flex flex-col justify-center box-content px-6 max-w-5xl py-12 md:py-20"
        variants={{
          visible: {
            height: 'auto',
          },
          hidden: {
            height: '100vh',
          },
        }}
        initial="hidden"
        animate="visible"
        transition={{
          type: 'spring',
          stiffness: 50,
          damping: 20,
          staggerChildren: 0.15,
        }}
      >
        <motion.img
          variants={{
            visible: {
              opacity: 1,
            },
            hidden: {
              opacity: 0,
            },
          }}
          className="w-[10rem] mx-[-0.8rem] md:w-[12rem] md:mx-[-1rem] mb-6 md:mb-10"
          src="/logo.svg"
          alt=""
        />
        <div className="typo-display-md lg:typo-display-xl">
          <motion.h1
            variants={{
              visible: {
                opacity: 1,
                y: 0,
              },
              hidden: {
                opacity: 0,
                y: 30,
              },
            }}
            transition={{
              type: 'spring',
              stiffness: 60,
              damping: 30,
            }}
            className="mb-[0.5em]"
          >
            Hei, Gulltaggen-juryen!
          </motion.h1>
          <motion.p
            variants={{
              visible: {
                opacity: 1,
                y: 0,
              },
              hidden: {
                opacity: 0,
                y: 30,
              },
            }}
            transition={{
              type: 'spring',
              stiffness: 60,
              damping: 30,
            }}
          >
            <span className="ink-gold">Nordvik Megler</span> er et internverktøy
            som hjelper meglere å jobbe mer effektivt og vinne flere oppdrag.
            Som internverktøy flest er det, vel, internt. Derfor er det en kjapp
            how-to nedenfor på de mest relevante delene av løsningen.
          </motion.p>
        </div>
        <motion.p
          variants={{
            visible: {
              opacity: 1,
              y: 0,
            },
            hidden: {
              opacity: 0,
              y: 30,
            },
          }}
          transition={{
            type: 'spring',
            stiffness: 100,
            damping: 30,
          }}
          className="typo-body-lg my-12"
        >
          For å logge inn bruker dere e-posten i innsendelsen og kode{' '}
          <span className="ink-gold font-mono tracking-wide">1234</span>.
        </motion.p>
        <div className="flex">
          <motion.div
            variants={{
              visible: {
                opacity: 1,
                y: 0,
                scale: 1,
              },
              hidden: {
                opacity: 0,
                y: 30,
                scale: 0.9,
              },
            }}
            transition={{
              type: 'spring',
              stiffness: 100,
              damping: 30,
            }}
          >
            <Button size="sm" href="/login">
              Logg inn på Nordvik Megler
            </Button>
          </motion.div>
        </div>
      </motion.div>
    </header>
  )
}
