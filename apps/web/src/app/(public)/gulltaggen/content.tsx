'use client'

import { MotionProps, motion } from 'framer-motion'

import { Button } from '@nordvik/ui/button'

import focus from './assets/focus.jpg'
import inspectionStart from './assets/inspection-start.jpg'
import kti from './assets/kti.jpg'
import profile from './assets/menu-profil.jpg'
import oa from './assets/oa.jpg'
import oppdrag from './assets/oppdrag.jpg'
import toppliste from './assets/toppliste.jpg'
import vis from './assets/vis.jpg'

export function Content() {
  return (
    <div className="mx-auto box-content px-6 pb-40 [&>h2]:typo-display-md [&>h2]:mt-12 md:[&>h2]:mt-20 [&>h2]:mb-2 max-w-5xl typo-body-xl [&>p]:max-w-[70ch] text-pretty [&>p]:mb-2.5 [&>img]:rounded-sm [&>img]:my-8">
      <h2>Mine oppdrag</h2>
      <p>
        Selve hjertet av løsningen. Dette er det viktigste verktøyet megleren
        har i arbeidet med å vinne oppdrag. Trykk dere inn på et oppdrag.
      </p>
      <Img src={oppdrag.src} alt="Liste over oppdrag" />
      <p>
        Befaringsmappen er meglers salgspresentasjon. Denne går megler gjennom
        med selger, og sender over til selger før eller etter befaring.
      </p>
      <p>
        Trykk på{' '}
        <Button className="inline-block" size="sm" variant="outline">
          Vis
        </Button>{' '}
        for å se salgspresentasjonen.
      </p>
      <Img src={vis.src} alt="Befaringsmappen med status og lenke for å åpne" />
      <h2>Salgspresentasjon</h2>
      <p>
        Slå gjerne på fullskjerm-modus i høyre hjørne. Test fremdriftsplan, se
        meglerprofilen og ditt område. Inne i salgspresentasjonen er også
        oppdragsavtalen: det formelle dokumentet selger signerer når de velger
        megler.
      </p>
      <Img src={inspectionStart.src} alt="Startsiden av befaringsmappen" />

      <h3 className="typo-title-xs mb-1.5">Oppdragsavtale</h3>
      <p>
        Før var oppdragsavtalen et PDF-dokument, mens her er det laget som en
        interaktiv løsning hvor megler og selger enkelt kan trykke og snakke seg
        gjennom. Når selger er klar signerer de bare med BankID.
      </p>
      <img src={oa.src} alt="Oppdragsavtale" />

      <h2>Din profil</h2>
      <p>
        Trykk på menyen i høyre hjørne for å komme til redigering av
        meglerprofilen.{' '}
      </p>

      <Img src={profile.src} alt="Meny med lenke til profil" />
      <p>
        Profilen standardiserer meglers CV, så de slipper å lage og oppdatere
        egne PDFer. For å senke terskelen for å skrive noe om seg selv har vi
        implementert AI. Den henter informasjon fra meglers kundeomtaler og bio
        for å trekke frem hva hver megler er spesielt god på. Trykk på få
        forslag for å prøve AI-funksjonen.
      </p>
      <Img src={focus.src} alt="Fokus på AI-funksjonen" />

      <h2>Toppliste</h2>
      <p>
        Meglere er notorisk konkurranseinnstilt, og dette er den mest brukte
        siden. Den henter oppdaterte salgstall for alle avdelinger og meglere,
        så hver megler kan se hvordan de ligger an på sitt kontor eller på tvers
        av kjeden.
      </p>
      <Img src={toppliste.src} alt="Toppliste" />

      <h2>Kundetilfredshet</h2>
      <p>
        Meglere får oversikt over alle sine kundeanmeldelser, og kan velge
        hvilke som skal vises frem på sin profil.
      </p>
      <Img src={kti.src} alt="Kundetilfredshet" />
    </div>
  )
}

const Img = motion.create('img')
// @ts-expect-error -- Framer Motion doesn't expose the correct types
Img.defaultProps = {
  variants: {
    hidden: {
      filter: 'blur(10px)',
      y: 10,
    },
    visible: {
      filter: 'blur(0px)',
      y: 0,
    },
  },
  transition: {
    type: 'spring',
    stiffness: 200,
    damping: 30,
  },
  initial: 'hidden',
  viewport: { once: true },
  whileInView: 'visible',
} as MotionProps
