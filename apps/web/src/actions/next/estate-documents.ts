'use server'

import { GQLNextDocument } from '@/api/generated-client'
import {
  fetchVitecHubApi,
  getVitecHub<PERSON>piAuth,
  vitecHubApiBaseUrl,
} from '@/api/vitec-hub-api'
import { HOUR, SECOND } from '@/db/util'
import { withCache } from '@/utils/with-cache'

import { SigningStatus } from './types'
import { DocumentType, DocumentTypeEnum } from './types-next'

export async function postDocument({
  estateId,
  head,
  docType,
  extension,
  signStatus,
  approvedBy,
  externalId,
  documentContent,
  contactIdList,
}: {
  estateId: string
  head: string
  extension: string
  signStatus?: SigningStatus
  docType: DocumentType
  approvedBy?: string
  externalId?: string
  documentContent: Blob | ArrayBuffer | Buffer
  contactIdList?: string[]
}): Promise<string | void> {
  if (!estateId) {
    console.error('Missing estateId')
    return
  }

  const url = new URL(`${vitecHubApiBaseUrl}/Estates/${estateId}/Documents`)

  url.searchParams.set('head', head)
  url.searchParams.set('extension', extension)
  url.searchParams.set('docType', docType.toString())

  if (signStatus) {
    url.searchParams.set('signStatus', signStatus.toString())
  }

  if (approvedBy) {
    url.searchParams.set('approvedBy', approvedBy)
  }

  if (externalId) {
    url.searchParams.set('externalId', externalId)
  }

  if (contactIdList) {
    contactIdList.forEach((contactId) => {
      url.searchParams.append('contactIdList', contactId)
    })
  }

  try {
    return fetchVitecHubApi(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      body: documentContent,
    })
  } catch (error) {
    console.error('[postDocument]: Failed to fetch Vitec URL', error)
    return
  }
}

const _documentTypes = [
  DocumentTypeEnum.ListingAgreement,
  DocumentTypeEnum.PropertyValuation,
]

export async function getEstateDocuments(
  estateId: string,
  documentTypes: DocumentType[] = _documentTypes,
) {
  const url = new URL(`${vitecHubApiBaseUrl}/Estates/${estateId}/Documents`)

  documentTypes.forEach((type) => {
    url.searchParams.append('docTypes', type.toString())
  })
  return withCache(
    `estate-documents-${estateId}-${documentTypes.join('-')}`,
    async () => {
      try {
        return await fetchVitecHubApi<GQLNextDocument[]>(url.toString())
      } catch (error) {
        console.warn('[getEstateDocuments]: Failed to fetch Vitec URL', error)
        return []
      }
    },
    SECOND * 5,
  )
}

export async function getValuations(estateId: string) {
  try {
    return await getEstateDocuments(estateId, [
      DocumentTypeEnum.ElectronicValuation,
    ])
  } catch (error) {
    console.error('[getValuations]: Failed to fetch Vitec URL', error)
    return []
  }
}

enum ValuationType {
  E_TAKST = 'etakst',
  BROKER_VALUATION = 'Meglers_Verdivurdering',
}

export async function getLatestValuation(
  estateId: string,
  valuationType: ValuationType = ValuationType.E_TAKST,
) {
  try {
    const valuations = await getValuations(estateId)

    const lowerCaseValuationType = valuationType.toLowerCase()

    return valuations
      .sort(
        (a, b) =>
          new Date(b.lastChanged).getTime() - new Date(a.lastChanged).getTime(),
      )
      .find((valuation) => {
        const lowerCaseHead = valuation.head.toLowerCase()

        return lowerCaseHead.includes(lowerCaseValuationType)
      })
  } catch (error) {
    console.error('[getLatestValuation]: Failed to fetch Vitec URL', error)
    return undefined
  }
}

export async function getEstateDocumentById(
  estateId: string,
  documentId: string,
  docType: DocumentType = DocumentTypeEnum.ElectronicValuation,
): Promise<Blob | undefined> {
  const url = new URL(
    `${vitecHubApiBaseUrl}/Estates/${estateId}/Documents/${documentId}`,
  )

  url.searchParams.set('docType', docType.toString())

  try {
    // direct fetch to handle binary data
    const authorization = getVitecHubApiAuth()
    const response = await fetch(url.toString(), {
      headers: {
        Authorization: authorization,
      },
      next: {
        revalidate: HOUR,
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch document: ${response.statusText}`)
    }

    return response.blob()
  } catch (error) {
    console.error('[getDocumentById]: Failed to fetch document', error)
    return undefined
  }
}
