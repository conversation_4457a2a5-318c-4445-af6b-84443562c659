'use server'

import { NextPrivateContact } from '@/actions/next/types-next'
import { isNotNull } from '@/server/utils'

import { getContact } from './contact-get'
import { getEstateContactRelations } from './estate-contact-relations'

export async function getSpouses(
  estateId: string,
): Promise<NextPrivateContact[] | undefined> {
  if (!estateId) {
    console.error('Missing estateId')
    return undefined
  }

  try {
    const spouseRelations = await getEstateContactRelations(estateId, [25])

    if (!spouseRelations) {
      return undefined
    }

    const spouseContacts = spouseRelations.relations.find(
      (relation) => relation.relationType === 25,
    )?.contacts

    if (!spouseContacts) {
      return undefined
    }

    const contacts = await Promise.all(
      spouseContacts.map((entry) => getContact(entry.contactId)),
    )

    return contacts.filter(isNotNull)
  } catch (error) {
    console.info('[getSpouses]: Failed to fetch Vitec URL', error)
    return undefined
  }
}
