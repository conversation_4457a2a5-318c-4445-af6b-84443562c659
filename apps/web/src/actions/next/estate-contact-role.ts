'use server'

import { fetchVitecHubApi } from '@/api/vitec-hub-api'

import { ContactRole } from './types'

export async function getEstateContactRole(
  estateId: string,
  contactId: string,
): Promise<ContactRole[] | undefined> {
  if (!contactId) {
    console.error('Missing contactId')
    return undefined
  }

  if (!estateId) {
    console.error('Missing estateId')
    return undefined
  }

  try {
    const response = await fetchVitecHubApi<
      { contactRoles: ContactRole[] } | undefined
    >(`Estates/${estateId}/Contacts/${contactId}/ContactRoles`)

    return response?.contactRoles
  } catch (error) {
    console.info('[getEstateContactRole]: Failed to fetch Vitec URL', error)
    return undefined
  }
}
