'use server'

import { NextPrivateContact } from '@/actions/next/types-next'
import { fetchVitecHubApi } from '@/api/vitec-hub-api'
import { isNotNull } from '@/server/utils'

import { getContact } from './contact-get'
import { ContactRole } from './types'

export type CompanyContactReturnType = NextPrivateContact & {
  role: ContactRole
}

export async function getCompanyContacts(
  contactId: string,
): Promise<CompanyContactReturnType[] | undefined> {
  if (!contactId) {
    console.error('Missing contactId')
    return undefined
  }

  try {
    const response = await fetchVitecHubApi<
      { contactRoles: ContactRole[] } | undefined
    >(`ContactRole/${contactId}`)

    if (!response || !response.contactRoles || !response.contactRoles.length) {
      return undefined
    }

    const contacts = await Promise.all(
      response.contactRoles.map(async (role) => {
        const contact = await getContact(role.childContactId)

        return {
          ...contact,
          role,
        }
      }),
    )

    return contacts.filter(isNotNull) as CompanyContactReturnType[]
  } catch (error) {
    console.info('[getCompanyContact]: Failed to fetch Vitec URL', error)
    return undefined
  }
}
