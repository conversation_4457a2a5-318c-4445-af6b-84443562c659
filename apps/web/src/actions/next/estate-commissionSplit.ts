'use server'

import { fetchVitecHub<PERSON>pi, vitecHubApiBaseUrl } from '@/api/vitec-hub-api'
import { MINUTE } from '@/db/util'
import { withCache } from '@/utils/with-cache'

import type { CommissionSplit } from './types'

/**
 * PROD - https://hub.megler.vitec.net/swagger/index.html#/Next/GetNextPath
 *
 * TEST - https://hubtest.megler.vitec.net/swagger/index.html#/Next/GetNextPath
 */
export async function estateCommissionSplit(
  estateId: string,
): Promise<{ commissionSplits: CommissionSplit[] }> {
  if (!estateId) {
    console.error('Missing estateId')
    return { commissionSplits: [] }
  }

  const url = new URL(
    `${vitecHubApiBaseUrl}/Estates/${estateId}/CommissionSplit`,
  )
  try {
    return withCache(
      url.toString(),
      () => fetchVitecHubApi(url.toString()),
      30 * MINUTE,
    )
  } catch (error) {
    console.error('[estateCommissionSplit]: Failed to fetch Vitec URL', error)
    return { commissionSplits: [] }
  }
}
