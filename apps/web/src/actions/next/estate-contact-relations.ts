'use server'

import { cache } from 'react'

import { fetchVitecHubApi, vitecHubApiBaseUrl } from '@/api/vitec-hub-api'

import { RelationType } from './types'

export type RelationsContact = {
  contactId: string
  proxyId: string | null
  registered: string
}

export type ContactRelation = {
  relationType: RelationType
  relationName: string
  contacts: RelationsContact[]
  groups: unknown[]
}

export const getEstateContactRelations = cache(
  async (
    estateId: string,
    relationType?: RelationType[],
  ): Promise<{ relations: ContactRelation[] } | undefined> => {
    if (!estateId) {
      console.error('Missing estateId')
      return undefined
    }

    const url = new URL(
      `${vitecHubApiBaseUrl}/Estates/${estateId}/AllContactRelations`,
    )

    if (relationType) {
      for (const type of relationType) {
        url.searchParams.append('relationType', type.toString())
      }
    }

    try {
      const response = await fetchVitecHubApi<{ relations: ContactRelation[] }>(
        url.toString(),
      )

      return response
    } catch (error) {
      console.info(
        '[getEstateContactRelations]: Failed to fetch Vitec URL',
        error,
      )
      return undefined
    }
  },
)
