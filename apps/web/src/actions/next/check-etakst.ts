'use server'

import { getCurrentUserOrThrow } from '@/lib/session'
import { addToEtakstCheckQueue } from '@/utils/etakst/etakst-checker'

import { getLatestValuation } from './estate-documents'

export async function checkForEtakst(estateId: string): Promise<boolean> {
  await getCurrentUserOrThrow()

  const hasEtakst = Boolean(await getLatestValuation(estateId))

  if (!hasEtakst) {
    await addToEtakstCheckQueue(estateId)
  }

  return hasEtakst
}
