'use server'

import { cache } from 'react'

import { NextPrivateContact } from '@/actions/next/types-next'
import { fetchVitecHubApi } from '@/api/vitec-hub-api'

/**
 * PROD - https://hub.megler.vitec.net/swagger/index.html
 *
 * TEST - https://hubtest.megler.vitec.net/swagger/index.html
 */
export const getContact = cache(
  async (contactId: string): Promise<NextPrivateContact | undefined> => {
    if (!contactId) {
      console.error('Missing contactId')
      return undefined
    }

    const url = `contacts/${contactId}/Private`

    try {
      const contact = await fetchVitecHubApi<NextPrivateContact>(url, {
        cache: 'no-cache',
      })

      return contact
    } catch (error) {
      console.info('[getContactSsn]: Failed to fetch Vitec URL', error)
      return undefined
    }
  },
)
