'use server'

import { cache } from 'react'

import { NextAssignmentBudget } from '@/actions/next/types-next'
import { fetchVitecHubApi } from '@/api/vitec-hub-api'
import { get, set } from '@/db/kv'
import { DAY } from '@/db/util'

export const getEstateBudget = cache(async function (estateId: string) {
  const cacheKey = `Estates/${estateId}/AssignmentBudget`

  try {
    const freshBudget = await fetchVitecHubApi<NextAssignmentBudget>(
      `Estates/${estateId}/AssignmentBudget`,
      { cache: 'no-cache' },
    )

    set(cacheKey, freshBudget, DAY)

    return freshBudget
  } catch (e) {
    console.info('Failed to fetch estate budget', e)
    const cached = await get<NextAssignmentBudget>(cacheKey)

    if (cached) {
      console.info('Returning cached estate budget', estateId)
      return cached
    }

    throw e
  }
})
