import { cache } from 'react'
import 'server-only'

import { NextEstateChecklistItem } from '@/actions/next/types-next'
import { fetchVitecHubApi } from '@/api/vitec-hub-api'

export type ChecklistReturnType = {
  checkListItems: NextEstateChecklistItem[]
  lastChanged: string
}

export const getChecklist = cache(
  async (estateId: string): Promise<ChecklistReturnType | undefined> => {
    if (!estateId) {
      console.error('Missing estateId')
      return undefined
    }

    const url = `estates/${estateId}/checkList`

    try {
      const checklist = await fetchVitecHubApi<ChecklistReturnType>(url, {
        cache: 'no-cache',
      })

      return checklist
    } catch (error) {
      console.info('[getChecklist]: Failed to fetch Vitec URL', error)
      return undefined
    }
  },
)
