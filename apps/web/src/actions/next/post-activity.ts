'use server'

import { fetchVitecHubApi } from '@/api/vitec-hub-api'

import { CreateActivityType } from './types-next'

type ActivityTypeName = 'Visning' | 'Oppdragsavtale'

// Activity Create Request Body
export interface ActivityCreateRequest {
  type?: CreateActivityType // Activity Type, if typeName is not provided
  typeName?: ActivityTypeName | null // Custom name for the activity type
  employeeId?: string // Reference to the user performing the activity
  start: string // Start time (ISO 8601 format)
  end: string // End time, must be the same day (ISO 8601 format)
  title: string // Title of the activity, limited to 63 characters
  note?: string | null // Optional note
  url?: string | null // Optional URL
  estateId?: string | null // Reference to the estate (either estateId or contactIds is required)
  contactIds?: string[] | null // Reference to one or more contacts (either estateId or contactIds is required)
  notification?: Notification | null // Optional notification settings
  private?: boolean // Whether the activity is private
  doneDate?: string | null // Date when the activity was completed (if already done)
}

// Notification settings
interface Notification {
  channel: 1 // Notification type (e.g., email, SMS)
  date?: string // Time before the event to trigger the notification
}

export async function postActivity(
  payload: ActivityCreateRequest,
): Promise<void> {
  try {
    await fetchVitecHubApi('Activities', {
      method: 'POST',
      body: JSON.stringify(payload),
    })
  } catch (error) {
    console.error(
      `[postEstateActivity]: Failed to post activity: ${JSON.stringify(payload)}`,
    )
    throw error
  }
}
