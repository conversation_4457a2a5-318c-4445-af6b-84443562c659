'use server'

import { fetchVitecHub<PERSON>pi, vitecHubApiBaseUrl } from '@/api/vitec-hub-api'

/**
 * PROD - https://hub.megler.vitec.net/swagger/index.html#/Next/SendSMS
 *
 * TEST - https://hubtest.megler.vitec.net/swagger/index.html#/Next/SendSMS
 */

export async function nextSendSMS({
  FromEmployeeId,
  UseSenderFallback,
  RecipientNumber,
  text,
  DepartmentId,
  ContactId,
  EstateId,
  Source,
}: {
  FromEmployeeId?: string
  UseSenderFallback: boolean
  RecipientNumber: string
  text: string
  DepartmentId: string
  ContactId: string
  EstateId?: string
  Source?: string
}): Promise<string | null> {
  if (!ContactId) {
    console.error('Missing contactId')
    return null
  }

  if (!RecipientNumber) {
    console.error('Missing RecipientNumber')
    return null
  }

  if (!text) {
    console.error('Missing text')
    return null
  }

  if (!DepartmentId) {
    console.error('Missing DepartmentId')
    return null
  }

  const url = new URL(`${vitecHubApiBaseUrl}/Next/SendSMS`)

  url.searchParams.append('RecipientNumber', RecipientNumber)
  url.searchParams.append('text', text)
  url.searchParams.append('DepartmentId', DepartmentId)
  url.searchParams.append('ContactId', ContactId)
  url.searchParams.append('UseSenderFallback', UseSenderFallback.toString())

  if (EstateId) {
    url.searchParams.append('EstateId', EstateId)
  }

  if (Source) {
    url.searchParams.append('Source', Source)
  }

  if (FromEmployeeId) {
    url.searchParams.append('FromEmployeeId', FromEmployeeId)
  }

  try {
    return await fetchVitecHubApi(url.toString(), { method: 'POST' })
  } catch (error) {
    console.error('[nextSendSMS]: Failed to send SMS', error)
    return null
  }
}
