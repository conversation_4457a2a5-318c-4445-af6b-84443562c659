import { NextEstateImage } from '@/actions/next/types-next'
import { fetchVitecHubApi } from '@/api/vitec-hub-api'

export async function estateImages(
  estateId: string,
): Promise<NextEstateImage[] | undefined> {
  if (!estateId) {
    console.error('Missing estateId')
    return undefined
  }

  try {
    const estateImages = await fetchVitecHubApi<NextEstateImage[]>(
      `Estates/${estateId}/Images`,
    )

    return estateImages
  } catch (error) {
    console.info('[getEstateImages]: Failed to fetch Vitec URL', error)
    return undefined
  }
}
