'use server'

import { fetchVitec<PERSON>ub<PERSON>pi, vitecHubApiBaseUrl } from '@/api/vitec-hub-api'
import { MINUTE } from '@/db/util'
import { withCache } from '@/utils/with-cache'

import type { Commission } from './types'

/**
 * PROD - https://hub.megler.vitec.net/swagger/index.html
 *
 * TEST - https://hubtest.megler.vitec.net/swagger/index.html
 */
export async function estateCommission(
  estateId: string,
): Promise<Commission | null> {
  if (!estateId) {
    console.error('Missing estateId')
    return null
  }

  const url = new URL(`${vitecHubApiBaseUrl}/Estates/${estateId}/Commission`)
  try {
    return withCache(
      url.toString(),
      () => fetchVitecHubApi(url.toString()),
      30 * MINUTE,
    )
  } catch (error) {
    console.error('[estateCommission]: Failed to fetch Vitec URL', error)
    return null
  }
}
