'use server'

import { fetchVitecHubApi, vitecHubApiBaseUrl } from '@/api/vitec-hub-api'

enum EstateMessageReceiver {
  Assignment = 1, // Send message to the agent
  Settlement = 2, // Send message to the settlement department
}

enum EstateMessageLinkType {
  EstateDocuments = 1,
  EstateImages = 2,
  EstateOverview = 3,
  EstateOrders = 4,
  EstateBidJournal = 5,
  EstateStakeholders = 6,
  EstateInterests = 7,
  EstateMortgage = 8,
}

interface EstateMessage {
  subject: string
  fromName?: string | null
  fromEmail?: string | null
  body: string // Message content. Must be plain text, no HTML.
  url?: string | null // URL to be included with the message (optional)
  messageReceiver?: EstateMessageReceiver
  estateMessageLinkType?: EstateMessageLinkType
}

export async function postEstateMessage(
  estateId: string,
  {
    messageReceiver = EstateMessageReceiver.Assignment,
    estateMessageLinkType = EstateMessageLinkType.EstateOverview,
    ...estateMessage
  }: EstateMessage,
): Promise<void> {
  if (!estateId) {
    console.error('Missing estateId')
    throw new Error('Missing estateId')
  }

  const url = new URL(`${vitecHubApiBaseUrl}/Estates/${estateId}/SendMessage`)

  try {
    await fetchVitecHubApi(url.toString(), {
      method: 'POST',
      body: JSON.stringify({
        ...estateMessage,
        messageReceiver,
        estateMessageLinkType,
      }),
    })
  } catch (error) {
    console.error(
      `[postEstateMessage]: Failed to post estate message for estateId: ${estateId}`,
    )
    throw error
  }
}
