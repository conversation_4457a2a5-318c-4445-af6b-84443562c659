import { cache } from 'react'

import { fetchVitecHub<PERSON>pi, vitecHubApiBaseUrl } from '@/api/vitec-hub-api'

import { NextPrivateContact } from './types-next'

export type PrivateProxy = {
  contactId: string
  proxy: NextPrivateContact
}

export type EstateContactInfo = {
  sellers: NextPrivateContact[]
  buyers: NextPrivateContact[]

  proxies: PrivateProxy[]
  titleHolders: NextPrivateContact[]
  heirs: NextPrivateContact[]
}

export const getEstateContactInfo = cache(
  async (estateId: string): Promise<EstateContactInfo | undefined> => {
    if (!estateId) {
      console.error('Missing estateId')
      return undefined
    }

    const url = new URL(
      `${vitecHubApiBaseUrl}/Estates/${estateId}/ContactInformation`,
    )

    try {
      const response = await fetchVitecHubApi<EstateContactInfo>(url.toString())

      return response
    } catch (error) {
      console.info('[getEstateContactInfo]: Failed to fetch Vitec URL', error)
      return undefined
    }
  },
)
