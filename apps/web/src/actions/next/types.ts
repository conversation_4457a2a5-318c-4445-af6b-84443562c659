export enum EstatePageCategory {
  EstateDocuments = '0',
  EstateImages = '1',
  EstateOverview = '2',
  EstateOrders = '3',
  EstateBidJournal = '4',
  ContactOverview = '5',
  ContactSearchProfile = '6',
  ContactOwnership = '7',
  ContactDocuments = '8',
  ContactBasicInformation = '9',
  EstateStakeholders = '10',
  EstateInterests = '11',
  ProjectDocuments = '12',
  ProjectImages = '13',
  ProjectOverview = '14',
  ProjectOrders = '15',
  ProjectBidJournal = '16',
  ProjectStakeholders = '17',
  ProjectInterests = '18',
  CommercialDocuments = '19',
  CommercialImages = '20',
  CommercialOverview = '21',
  CommercialOrders = '22',
  CommercialBidJournal = '23',
  CommercialStakeholders = '24',
  CommercialInterests = '25',
  EstateMortgage = '26',
  ProjectMortgage = '27',
  CommercialMortgage = '28',
}

export enum SigningStatus {
  NotSigned = 0, // Ikke signert
  ManuallySigned = 1, // Manuelt signert
  ElectronicallySigned = 2, // Elektronisk signert med signeringsinformasjon i dokumentet
}

export interface CommissionSplit {
  employeeId: string
  type: CommissionType
  commissionValue: number
}

export enum CommissionType {
  NotSet = 0,
  Percent = 1,
  FixedPrice = 2,
  Hourly = 3,
}

export interface Commission {
  type: CommissionType
  commissionType: string
  status: number
  sumCommission: number
  sumOtherIncome: number
  sumOutlay: number
  sumOtherExpenses: number
  sumTotal: number
  hourlySum: number
  hourlyEstimatedCommission: number
  percentBaseCommission: number
  percentCommonDebtIncluded: boolean
  percentEstimatedCommission: number
  fixedPriceCommission: number
  fixedPriceIncludes: unknown
  commissionSplits: {
    employeeId: string
    type: CommissionType
    percent: number
  }[]
}

export type ContactRole = {
  contactEstatesRelationType: number
  contactEstatesRelationTypeName: string
  contactRoleName: string
  companyContactRoleName: string
  roleId: string
  parentContactId: string
  childContactId: string
  ownerPercentage: number
  baseType: number
  typeId: string
  from: string | null
  to: string | null
}

export const contactRoleTypes = {
  0: 'Ikke definert',
  1: 'Ektefelle',
  2: 'Ansettelse',
  3: 'Aksjonær',
  4: 'Daglig leder',
  5: 'Styreleder',
  6: 'Styremedlem',
  7: 'Signaturberettiget',
  8: 'Prokurist',
  9: 'Reell rettighetshaver',
  10: 'Revisor',
  11: 'Firmakontaktperson',
}

export enum RelationType {
  Seller = 1,
  Buyer = 2,
  Viewing = 3,
  Bidder = 4,
  Invoicee = 5,
  OriginalBuyer = 6,
  HousingCoop = 7,
  LegalHolder = 8,
  LegalIssuer = 9,
  Mortagee = 10,
  Proxy = 11,
  Valuator = 12,
  Manager = 13,
  Interested = 14,
  Match = 15,
  LeaseHolder = 16,
  Creditor = 17,
  Debitor = 18,
  LegalRepresentative = 19,
  County = 20,
  StateMaps = 21,
  LandOwner = 22,
  FormerOwner = 23,
  Heir = 24,
  SellersSpouse = 25,
  AccountManagerSeller = 26,
  AccountManagerBuyer = 27,
}
