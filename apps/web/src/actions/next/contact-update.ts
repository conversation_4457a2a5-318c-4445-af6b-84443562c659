import { NextPrivateContact } from '@/actions/next/types-next'
import { fetchVitecHubApi } from '@/api/vitec-hub-api'
import cache from '@/db/kv'

export async function contactUpdate(
  fields: Partial<NextPrivateContact>,
): Promise<string | undefined> {
  if (!fields.contactId) {
    console.error('Missing contactId')
    return undefined
  }

  const key = `contacts/${fields.contactId}/Private`

  try {
    cache.del(key)

    const body = JSON.stringify(fields)

    const id = await fetchVitecHubApi<string>(`Contact`, {
      body,
      method: 'PUT',
    })

    return id
  } catch (error) {
    console.info('[getContactSsn]: Failed to fetch Vitec URL', error)
    throw new Error('Failed to update contact')
  }
}
