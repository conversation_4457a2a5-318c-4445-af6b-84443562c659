import { fetchVitecHubApi, vitecHubApiBaseUrl } from '@/api/vitec-hub-api'

// 1 = NextInternal (Tips opprettet i Next som går til en annen megler eller avdeling i Next)
// 2 = External (Tips opprettet f.eks. på hjemmeside eller portal og som sendes inn til Next)
// 3 = FromBank (Tips fra bank som sendes til Next)
// 4 = ToBank (Tips til bank som er opprettet i Next eller på hjemmeside)

export enum TipType {
  NextInternal = 1,
  External = 2,
  FromBank = 3,
  ToBank = 4,
}

export function createTipRef(identifier: string): string {
  const year = new Date().getFullYear()
  return `${year}-STOREBRAND-OPPDRAGSAVTALE-${identifier}`
}

export type TipPayload = {
  firstName: string
  lastName: string
  mobilePhone: string
  email?: string
  estateId?: string | null
  postalCode: string
  streetAddress?: string | null
  comment?: string
  source?: string
  productId?: string
  recipientId?: string
  type?: TipType
  userId?: string
  departmentId?: string
  contactId?: string
}

export async function postTip(
  payload: TipPayload,
  identifier: string,
): Promise<string> {
  const url = new URL(`${vitecHubApiBaseUrl}/Tips`)

  const payloadWithDefaults = {
    ...payload,
    type: payload.type ?? TipType.ToBank,
    source: payload.source ?? 'megler.nordvikbolig.no',
    comment: payload.comment
      ? [createTipRef(identifier), payload.comment ?? ''].join(', ')
      : createTipRef(identifier),
  }

  try {
    const response = await fetchVitecHubApi<string>(url.toString(), {
      method: 'POST',
      body: JSON.stringify(payloadWithDefaults),
    })

    console.info(
      `[postTip]: Successfully posted tip: ${JSON.stringify(payloadWithDefaults)}`,
    )

    return response
  } catch (error) {
    console.error(
      `[postTip]: Failed to post tip: ${JSON.stringify(payloadWithDefaults)}`,
      error,
    )
    throw error
  }
}
