'use server'

import mailChimp, {
  MessagesSendTemplateRequest,
} from '@mailchimp/mailchimp_transactional'

import { prisma } from '@/db/prisma'
import { generateFailedMessageId } from '@/lib/email-utils'
import { isProd } from '@/lib/getBaseUrl'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { isNotNull } from '@/server/utils'

import { employeeMailSignature } from './employee-signature'

const mailchimpTx = mailChimp(process.env.MAILCHIMP_API_KEY!)

type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E }

async function safeAsync<T>(
  operation: () => Promise<T>,
  errorMessage: string,
): Promise<Result<T, string>> {
  try {
    const result = await operation()
    return { success: true, data: result }
  } catch (error) {
    console.error(errorMessage, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : errorMessage,
    }
  }
}

async function createFailedEmailAuditRecords(
  emailAuditId: string,
  emails: { email: string; name?: string; contactId?: string }[],
  error: Error,
): Promise<void> {
  const createFailedRecords = async () => {
    await Promise.all(
      emails.map((email) =>
        prisma.email_audit_recipient.create({
          data: {
            email_audit_id: emailAuditId,
            recipient_email: email.email,
            contact_id: email.contactId || null,
            message_id: generateFailedMessageId(),
            status: 'failed',
            sent_at: new Date(),
            reject_reason: error.message || 'Failed to send via Mandrill API',
            raw_last_event: {
              error: error.message,
              timestamp: new Date().toISOString(),
            },
          },
        }),
      ),
    )
  }

  const result = await safeAsync(
    createFailedRecords,
    'Failed to create failed email audit records',
  )

  if (!result.success) {
    // Log the error but don't throw - this is a non-critical operation
    console.error('Audit record creation failed:', result.error)
  }
}

async function seedRecipientRows(
  emailAuditId: string,
  mandrillResponse: unknown[],
  emails: { email: string; name?: string; contactId?: string }[],
): Promise<void> {
  const contactIdByEmail = new Map<string, string | undefined>()
  for (const e of emails) {
    contactIdByEmail.set(e.email.toLowerCase(), e.contactId)
  }

  const seedOperation = async () => {
    await Promise.all(
      mandrillResponse.map((r) => {
        const resp = r as {
          email?: string
          _id?: string
          status?: string
          reject_reason?: string
        }
        const email = resp.email
        const messageId = resp._id
        if (!email || !messageId) return Promise.resolve()

        const status = resp.status || 'sent'
        let rejectReason: string | null = null

        if (status === 'rejected' || status === 'invalid') {
          rejectReason = resp.reject_reason || 'Email was rejected by Mandrill'
        }

        return prisma.email_audit_recipient.upsert({
          where: {
            message_id_recipient_email: {
              message_id: messageId,
              recipient_email: email,
            },
          },
          create: {
            email_audit_id: emailAuditId,
            recipient_email: email,
            contact_id: contactIdByEmail.get(email.toLowerCase()) || null,
            message_id: messageId,
            status: status,
            sent_at: new Date(),
            reject_reason: rejectReason,
            raw_last_event: resp,
          },
          update: {},
        })
      }),
    )
  }

  const result = await safeAsync(
    seedOperation,
    'Failed seeding email_audit_recipient rows',
  )

  if (!result.success) {
    console.error('Recipient seeding failed:', result.error)
  }
}

export async function sendMail({
  title,
  body,
  subject,
  from,
  emails,
  attachments,
  includeSignatureForEmployeeId,
  blindCopyTo,
  initialReceiver,
  context,
  sendAt,
}: {
  title: string
  body: string
  subject: string
  from: { email: string; name: string }
  emails: { email: string; name?: string; contactId?: string }[]
  includeSignatureForEmployeeId?: string
  attachments?: MessagesSendTemplateRequest['message']['attachments']
  blindCopyTo?: string
  initialReceiver?:
    | { email?: string; name?: string; role?: string }
    | { email?: string; name?: string; role?: string }[]
  context?: { type?: string; id?: string }
  // UTC timestamp in YYYY-MM-DD HH:MM:SS format
  sendAt?: string
}) {
  if (!isProd) {
    body = appendDebugInfo(
      body,
      Array.isArray(initialReceiver)
        ? initialReceiver.filter(isNotNull)
        : [initialReceiver].filter(isNotNull),
      blindCopyTo,
    )

    emails =
      process.env.TEST_EMAIL?.split(';').map((email) => ({
        email,
        name: 'Test User',
      })) ?? []

    // It has to use @norvikbolig.no to be able to send to the test email
    blindCopyTo = '<EMAIL>'
  }

  if (includeSignatureForEmployeeId) {
    const apiResponse = await nordvikApi.employeeSessionInfo({
      employeeId: includeSignatureForEmployeeId,
    })

    const employee = apiResponse.employee

    if (employee) {
      const signature = employeeMailSignature({
        ...from,
        ...employee,
        image: employee?.image?.small,
      })

      body += signature
    } else {
      console.error(
        `Failed to fetch employee data for employeeId: ${includeSignatureForEmployeeId}`,
      )
    }
  }

  const content = [
    {
      name: 'text',
      content: body,
    },
    {
      name: 'source',
      content: `<a href="https://www.nordvikbolig.no/" style="color: #656565;">nordvikbolig.no</a>`,
    },
  ]

  content.push({
    name: 'header',
    content: title,
  })

  // Create and persist the email_audit row (generic audit header)
  const emailAudit = await prisma.email_audit.create({
    data: {
      subject,
      from_email: from.email,
      template_name: 'transactional-template',
      context_type: context?.type || 'unknown',
      context_id: context?.id || null,
    },
  })

  // Build global + per-recipient metadata for Mandrill. ONLY send email_audit_id (webhook links on this).
  const metadata: { website: string; email_audit_id: string } = {
    website: 'nordvikbolig.no',
    email_audit_id: emailAudit.id,
  }

  const recipient_metadata = emails
    .filter((e) => e.contactId)
    .map((e, index) => ({
      rcpt: e.email,
      values: {
        user_id: index,
        employee_id: includeSignatureForEmployeeId,
        contact_id: e.contactId,
        email_audit_id: emailAudit.id,
      },
    }))

  // Try to send email via Mandrill
  const mandrillResult = await safeAsync(
    () =>
      mailchimpTx.messages.sendTemplate({
        template_name: 'transactional-template',
        template_content: content,
        message: {
          subject,
          from_email: from.email,
          from_name: from.name,
          to: emails,
          bcc_address: blindCopyTo,
          headers: {
            'Reply-To': from.email,
          },
          attachments,
          metadata,
          recipient_metadata:
            recipient_metadata.length > 0 ? recipient_metadata : undefined,
        },
        send_at: sendAt ?? undefined,
      }),
    'Failed to send email via Mandrill',
  )

  // Handle email sending failure
  if (!mandrillResult.success) {
    const sendingError = new Error(mandrillResult.error)

    // Create failed recipient records
    await createFailedEmailAuditRecords(emailAudit.id, emails, sendingError)

    return {
      mandrillResponse: null,
      emailAuditId: emailAudit.id,
      error: sendingError.message,
      success: false,
    }
  }

  // Email sent successfully, seed recipient rows
  if (Array.isArray(mandrillResult.data)) {
    await seedRecipientRows(emailAudit.id, mandrillResult.data, emails)
  }

  return {
    mandrillResponse: mandrillResult.data,
    emailAuditId: emailAudit.id,
    success: true,
  }
}

export async function linkEmailAuditToInspectionAudit(params: {
  inspectionFoldersAuditId: number
  emailAuditId: string
}): Promise<Result<boolean, string>> {
  const { inspectionFoldersAuditId, emailAuditId } = params

  return safeAsync(async () => {
    await prisma.email_audit.update({
      where: { id: emailAuditId },
      data: {
        inspection_folders_audit: { connect: { id: inspectionFoldersAuditId } },
      },
    })
    return true
  }, 'Failed linking inspection_folders_audit to email_audit')
}

function appendDebugInfo(
  body: string,
  initialReceiver: { email?: string; name?: string; role?: string }[],
  blindCopyTo: string | undefined,
) {
  const debugHeader = `
  <div style="padding: 10px; background-color: #f0f0f0; border-radius: 5px; border: 1px solid red; margin-bottom: 10px;">
  <p style="color: black;">Initial receivers:</p>
  ${initialReceiver.length > 0 ? `<ul>${initialReceiver.map((r) => `<li style="color: black;">${r.email ?? 'Unknown email'} ${r.name ?? ' '} (${r.role ?? ' '})</li>`).join('')}</ul>` : ''}
  ${blindCopyTo ? `<p style="color: black;">Blind copy to: ${blindCopyTo}</p>` : ''}
  </div>
  `

  return debugHeader + body
}
