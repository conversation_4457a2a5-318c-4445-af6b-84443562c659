const VITEC_URL = process.env.VITEC_URL
const VITEC_INSTALLATION_ID = process.env.VITEC_INSTALLATION_ID
const USER = process.env.VITEC_USER
const PASSWORD = process.env.VITEC_PASSWORD

if (!VITEC_URL || !VITEC_INSTALLATION_ID || !USER || !PASSWORD) {
  throw new Error('Missing VITEC environment variables')
}

export const getVitecAuth = () =>
  `Basic ${Buffer.from(`${USER}:${PASSWORD}`).toString('base64')}`

export const nextBaseUrl = `${VITEC_URL}/${VITEC_INSTALLATION_ID}`
