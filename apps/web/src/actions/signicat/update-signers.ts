'use server'

import prisma from '@/db/prisma'
import signicat from '@/external-services/signicat/client'

export async function updateSigners(listingAgreement: {
  id: string
  signicat_document_id: string
  estate_id: string
}) {
  try {
    const signers = await signicat.listSigners(
      listingAgreement.signicat_document_id,
    )

    const transactions = signers.map(async (signer) => {
      const signed_at =
        signer.documentSignature?.signedTime ??
        (signer.url ? undefined : new Date())

      const existingSigner = await prisma.signicat_signer.findFirst({
        where: {
          external_signer_id: signer.externalSignerId,
          listing_agreements_id: listingAgreement.id,
        },
      })

      if (existingSigner?.id) {
        const operations: Promise<unknown>[] = []
        if (signed_at && !existingSigner.signed_at) {
          operations.push(
            prisma.signicat_signer.update({
              where: {
                id: existingSigner.id,
              },
              data: {
                signed_at,
                url: signer.url,
              },
            }),
          )
        } else if (existingSigner.url !== signer.url) {
          operations.push(
            prisma.signicat_signer.update({
              where: {
                id: existingSigner.id,
              },
              data: {
                url: signer.url,
              },
            }),
          )
        }

        return Promise.all(operations)
      }

      return prisma.signicat_signer.create({
        data: {
          id: signer.id,
          external_signer_id: signer.externalSignerId,
          first_name: signer.signerInfo.firstName,
          last_name: signer.signerInfo.lastName,
          signed_at,
          url: signer.url,
          listing_agreements_id: listingAgreement.id,
          email: signer.signerInfo.email!,
          title: signer.signerInfo.title,
        },
      })
    })

    await Promise.all(transactions)

    console.info(
      `Synced ${signers.length} signers for listing agreement ${listingAgreement.id}`,
    )
  } catch (error) {
    console.error(`Sync Signicat signers error: ${error}`)
  }
}
