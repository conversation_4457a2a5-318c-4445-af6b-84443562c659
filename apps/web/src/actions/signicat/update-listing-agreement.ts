'use server'

import { differenceInDays, differenceInMinutes } from 'date-fns'
import { isBefore } from 'date-fns/isBefore'
import { subMinutes } from 'date-fns/subMinutes'

import { DocumentStatus } from '@nordvik/signicat-express-sdk/types'

import { EstateStatusNumber } from '@/app/(protected)/(sidebar)/oppdrag/util'
import { cancelEstateDocuments } from '@/app/api/signicat/util/cancel-estate-documents'
import { getEstateStatus } from '@/app/api/signicat/util/get-estate-status'
import { processSignedDocumentEvent } from '@/app/api/signicat/util/process-signed-document-event'
import prisma from '@/db/prisma'
import signicat from '@/external-services/signicat/client'

const SIGNED_BACKOFF_MINUTES = 10

export async function updateListingAgreement(listingAgreement: {
  id: string
  estate_id: string
  signicat_document_id: string
}) {
  try {
    // Ensure we only keep the newest active document for this estate and cancel older duplicates
    const summary = await ensureNewestActiveDocument(
      listingAgreement.estate_id,
      listingAgreement.signicat_document_id,
    )

    await checkIfDocumentIsSignedOrDeadlineChanged({
      estateId: listingAgreement.estate_id,
      signedDate: summary.signedDate,
      deadline: summary.deadline,
      documentId: summary.documentId,
    })

    const estateStatus = await getEstateStatus(listingAgreement.estate_id)

    if (
      estateStatus === EstateStatusNumber.InPreperation &&
      summary.status.documentStatus !== DocumentStatus.Signed &&
      differenceInDays(new Date(), summary.lastUpdated) > 2
    ) {
      console.info(
        `Estate ${listingAgreement.estate_id} has status ${estateStatus} and document status ${summary.status.documentStatus}`,
      )

      try {
        await cancelEstateDocuments(
          listingAgreement.estate_id,
          'Signed agreement in Vitec Next',
        )
      } catch (error) {
        console.error(`Failed to cancel estate documents: ${error}`)
      }
    } else if (estateStatus === EstateStatusNumber.Lost) {
      console.info(
        `Estate ${listingAgreement.estate_id} has status ${estateStatus} and has an unsigned document in Signicat`,
      )
      try {
        await cancelEstateDocuments(
          listingAgreement.estate_id,
          'Estate lost in Vitec Next',
        )
      } catch (error) {
        console.error(`Failed to cancel estate documents: ${error}`)
      }
    } else if (estateStatus !== 0) {
      console.warn(
        `Estate ${listingAgreement.estate_id} has status ${estateStatus} and has an unsigned document in Signicat`,
      )
    }
  } catch (error) {
    console.error(`Sync Signicat summary error: ${error}`)
  }
}

export async function checkIfDocumentIsSignedOrDeadlineChanged({
  estateId,
  signedDate,
  deadline,
  documentId,
}: {
  estateId: string
  signedDate?: string | null
  deadline: string | null
  documentId: string
}) {
  if (
    signedDate &&
    // Give the event the chance to be fired, don't be too eager
    isBefore(
      new Date(signedDate),
      subMinutes(new Date(), SIGNED_BACKOFF_MINUTES),
    )
  ) {
    console.info(
      `Found signed document for estate ${estateId} during sync, signed ${differenceInMinutes(signedDate, new Date())} minutes ago`,
    )
    await processSignedDocumentEvent({
      externalId: estateId,
      id: documentId,
    })
  } else {
    await prisma.listing_agreements.updateMany({
      where: { estate_id: estateId },
      data: {
        deadline_for_signing: deadline,
      },
    })
  }
}

// Keep the newest active document for an estate and cancel older active ones
async function ensureNewestActiveDocument(
  estateId: string,
  currentDocumentId: string,
) {
  // Fetch all document summaries for this estate
  const { data } = await signicat.listDocumentSummaries({
    externalId: estateId,
  })

  if (!data || data.length === 0) {
    // Fallback to the current document if API returns nothing
    return await signicat.getDocumentSummary(currentDocumentId)
  }

  // Consider a document active if it's not in a terminal state
  const terminalStatuses = [
    DocumentStatus.Signed,
    DocumentStatus.Canceled,
    DocumentStatus.Expired,
  ]

  const activeDocs = data.filter((d) =>
    d.status?.documentStatus
      ? !terminalStatuses.includes(d.status.documentStatus)
      : true,
  )

  // If no active docs, keep current summary
  if (activeDocs.length === 0) {
    return await signicat.getDocumentSummary(currentDocumentId)
  }

  // Sort by created desc to find the newest document
  activeDocs.sort(
    (a, b) => Number(new Date(b.created)) - Number(new Date(a.created)),
  )
  const newest = activeDocs[0]

  // Cancel all other active documents
  await Promise.all(
    activeDocs
      .filter((d) => d.documentId !== newest.documentId)
      .map(async (d) => {
        try {
          await signicat.cancelDocument(
            d.documentId,
            'Duplicate document (keeping newest)',
          )
          console.info(
            `Cancelled duplicate document ${d.documentId} for estate ${estateId}`,
          )
        } catch (error) {
          console.warn(
            `Failed to cancel duplicate document ${d.documentId} for estate ${estateId}: ${error}`,
          )
        }
      }),
  )

  // If the newest differs from the one we have stored, update DB to point to the newest
  if (newest.documentId !== currentDocumentId) {
    try {
      await prisma.listing_agreements.updateMany({
        where: { estate_id: estateId, signing_finished_at: null },
        data: {
          signicat_document_id: newest.documentId,
          deadline_for_signing: newest.deadline ?? null,
          updated_at: new Date(),
        },
      })
    } catch (error) {
      console.error(
        `Failed to update listing agreement to newest document for estate ${estateId}: ${error}`,
      )
    }
  }

  return newest
}
