'use server'

import { RedirectType, redirect } from 'next/navigation'

import { getCurrentUser } from '@/lib/session'

export async function redirectFromErrorPage(redirectTo?: string) {
  const user = await getCurrentUser()

  if (user) {
    return redirect(redirectTo || '/', RedirectType.replace)
  } else {
    const appUrl = process.env.NEXT_PUBLIC_NORDVIK_APP_URL
      ? `${process.env.NEXT_PUBLIC_NORDVIK_APP_URL}/customer/home/<USER>
      : null

    return redirect(redirectTo || appUrl || '/', RedirectType.replace)
  }
}
