import { InvokeCommand } from '@aws-sdk/client-lambda'

import lambda from '@/external-services/aws/lambda'

export async function htmlToPdf(html: string) {
  console.info(`Sending html to pdf lambda with length ${html.length}`)

  const command = new InvokeCommand({
    FunctionName: 'nordvik-lambda-production-pdfGen',
    InvocationType: 'RequestResponse',
    Payload: JSON.stringify({ html, format: 'a4' }),
  })

  try {
    const response = await lambda.send(command)

    if (response.FunctionError || !response.Payload) {
      console.error('Lambda error:', response.FunctionError)
      throw new Error('Error invoking lambda')
    }

    const resp = JSON.parse(Buffer.from(response.Payload).toString()) as {
      body: string
    }

    return resp.body
  } catch (error) {
    console.error('Error invoking lambda:', error)
    throw new Error('Error invoking lambda')
  }
}
