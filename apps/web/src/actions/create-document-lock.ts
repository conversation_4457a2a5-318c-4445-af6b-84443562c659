import cache from '@/db/kv'
import { CACHE_KEYS } from '@/lib/cache-keys'

function getLockKey(estateId: string) {
  return CACHE_KEYS.ESTATE.LOCK(estateId)
}

export async function acquireLock(
  estateId: string,
  ttl: number = 40,
): Promise<boolean> {
  const key = getLockKey(estateId)
  // try to set the key atomically if it does not exist.
  const result = await cache.set(key, estateId, { ex: ttl, nx: true })
  // if result is null (or false), then the key was not set.
  return Boolean(result)
}

export async function releaseLock(estateId: string): Promise<void> {
  await cache.del(getLockKey(estateId))
}
