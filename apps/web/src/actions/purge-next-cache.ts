'use server'

import { revalidatePath } from 'next/cache'

import { syncEstate } from '@/actions/sync/sync-estate'
import { syncLatest } from '@/actions/sync/sync-latest'
import cache from '@/db/kv'
import { CACHE_PATTERNS } from '@/lib/cache-keys'
import { getCurrentUserAsBrokerOrThrow } from '@/lib/session'
import { estatesForBrokerIdCountCacheKey } from '@/server/model/BrokerEstate/resolvers'

export const purgeBrokerEstatesCache = async (pathToRevalidate?: string) => {
  const user = await getCurrentUserAsBrokerOrThrow()

  let count = 0
  try {
    const response = await syncLatest(user.employeeId)
    count = response?.count || 0
    console.info(
      `Synced latest estates for ${user.employeeId}, ${count} estates`,
    )
  } catch (error) {
    console.error(`Failed to sync latest estates for ${user.employeeId}`, error)
  }

  const keys = await cache.keys(
    CACHE_PATTERNS.ESTATE.FOR_BROKER_ALL(user.employeeId),
  )

  await cache.del(...keys, estatesForBrokerIdCountCacheKey(user?.employeeId))

  if (pathToRevalidate) {
    revalidatePath(pathToRevalidate)
  }

  return count
}

export const purgeBrokerEstateCache = async ({
  estateId,
  pathToRevalidate,
}: {
  estateId: string
  pathToRevalidate?: string
}) => {
  const keys = await cache.keys(CACHE_PATTERNS.ESTATE.BY_ID_ALL(estateId))

  try {
    await syncEstate(estateId)
  } catch (error) {
    console.error(`Failed to sync estate ${estateId}`, error)
  }

  if (keys.length) {
    await cache.del(...keys)
  }

  if (pathToRevalidate) {
    revalidatePath(pathToRevalidate)
  }
}
