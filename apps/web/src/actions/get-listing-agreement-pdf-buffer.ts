'use server'

import prisma from '@/db/prisma'
import { getFile } from '@/external-services/aws/s3'
import signicat from '@/external-services/signicat/client'

export async function getListingAgreementPdfBuffer(estate_id: string) {
  const listing = await prisma.listing_agreements.findUnique({
    where: { estate_id },
    select: {
      s3_signed_document_key: true,
      signing_finished_at: true,
      signicat_document_id: true,
    },
  })

  if (!listing?.signicat_document_id) {
    return undefined
  }

  if (!listing.signing_finished_at) {
    try {
      const blob = await signicat.getFile(
        listing.signicat_document_id,
        'unsigned',
      )

      const buffer = await blob.arrayBuffer()
      return Buffer.from(buffer).toString('base64')
    } catch (error) {
      console.error('error', error)
      return undefined
    }
  } else {
    if (!listing.s3_signed_document_key) {
      return undefined
    }

    return getFile(listing.s3_signed_document_key)
  }
}
