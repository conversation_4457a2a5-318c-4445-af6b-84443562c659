import gql from 'graphql-tag'

import { get, set } from '@/db/kv'
import { DAY } from '@/db/util'
import { executeRawQuery } from '@/server/nordvik-client-adaptor'

export async function riskCompletionCheck(estateId: string): Promise<{
  isComplete: boolean
  changedDate?: string
}> {
  const cacheKey = `riskCompletionCheck:v2:${estateId}`

  const cached = await get<
    | {
        isComplete: boolean
        changedDate: string
      }
    | undefined
  >(cacheKey)

  if (cached) {
    return cached
  }

  let checklist: {
    firstTag: string
    value: number
    changedDate: string
  }[] = []

  const response = await executeRawQuery<{
    estate: {
      checklist: {
        firstTag: string
        value: number
        changedDate: string
      }[]
    }
  }>(
    gql`
      query checklist($estateId: String!) {
        estate(id: $estateId, statuses: [-1, 0, 1, 2, 3, 4, 5, 7, 8, 9]) {
          checklist {
            firstTag
            value
            changedDate
          }
        }
      }
    `,
    {
      estateId,
    },
  )

  checklist = response.estate?.checklist

  const riskTags = ['AML_DONE', 'AMLoppslogiver']

  const riskChecklistItem = checklist?.find((checkmark) => {
    for (const tag of riskTags) {
      if (checkmark.firstTag.startsWith(tag)) {
        return true
      }
    }
  })

  const isComplete = riskChecklistItem?.value === 1

  // if true we can cache the result
  if (isComplete && riskChecklistItem?.changedDate) {
    set(
      cacheKey,
      {
        isComplete,
        changedDate: riskChecklistItem.changedDate,
      },
      DAY * 30,
    )
  }

  return {
    isComplete,
    changedDate: riskChecklistItem?.changedDate,
  }
}
