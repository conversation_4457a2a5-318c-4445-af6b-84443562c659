import { renderNotifyBrokerOfLeadsEmail } from '@/lib/email-template/notify-broker-of-leads'
import Estate from '@/server/model/BrokerEstate/model'

import { sendMail } from './mail/sendMail'

export async function notifyBrokerOfLead(estate: Estate) {
  try {
    const broker = await estate.broker

    if (!broker) {
      console.warn(
        `No broker found for estate ${estate.estateId}, skipping notification`,
      )
      return
    }

    const body = await renderNotifyBrokerOfLeadsEmail({
      isValuation: estate.isValuation,
      brokerName: estate.broker?.name,
      address: estate.address.streetAddress,
    })

    await sendMail({
      title: '',
      emails: [
        {
          email: broker.email,
          name: broker.name,
        },
      ],
      subject: `Lead er sendt til Storebrand for ${estate.address.streetAddress}`,
      body,
      from: {
        email: process.env.NO_REPLY_EMAIL ?? '<EMAIL>',
        name: 'Nord<PERSON>',
      },
      initialReceiver: { email: broker.email, name: broker.name },
      context: {
        type: 'lead_notification',
        id: estate.id ?? undefined,
      },
    })
  } catch (error) {
    console.error(
      `Failed to send lead notification email to broker for estate ${estate.id}:`,
      error,
    )
  }
}
