'use server'

import prisma from '@/db/prisma'
import { getBaseUrl } from '@/lib/getBaseUrl'
import retry from '@/lib/retry'

export async function getShortUrl(url: string, createIfNotExists = true) {
  const existingShortUrl = await prisma.urls.findFirst({
    where: {
      longUrl: url,
      deletedAt: null,
    },
  })

  if (existingShortUrl?.shortUrl) {
    return prependShortUrl(existingShortUrl.shortUrl)
  }

  if (!createIfNotExists) {
    return null
  }

  const newShortUrl = Math.random().toString(36).substring(8)

  retry(() =>
    prisma.urls.create({
      data: {
        shortUrl: newShortUrl,
        longUrl: url,
      },
    }),
  )

  return prependShortUrl(newShortUrl)
}

function prependShortUrl(shortUrl: string) {
  return `${getBaseUrl()}/l/${shortUrl}`
}
