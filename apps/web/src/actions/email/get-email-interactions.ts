'use server'

import { addSeconds } from 'date-fns'

import { Prisma } from '@nordvik/database'

import { prisma } from '@/db/prisma'
import {
  isEmailBounced,
  isEmailFailureStatus,
  isEmailRejectedOrFailed,
} from '@/lib/email-utils'
import { EmailEventType } from '@/types/email'

/**
 * Determines if an email should be considered as opened
 */
function shouldShowAsOpened(recipient: {
  opened_at: Date | null
  status: string
  open_count: number
}): boolean {
  return (
    (recipient.opened_at ||
      (recipient.status === 'opened' && recipient.open_count > 0)) &&
    !isEmailFailureStatus(recipient.status)
  )
}

export async function getEmailInteractionsForEstate(estateId: string) {
  const if_audits = await prisma.inspection_folders_audit.findMany({
    where: {
      estate_id: estateId,
    },
    select: {
      email_audits: {
        select: {
          id: true,
          recipients: true,
        },
      },
    },
  })

  const emailInteractions: {
    id: string
    eventType: EmailEventType
    eventTimestamp: string
    recipientEmail: string
    contactId?: string
    messageId: string
    openCount?: number
    emailAuditId: string
    bounceType?: string
    rejectReason?: string
  }[] = []

  const emailAudits = if_audits
    .flatMap((audit) => audit.email_audits)
    .filter((email): email is NonNullable<typeof email> => !!email)

  for (const emailAudit of emailAudits) {
    for (const recipient of emailAudit.recipients) {
      // Add some seconds prevent showing bounced or error emails before the sended entry
      const eventTimestamp = addSeconds(
        recipient.last_event_at || recipient.sent_at,
        10,
      ).toISOString()

      // Handle bounced emails
      if (isEmailBounced(recipient.status)) {
        emailInteractions.push({
          id: `bounced_${recipient.id}`,
          eventType: EmailEventType.BOUNCED,
          eventTimestamp,
          recipientEmail: recipient.recipient_email,
          contactId: recipient.contact_id || undefined,
          messageId: recipient.message_id,
          emailAuditId: emailAudit.id,
          bounceType: recipient.bounce_type || undefined,
        })
      }

      // Handle rejected or failed emails
      if (isEmailRejectedOrFailed(recipient.status)) {
        emailInteractions.push({
          id: `rejected_${recipient.id}`,
          eventType:
            recipient.status === 'failed'
              ? EmailEventType.FAILED
              : EmailEventType.REJECTED,
          eventTimestamp,
          recipientEmail: recipient.recipient_email,
          contactId: recipient.contact_id || undefined,
          messageId: recipient.message_id,
          emailAuditId: emailAudit.id,
          rejectReason: recipient.reject_reason || undefined,
        })
      }

      // Only add email opened interaction if it should be shown as opened
      if (shouldShowAsOpened(recipient)) {
        // Use opened_at if available, otherwise fall back to last_event_at
        let openedTimestamp = recipient.opened_at || recipient.last_event_at

        if (!openedTimestamp) {
          const rawMsg = (recipient.raw_last_event as Prisma.JsonObject)?.msg
          if (rawMsg && typeof rawMsg === 'object' && rawMsg !== null) {
            const msgObj = rawMsg as Prisma.JsonObject
            const opens = msgObj.opens
            if (Array.isArray(opens) && opens.length > 0) {
              const firstOpen = opens[0] as Prisma.JsonObject
              const timestamp = firstOpen.ts
              if (typeof timestamp === 'number') {
                openedTimestamp = new Date(timestamp * 1000)
              }
            }
          }
        }

        if (openedTimestamp) {
          emailInteractions.push({
            id: `opened_${recipient.id}`,
            eventType: EmailEventType.OPENED,
            eventTimestamp: openedTimestamp.toISOString(),
            recipientEmail: recipient.recipient_email,
            contactId: recipient.contact_id || undefined,
            messageId: recipient.message_id,
            openCount: recipient.open_count,
            emailAuditId: emailAudit.id,
          })
        }
      }
    }
  }

  return emailInteractions.sort(
    (a, b) =>
      new Date(b.eventTimestamp).getTime() -
      new Date(a.eventTimestamp).getTime(),
  )
}
