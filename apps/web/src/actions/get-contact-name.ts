import { getContactById } from '@/server/model/Contact/factory'

export async function safeGetContactName(
  contactId: string,
): Promise<string | undefined> {
  try {
    const contact = await getContactById(contactId)
    if (!contact) return undefined
    const name = `${contact.firstName ?? ''} ${contact.lastName ?? ''}`.trim()
    return name || undefined
  } catch (e) {
    console.warn('Failed to fetch contact for revisit email', e)
    return undefined
  }
}
