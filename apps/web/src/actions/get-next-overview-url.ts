'use server'

import { EstatePageCategory } from '@/actions/next/types'

import { nextGetPath } from './next/nextGetPath'

export async function getNextOverviewUrl(estateId: string) {
  try {
    return nextGetPath(estateId, EstatePageCategory.EstateOverview)
  } catch (error) {
    console.error('[getEstateVitecUrl]: Failed to fetch Vitec URL', error)
    return ''
  }
}

// Sellers
export async function getNextStakeholdersUrl(estateId: string) {
  try {
    return nextGetPath(estateId, EstatePageCategory.EstateStakeholders)
  } catch (error) {
    console.error('[getEstateVitecUrl]: Failed to fetch Vitec URL', error)
    return ''
  }
}
