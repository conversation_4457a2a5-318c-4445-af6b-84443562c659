'use server'

import DataLoader from 'dataloader'

import signicat from '@/external-services/signicat/client'

export async function getDocumentSummary(
  documentId: string | null | undefined,
) {
  if (!documentId) {
    return { data: undefined, error: 'No document ID provided' }
  }
  return documentSummaryLoader.load(documentId)
}

export async function listSigners(documentId: string) {
  if (!documentId) {
    return { data: undefined, error: 'No document ID provided' }
  }
  return signersLoader.load(documentId)
}

const documentSummaryLoader = new DataLoader((documentIds: string[]) =>
  Promise.all(
    documentIds.map(async (documentId: string) => {
      try {
        const data = await signicat.getDocumentSummary(documentId)
        return { data, error: undefined }
      } catch (error) {
        console.error('Failed to get document summary', error)
        return { data: undefined, error }
      }
    }),
  ),
)

const signersLoader = new DataLoader((documentIds: string[]) =>
  Promise.all(
    documentIds.map(async (documentId: string) => {
      try {
        const data = await signicat.listSigners(documentId)
        return { data, error: undefined }
      } catch (error) {
        console.error('Failed to list signees', error)
        return { data: null, error }
      }
    }),
  ),
)
