import prisma from '@/db/prisma'

export type InspectionSummary = Awaited<
  ReturnType<typeof getInspectionsSummary>
>[number]

export async function getInspectionsSummary({
  from,
  to,
}: {
  from: Date
  to: Date
}) {
  const inspections = await prisma.inspection_folders.findMany({
    where: {
      sent_at: {
        gte: from,
        lte: to,
      },
      inspection_folders_audit: {
        some: {},
      },
      created_by: {
        employeeId: {
          not: 'UNNO',
        },
      },
    },
    include: {
      inspection_event: {
        select: { id: true, created_at: true },
      },
      excluded_partners: {
        select: {
          name: true,
        },
      },
      inspection_folders_audit: {
        select: {
          id: true,
          sent_at: true,
          sent_by: {
            select: {
              employeeId: true,
              name: true,
            },
          },
          listing_agreement_active: true,
        },
        orderBy: {
          sent_at: 'desc',
        },
      },
      listing_agreement: {
        select: { signing_finished_at: true, initiated_signing_at: true },
      },
    },
    orderBy: {
      sent_at: 'desc',
    },
  })

  const list = inspections.map((inspection) => {
    const firstSentDate = inspection.inspection_folders_audit[0].sent_at
    const eventsBeforeFirstSentCount = inspection.inspection_event.filter(
      (event) => event.created_at < firstSentDate,
    ).length

    return {
      id: inspection.id,
      estateId: inspection.estate_id,
      // check if the inspection has been sent with agreement at some point
      withAgreement: inspection.inspection_folders_audit.some(
        (audit) => audit.listing_agreement_active,
      ),
      eventsBeforeFirstSentCount,
      eventsAfterFirstSentCount:
        inspection.inspection_event.length - eventsBeforeFirstSentCount,
      broker: inspection.inspection_folders_audit[0].sent_by,
      excludedPartners: inspection.excluded_partners,
      relevantLinksCount: inspection.relevant_links.length,
      sentToSignicat: Boolean(
        inspection.listing_agreement?.initiated_signing_at,
      ),
      signed: Boolean(inspection.listing_agreement?.signing_finished_at),
      sentDate: inspection.sent_at,
    }
  })

  return list
}

export async function allEvents({ from, to }: { from: Date; to: Date }) {
  const records = await prisma.inspection_event.findMany({
    where: {
      created_at: {
        gte: from,
        lte: to,
      },
    },
    include: {
      inspection_folders: {
        select: {
          created_by: true,
        },
      },
    },
  })

  return records.map((record) => ({
    broker: record.inspection_folders?.created_by,
    ...record,
  }))
}
