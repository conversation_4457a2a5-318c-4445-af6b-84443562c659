'use server'

import { notFound } from 'next/navigation'

import { auth } from '@/auth'
import prisma from '@/db/prisma'
import nordvikApi from '@/server/nordvik-client-adaptor'

export async function impersonate(employeeId: string) {
  if (!employeeId) {
    console.info('No employeeId provided')
    return notFound()
  }

  const session = await auth()
  if (!session) throw new Error('Session not found')

  const currentUser = session.user

  if (!currentUser) {
    return notFound()
  }

  let userToImpersonate = await prisma.magic_link_users.findFirst({
    where: { employeeId },
  })

  if (!userToImpersonate) {
    const { employee } = await nordvikApi.employeeSessionInfo({ employeeId })

    if (!employee) {
      return notFound()
    }

    userToImpersonate = await prisma.magic_link_users.create({
      data: {
        email: employee.email?.toString() ?? '',
        emailVerified: new Date(),
        name: employee.name?.toString(),
        image: employee.image?.small?.toString(),
        employeeId: employee.employeeId?.toString(),
      },
    })
  }

  const isAlreadyImpersonated = currentUser.isImpersonated

  const userIdTorevertTo = isAlreadyImpersonated
    ? currentUser.impersonatedByUserId
    : currentUser.id

  // Updates all sessions for the current user to impersonate because we don't get the session id or session token
  await prisma.sessions.update({
    where: { id: session?.id },
    data: {
      userId: userToImpersonate.id,
      isimpersonated: true,
      impersonatedbyuserid: userIdTorevertTo,
    },
  })
}

export async function revertImpersonate() {
  const session = await auth()
  if (!session) throw new Error('Session not found')

  await prisma.sessions.update({
    where: { id: session.id },
    data: {
      userId: session?.user?.impersonatedByUserId,
      isimpersonated: false,
      impersonatedbyuserid: undefined,
    },
  })
}
