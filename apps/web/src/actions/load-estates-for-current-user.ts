import DataLoader from 'dataloader'
import { Session } from 'next-auth'

import {
  EstatesForBrokerByIdDocument,
  EstatesForBrokerIdCountDocument,
  GQLEstateTabFilter,
  GQLEstatesForBrokerByIdQuery,
  GQLEstatesForBrokerByIdQueryVariables,
  GQLEstatesForBrokerIdCountQuery,
  GQLEstatesForBrokerIdCountQueryVariables,
} from '@/api/generated-client'
import { gqlFetch } from '@/api/graphql'
import fetchStatusCountParams from '@/app/(protected)/(sidebar)/oppdrag/status/[status]/fetch-status-count-params'
import { ESTATE_LIST_DEFAULT_LIMIT } from '@/app/(protected)/(sidebar)/oppdrag/util'

export function loadEstatesForCurrentUserLoader(
  user: Session['user'],
): Promise<boolean> {
  return loadEstatesForCurrentUserDataLoader.load(user)
}

async function loadEstatesForCurrentUser(
  user?: Session['user'],
): Promise<boolean> {
  if (!user) {
    return false
  }

  await Promise.all([
    gqlFetch<
      GQLEstatesForBrokerByIdQuery,
      GQLEstatesForBrokerByIdQueryVariables
    >(EstatesForBrokerByIdDocument, {
      brokerId: user?.employeeId,
      email: user?.employeeId ? undefined : user?.email,
      tabs: [GQLEstateTabFilter.Requested],
      limit: ESTATE_LIST_DEFAULT_LIMIT,
      offset: 0,
      archived: false,
    }),
    gqlFetch<
      GQLEstatesForBrokerIdCountQuery,
      GQLEstatesForBrokerIdCountQueryVariables
    >(EstatesForBrokerIdCountDocument, fetchStatusCountParams(user)),
  ])

  return true
}

const loadEstatesForCurrentUserDataLoader = new DataLoader(
  async (users: Session['user'][]) => {
    return Promise.all(users.map(loadEstatesForCurrentUser))
  },
  {
    cacheKeyFn: (user) => user?.employeeId,
  },
)
