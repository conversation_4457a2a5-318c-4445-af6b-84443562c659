'use server'

import { revalidatePath } from 'next/cache'

import { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import retry from '@/lib/retry'

import { acquireLock, releaseLock } from '../create-document-lock'
import { htmlToPdf } from '../html-pdf'

import {
  type CreateSignicatDocumentOptions,
  createDocument,
} from './create-seller-contract'
import { SignicatTag } from './signicat-tag'

export async function sign({
  htmlString,
  estate,
  ...rest
}: {
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>
  broker: {
    employeeId: string
    email: string
    name: string
    mobilePhone?: string
  }
  options?: CreateSignicatDocumentOptions
  htmlString: string
  departmentId: string
  listingAgreementId: string
  tags?: SignicatTag[]
}) {
  const canContinue = await acquireLock(estate.estateId)

  if (!canContinue) {
    console.warn(`Lock already acquired for estate: ${estate.estateId}`)
    throw new Error('Lock already acquired')
  }

  if (!htmlString) {
    console.error(`Missing htmlString for estate: ${estate.estateId}`)
    throw new Error("Missing Can't create empty document")
  }
  console.info(
    `Creating pdf for estate: ${estate.estateId}, html length: ${htmlString.length}`,
  )

  try {
    const documentBuffer = await retry(() => convertHtmlToPdf(htmlString))

    console.info(
      `Creating document for estate: ${estate.estateId}, bufferlength: ${documentBuffer.length}`,
    )

    const doc = await createDocument({
      title: `Oppdragsavtale for ${estate.address?.streetAddress}`,
      estate: estate,
      ...rest,
      documentBuffer,
    })

    revalidatePath(`/oppdragsavtale/${estate.estateId}`)

    return doc
  } catch (error) {
    console.error('Error creating document:', error)
    throw new Error('Error creating document')
  } finally {
    console.info(`Releasing lock for estate: ${estate.estateId}`)
    await releaseLock(estate.estateId)
    console.info(`Lock released for estate: ${estate.estateId}`)
  }
}

export async function convertHtmlToPdf(html: string) {
  const pdfBuffer = await htmlToPdf(html)

  // Check if the generated PDF is likely blank based on buffer length
  const pdfSizeInBytes = pdfBuffer.length
  const blankPdfThreshold = 15500 // Threshold in bytes

  if (pdfSizeInBytes < blankPdfThreshold) {
    console.error('Generated PDF is blank')
    throw new Error('Generated PDF is blank')
  }

  return pdfBuffer
}
