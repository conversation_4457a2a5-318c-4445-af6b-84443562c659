import { getInspectionCustomerLink } from '@befaring/actions/get-offer-customer-link'
import { getSignersData } from '@befaring/actions/sellers'
import { BrokerRole } from '@befaring/lib/broker-constants'
import { validateSellersWithProxy } from '@befaring/lib/check-valid-fields'
import { getBrokerByRole } from '@befaring/lib/format-brokers'
import { addDays } from 'date-fns/addDays'
import { after } from 'next/server'

import {
  AuthenticationMechanism,
  type DocumentCreateOptions,
  Language,
  SignaturePackageFormat,
} from '@nordvik/signicat-express-sdk/types'

import { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { sendSlackMessage } from '@/app/api/vercel-webhook/send-slack-message'
import prisma from '@/db/prisma'
import signicat from '@/external-services/signicat/client'
import { isProd } from '@/lib/getBaseUrl'
import { getCurrentUser } from '@/lib/session'
import { SIGNICAT_DAYS_FOR_SIGNING } from '@/utils/constants'

import { checkForExistingDocumentForEstate } from '../check-for-existing-document'

import getBrokerSigner from './get-broker-signer'
import getSellerSigner, { getSignatureType } from './get-seller-signer'
import { SignicatTag } from './signicat-tag'
import { SigningSeller } from './signing-seller'

export interface CreateSignicatDocumentOptions {
  skipInitialNotificationFor?: string[]
}

export async function createDocument({
  estate,
  title,
  broker,
  departmentId,
  documentBuffer,
  options,
  listingAgreementId,
  tags,
}: {
  title: string
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>
  documentBuffer: string
  broker: {
    employeeId: string
    email: string
    name: string
  }
  options?: CreateSignicatDocumentOptions
  departmentId: string
  listingAgreementId: string
  tags?: SignicatTag[]
}) {
  if (!estate) {
    throw new Error('Estate not found')
  }

  const estateId = estate.estateId

  const isValuation = Boolean(estate.isValuation)

  const [sellers, sellerLink, currentUser] = await Promise.all([
    getSignersData({
      estateId: estate.estateId,
      onlyUnique: true,
      signersIfCompany: true,
    }),
    getInspectionCustomerLink({
      estateId,
      listingAgreementId: listingAgreementId || estate.listingAgreement?.id,
      inspectionId: estate.inspectionFolder?.id,
      isValuation,
    }),
    getCurrentUser(),
  ])

  let validatedSellersOrProxies: SigningSeller[] = []

  try {
    validatedSellersOrProxies = validateSellersWithProxy(sellers)
  } catch (error) {
    console.error(error)
    throw new Error('Invalid sellers')
  }

  const mainBroker = getBrokerByRole(BrokerRole.Main, {
    brokers: estate.brokers,
    brokersIdWithRoles: estate.brokersIdWithRoles,
  })

  try {
    var offerTokenResult = await prisma.offer_access_tokens.findFirst({
      where: {
        estate_id: estateId,
        valid: true,
      },
      select: {
        token: true,
      },
    })
  } catch (error) {
    console.error(`Couldn't get view token`, error)
  }

  const authMechanism = AuthenticationMechanism.Off

  after(async () => {
    try {
      // If somehow Broker manages to send to signing without sending the inspection folder, we need to mark it as published here
      await prisma.inspection_folders.update({
        where: {
          estate_id: estateId,
        },
        data: {
          published_at: new Date(),
          published_by_id: currentUser?.id,
          listing_agreement_active: true,
        },
      })
    } catch (error) {
      console.error(`Couldn't mark inspection as published`, error)
    }
  })

  const handwrittenSignature =
    !isProd && process.env.HANDWRITTEN_SIGNATURE === 'true'

  const signatureType = getSignatureType({ handwrittenSignature })

  console.info(
    `Creating document for estate: ${estateId}, with ${validatedSellersOrProxies.length} sellers and auth mechanism: ${authMechanism} and signature type: ${JSON.stringify(signatureType)}`,
  )

  const request: DocumentCreateOptions = {
    advanced: {
      departmentId,
      timeToLive: {
        deadline: addDays(new Date(), SIGNICAT_DAYS_FOR_SIGNING).toISOString(),
      },
      tags: [...(tags ?? []), SignicatTag.NordvikMegler],
    },
    title,
    description: title,
    dataToSign: {
      base64Content: documentBuffer,
      convertToPDF: false,
      title,
      description: title,
      fileName: `Oppdragsavtale ${estate.address?.streetAddress}.pdf`,
      packaging: {
        signaturePackageFormats: [SignaturePackageFormat.Pades],
        padesSettings: {
          includeSsnInPades: false,
          primaryLanguage: Language.NO,
        },
      },
    },
    signers: [
      ...validatedSellersOrProxies.map((seller) =>
        getSellerSigner({
          seller,
          estate,
          redirect: sellerLink,
          viewToken: offerTokenResult?.token,
          skipInitialNotification:
            options?.skipInitialNotificationFor?.includes(seller.contactId),
          authMechanism,
          signatureType,
        }),
      ),
      getBrokerSigner(broker, handwrittenSignature, estate),
    ],
    contactDetails: {
      email: mainBroker?.employee.email ?? '<EMAIL>',
      name: mainBroker?.employee.name ?? 'Nordvik Eiendomsmegler',
    },
    externalId: estateId,
    notification: {
      canceledReceipt: {},
      expiredReceipt: {
        email: [
          {
            language: Language.NO,
            subject: 'Signering av oppdragsavtalen har utløpt',
            text: 'Frist for signering av oppdragsavtalen for {address} har utløpt. Kontakt megler hvis du fortsatt ønsker å signere oppdraget.',
            senderName: 'Nordvik',
          },
        ],
      },
      finalReceipt: {
        includeSignedFile: true,
        email: [
          {
            language: Language.NO,
            subject: 'Alle parter har signert oppdragsavtalen for {address}',
            text: 'Gratulerer, oppdragsavtalen for {address} er signert av alle parter! Vedlagt ligger den endelige avtalen, og du finner den også på oppdraget i VitecNext.',
            senderName: 'Nordvik',
          },
        ],
        sms: [
          {
            language: Language.NO,
            text: `Hei! Oppdragsavtalen for {address} er nå signert av alle parter.`,
            sender: 'Nordvik',
          },
        ],
      },
      reminder: {
        // Every third day at 11:00
        chronSchedule: '0 0 11 */3 * ?',
        // after 2 days at 11:00
        firstReminderAfter: new Date(
          new Date().setHours(11, 0, 0, 0) + 3 * 24 * 60 * 60 * 1000,
        ),
        maxReminders: 3,
        email: [
          {
            language: Language.NO,
            subject: 'Påminnelse om signering',
            text: 'Oppdragsavtalen for {address} venter på din signatur. Trykk på lenken under for å signere dokumentet.',
            senderName: 'Nordvik',
          },
        ],
        sms: [
          {
            language: Language.NO,
            text: 'Hei, {name} \n\nOppdragsavtalen for {address} venter på din signatur. Trykk på lenken for å starte signeringen: \n\n{url}',
            sender: 'Nordvik',
          },
        ],
      },
      signatureReceipt: {
        email: [
          {
            senderName: 'Nordvik',
            language: Language.NO,
            subject: 'Du har signert oppdragsavtalen',
            text: `\n\nDu har signert oppdragsavtalen for salget av {address}! Vi er veldig glade for at du har valgt Nordvik til å bistå med salget av din bolig.
                  \n\nNår alle selgerne og megler har signert avtalen, vil du motta den signerte oppdragsavtalen på e-post. Dette kan ta opptil to virkedager.
                  \n\nSå snart oppdragsavtalen er signert av alle parter, vil vi starte klargjøringen av boligen din for salg.  Megler vil ta kontakt med deg for å planlegge fremdriften i salgsprosessen.
                  \n\nVi ser frem til et godt samarbeid og en så enkel og smidig prosess som mulig for deg.`,
          },
        ],
      },
      signRequest: {
        includeOriginalFile: false,
        email: [
          {
            language: Language.NO,
            subject: 'Signer oppdragsavtale for {address}',
            text: 'Du har mottatt et nytt dokument til signering: "Oppdragsavtale for {address}". Trykk på lenken under for å starte signeringen.',
            senderName: 'Nordvik',
          },
        ],
        sms: [
          {
            language: Language.NO,
            text: `Hei, {name} \n\n${mainBroker?.employee.name ?? 'Nordvik'} har sendt deg et dokument til signering: "Oppdragsavtale for {address}". Trykk på lenken for å starte signeringen: \n\n{url}`,
            sender: 'Nordvik',
          },
        ],
      },
    },
  }

  try {
    const hasDocument = await checkForExistingDocumentForEstate(estateId)
    if (hasDocument) {
      console.error(
        `Document already exists for estate: ${estateId}, returning existing document`,
      )

      return await signicat.getDocument(hasDocument)
    }
    return await signicat.createDocument(request)
  } catch (error) {
    console.error(error)
    await sendSlackMessage(
      [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `:warning: *Failed to create document:*
estateId: ${estateId}
error: ${error}
            `,
          },
        },
      ],
      'NORDVIK_ERRORS',
    ).catch(console.error)
    throw error
  }
}
