'use server'

import { revalidatePath, revalidateTag } from 'next/cache'

import prisma from '@/db/prisma'

const COMPANY_SIGNERS_TAG = 'company-signers'

export async function saveCompanySigners({
  companyId,
  signers,
  estateId,
}: {
  companyId: string
  signers: string[]
  estateId: string
}) {
  const existing = await prisma.company_sign_rights.findFirst({
    where: {
      company_contact_id: companyId,
      listing_agreements: { estate_id: estateId },
    },
    select: {
      id: true,
    },
  })

  if (existing) {
    await prisma.company_sign_rights.update({
      where: {
        id: existing.id,
      },
      data: {
        signers: {
          set: signers,
        },
      },
    })
  } else {
    await prisma.company_sign_rights.create({
      data: {
        company_contact_id: companyId,
        signers,
        listing_agreements: {
          connect: {
            estate_id: estateId,
          },
        },
      },
    })
  }

  revalidateTag(COMPANY_SIGNERS_TAG)
  revalidatePath(`/oppdragsavtale/${estateId}`)
}
