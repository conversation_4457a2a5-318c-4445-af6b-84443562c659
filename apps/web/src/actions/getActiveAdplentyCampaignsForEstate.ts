import getAdplentyLoginUrl from './getAdplentyLoginUrl'

const partnerApiKey = process.env.ADPLENTY_PARTNER_API_KEY

export interface AdplentyCampaign {
  target_group: Record<string, unknown>
  url: string
  budget: {
    budget: number
    price: number
    fee: number
  }
  general: {
    name: string
    channels: string[]
    run_dates: {
      start: string
      end: string
    }
  }
  target_geo: string[]
  templateid: string
  template: Record<string, unknown>
  adStudioId: string
  packid: string
  pack_name: string
  currency: string
  libraries: string[]
  entity: Record<string, unknown>
  date: string
  status: string
  charges: Record<string, unknown>[]
  id: string
  platform: string
  accountid: string
  userid: string
  partnerid: string
  createdDate: string
}

const fetchAdplentyCampaigns = async (session: string, caseId: string) => {
  'use server'
  const url = new URL(
    `https://api-pro.adplenty.io/account/marketplace/${caseId}/campaigns`,
  )
  url.searchParams.append('partner-api-key', partnerApiKey!)
  url.searchParams.append('token-session-id', session)

  const response = await fetch(url.toString(), {
    headers: {
      redirect: 'follow',
      platform: 'marketplace',
    },
  })

  const data = await response.json()

  if (data?.response?.length) {
    return data.response as AdplentyCampaign[]
  }

  return []
}

export async function getActiveCampaigns(estateId: string) {
  'use server'
  try {
    const data = await getAdplentyLoginUrl({
      platform: 'marketplace',
      estateId,
    })

    if (data.error) {
      console.info("Couldn't get Adplenty url", data.error)
      return null
    }

    /* Example of loginUrl:
      'https://marketplace.adplenty.io/login?lang=no&session=b3f5cc59e94b4ed081bb38eec4df9572&redir=/app/product/case_nordvik/608a8e8d3d1f576ca2c5b9c6f161d09a&d=nordvik-mp.adplenty.cloud'
    */
    if (!data.response?.loginUrl) {
      console.error('No login URL found')
      return null
    }

    const url = new URL(data.response.loginUrl)
    const session = url.searchParams.get('session')
    const redirectParam = url.searchParams.get('redir')

    if (!redirectParam || !session) return null

    // parse url to get estateId
    const urlEstatePart = redirectParam.split('case_nordvik/').at(-1)
    if (!urlEstatePart) return null

    return fetchAdplentyCampaigns(session, urlEstatePart)
  } catch (error) {
    console.error(error)
  }
}
