import { NextPrivateContact } from '@/actions/next/types-next'
import { GQLInspectionLeadType } from '@/api/generated-client'
import { EstateStatusNumber } from '@/app/(protected)/(sidebar)/oppdrag/util'
import mongo from '@/db/mongo'
import prisma from '@/db/prisma'
import { isProd } from '@/lib/getBaseUrl'
import Estate from '@/server/model/BrokerEstate/model'

import { TipPayload, createTipRef, postTip } from './next/send-lead'
import { notifyBrokerOfLead } from './notify-broker-of-lead'
import { sendConsent } from './send-consent'

export async function sendFinanceLead({
  contact,
  estate,
  source,
  createdBy,
  comment,
}: {
  contact: NextPrivateContact
  estate: Estate
  source?: string
  createdBy?: {
    contactId?: string
    employeeId?: string
  }
  comment: string
}) {
  if (!contact?.email) {
    throw new Error(`Missing email for contact ${contact?.contactId}`)
  }

  const shortenedEstateId = estate.estateId.replaceAll('-', '').substring(0, 21)

  console.info(
    `[sendFinanceLead]: Sending finance lead for estate ${shortenedEstateId}`,
  )

  if (!isProd) {
    console.info(
      `[sendFinanceLead]: Would have sent finance lead for estate ${shortenedEstateId} on production`,
    )
    return
  }

  let leadId: string | undefined

  try {
    const payload = {
      contact_id: contact.contactId,
      estate_id: estate.estateId,
      lead_type: GQLInspectionLeadType.Financing,
      source,
    } as const

    const existing = await prisma.inspection_leads.findFirst({
      where: { ...payload, success: true },
    })

    if (existing) {
      console.info(
        `[sendFinanceLead]: Finance lead already exists for estate ${shortenedEstateId} and contact ${contact.contactId}`,
      )
      return
    }

    const response = await prisma.inspection_leads.create({
      data: {
        ...payload,
        created_by_contact_id: createdBy?.contactId,
        created_by_employee_id: createdBy?.employeeId,
      },
    })

    leadId = response.id
  } catch (error) {
    console.warn(
      `Failed to save finance lead for estate ${estate.estateId} and contact ${contact.contactId}`,
      error,
    )
  }

  const tipPayload = await buildTipPayload(estate, contact, comment, source)

  const [tipId] = await Promise.all([
    postTip(tipPayload, shortenedEstateId).then((tipId) => {
      console.info(`Tip sent for estate ${shortenedEstateId}`)
      return tipId
    }),
    sendConsent(
      contact.email,
      [{ handle: 'financing', transactionType: 'CONFIRMED' }],
      buildConsentPayload(contact),
    ).then(() => console.info(`Consent sent for estate ${shortenedEstateId}`)),
    saveLeadToMongo(createTipRef(shortenedEstateId)),
    notifyBrokerOfLead(estate),
  ])

  if (leadId) {
    return prisma.inspection_leads.update({
      where: { id: leadId },
      data: {
        success: true,
        vitec_tip_id: typeof tipId === 'string' ? tipId : undefined,
        comment: tipPayload.comment,
      },
    })
  }
}

async function buildTipPayload(
  estate: Estate,
  contact: NextPrivateContact,
  comment: string,
  source?: string,
): Promise<TipPayload> {
  const found = await prisma.magic_link_users
    .findFirst({
      where: {
        employeeId: estate.brokerId,
        broker_partner: {
          some: {
            category: 'bank_advisor',
          },
        },
      },
      select: {
        broker_partner: {
          select: {
            category: true,
            name: true,
          },
        },
      },
    })
    .catch()

  const bankAdvisorName = found?.broker_partner?.at(0)?.name

  const bankAdvisorText = bankAdvisorName
    ? `Bankrådgiver: ${bankAdvisorName}\n\n`
    : ''

  const departmentId =
    estate.departmentId?.toString() ??
    estate.department?.departmentId?.toString()

  return {
    estateId: estate.status > EstateStatusNumber.Sold ? null : estate.estateId, // Archived estates can't have tips
    firstName: contact.firstName!,
    lastName: contact.lastName!,
    mobilePhone: contact.mobilePhone!,
    postalCode: contact.postalCode!,
    contactId: contact.contactId!,
    streetAddress: estate.address?.streetAddress,
    email: contact.email ?? undefined,
    userId: estate.brokerId,
    departmentId: estate.brokerId ? undefined : departmentId,
    source: source,
    comment: `${bankAdvisorText}${comment ?? ''}`,
  }
}

function buildConsentPayload(contact: NextPrivateContact) {
  return {
    name: `${contact.firstName} ${contact.lastName}`,
    email: contact.email!,
    mobile: contact.mobilePhone!,
    address: contact.address!,
    postalCode: contact.postalCode!,
    city: contact.city!,
  }
}

async function saveLeadToMongo(reference: string) {
  const today = new Date()

  const lead = {
    partner: 'storebrand',
    source: 'nordvik-megler-oppdragsavtale',
    referrer: undefined, // http referrer, but don't need that here
    reference,
    date: today,
  }

  try {
    await mongo.connect()
    await mongo.db('nordvik').collection('leads').insertOne(lead)
    console.info(`Lead saved successfully: ${reference}`)
  } catch (error) {
    console.error('Failed to save lead to MongoDB', error)
  }
}
