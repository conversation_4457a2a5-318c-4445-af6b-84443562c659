import { fetchSyncApi } from '@/api/sync-api'
import { isProd } from '@/lib/getBaseUrl'

type TransactionType = 'OPT_OUT' | 'CONFIRMED'

interface Purpose {
  handle: string
  transactionType: TransactionType
}

interface BodyType {
  name: string
  email: string
  mobile: string
  address?: string
  postalCode?: string
  city?: string
}

export async function sendConsent(
  identifier: string,
  purposes: Purpose[],
  dsDataElements: BodyType,
  test: boolean = !isProd,
) {
  if (!identifier) {
    throw new Error('No identifier (email) provided')
  }

  if (!purposes || purposes.length === 0) {
    console.info('No purposes to send')
    return
  }

  const payload = {
    source: 'Nordvik Megler',
    identifier: identifier,
    test,
    purposes: purposes,
    dsDataElements,
  }

  try {
    const response = await fetchSyncApi(`api/consent`, {
      method: 'POST',
      body: JSON.stringify(payload),
    })

    console.info(
      `[sendConsent]: Successfully sent consent: ${JSON.stringify(payload)}`,
    )

    return response
  } catch (error) {
    console.error('consentApi error', error)
    throw error
  }
}
