import { get, set } from '@/db/kv'
import { MINUTE } from '@/db/util'

const OLD_MEGLER_URL = process.env.OLD_MEGLER_URL!
interface PostDate {
  date: string
  timezone_type: number
  timezone: string
}
interface BefaringResponse {
  success: boolean
  entries: { title: string; url: string; postDate: PostDate; id: string }[]
  metadata?: {
    count: number
  }
}

export async function befaringsForEstate(
  estateId: string,
): Promise<BefaringResponse | null> {
  if (!estateId) {
    console.error('Missing estateId')
    return null
  }

  const cache = await get(`befaring-${estateId}`)

  if (cache) {
    return cache as BefaringResponse
  }

  const url = new URL(`${OLD_MEGLER_URL}/actions/common-module/befaring/url`)
  url.searchParams.append('estateId', estateId)

  try {
    const response = await fetch(url.href)
    if (!response.ok) {
      console.error('Failed to fetch befarings', response)
      return null
    }
    try {
      const befaring = (await response.json()) as BefaringResponse
      if (befaring.success) {
        await set(`befaring-${estateId}`, befaring, MINUTE * 30)
      }
      return befaring
    } catch (error) {
      console.error('Failed to parse befarings', error)
      return null
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

export async function estateHasBefaring(estateId: string): Promise<boolean> {
  try {
    const befarings = await befaringsForEstate(estateId)

    if (!befarings) {
      return false
    }

    return Boolean(befarings.metadata?.count && befarings.metadata.count > 0)
  } catch (error) {
    console.error(error)
    return false
  }
}
