import { isAfter, startOfYesterday } from 'date-fns'

import { fetchNordvikApi } from '@/api/nordvik-api'
import { HOUR, MINUTE } from '@/db/util'
import { BACKOFFICE_DEPARTMENT_IDS } from '@/server/model/Toplist/factory'
import { withCache } from '@/utils/with-cache'

type Stats = {
  value: number
  inspection: number
  signed: number
  sold: number
  marketingpackage: number
  valuation: number
  image?: string
  name: string
}

type EmployeeStats = Stats & {
  employeeId: string
  department: string
}

type DepartmentStats = Stats & {
  departmentId: number
}

type StatsResponseType = [
  {
    currentYear: EmployeeStats[] | DepartmentStats[]
    lastYear: EmployeeStats[] | DepartmentStats[]
  },
]

export async function nordvikStats(
  params: {
    employeeId?: string
    departmentId?: number
    period?: string
    from?: string
    to?: string
    type?: 'employee' | 'department'
  },
  options?: RequestInit,
): Promise<StatsResponseType> {
  const {
    employeeId,
    departmentId,
    period,
    from,
    to,
    type = 'employee',
  } = params

  if (
    departmentId &&
    BACKOFFICE_DEPARTMENT_IDS.includes(Number(departmentId))
  ) {
    return [
      {
        currentYear: [],
        lastYear: [],
      },
    ]
  }

  const qp = new URLSearchParams()

  qp.set('type', type)

  if (employeeId) {
    qp.set('employeeId', employeeId)
  }

  if (departmentId) {
    qp.set('departmentId', departmentId.toString())
  }

  if (period) {
    qp.set('period', period)
  }

  if (from) {
    qp.set('from', from)
  }

  if (to) {
    qp.set('to', to)
  }

  const search = qp.toString()

  const dynamicData = period || isTodayOrLater(to)

  return withCache(
    search,
    () =>
      fetchNordvikApi<StatsResponseType>(`stats?${qp.toString()}`, {
        next: {
          revalidate: !dynamicData ? HOUR * 5 : MINUTE * 5,
        },
        ...options,
      }),
    HOUR,
  )
}

function isTodayOrLater(to?: string) {
  if (!to) {
    console.info('Missing from or to date in toplist query.')
    return false
  }
  return isAfter(new Date(to), startOfYesterday())
}
