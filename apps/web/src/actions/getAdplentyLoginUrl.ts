import { redirect } from 'next/navigation'

import prisma from '@/db/prisma'
import { encodeBase64 } from '@/lib/base64'
import { getCurrentUser } from '@/lib/session'

const partnerApiKey = process.env.ADPLENTY_PARTNER_API_KEY!

export default async function getAdplentyLoginUrl({
  platform,
  estateId,
}: {
  platform: string
  estateId?: string | null
}): Promise<{
  response?: {
    loginUrl: string
  }
  error?: string
}> {
  const user = await getCurrentUser()

  if (!user) {
    return redirect('/login')
  }

  if (!partnerApiKey) {
    return {
      error: 'Missing ADPLENTY_PARTNER_API_KEY',
    }
  }

  const session = await prisma.sessions.findFirst({
    where: {
      userId: user.id,
    },
  })

  if (!session?.sessionToken) {
    return redirect('/login')
  }

  const url = new URL(
    `${process.env.ADPLENTY_LOGIN_URL!}/${encodeBase64(session.sessionToken)}`,
  )

  // saas for Nordvik Ads and marketplace for Nordvik Ekstra
  url.searchParams.append('platform', platform)

  // system param is used to make sure the callback comes back to the correct system
  url.searchParams.append('system', 'next')

  if (estateId) {
    url.searchParams.append('caseId', estateId)
  }

  console.info('Redirecting to Adplenty:', url.toString())

  try {
    const response = await fetch(url.toString(), {
      headers: {
        'Content-Type': 'application/json',
        'partner-api-key': partnerApiKey,
      },
    })

    return response.json() as Promise<{
      response?: {
        loginUrl: string
      }
      error?: string
    }>
  } catch (error) {
    console.info(error)
    return {
      error: 'Failed to fetch Adplenty login URL',
    }
  }
}
