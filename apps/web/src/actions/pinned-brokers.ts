'use server'

import cache, { get, set } from '@/db/kv'
import prisma from '@/db/prisma'
import { MINUTE } from '@/db/util'
import { CACHE_KEYS } from '@/lib/cache-keys'
import { getAllbrokers } from '@/server/model/Broker/factory'

export type PinnedBroker = {
  employeeId: string
  name: string
  image: string
  departmentName: string
}

export async function fetchPinnedBrokers(
  userId: string,
): Promise<PinnedBroker[]> {
  if (!userId) throw new Error('User ID is required')

  const cacheKey = `pinnedBrokers:${userId}`
  const cachedResult = await get<PinnedBroker[]>(cacheKey)

  if (cachedResult) {
    return cachedResult
  }

  const [allBrokers, pinnedSet] = await Promise.all([
    getAllbrokers(), // always cached
    prisma.pinned_broker
      .findMany({
        where: { userId },
        select: { employeeId: true },
      })
      .then((entries) => new Set(entries.map((entry) => entry.employeeId))),
  ])

  const result = allBrokers.items
    .filter((broker) => pinnedSet.has(broker.employeeId))
    .map((entry) => ({
      employeeId: entry.employeeId,
      name: entry.name,
      image: entry.image?.small ?? '/placeholder-avatar.jpg',
      departmentName: entry.department?.name ?? '',
    }))

  await set(cacheKey, result, MINUTE * 5)

  return result
}

export async function addPinnedBroker(userId: string, employeeId: string) {
  if (!userId || !employeeId) throw new Error('Invalid input data')

  const cacheKey = CACHE_KEYS.USER.PINNED_BROKERS(userId)
  await cache.del(cacheKey)

  return await prisma.pinned_broker.create({
    data: { userId, employeeId },
  })
}

export async function removePinnedBroker(userId: string, employeeId: string) {
  if (!userId || !employeeId) throw new Error('Invalid input data')

  const cacheKey = CACHE_KEYS.USER.PINNED_BROKERS(userId)
  await cache.del(cacheKey)

  return await prisma.pinned_broker.deleteMany({
    where: { userId, employeeId },
  })
}
