import DataLoader from 'dataloader'

import { fetchSyncApi } from '@/api/sync-api'

export function syncLatest(employeeId) {
  return syncLatestDataloader.load(employeeId)
}

async function syncLatestEstates(
  employeeId: string,
): Promise<{ count: number }> {
  if (!employeeId) {
    console.error('Missing employeeId')
    throw `[syncLatest]: Missing employeeId`
  }

  try {
    const count = await fetchSyncApi<{ count: number }>(
      `api/employee/sync_latest_estates`,
      { method: 'POST', body: JSON.stringify({ employeeId }) },
    )

    return count
  } catch (error) {
    console.error('Failed to sync latest estates', error)
    throw `[syncLatest]: Failed to sync for ${employeeId}`
  }
}

const syncLatestDataloader = new DataLoader((employeeIds: string[]) =>
  Promise.all(
    employeeIds.map(async (employeeId: string) => {
      try {
        return await syncLatestEstates(employeeId)
      } catch (error) {
        console.error(
          `Failed to get sync latest estates for ${employeeId}`,
          error,
        )
      }
    }),
  ),
)
