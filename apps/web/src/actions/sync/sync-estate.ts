import DataLoader from 'dataloader'

import { fetchSyncApi } from '@/api/sync-api'

export function syncEstate(estateId: string) {
  return syncEstateDataloader.load(estateId)
}

async function syncEstateTrigger(estateId: string) {
  if (!estateId) {
    console.error('Missing estateId')
    throw `[syncEstate]: Missing estateId`
  }

  try {
    await fetchSyncApi(`api/estate/sync`, {
      method: 'POST',
      body: JSON.stringify({ estateId }),
    })
  } catch (error) {
    console.error(`Failed to sync estate ${estateId}`, error)
    throw `[syncEstate]: Failed to sync for ${estateId}`
  }
}

const syncEstateDataloader = new DataLoader((estateIds: string[]) =>
  Promise.all(
    estateIds.map(async (estateId: string) => {
      try {
        return await syncEstateTrigger(estateId)
      } catch (error) {
        console.error(`Failed to get sync estate ${estateId}`, error)
      }
    }),
  ),
)
