import { get, set } from '@/db/kv'
import { DAY, MINUTE } from '@/db/util'
import { CACHE_KEYS } from '@/lib/cache-keys'
import { Context } from '@/server/context'
import { executeRawQuery } from '@/server/nordvik-client-adaptor'

import { getEstateById } from '../BrokerEstate/factory'
import Estate from '../BrokerEstate/model'

interface DuplicateCheckInput {
  estateId: string
}

interface DuplicateCheckResult {
  hasDuplicates: boolean
}

export async function checkStorebrandDuplicates(
  input: DuplicateCheckInput,
  context?: Context,
): Promise<DuplicateCheckResult> {
  const { estateId } = input

  const cacheKey = CACHE_KEYS.STOREBRAND.DUPLICATE_CHECK(estateId)

  try {
    const cached = await get<DuplicateCheckResult>(cacheKey)
    if (cached) {
      return cached
    }
  } catch (cacheError) {
    console.warn(
      'Cache read failed for Storebrand duplicate check:',
      cacheError,
    )
  }

  const estate = await getEstateById({ id: estateId }, context)

  console.warn(`Checking for Storebrand duplicates for estate ${estateId}`)

  if (!estate) {
    console.warn(`Estate not found for Storebrand duplicate check: ${estateId}`)
    return {
      hasDuplicates: false,
    }
  }

  const result = await performDuplicateCheck(estate)

  try {
    await set(cacheKey, result, result.hasDuplicates ? DAY * 7 : MINUTE * 10)
  } catch (cacheError) {
    console.warn(
      'Cache write failed for Storebrand duplicate check:',
      cacheError,
    )
  }

  return result
}

async function performDuplicateCheck(
  estate: Estate,
): Promise<DuplicateCheckResult> {
  // Build a single batched GraphQL query that includes estate ID check AND all seller checks
  const { query, variables } = buildBatchedTipsQuery(
    estate.estateId,
    estate.sellers,
    estate.createdAt,
  )

  if (!query) {
    // No valid seller data to check
    return {
      hasDuplicates: false,
    }
  }

  try {
    const result = await executeRawQuery<Record<string, { count?: number }>>(
      query,
      variables,
    )

    // Check if any of the aliased queries returned count > 0
    const hasDuplicates = Object.values(result).some(
      (tips) => (tips?.count ?? 0) > 0,
    )

    return {
      hasDuplicates,
    }
  } catch (error) {
    console.warn('Failed to perform batched seller duplicate checks:', error)
    return {
      hasDuplicates: false,
    }
  }
}

/**
 * Builds a single batched GraphQL query with aliases for all seller tip checks.
 * This replaces multiple individual API calls with one efficient batched query.
 */
function buildBatchedTipsQuery(
  estateId: Estate['estateId'],
  sellers: Estate['sellers'],
  createdDateAfter: Estate['createdAt'],
): {
  query: string | null
  variables: Record<string, string | Date | undefined>
} {
  const queryParts: string[] = []
  const variables: Record<string, string | Date | undefined> = {
    createdDateAfter,
  }

  let aliasIndex = 0

  // Add estate ID check if available
  if (estateId) {
    queryParts.push(`
      tips_estate: tips(
        estateId: $estateId
        originType: 4
        createdDateAfter: $createdDateAfter
      ) {
        count
      }
    `)
    variables.estateId = estateId
  }

  for (const seller of sellers) {
    // Check by contactId
    if (seller.contactId) {
      const alias = `tips_contact_${aliasIndex}`
      const varName = `contactId_${aliasIndex}`

      queryParts.push(`
        ${alias}: tips(
          contactId: $${varName}
          originType: 4
          createdDateAfter: $createdDateAfter
        ) {
          count
        }
      `)
      variables[varName] = seller.contactId
      aliasIndex++
    }

    // Check by mobilePhone
    if (seller.mobilePhone) {
      const alias = `tips_phone_${aliasIndex}`
      const varName = `mobilePhone_${aliasIndex}`

      queryParts.push(`
        ${alias}: tips(
          mobilePhone: $${varName}
          originType: 4
          createdDateAfter: $createdDateAfter
        ) {
          count
        }
      `)
      variables[varName] = seller.mobilePhone
      aliasIndex++
    }

    // Check by email
    if (seller.email) {
      const alias = `tips_email_${aliasIndex}`
      const varName = `email_${aliasIndex}`

      queryParts.push(`
        ${alias}: tips(
          email: $${varName}
          originType: 4
          createdDateAfter: $createdDateAfter
        ) {
          count
        }
      `)
      variables[varName] = seller.email
      aliasIndex++
    }
  }

  if (queryParts.length === 0) {
    return { query: null, variables: {} }
  }

  // Build variable declarations for the query
  const variableDeclarations = Object.keys(variables)
    .map((key) => {
      if (key === 'createdDateAfter') return `$${key}: Date`
      if (key === 'estateId') return `$${key}: String`
      if (key.startsWith('contactId_')) return `$${key}: String`
      if (key.startsWith('mobilePhone_')) return `$${key}: String`
      if (key.startsWith('email_')) return `$${key}: String`
      return `$${key}: String`
    })
    .join(', ')

  const query = `
    query BatchedStorebrandTipsCheck(${variableDeclarations}) {
      ${queryParts.join('\n')}
    }
  `

  return { query, variables }
}
