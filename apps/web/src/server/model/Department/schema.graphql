type Department {
  id: NodeID!
  departmentId: Int!
  departmentNumber: Int
  slug: String
  name: String!
  marketName: String
  legalName: String
  organisationNumber: String
  phone: String
  email: String
  kti: Float
  rating: DepartmentRating
  streetAddress: String
  postalCode: String
  city: String
  employees: [DepartmentEmployee]
  displayKtiOnEmployee: Boolean
}

type DepartmentEmployee {
  employeeId: String
  name: String
  email: String
  mobilePhone: String
  title: String
  slug: String
}

type DepartmentRating {
  average: Float
  count: Int
  reviewsCount: Int
  weighted: Float
}

type DepartmentsResponse {
  items: [Department!]!
  totalCount: Int!
}

type Query {
  allDepartments: DepartmentsResponse
  department(departmentId: Int!): Department
}
