import { DAY } from '@/db/util'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { isNotNull } from '@/server/utils'
import { withCache } from '@/utils/with-cache'

import Department from './model'

export async function getAllDepartments() {
  const departments = await withCache(
    'getAllDepartments',
    async () => {
      const response = await nordvikApi.getAllDepartments({})
      return response.departments?.departmentsEntries?.filter(isNotNull)
    },
    DAY,
  )

  return {
    totalCount: departments?.length ?? 0,
    items: departments?.map((n) => new Department(n)) ?? [],
  }
}

export async function getDepartment({
  departmentId,
}: {
  departmentId: number
}) {
  const cacheKey = `department:${departmentId}`
  const dept = await withCache(
    cacheKey,
    async () => {
      const { department } = await nordvikApi.getDepartment({ departmentId })
      return department ?? undefined
    },
    DAY,
  )

  if (!dept) return undefined
  return new Department(dept)
}
