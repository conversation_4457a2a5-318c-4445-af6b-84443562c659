import { fetchSyncApi } from '@/api/sync-api'
import cache, { get, set } from '@/db/kv'
import { HOUR, MINUTE } from '@/db/util'
import { getCurrentUserOrThrow } from '@/lib/session'
import { Context } from '@/server/context'
import {
  GQLBrokerRatingsList,
  GQLQueryCurrentBrokerRatingsTotalArgs,
  GQLSortDirection,
  GQLUpdateBrokerPayload,
} from '@/server/generated-schema'
import {
  NordvikNoGQLEmployee,
  NordvikNoGQLSortDirection,
} from '@/server/nordvik-client'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { isNotNull } from '@/server/utils'
import { withCache } from '@/utils/with-cache'

import { getNews } from '../Article/factory'
import BrokerRating from '../BrokerRating/model'

import Broker from './model'

export async function getAllbrokers() {
  const cached = await get<NordvikNoGQLEmployee[]>('employees:all')

  if (cached?.length) {
    void revalidateBrokers()
    return returnBrokers(cached)
  }

  const entries = await getAllRawBrokers()
  const response = returnBrokers(entries)

  return response
}

function returnBrokers(brokers: NordvikNoGQLEmployee[]) {
  return {
    totalCount: brokers.length,
    items: brokers.map((entry) => new Broker(entry)),
  }
}

const getDepartments = (input?: number | (number | undefined)[]) => {
  if (typeof input === 'number') {
    return [input]
  }
  return input?.filter((v) => typeof v === 'number') ?? []
}

export const getUserNotifications = async () => {
  const user = await getCurrentUserOrThrow()

  if (user.id) {
    const departments: number[] = []

    if (user.employeeId) {
      const { employee } = await withCache(
        `getUserNotifications:employee:department:${user.employeeId}`,
        () =>
          nordvikApi.employeeSessionInfo({
            employeeId: user.employeeId,
          }),
        HOUR * 4,
      )

      if (employee?.departmentId) {
        departments.push(...getDepartments(employee.departmentId))
      }
    }

    const newsCount = await getUnreadNewsCount({
      userId: user.id,
    })

    const totalCount = newsCount
    return {
      totalCount,
      newsCount,
    }
  }

  return {
    totalCount: 0,
    newsCount: 0,
  }
}

const getUnreadNewsCount = async ({ userId }: { userId: string }) => {
  const news = await getNews(
    {},
    {
      limit: 20,
      page: 1,
    },
  )

  const newsPromises = await Promise.all(
    news.items.map(async (article) => article.viewerHasRead({ userId })),
  )

  return newsPromises.filter((n) => !n).length
}

export const getCurrentBroker = async (context?: Context) => {
  const user = context?.user ?? (await getCurrentUserOrThrow())

  if (user.employeeId) {
    const employeeId = user.employeeId === 'AW' ? 'TGK' : user.employeeId
    if (context?.loaders.employeeLoader) {
      const employee = await context.loaders.employeeLoader.load(employeeId)
      return employee ? new Broker(employee) : undefined
    }

    // Fallback to direct API call if loader is not available
    const { employee } = await nordvikApi.employeeWithRatingAndAwards({
      employeeId,
    })

    return employee ? new Broker(employee) : undefined
  }
}

export async function revalidateBrokers() {
  const ttl = await cache.ttl('employees:all')
  console.info(`Brokers cache TTL: ${ttl}`)
  if (ttl < MINUTE * 15) {
    console.info('Revalidating brokers')
    await getAllRawBrokers()
  }
}

export async function getAllRawBrokers() {
  const { employees } = await nordvikApi.employees()

  const entries = employees?.employeesEntries?.filter(isNotNull) ?? []

  void set('employees:all', entries, MINUTE * 60)

  return entries
}

export async function getCurrentBrokerRatingsTotal(
  args: GQLQueryCurrentBrokerRatingsTotalArgs,
) {
  const user = await getCurrentUserOrThrow()
  if (!user.employeeId) {
    throw new Error('User not logged in')
  }

  const key = `currentBrokerRatingsTotal:${user.employeeId}:filters:${JSON.stringify(args)}`

  const cached = await get(key)
  if (cached) {
    return cached
  }

  const currentDate = new Date()
  const last30DaysStart = new Date(
    currentDate.getTime() - 30 * 24 * 60 * 60 * 1000,
  ).toISOString()
  const lastYearStart = new Date(
    currentDate.getFullYear() - 1,
    0,
    1,
  ).toISOString()
  const lastYearEnd = new Date(
    currentDate.getFullYear() - 1,
    11,
    31,
  ).toISOString()
  const currentYearStart = new Date(
    currentDate.getFullYear(),
    0,
    1,
  ).toISOString()

  const { employee: ratings } = await nordvikApi.brokerRatingsTotal({
    employeeId: user.employeeId === 'AW' ? 'TGK' : user.employeeId,
    last30DaysStart,
    lastYearStart,
    lastYearEnd,
    currentYearStart,
    currentDate: currentDate.toISOString(),
    dateFrom: args.dateFrom,
    dateTo: args.dateTo,
    hasReview: args.hasReview,
    featured: args.featured,
    rating: args.rating,
  })

  if (!ratings) {
    throw new Error('Employee not found')
  }

  const response = {
    allDates: ratings?.allDates?.pagination?.total,
    allRatings: ratings?.allRatings?.pagination?.total,
    fiveStar: ratings?.fiveStar?.pagination?.total,
    fourStar: ratings?.fourStar?.pagination?.total,
    threeStar: ratings?.threeStar?.pagination?.total,
    twoStar: ratings?.twoStar?.pagination?.total,
    oneStar: ratings?.oneStar?.pagination?.total,
    withReview: ratings?.withReview?.pagination?.total,
    currentYear: ratings?.currentYear?.pagination?.total,
    lastYear: ratings?.lastYear?.pagination?.total,
    last30Days: ratings?.last30Days?.pagination?.total,
  }

  void set(key, response, MINUTE * 60)

  return response
}

export async function getCurrentBrokerRatings({
  limit,
  offset,
  dateFrom,
  dateTo,
  hasReview,
  sortDir,
  featured,
  rating,
}: {
  limit?: number
  offset?: number
  dateFrom?: string
  dateTo?: string
  hasReview?: boolean
  sortDir?: GQLSortDirection
  featured?: boolean
  rating?: number
}): Promise<GQLBrokerRatingsList> {
  const user = await getCurrentUserOrThrow()
  if (!user.employeeId) {
    throw new Error('User not logged in')
  }

  const { employee } = await nordvikApi.brokerRatings({
    employeeId: user.employeeId === 'AW' ? 'TGK' : user.employeeId,
    pagination: {
      limit,
      offset,
    },
    sortDir:
      sortDir === GQLSortDirection.Asc
        ? NordvikNoGQLSortDirection.Asc
        : NordvikNoGQLSortDirection.Desc,
    rating,
    dateFrom,
    dateTo,
    hasReview,
    featured,
  })
  if (!employee) {
    throw new Error('Employee not found')
  }

  return {
    data:
      employee.ratings?.data?.map((rating) =>
        rating ? new BrokerRating(rating) : undefined,
      ) ?? [],
    pagination: {
      limit: employee.ratings?.pagination?.limit,
      offset: employee.ratings?.pagination?.offset,
      count: employee.ratings?.pagination?.count,
      total: employee.ratings?.pagination?.total,
    },
  }
}

export async function updateBroker(args: GQLUpdateBrokerPayload) {
  const user = await getCurrentUserOrThrow()
  if (!user.employeeId) {
    throw new Error('User not logged in')
  }

  await fetchSyncApi('webhook/employee', {
    method: 'POST',
    body: JSON.stringify({
      ...args,
      source: 'nordvik-megler',
      employeeId: user.employeeId,
    }),
  })
}
