import { formListForEstate } from '@/utils/forms'

import EstateForm from './model'

export const getFormsByEstateId = async (
  estateId: string,
  estateData: Record<string, unknown>,
) => {
  const forms = formListForEstate(estateId)

  const relevantForms = forms.filter((form) => {
    return Object.keys(form.relevantForEstateWithProps ?? {}).every((prop) => {
      return estateData[prop] === form.relevantForEstateWithProps![prop]
    })
  })

  try {
    const result = await Promise.all(
      relevantForms.map(async (form) => {
        const status = await form.status()
        return new EstateForm({
          ...form,
          status: async () => status,
        })
      }),
    )

    return result
  } catch (error) {
    console.error('Error loading forms with estate data:', error)
    return []
  }
}
