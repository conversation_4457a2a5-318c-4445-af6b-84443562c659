type BrokerEstateForm {
  type: String
  name: String
  link: String
  status: BrokerEstateFormStatus
  relevantForEstateWithProps: RelevantForEstateWithProps
}

type BrokerEstateFormStatus {
  signingFinished: Boolean
  isNotificationSent: Boolean
}

type RelevantForEstateWithProps {
  status: Int
  projectRelation: Int
}

input EstateProps {
  status: Int
  projectRelation: Int
}

type Query {
  estateFormsByEstateId(
    estateId: String!
    estateProps: EstateProps
  ): [BrokerEstateForm!]!
}
