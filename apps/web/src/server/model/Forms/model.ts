import type { GQLBrokerEstateForm } from '@/server/generated-schema'
import {
  EstateFormType,
  type FormItemForEstate,
  getFormLink,
  getFormStatus,
} from '@/utils/forms'

export default class EstateForm implements GQLBrokerEstateForm {
  constructor(private data: FormItemForEstate) {}

  get type() {
    return this.data.type
  }

  get name() {
    return this.data.name
  }

  get link() {
    return this.data.link
  }

  async status() {
    const status = await this.data.status()
    return {
      signingFinished: status.signingFinished,
      isNotificationSent: status.isNotificationSent,
    }
  }

  get relevantForEstateWithProps() {
    return this.data.relevantForEstateWithProps
  }

  static createForEstate(
    estateId: string,
    type: EstateFormType,
    name: string,
    relevantForEstateWithProps?: Record<string, unknown>,
  ): EstateForm {
    const formItem: FormItemForEstate = {
      type,
      name,
      link: getFormLink(estateId, type),
      status: async () => {
        const status = await getFormStatus(estateId, type)
        return {
          signingFinished: Boolean(status?.signingFinished),
          isNotificationSent: Boolean(status?.isNotificationSent),
        }
      },
      relevantForEstateWithProps,
    }

    return new EstateForm(formItem)
  }
}
