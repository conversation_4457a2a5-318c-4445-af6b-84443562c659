import gql from 'graphql-tag'

import { HOUR } from '@/db/util'
import { executeRawQuery } from '@/server/nordvik-client-adaptor'
import { withCache } from '@/utils/with-cache'

export async function getCommissionAcceptedDate(estateId: string, ttl = HOUR) {
  return withCache(
    `commissionAcceptedDate:${estateId}`,
    async () => {
      const response = await executeRawQuery<{
        estate: { commissionAcceptedDate: string | null } | null
      }>(
        gql`
          query commissionAcceptedDate($estateId: String!) {
            estate(id: $estateId) {
              commissionAcceptedDate
            }
          }
        `,
        { estateId },
      )

      return response.estate?.commissionAcceptedDate ?? null
    },
    ttl,
  )
}
