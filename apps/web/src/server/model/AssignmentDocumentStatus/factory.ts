'use server'

import { cache } from 'react'

import { get, set } from '@/db/kv'

import { providers } from './helpers/providers'
import { getStrategyOrThrow } from './strategies'
import { ProviderType, RawProviderStatus, withTimeout } from './types'

const TTL_SECONDS = 30

const SINGLE_KV_PREFIX = 'assignment-doc-status'
function singleKvKey(estateId: string, type: string) {
  return `${SINGLE_KV_PREFIX}:${estateId}:${type}`
}

export async function getAssignmentDocumentStatus(
  estateId: string,
  type: ProviderType,
): Promise<RawProviderStatus> {
  // Try single-item KV cache first
  const fromKv = await get<RawProviderStatus>(singleKvKey(estateId, type))
  if (fromKv) return fromKv

  const provider = providers.find((p) => p.type === type)
  if (!provider) {
    return {
      type,
      state: 'ERROR',
      updatedAt: new Date().toISOString(),
      message: 'Unknown document type',
    }
  }
  try {
    const providerType = provider.type
    const res = await withTimeout(
      getStrategyOrThrow(providerType).fetch(estateId),
      provider.timeoutMs ?? 5000,
    )
    // Store individual result
    await set(singleKvKey(estateId, type), res, TTL_SECONDS)
    return res
  } catch (e) {
    const err: RawProviderStatus = {
      type,
      state: 'ERROR',
      updatedAt: new Date().toISOString(),
      message: (e as Error).message,
    }
    try {
      await set(singleKvKey(estateId, type), err, TTL_SECONDS)
    } catch {
      /* ignore */
    }
    return err
  }
}

export const cachedAssignmentDocumentStatus = cache(
  async (estateId: string, type: string) =>
    getAssignmentDocumentStatus(estateId, type as ProviderType),
)
