import { EstateFormType, getFormStatus } from '@/utils/forms'

import type { RawProviderStatus } from '../types'

import type { ProviderStrategy } from './base'

export class TakeoverProtocolStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const status = await getFormStatus(estateId, EstateFormType.Otp)
    return {
      type: 'TAKEOVER_PROTOCOL',
      state: status?.signingFinished
        ? 'COMPLETE'
        : status?.isNotificationSent
          ? 'STARTED'
          : 'NONE',
      updatedAt: undefined,
    }
  }
}
