import { EstateFormType, getFormStatus } from '@/utils/forms'

import type { RawProviderStatus } from '../types'

import type { ProviderStrategy } from './base'

export class SettlementFormBuyerProjectStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const status = await getFormStatus(
      estateId,
      EstateFormType.SettlementBuyerProject,
    )
    return {
      type: 'SETTLEMENT_FORM_BUYER_PROJECT',
      state: status?.signingFinished
        ? 'COMPLETE'
        : status?.isNotificationSent
          ? 'STARTED'
          : 'NONE',
      updatedAt: undefined,
    }
  }
}
