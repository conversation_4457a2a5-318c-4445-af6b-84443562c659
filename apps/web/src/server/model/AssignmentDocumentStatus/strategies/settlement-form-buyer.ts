import { EstateFormType, getFormStatus } from '@/utils/forms'

import type { RawProviderStatus } from '../types'

import type { ProviderStrategy } from './base'

export class SettlementFormBuyerStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const status = await getFormStatus(estateId, EstateFormType.SettlementBuyer)
    return {
      type: 'SETTLEMENT_FORM_BUYER',
      state: status?.signingFinished
        ? 'COMPLETE'
        : status?.isNotificationSent
          ? 'STARTED'
          : 'NONE',
      updatedAt: undefined,
    }
  }
}
