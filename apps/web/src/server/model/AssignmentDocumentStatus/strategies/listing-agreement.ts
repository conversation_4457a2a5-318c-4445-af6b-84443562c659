import prisma from '@/db/prisma'

import type { RawProviderStatus } from '../types'

import { BaseBatchingStrategy } from './base'

export class ListingAgreementStrategy extends BaseBatchingStrategy {
  constructor() {
    super({ maxBatchSize: 100 })
  }

  protected async batchFetch(
    estateIds: readonly string[],
  ): Promise<readonly (RawProviderStatus | Error)[]> {
    const rows = await prisma.inspection_folders.findMany({
      where: { estate_id: { in: estateIds as string[] } },
      select: {
        estate_id: true,
        sent_at: true,
        updated_at: true,
        listing_agreement: {
          select: { signing_finished_at: true, updated_at: true },
        },
      },
    })

    const byId = new Map<string, (typeof rows)[number]>()
    for (const r of rows) byId.set(r.estate_id, r)

    return estateIds.map((id) => {
      const inspection = byId.get(id)
      if (!inspection) {
        return {
          type: 'LISTING_AGREEMENT',
          state: 'NONE',
          updatedAt: undefined,
        } as RawProviderStatus
      }
      return {
        type: 'LISTING_AGREEMENT',
        state: inspection.listing_agreement?.signing_finished_at
          ? 'COMPLETE'
          : inspection.sent_at
            ? 'STARTED'
            : 'NONE',
        updatedAt: (
          inspection.listing_agreement?.updated_at ?? inspection.updated_at
        )?.toISOString(),
      } as RawProviderStatus
    })
  }
}
