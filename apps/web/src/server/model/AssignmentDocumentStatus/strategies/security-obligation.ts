import type { RawProviderStatus } from '../types'

import type { ProviderStrategy, StrategyFetchContext } from './base'

const SECURITY_OBLIGATION_COMPLETED_TAGS = [
  'S_SIKRING_E_TINGLYST',
  'S_SIKRING_OPPRETTET_HJEMMEL',
  'S_SIKRING_IKKE_TL_SAMTYKKE',
]

const SECURITY_OBLIGATION_TAGS = [
  'S_SIKRING_E_OPPRETTET',
  'S_SIKRING_E_SIGNERT',
  ...SECURITY_OBLIGATION_COMPLETED_TAGS,
] as const

export type SecurityObligationTag = (typeof SECURITY_OBLIGATION_TAGS)[number]

export class SecurityObligationStrategy implements ProviderStrategy {
  async fetch(
    _estateId: string,
    ctx?: StrategyFetchContext,
  ): Promise<RawProviderStatus> {
    const isCompleted = ctx?.estate.checklist?.find(
      (c) =>
        c.firstTag &&
        SECURITY_OBLIGATION_COMPLETED_TAGS.includes(c.firstTag) &&
        c.value,
    )

    if (isCompleted) {
      return {
        type: 'SECURITY_OBLIGATION',
        state: 'COMPLETE',
        updatedAt: isCompleted.changedDate ?? undefined,
      }
    }

    const isStarted = ctx?.estate.checklist?.find(
      (c) =>
        c.firstTag && SECURITY_OBLIGATION_TAGS.includes(c.firstTag) && c.value,
    )

    if (isStarted) {
      return {
        type: 'SECURITY_OBLIGATION',
        state: 'STARTED',
        updatedAt: isStarted.changedDate ?? undefined,
      }
    }

    return {
      type: 'SECURITY_OBLIGATION',
      state: 'NONE',
      updatedAt: undefined,
    }
  }
}
