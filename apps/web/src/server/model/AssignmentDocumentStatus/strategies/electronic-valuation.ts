import prisma from '@/db/prisma'

import type { RawProviderStatus } from '../types'

import { BaseBatchingStrategy } from './base'

export class ElectronicValuationStrategy extends BaseBatchingStrategy {
  constructor() {
    super({ maxBatchSize: 100 })
  }

  protected async batchFetch(
    estateIds: readonly string[],
  ): Promise<readonly (RawProviderStatus | Error)[]> {
    const rows = await prisma.etakst_check_queue.findMany({
      where: { estate_id: { in: estateIds as string[] } },
      select: {
        estate_id: true,
        completed_at: true,
      },
    })

    const byId = new Map<string, (typeof rows)[number]>()
    for (const r of rows) byId.set(r.estate_id, r)

    return estateIds.map((id) => {
      const valuation = byId.get(id)
      if (!valuation) {
        return {
          type: 'ELECTRONIC_VALUATION',
          state: 'NONE',
          updatedAt: undefined,
        } as RawProviderStatus
      }

      return {
        type: 'ELECTRONIC_VALUATION',
        state: valuation.completed_at ? 'COMPLETE' : 'STARTED',
        updatedAt: valuation?.completed_at?.toISOString(),
      } as RawProviderStatus
    })
  }
}
