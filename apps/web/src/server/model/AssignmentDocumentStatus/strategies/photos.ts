import { gql } from 'graphql-request'

import { executeRawQuery } from '@/server/nordvik-client-adaptor'

import type { RawProviderStatus } from '../types'

import { BaseBatchingStrategy } from './base'

export const PHOTO_TAGS = [
  'S_FOTOGRAF_BESTILL',
  'S_DIAKRIT_BESTILL',
  'S_PHOLIO_BESTILL',
  'S_H5_BESTILL',
  'S_EFKT_BESTILL',
]

const MIN_PHOTOS_FOR_COMPLETION = 7

export class PhotosStrategy extends BaseBatchingStrategy {
  constructor() {
    super({ maxBatchSize: 50 })
  }

  protected async batchFetch(
    estateIds: readonly string[],
  ): Promise<readonly (RawProviderStatus | Error)[]> {
    const estates = await executeRawQuery<{
      estatesGroup: {
        estatesEntries: [
          {
            estateId: string
            images: [
              {
                imageId: string
                imageSequence: number
              },
            ]
            checklist: [
              {
                firstTag: string
                value: number | null
              },
            ]
          },
        ]
      }
    }>(
      gql`
        query estate($estateIds: [String!]!) {
          estatesGroup(ids: $estateIds) {
            estatesEntries {
              estateId
              images {
                imageId
                imageSequence
              }
              checklist {
                firstTag
                value
              }
            }
          }
        }
      `,
      { estateIds },
    )

    const tasks = estates.estatesGroup?.estatesEntries?.map(async (estate) => {
      const getState = () => {
        if (estate.images && estate.images.length > MIN_PHOTOS_FOR_COMPLETION)
          return 'COMPLETE'
        if (
          PHOTO_TAGS.some((tag) =>
            estate.checklist?.some(
              (item) => item.firstTag === tag && item.value,
            ),
          )
        ) {
          return 'STARTED'
        }
        return 'NONE'
      }

      const status: RawProviderStatus = {
        type: 'PHOTOS',
        state: getState(),
        updatedAt: undefined,
      }
      return status
    })

    return Promise.all(tasks ?? [])
  }
}
