import { EstateFormType, getFormStatus } from '@/utils/forms'

import type { RawProviderStatus } from '../types'

import type { ProviderStrategy } from './base'

export class SettlementFormSellerStrategy implements ProviderStrategy {
  async fetch(estateId: string): Promise<RawProviderStatus> {
    const status = await getFormStatus(
      estateId,
      EstateFormType.SettlementSeller,
    )
    return {
      type: 'SETTLEMENT_FORM_SELLER',
      state: status?.signingFinished
        ? 'COMPLETE'
        : status?.isNotificationSent
          ? 'STARTED'
          : 'NONE',
      updatedAt: undefined,
    }
  }
}
