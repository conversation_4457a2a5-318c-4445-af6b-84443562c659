import type { ProviderType } from '../types'

import type { ProviderStrategy } from './base'
import { EnergyCertificateStrategy } from './energy-certificate'
import { ListingAgreementStrategy } from './listing-agreement'
import { PhotosStrategy } from './photos'
import { SecurityObligationStrategy } from './security-obligation'
import { SelfDeclarationStrategy } from './self-declaration'
import { SellerInterviewStrategy } from './seller-interview'
import { SettlementFormBuyerStrategy } from './settlement-form-buyer'
import { SettlementFormBuyerProjectStrategy } from './settlement-form-buyer-project'
import { SettlementFormSellerStrategy } from './settlement-form-seller'
import { SurveyReportStrategy } from './survey-report'
import { TakeoverProtocolStrategy } from './takeover-protocol'

export { BaseBatchingStrategy, isBatchingStrategy } from './base'
export type {
  StrategyFetchContext,
  BatchingProviderStrategy,
  ProviderStrategy,
} from './base'

export const providerStrategies: Record<ProviderType, ProviderStrategy> = {
  TAKEOVER_PROTOCOL: new TakeoverProtocolStrategy(),
  SETTLEMENT_FORM_BUYER: new SettlementFormBuyerStrategy(),
  SETTLEMENT_FORM_SELLER: new SettlementFormSellerStrategy(),
  SETTLEMENT_FORM_BUYER_PROJECT: new SettlementFormBuyerProjectStrategy(),
  SELLER_INTERVIEW: new SellerInterviewStrategy(),
  SELF_DECLARATION: new SelfDeclarationStrategy(),
  ENERGY_CERTIFICATE: new EnergyCertificateStrategy(),
  SECURITY_OBLIGATION: new SecurityObligationStrategy(),
  SURVEY_REPORT: new SurveyReportStrategy(),
  PHOTOS: new PhotosStrategy(),
  LISTING_AGREEMENT: new ListingAgreementStrategy(),
}

export function getStrategyOrThrow(type: ProviderType) {
  const strategy = providerStrategies[type]
  if (!strategy) throw new Error(`No strategy registered for type ${type}`)
  return strategy
}
