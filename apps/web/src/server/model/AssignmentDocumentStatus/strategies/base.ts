import DataLoader from 'dataloader'

import type { GQLBrokerEstate, GQLNextDocument } from '@/api/generated-client'
import type { SingleDocTypeLoadKey } from '@/server/model/AssignmentDocumentStatus/helpers/estate-documents-loader'

import type { RawProviderStatus } from '../types'

export interface StrategyFetchContext {
  loader?: DataLoader<string, RawProviderStatus>
  documentsLoader?: DataLoader<SingleDocTypeLoadKey, GQLNextDocument[], string>
  estate: Pick<GQLBrokerEstate, 'estateId' | 'projectRelation' | 'checklist'>
}

export interface ProviderStrategy {
  fetch(
    estateId: string,
    ctx?: StrategyFetchContext,
  ): Promise<RawProviderStatus>
}

export interface BatchingProviderStrategy extends ProviderStrategy {
  getLoader(): DataLoader<string, RawProviderStatus>
}

export function isBatchingStrategy(
  s: ProviderStrategy,
): s is BatchingProviderStrategy {
  return (
    typeof (s as unknown as Partial<BatchingProviderStrategy>)?.getLoader ===
    'function'
  )
}

export abstract class BaseBatchingStrategy implements BatchingProviderStrategy {
  constructor(private readonly options?: { maxBatchSize?: number }) {}

  protected abstract batchFetch(
    estateIds: readonly string[],
  ): Promise<readonly (RawProviderStatus | Error)[]>

  async fetch(
    estateId: string,
    ctx?: StrategyFetchContext,
  ): Promise<RawProviderStatus> {
    const loader = ctx?.loader ?? this.createLoader()
    return loader.load(estateId)
  }

  getLoader(): DataLoader<string, RawProviderStatus> {
    return this.createLoader()
  }

  private createLoader(): DataLoader<string, RawProviderStatus> {
    return new DataLoader<string, RawProviderStatus>(
      async (estateIds) => this.batchFetch(estateIds as string[]),
      {
        cache: true,
        maxBatchSize: this.options?.maxBatchSize,
      },
    )
  }
}
