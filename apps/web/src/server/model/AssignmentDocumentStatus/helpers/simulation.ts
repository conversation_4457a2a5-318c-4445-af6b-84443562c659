// Shared simulation helpers for providers that don't call real services yet
import { RawProviderStatus } from '../types'

// to emulate network / upstream variability.
export const SIMULATED_MIN = 120
export const SIMULATED_MAX = 1000

export function simulateLatency() {
  const min = SIMULATED_MIN
  const max = SIMULATED_MAX
  const delay = Math.floor(Math.random() * (max - min + 1)) + min
  return new Promise((r) => setTimeout(r, delay))
}

export async function simulateProvider(
  estateId: string,
  type: RawProviderStatus['type'],
  phase: RawProviderStatus['state'] = 'COMPLETE',
): Promise<RawProviderStatus> {
  console.info(`Simulating provider ${type} for estate ${estateId}`)
  await simulateLatency()
  return {
    type,
    state: phase,
    updatedAt: new Date().toISOString(),
  }
}
