import { GQLEstateTabFilter } from '@/api/generated-client'

import { ProviderDefinition } from '../types'

import { providers } from './providers'

type estateParam = Parameters<NonNullable<ProviderDefinition['filter']>>[0]

export const providerTypesForStatus = (
  key: GQLEstateTabFilter,
  estate: estateParam,
) => {
  return providers
    .filter((p) => !p.enabledFor || p.enabledFor.includes(key))
    .filter((p) => {
      if (!p.filter) return true
      if (estate) return p.filter(estate)
      return false
    })
    .map((p) => ({ type: p.type, name: p.name }))
}

export function providersForStatus(
  key: GQLEstateTabFilter,
  estate: estateParam,
) {
  const selected =
    key != null
      ? providers
          .filter((p) => !p.enabledFor || p.enabledFor.includes(key))
          .filter((p) => {
            if (!p.filter) return true
            if (estate) return p.filter(estate)
            return false
          })
      : providers

  return selected.length > 0 ? selected : providers
}
