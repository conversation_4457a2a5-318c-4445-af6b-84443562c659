import { GQLEstateTabFilter } from '@/api/generated-client'

import { ProviderDefinition } from '../types'

export const providers: readonly ProviderDefinition[] = [
  // Sold
  {
    type: 'TAKEOVER_PROTOCOL',
    name: 'Overtakelsesprotokoll',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_BUYER',
    name: 'Oppgjørsskjema kjøper',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_SELLER',
    name: 'Oppgj<PERSON><PERSON><PERSON><PERSON><PERSON> selger',
    enabledFor: [GQLEstateTabFilter.Sold],
  },
  {
    type: 'SETTLEMENT_FORM_BUYER_PROJECT',
    name: 'Oppgj<PERSON><PERSON><PERSON><PERSON><PERSON> kjøper (prosjekt)',
    enabledFor: [GQLEstateTabFilter.Sold],
    filter: (estate) => estate.projectRelation === 2,
  },

  // In preparation
  {
    type: 'SELLER_INTERVIEW',
    name: 'Selge<PERSON> intervju',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'SELF_DECLARATION',
    name: 'Egenerklæring',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'ENERGY_CERTIFICATE',
    name: 'Energiattest',
    enabledFor: [GQLEstateTabFilter.InPreparation],
    filter: (estate) => {
      const byTakstman = estate.checklist.some(
        (item) =>
          item.firstTag === 'S_ENERGIA_AV_TAKSTMANN' && item.value === 1,
      )
      return !byTakstman
    },
  },
  {
    type: 'SECURITY_OBLIGATION',
    name: 'Sikringsobligasjon',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },
  {
    type: 'SURVEY_REPORT',
    enabledFor: [GQLEstateTabFilter.InPreparation],
    name: 'Takstrapport',
  },
  {
    type: 'PHOTOS',
    name: 'Foto',
    enabledFor: [GQLEstateTabFilter.InPreparation],
  },

  // Request
  {
    type: 'LISTING_AGREEMENT',
    name: 'Oppdragsavtale',
    enabledFor: [GQLEstateTabFilter.Requested, GQLEstateTabFilter.Valuation],
  },
  {
    type: 'INSPECTION_FOLDER',
    name: 'Befaringmappe',
    enabledFor: [GQLEstateTabFilter.Requested, GQLEstateTabFilter.Valuation],
  },

  // Valuation
  {
    type: 'ELECTRONIC_VALUATION',
    name: 'E-takst',
    enabledFor: [GQLEstateTabFilter.Valuation],
  },
]
