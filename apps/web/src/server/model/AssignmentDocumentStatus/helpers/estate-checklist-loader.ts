import DataLoader from 'dataloader'

import type { GQLEstateChecklistItem } from '@/api/generated-client'
import nordvikApi from '@/server/nordvik-client-adaptor'

type ChecklistMap = Map<string, GQLEstateChecklistItem[]>

/**
 * Request-scoped DataLoader for estate checklists.
 * - Keys: estateId (string)
 * - Value: checklist array for the estate (empty array if none)
 *
 * Batches multiple estateIds into one GraphQL call using estatesGroup(ids: $estateIds),
 * then maps results back to input order. No cross-request caching.
 */
export function createEstateChecklistLoader(): DataLoader<
  string,
  GQLEstateChecklistItem[]
> {
  return new DataLoader<string, GQLEstateChecklistItem[]>(
    async (estateIds) => {
      const uniqueIds = Array.from(new Set(estateIds))

      const response = await nordvikApi.estatesChecklists({
        estateIds: uniqueIds,
      })

      const map: ChecklistMap = new Map()
      const entries = response.estatesGroup?.estatesEntries ?? []
      for (const estate of entries) {
        // Cast each item to generated type shape (fields align)
        if (!estate?.estateId) continue
        map.set(
          estate.estateId,
          (estate.checklist ?? []) as GQLEstateChecklistItem[],
        )
      }

      // Resolve in the same order as keys
      return estateIds.map((id) => map.get(id) ?? [])
    },
    {
      cache: true,
      maxBatchSize: 250,
    },
  )
}
