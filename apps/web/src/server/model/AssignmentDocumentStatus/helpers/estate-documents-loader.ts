import DataLoader from 'dataloader'

import { getEstateDocuments } from '@/actions/next/estate-documents'
import type { GQLNextDocument } from '@/api/generated-client'

export type SingleDocTypeLoadKey = Readonly<{
  estateId: string
  docType: number
}>

/**
 * Create a DataLoader that batches loads for the same estate and fetches the union
 * of requested docTypes exactly once per estate per request. Subsequent loads for
 * the same estate with additional docTypes will fetch only the missing types.
 */
export function createEstateDocumentsLoader(): DataLoader<
  SingleDocTypeLoadKey,
  GQLNextDocument[],
  string
> {
  const perEstateCache = new Map<
    string,
    { fetched: Set<number>; docs: GQLNextDocument[] }
  >()

  return new DataLoader<SingleDocTypeLoadKey, GQLNextDocument[], string>(
    async (keys) => {
      // Group by estateId and compute union of requested types per estate
      const grouped = new Map<
        string,
        { keys: SingleDocTypeLoadKey[]; unionTypes: Set<number> }
      >()
      for (const key of keys) {
        const entry = grouped.get(key.estateId) ?? {
          keys: [] as SingleDocTypeLoadKey[],
          unionTypes: new Set<number>(),
        }
        entry.keys.push(key)
        entry.unionTypes.add(Number(key.docType))
        grouped.set(key.estateId, entry)
      }

      // For each estate, ensure cache has at least union of the requested types
      const ensureTasks: Promise<void>[] = []
      for (const [estateId, { unionTypes }] of grouped) {
        const cached = perEstateCache.get(estateId)
        if (!cached) {
          // First fetch for this estate: request union
          ensureTasks.push(
            getEstateDocuments(estateId, Array.from(unionTypes)).then(
              (docs) => {
                perEstateCache.set(estateId, {
                  fetched: new Set(unionTypes),
                  docs: docs ?? [],
                })
              },
            ),
          )
        } else {
          // Fetch only missing types, if any
          const missing: number[] = Array.from(unionTypes).filter(
            (t) => !cached.fetched.has(Number(t)),
          )
          if (missing.length > 0) {
            ensureTasks.push(
              getEstateDocuments(estateId, missing).then((newDocs) => {
                for (const t of missing) cached.fetched.add(Number(t))
                if (newDocs?.length) {
                  // Merge; dedupe by documentId
                  const existing = new Map(
                    cached.docs.map((d) => [d.documentId, d] as const),
                  )
                  for (const d of newDocs) existing.set(d.documentId, d)
                  cached.docs = Array.from(existing.values())
                }
              }),
            )
          }
        }
      }

      await Promise.all(ensureTasks)

      // Resolve in the same order as keys: filter cached docs by requested types
      return keys.map((key) => {
        const cache = perEstateCache.get(key.estateId)
        const requested = new Set<number>([Number(key.docType)])
        const docs = cache?.docs ?? []
        if (requested.size === 0) return docs
        return docs.filter((d) => requested.has(Number(d.docType)))
      })
    },
    {
      cache: true,
      // Cache per unique combination to satisfy DataLoader api, additional cross-key caching is handled by perEstateCache above
      cacheKeyFn: (k) => `${k.estateId}|${Number(k.docType)}`,
      maxBatchSize: 500,
    },
  )
}
