enum AssignmentDocumentType {
  LISTING_AGREEMENT
  ENERGY_CERTIFICATE
  SAFETY_DECLARATION
  SELLER_INTERVIEW
  SURVEY_REPORT
  PHOTOS
}

enum AssignmentDocumentState {
  PENDING # Not started / requested
  IN_PROGRESS
  COMPLETE
  ERROR
  MISSING # Explicitly missing / needs action
}

type AssignmentDocumentStatusItem {
  type: AssignmentDocumentType!
  state: AssignmentDocumentState!
  updatedAt: DateTime!
  # Optional human friendly detail or provider message
  message: String
}

type AssignmentDocumentStatuses {
  estateId: String!
  items: [AssignmentDocumentStatusItem!]!
  # Whether one or more providers failed; individual failed items will have state=ERROR
  partialFailure: Boolean!
  generatedAt: DateTime!
}

extend type Query {
  assignmentDocumentStatus(
    estateId: String!
    type: AssignmentDocumentType!
  ): AssignmentDocumentStatusItem!
}
