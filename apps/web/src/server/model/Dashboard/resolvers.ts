import { Session } from 'next-auth'
import { unstable_cache } from 'next/cache'

import { DAY, MINUTE } from '@/db/util'
import { CACHE_KEYS, CacheKeyUtils } from '@/lib/cache-keys'
import { BrokerUser, getCurrentUserAsBrokerOrThrow } from '@/lib/session'
import { GQLQueryResolvers } from '@/server/generated-schema'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { withCache } from '@/utils/with-cache'

import { averageCommission } from './helpers/average-commission'
import {
  cacheEmployeeDashboardDataExecutor,
  dashboardCache,
} from './helpers/dashboard-cache'
import { expectedRevenue } from './helpers/expected-revenue'
import { importantTasks } from './helpers/important-tasks'
import { keyFigures } from './helpers/key-figures'
import { kti } from './helpers/kti'
import { revenue } from './helpers/revenue'
import { leads } from './helpers/storebrand-leads'
import { toplist } from './helpers/toplist'
import { upcomingActivities } from './helpers/upcoming-activities'

export type IdProps = {
  departmentId: number
  id: string
}

export type DashboardProps = {
  isPersonal: boolean
  ids: IdProps
  currentUser?: Session['user']
  refresh?: boolean
}

export const Query: GQLQueryResolvers = {
  dashboardRevenue: async (_, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())

    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    if (user.employeeId === 'AW') {
      return revenue({
        isPersonal: args.type === 'personal',
        ids: {
          departmentId: 5,
          id: 'TGK',
        },
      })
    }

    return revenue({
      isPersonal: args.type === 'personal',
      ids: {
        departmentId: user.department.departmentId,
        id: user.employeeId,
      },
    })
  },
  dashboardToplist: async (_, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())

    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    if (user.employeeId === 'AW') {
      return toplist({
        isPersonal: args.type === 'personal',
        ids: {
          departmentId: 5,
          id: 'TGK',
        },
        section: args.section,
        currentUser: user,
      })
    }

    return toplist({
      isPersonal: args.type === 'personal',
      ids: {
        departmentId: user.department.departmentId,
        id: user.employeeId,
      },
      section: args.section,
      currentUser: user,
      period: args.period,
      excludeCurrent: args.excludeCurrent,
      amountOfEntries: args.amountOfEntries,
    })
  },
  dashboardLeads: async (_, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())

    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    if (user.employeeId === 'AW') {
      return leads({
        isPersonal: args.type === 'personal',
        ids: {
          departmentId: 5,
          id: 'TGK',
        },
        currentUser: user,
        period: args.period,
      })
    }

    return leads({
      isPersonal: args.type === 'personal',
      ids: {
        departmentId: user.department.departmentId,
        id: user.employeeId,
      },
      currentUser: user,
      period: args.period,
    })
  },
  dashboardExpectedRevenue: async (_, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())

    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    return expectedRevenue({
      isPersonal: args.type === 'personal',
      ids: {
        departmentId: user.department.departmentId,
        id: user.employeeId,
      },
    })
  },
  dashboardKeyFigures: async (_, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())

    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    if (user.employeeId === 'AW') {
      return keyFigures({
        isPersonal: args.type === 'personal',
        ids: {
          departmentId: 5,
          id: 'TGK',
        },
        period: args.period,
      })
    }

    return keyFigures({
      isPersonal: args.type === 'personal',
      ids: {
        departmentId: user.department.departmentId,
        id: user.employeeId,
      },
      period: args.period,
    })
  },
  dashboardAverageCommission: async (root, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())

    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    if (user.employeeId === 'AW') {
      return averageCommission({
        isPersonal: args.type === 'personal',
        ids: {
          departmentId: 5,
          id: 'TGK',
        },
        departmentName: 'TGK',
      })
    }

    return averageCommission({
      isPersonal: args.type === 'personal',
      ids: {
        departmentId: user.department.departmentId,
        id: user.employeeId,
      },
      departmentName: user.department.name,
    })
  },
  dashboardKti: async (root, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())

    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    if (user.employeeId === 'AW') {
      return kti({
        departmentId: 5,
        id: 'TGK',
      })
    }

    const ktiResult = await kti({
      departmentId: user.department.departmentId,
      id: user.employeeId,
    })

    return ktiResult
  },
  dashboardUpcomingActivities: async (_, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())
    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    const cachedUpcomingActivities = unstable_cache(
      () => upcomingActivities(user.employeeId),
      [`dashboardUpcomingActivities:${user.employeeId}`],
      {
        tags: [`dashboardUpcomingActivities:${user.employeeId}`],
        revalidate: MINUTE * 15, // 15 minutes
      },
    )

    return cachedUpcomingActivities()
  },
  dashboardImportantTasks: async (_, args, ctx) => {
    const user = ctx.user ?? (await getCurrentUserAsBrokerOrThrow())
    if (!isBrokerUser(user)) {
      throw new Error('User is not a broker')
    }

    return importantTasks(user.employeeId)
  },
  dashboardCache: async () => {
    await dashboardCache()
    return true
  },
  dashboardCacheForEmployee: async (_, { employeeId, section }) => {
    if (!employeeId) {
      return false
    }

    const { employee } = await withCache(
      CACHE_KEYS.DASHBOARD.CACHE_FOR_EMPLOYEE(employeeId),
      () => nordvikApi.employeeSessionInfo({ employeeId }),
      DAY,
    )

    if (!employee?.department?.departmentId) {
      return false
    }

    await cacheEmployeeDashboardDataExecutor(
      {
        id: employee.employeeId!,
        departmentId: employee.department!.departmentId!,
      },
      section,
      employee.department?.name,
    )

    return true
  },
}

export function generateCacheKey(
  prefix: string,
  isPersonal: boolean,
  ids: IdProps,
  extras?: string[],
) {
  return CacheKeyUtils.generateCacheKey(prefix, isPersonal, ids, extras)
}

export function mergeDepartments(departmentId: number) {
  switch (departmentId) {
    case 31:
      return [31, 24]
    case 24:
      return [31, 24]
    default:
      return [departmentId]
  }
}

function isBrokerUser(user: Session['user']): user is BrokerUser {
  return !!user.employeeId && !!user.department?.departmentId
}
