import { addDays, endOfWeek } from 'date-fns'

import mongo from '@/db/mongo'
import { getActivityTypeName } from '@/server/model/BrokerEstate/utils'

export async function upcomingActivities(employeeId: string) {
  try {
    await mongo.connect()
  } catch (error) {
    console.warn('MongoDB connection error:', error)
    return []
  }

  const now = new Date()
  const endOfNextWeek = addDays(endOfWeek(now, { weekStartsOn: 1 }), 7)

  try {
    const activities = await mongo
      .db('nordvik')
      .collection('estates')
      .aggregate([
        {
          $match: {
            employeeId,
            activities: {
              $elemMatch: {
                start: {
                  $gte: now.toISOString(),
                  $lte: endOfNextWeek.toISOString(),
                },
              },
            },
          },
        },
        {
          $unwind: '$activities',
        },
        {
          $match: {
            'activities.start': {
              $gte: now.toISOString(),
              $lte: endOfNextWeek.toISOString(),
            },
          },
        },
        {
          $project: {
            estateId: 1,
            employeeId: 1,
            departmentId: 1,
            address: 1,
            activity: '$activities',
          },
        },
        {
          $sort: {
            'activity.start': 1,
          },
        },
        {
          $limit: 15, // reasonable limit for dashboard @einar?
        },
      ])
      .toArray()

    return activities.map((activity) => ({
      ...activity.activity,
      typeName: getActivityTypeName(activity.activity.type),
      estateId: activity.estateId,
      estateAddress: activity.address?.streetAdress,
    }))
  } catch (error) {
    console.error('Error fetching upcoming activities:', error)
    return []
  }
}
