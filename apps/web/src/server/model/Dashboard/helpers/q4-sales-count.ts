import { GQLToplistSection } from '@/api/generated-client'
import { MINUTE } from '@/db/util'
import { withCache } from '@/utils/with-cache'

import { getToplist } from '../../Toplist/factory'

export type Q4SalesProgress = {
  total: number
}

export async function getQ4SalesCount(): Promise<Q4SalesProgress | null> {
  try {
    const data = await withCache(
      `q4SalesCount`,
      () =>
        getToplist({
          type: 'department',
          section: GQLToplistSection.Sold,
          startDate: '2025-10-01',
          endDate: '2025-12-31',
        }),
      MINUTE * 5,
    )

    if (data.items.length === 0) return null

    const total = data.items.reduce((acc, item) => acc + item.value, 0)

    return { total }
  } catch (error) {
    console.error('Failed to fetch Q4 sales count', error)
    return { total: 0 }
  }
}
