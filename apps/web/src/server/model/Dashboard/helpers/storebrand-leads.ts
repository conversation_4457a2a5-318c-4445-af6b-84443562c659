import gql from 'graphql-tag'
import { Session } from 'next-auth'

import {
  GQLLeadsEntry,
  GQLToplistEntry,
  GQLToplistSection,
} from '@/api/generated-client'
import { get, set } from '@/db/kv'
import { DAY, HOUR } from '@/db/util'
import { executeRawQuery } from '@/server/nordvik-client-adaptor'
import { isNotNull } from '@/server/utils'
import { withCache } from '@/utils/with-cache'

import { BACKOFFICE_DEPARTMENT_IDS, getToplist } from '../../Toplist/factory'
import { DashboardProps, IdProps, generateCacheKey } from '../resolvers'

import { getDateParams } from './key-figures'

const EMPLOYEE_LEAD_BUDGET = 2

export async function leads({
  isPersonal,
  ids,
  currentUser,
  refresh = false,
  period = `:${new Date().getMonth()}`,
}: DashboardProps & {
  period?: string
}) {
  return withCache(
    generateCacheKey(`dashboardLeads:v1`, isPersonal, ids, [period]),
    () => composeList({ isPersonal, ids, period, user: currentUser }),
    HOUR,
    refresh,
  )
}

export async function composeList({
  isPersonal,
  ids,
  period = `:${new Date().getMonth()}`,
  user,
}: {
  isPersonal: boolean
  ids: IdProps
  period: string
  user?: Session['user']
}) {
  const type = isPersonal ? 'employee' : 'department'

  const dateParams = getDateParams(period)

  const data = await getToplist({
    type,
    section: GQLToplistSection.Partner,
    departmentId: isPersonal ? ids.departmentId : undefined,
    ...dateParams,
  })

  if (!data) throw new Error('No data')

  // if isPersonal is false and departmentId = 1 we add an entry with the sum of all entries
  if (!isPersonal && ids.departmentId === 1) {
    const totalValue = data.items.reduce((acc, item) => acc + item.value, 0)
    data.items.unshift({
      name: 'Nordvik',
      value: totalValue,
      departmentId: ids.departmentId,
      department: 'Nordvik',
      id: 'total',
      avatarUrl: '',
      count: 0,
      reviews: 0,
    })
  }

  const id = isPersonal ? ids.id : ids.departmentId

  const { employees, employeesCount } = await getDepartmentEmployees()

  const list = processLeadsList(data.items, isPersonal, employeesCount).sort(
    (a, b) => b.value - a.value,
  )

  let amountOfEntries = 3

  const { current, topEntries } = list.reduce<{
    current: GQLLeadsEntry | null
    topEntries: GQLLeadsEntry[]
  }>(
    (acc, item, index) => {
      const isCurrentItem = [item.id, item.departmentId].includes(id)

      if (isCurrentItem) {
        acc.current = item
      }

      if (isCurrentItem) {
        amountOfEntries += 1
      }

      if (index < amountOfEntries && !isCurrentItem) {
        acc.topEntries.push({
          ...item,
          value: item.value ?? 0,
        })
      }

      return acc
    },
    { current: null, topEntries: [] },
  )

  if (!topEntries) throw new Error('No data')

  let entries: GQLLeadsEntry[] = [
    ...topEntries,
    patchCurrent(type, employeesCount, current, user),
  ]
    .filter(isNotNull)
    .sort((a, b) => a.value - b.value)

  if (entries.length < 4) {
    const employeeCount = employeesCount[ids.departmentId] || 0
    let attempts = 0
    const maxAttempts = 100 // Safety limit to prevent infinite loops

    while (entries.length < 4 && attempts < maxAttempts) {
      attempts++
      const names = entries.map((entry) => entry.name)

      if (isPersonal) {
        if (entries.length >= employeeCount) {
          break
        }
        const employee = employees.find(
          (entry) =>
            entry.department.departmentId === ids.departmentId &&
            !names.includes(entry.name),
        )

        if (employee) {
          entries.unshift({
            name: employee.name,
            value: 0,
            departmentId: employee.department?.departmentId,
            budget: EMPLOYEE_LEAD_BUDGET,
            actual: 0,
            current: false,
            employeeCount: employeesCount[employee.department.departmentId],
          })
        } else {
          // No more employees available, break out
          break
        }
      } else {
        const department = employees.find(
          (employee) => !names.includes(employee.department.name),
        )

        if (department) {
          entries.unshift({
            name: department.department.name,
            value: 0,
            departmentId: department.department?.departmentId,
            budget: EMPLOYEE_LEAD_BUDGET * employeeCount,
            actual: 0,
            current: false,
            employeeCount: employeesCount[department.department.departmentId],
          })
        } else {
          // No more departments available, break out
          break
        }
      }
    }
  }

  const multiplier = period.includes('all') ? new Date().getMonth() + 1 : 1

  if (multiplier > 1) {
    entries = entries.map((item) => ({
      ...item,
      value: item.value / multiplier,
      actual: item.actual,
      budget: item.budget * multiplier,
    }))
  }

  return {
    entries,
    budget: EMPLOYEE_LEAD_BUDGET,
  }
}

/**
 * If the broker or department is not in the list, we still want to show the current user's name or department name
 * and set the position to null.
 */
function patchCurrent(
  type: 'employee' | 'department',
  employeesCount?: Record<number, number>,
  current?: GQLLeadsEntry | null,
  user?: Session['user'],
): GQLLeadsEntry {
  const departmentId = current?.departmentId || user?.department?.departmentId
  const count = departmentId ? employeesCount?.[departmentId] : 0

  if (type === 'employee') {
    return {
      name: current?.name || user?.name || 'Deg',
      value: current?.value ?? 0,
      budget: current?.budget ?? EMPLOYEE_LEAD_BUDGET,
      actual: current?.actual ?? 0,
      current: true,
      employeeCount: 0,
      id: user?.employeeId,
    }
  }
  return {
    name: current?.name || user?.department?.name || 'Ditt kontor',
    value: current?.value ?? 0,
    budget: current?.budget || (count ? count * EMPLOYEE_LEAD_BUDGET : 0),
    actual: current?.actual ?? 0,
    current: true,
    employeeCount: current?.employeeCount || count || 0,
  }
}

function processLeadsList(
  list: GQLToplistEntry[],
  isPersonal: boolean,
  employeesCount: Record<number, number>,
): GQLLeadsEntry[] {
  return list.map((item) => {
    const count = item.departmentId && employeesCount[item.departmentId]
    const multiplier = isPersonal ? 1 : count || 0
    return {
      name: item.name,
      departmentId: item.departmentId,
      id: item.id,
      value: item.value / multiplier,
      actual: item.value,
      budget: EMPLOYEE_LEAD_BUDGET * multiplier,
      current: false,
      employeeCount: count || 0,
    }
  })
}

type EmployeeEntry = {
  employeeId: string
  name: string
  department: {
    name: string
    departmentId: number
  }
}

export async function getDepartmentEmployees() {
  const cacheKey = 'brokerCountNoBackOffice:v1'

  const cached = await get<{
    employees: EmployeeEntry[]
    employeesCount: Record<number, number>
  }>(cacheKey)

  if (cached) {
    return cached
  }

  const response = await executeRawQuery<{
    employees: {
      employeesEntries: EmployeeEntry[]
    }
  }>(gql`
    query Departments {
      employees(
        webPublish: true
        roles: [1, 2, 3, 4, 5, 6, 9, 10, 11, 12]
        active: true
      ) {
        employeesEntries {
          employeeId
          name
          department {
            name
            departmentId
          }
        }
      }
    }
  `)

  // count the number of employees in each department
  const employeesCount = response.employees.employeesEntries.reduce(
    (acc, entry) => {
      const departmentId = entry.department.departmentId
      if (BACKOFFICE_DEPARTMENT_IDS.includes(entry.department.departmentId)) {
        acc[departmentId] = 0
      } else {
        acc[departmentId] = (acc[departmentId] || 0) + 1
      }
      return acc
    },
    {} as Record<number, number>,
  )

  employeesCount[1] = Object.values(employeesCount).reduce(
    (sum, count) => sum + count,
    0,
  )

  const result = {
    employees: response.employees.employeesEntries,
    employeesCount,
  }

  try {
    await set(cacheKey, result, DAY)
  } catch (error) {
    console.error(`Failed to set cache for key: ${cacheKey}`, error)
  }

  return result
}
