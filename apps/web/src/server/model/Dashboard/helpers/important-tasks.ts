import { gql } from 'graphql-request'

import { getNextOverviewUrl } from '@/actions/get-next-overview-url'
import { riskCompletionCheck } from '@/actions/risk-check-completed'
import { SIGNICAT_BROKER_TITLE } from '@/actions/signing/get-broker-signer'
import { EstateStatusNumber } from '@/app/(protected)/(sidebar)/oppdrag/util'
import prisma from '@/db/prisma'
import { GQLImportantTaskType } from '@/server/generated-schema'
import { executeRawQuery } from '@/server/nordvik-client-adaptor'
import { getLinkToObjectTabInNext } from '@/utils/get-link-to-object-tab-in-next'

export interface ImportantTask {
  type: GQLImportantTaskType
  estateId: string
  estateAddress: string
  mainBrokerId: string
  signUrl?: string
  eiendomsverdiUrl?: string
  amlUrl?: string
}

interface BasicTask {
  type: GQLImportantTaskType
  estateId: string
}

interface EstateEntry {
  address: string | null
  mainBrokerId: string | null
  status: number
}

export async function importantTasks(
  employeeId: string,
): Promise<ImportantTask[]> {
  const encodedEmployeeId = Buffer.from(employeeId).toString('base64')
  const tasks: BasicTask[] = []
  const signerUrlByEstate = new Map<string, string | undefined>()

  // Discover tasks (run in parallel)
  await Promise.all([
    checkForSigningTasks(
      employeeId,
      encodedEmployeeId,
      tasks,
      signerUrlByEstate,
    ),
    checkForEtakstAndAmlTasks(tasks),
  ])

  const estateIds = [...new Set(tasks.map((task) => task.estateId))]

  if (estateIds.length === 0) {
    return []
  }

  const estatesData = await executeRawQuery<{
    estatesGroup: {
      estatesEntries: {
        estateId: string
        address: {
          streetAdress: string
        } | null
        employeeId: string | null
        status: number
      }[]
    }
  }>(
    gql`
      query GetEstatesDetails($estateIds: [String!]) {
        estatesGroup(ids: $estateIds) {
          estatesEntries {
            estateId
            address {
              streetAdress
            }
            employeeId
            status
          }
        }
      }
    `,
    { estateIds: estateIds },
    { cache: 'force-cache' },
  )

  const estateMap = new Map<string, EstateEntry>(
    estatesData.estatesGroup.estatesEntries.map((estate) => {
      return [
        estate.estateId,
        {
          address: estate.address?.streetAdress ?? '',
          mainBrokerId: estate.employeeId || '',
          status: estate.status,
        },
      ]
    }),
  )

  // Prepare IDs for EV and AML link building (deduped)
  const rawEtakstEstateIds = Array.from(
    new Set(
      tasks
        .filter((t) => t.type === GQLImportantTaskType.EtakstChecksIncomplete)
        .map((t) => t.estateId),
    ),
  )
  const rawAmlEstateIds = Array.from(
    new Set(
      tasks
        .filter((t) => t.type === GQLImportantTaskType.AmlCheckIncomplete)
        .map((t) => t.estateId),
    ),
  )

  // Pre-filter Etakst/AML estates before building links using estatesGroup meta
  const isEligibleForLinks = (estateId: string) => {
    const meta = estateMap.get(estateId)
    return (
      !!meta &&
      typeof meta.status === 'number' &&
      meta.status < EstateStatusNumber.Sold &&
      meta.mainBrokerId === employeeId
    )
  }

  const etakstEstateIds = rawEtakstEstateIds.filter(isEligibleForLinks)
  const amlEstateIds = rawAmlEstateIds.filter(isEligibleForLinks)

  // For Etakst tasks we now store the Next valueassessment tab link in evUrlByEstate
  const evUrlByEstate = new Map<string, string | undefined>()
  const amlUrlByEstate = new Map<string, string | undefined>()

  const estatesForLinks = Array.from(
    new Set([...etakstEstateIds, ...amlEstateIds]),
  )

  // Batch-fetch all estates needed for link generation once
  const estateObjById = new Map<string, EstateEntry | undefined>()
  if (estatesForLinks.length > 0) {
    await Promise.all(
      estatesForLinks.map(async (estateId) => {
        try {
          const estate = estateMap.get(estateId)
          estateObjById.set(estateId, estate ?? undefined)
        } catch (e) {
          console.error('Failed to fetch estate for links', estateId, e)
          estateObjById.set(estateId, undefined)
        }
      }),
    )
  }

  await Promise.all([
    ...etakstEstateIds.map(async (estateId) => {
      try {
        const estate = estateObjById.get(estateId)
        if (!estate) return
        const linkToNext = await getNextOverviewUrl(estateId)
        if (linkToNext) {
          evUrlByEstate.set(
            estateId,
            getLinkToObjectTabInNext(linkToNext, 'valueassessment'),
          )
        }
      } catch (e) {
        console.error('Failed to build Next valueassessment link', estateId, e)
      }
    }),
    ...amlEstateIds.map(async (estateId) => {
      try {
        const estate = estateObjById.get(estateId)
        if (!estate) return
        const linkToNext = await getNextOverviewUrl(estateId)
        if (linkToNext) {
          amlUrlByEstate.set(
            estateId,
            getLinkToObjectTabInNext(linkToNext, 'overview'),
          )
        }
      } catch (e) {
        console.error('Failed to build AML link for estate', estateId, e)
      }
    }),
  ])

  const result = tasks
    .map((task) => {
      const meta = estateMap.get(task.estateId)
      return {
        ...task,
        estateAddress: meta?.address ?? '',
        mainBrokerId: meta?.mainBrokerId ?? '',
        status: meta?.status,
        signUrl:
          task.type === GQLImportantTaskType.SignListingAgreement
            ? signerUrlByEstate.get(task.estateId)
            : undefined,
        eiendomsverdiUrl:
          task.type === GQLImportantTaskType.EtakstChecksIncomplete
            ? evUrlByEstate.get(task.estateId)
            : undefined,
        amlUrl:
          task.type === GQLImportantTaskType.AmlCheckIncomplete
            ? amlUrlByEstate.get(task.estateId)
            : undefined,
      }
    })
    .filter((task) => {
      if (task.type === GQLImportantTaskType.EtakstChecksIncomplete) {
        if (task.mainBrokerId !== employeeId) {
          return false
        }
      }
      if (task.type === GQLImportantTaskType.AmlCheckIncomplete) {
        if (task.mainBrokerId !== employeeId) return false
      }
      return (
        typeof task.status === 'number' && task.status < EstateStatusNumber.Sold
      )
    })

  // Enforce order: sign, etakst, aml
  const priority: Record<GQLImportantTaskType, number> = {
    [GQLImportantTaskType.SignListingAgreement]: 0,
    [GQLImportantTaskType.EtakstChecksIncomplete]: 1,
    [GQLImportantTaskType.AmlCheckIncomplete]: 2,
  }

  result.sort((a, b) => priority[a.type] - priority[b.type])

  return result as ImportantTask[]
}

async function checkForSigningTasks(
  employeeId: string,
  encodedEmployeeId: string,
  tasks: BasicTask[],
  signerUrlByEstate: Map<string, string | undefined>,
): Promise<void> {
  const signerRows = await prisma.signicat_signer.findMany({
    where: {
      title: SIGNICAT_BROKER_TITLE,
      signed_at: null,
      url: { not: null },
      external_signer_id: { in: [employeeId, encodedEmployeeId] },
      listing_agreement: {
        deleted_at: null,
        signing_finished_at: null,
        deadline_for_signing: { gte: new Date() },
        // At least one non-broker signer must exist...
        signers: {
          some: {
            title: { not: SIGNICAT_BROKER_TITLE },
          },
        },
        // ...and all non-broker signers must have signed
        NOT: {
          signers: {
            some: {
              title: { not: SIGNICAT_BROKER_TITLE },
              signed_at: null,
            },
          },
        },
      },
    },
    select: {
      url: true,
      listing_agreement: { select: { estate_id: true } },
    },
  })

  for (const row of signerRows) {
    const estateId = row.listing_agreement?.estate_id
    if (!estateId) continue
    tasks.push({
      type: GQLImportantTaskType.SignListingAgreement,
      estateId,
    })
    signerUrlByEstate.set(estateId, row.url ?? undefined)
  }
}

async function checkForEtakstAndAmlTasks(tasks: BasicTask[]): Promise<void> {
  const eightDaysAgo = new Date()
  eightDaysAgo.setDate(eightDaysAgo.getDate() - 8)

  try {
    const incomplete = await prisma.etakst_check_queue.findMany({
      where: {
        created_at: { gte: eightDaysAgo },
        is_complete: false,
      },
      select: {
        estate_id: true,
      },
    })

    if (incomplete.length === 0) {
      return
    }

    const tasksToAdd: BasicTask[] = []

    // Add AML tasks concurrently where risk check is not complete
    await Promise.allSettled(
      incomplete.map(async (entry) => {
        try {
          const [{ isComplete: hasAml }, hasEtakst] = await Promise.all([
            riskCompletionCheck(entry.estate_id),
            prisma.estate_etakst.findFirst({
              where: { estate_id: entry.estate_id },
              select: { id: true },
            }),
          ])

          if (!hasAml && hasEtakst) {
            tasksToAdd.push({
              type: GQLImportantTaskType.AmlCheckIncomplete,
              estateId: entry.estate_id,
            })
          }

          if (!hasEtakst && hasAml) {
            tasksToAdd.push({
              type: GQLImportantTaskType.EtakstChecksIncomplete,
              estateId: entry.estate_id,
            })
          }
        } catch (e) {
          console.error(
            'Failed to verify AML status for estate',
            entry.estate_id,
            e,
          )
        }
      }),
    )

    tasks.push(...tasksToAdd)
  } catch (error) {
    console.error('Failed to check for Etakst/AML tasks:', error)
  }
}
