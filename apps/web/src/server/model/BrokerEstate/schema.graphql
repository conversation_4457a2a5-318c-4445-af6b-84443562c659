# Uniquely identifies a building in Norway
type LandIdentificationMatrix {
  knr: Int
  gnr: Int
  bnr: Int
  fnr: Int
  snr: Int
  ownPart: String
}

type EstatePrice {
  totalPrice: Float
  soldPrice: Float
  priceSuggestion: Float
  collectiveDebt: Float
}

type AreaSize {
  BRAItotal: Float
}

type EstateBroker {
  role: Int
  image: BrokerImage
  slug: String
  name: String
  email: String
  title: String
  mobilePhone: String
  workPhone: String
  employeeId: String
  employeeRoles: [BrokerRole]
}

type BrokerAddress {
  apartmentNumber: String
  city: String
  streetAddress: String
  zipCode: String
  municipality: String
}
type EstateChecklistItem {
  changedBy: String
  changedDate: DateTime
  firstTag: String
  tags: [String]
  value: Int
}

type FinnData {
  finnCode: String
  finnExpireDate: DateTime
  finnPublishDate: DateTime
}

type BrokerEstate {
  id: NodeID!
  estateId: String!
  broker: Broker
  mainBroker: Broker
  assistantBroker: Broker
  userID: String
  type: String
  numberOfBedrooms: Int
  assignmentNumber: String
  assignmentType: Int
  assignmentTypeGroup: Int
  isValuation: Boolean
  ownAssignmentType: String
  address: BrokerAddress
  sellPreference: String
  propertyType: String
  createdAt: DateTime
  updatedAt: DateTime
  landIdentificationMatrix: LandIdentificationMatrix
  department: Department
  priceHistory: [BrokerEstatePriceHistory]
  status: Int!
  takeOverDate: DateTime
  noOfBedRooms: Int
  noOfRooms: Int
  brokers: [EstateBroker]
  areaSize: AreaSize
  estatePrice: EstatePrice
  commissionAcceptedDate: DateTime
  images: [BrokerEstateImage!]
  mainImage: BrokerEstateImage
  placeholderImage: String
  activities: [EstateActivity!]!
  upcomingEvents: [EstateActivity!]!
  sellers: [BrokerEstateSeller!]!
  mainSeller: BrokerEstateSeller
  companyContacts: [Contact!]!
  extraContacts(source: Source): [Contact!]!
  showings: [BrokerEstateShowing!]!
  links: [BrokerEstateLink!]!
  linkToNext: String
  hjemUrl: String
  expireDate: DateTime
  soldDate: DateTime
  campaigns: [BrokerEstateCampaign!]!
  hasInspection: Boolean
  inspection: Inspection
  inspectionDate: DateTime
  forms: [BrokerEstateForm!]!
  inspectionFolder: InspectionFolder
  listingAgreement: ListingAgreement
  ownership: Int
  ownershipType: String
  partOwnership: PartOwnership
  businessManagerContact: BusinessManagerContact
  brokersIdWithRoles: [BrokerIdWithRole]
  estateType: String
  estateTypeId: String
  estateTypeExternal: Int
  finn: FinnData
  checklist: [EstateChecklistItem!]!
  isWithdrawn: Boolean!
  sumArea: SumArea
  changedDate: DateTime
  documents: [NextDocument!]!
  hasCompanySeller: Boolean
  estatePriceModel: EstatePriceModel
  estateSizeModel: EstateSizeModel
  location: Location
  latitude: Float
  longitude: Float
  departmentId: Int
  brokerId: String
  stats: EstateStats
  heading: String
  riskCheckmark: Boolean
  isEtakstPublished: Boolean
  etakst: NextDocument
  matrikkel: [LandIdentificationMatrix]!
  marketingStart: MarketingStart
  ads: [EstateAd!]!
  inspectionEvents: [InspectionEvent!]
  projectRelation: Int
  isPublished: Boolean
}

enum EstateAdSource {
  NORDVIKBOLIG
  HJEM
  FINN
}

type EstateAd {
  source: EstateAdSource
  link: String
}

type MarketingStart {
  date: DateTime
  source: String
}

type EstateStats {
  showings: Int
  showingsTotal: Int
  privateShowingsCount: Int
  showingRegistrations: Int
  showingRegistrationsTotal: Int
  showingParticipants: Int
  bids: Int
  bidders: Int
  interested: Int
  followUp: Int
}

type Location {
  type: String
  coordinates: [Float!]
}

type EstateSizeModel {
  grossArea: Float
  primaryRoomArea: Float
  primaryRoomAreaDescription: String
  usableArea: Float
}

type EstatePriceModel {
  additionalAgreementOptions: Float
  collectiveAssets: Float
  collectiveDebt: Float
  communityTax: Float
  communityTaxYear: Int
  estimatedValue: Float
  leasingPartyTransportFee: Float
  loanFare: Float
  originalAgreementPrice: Float
  originalExpensesPrice: Float
  otherExpenses: String
  priceSuggestion: Float
  purchaseCostsAmount: Float
  salesCostDescription: String
  soldPrice: Float
  totalPrice: Float
  totalPriceExclusiveCostsAndDebt: Float
  transportAgreementCosts: Float
  waterRate: Float
  waterRateDescription: String
  waterRateYear: Int
  yearlyLeaseFee: Float
  yearlySocietyTax: Float
}

type BusinessManagerContact {
  city: String
  address: String
  companyName: String
  firstName: String
  lastName: String
}

type NextDocument {
  documentId: String!
  head: String!
  extension: String!
  docType: Int!
  lastChanged: DateTime!
  signStatus: Int
  approvalDate: DateTime
  approvedBy: String
}

type SumArea {
  bra: Float!
  braI: Float!
  braE: Float!
  braB: Float!
  braS: Float!
  pRom: Float!
  tba: Float!
  sRom: Float!
  bta: Float!
}

type PartOwnership {
  partName: String
  partOrgNumber: String
  partNumber: Int
  estateHousingCooperativeStockHousingUnitNumber: String
  estateHousingCooperativeStockNumber: String
  partAbout: String
  contactId: String
  businessManagerContactId: String
}

type BrokerIdWithRole {
  employeeId: String!
  brokerRole: Int!
  employee: BrokerIdWithRoleDetails!
}

type BrokerIdWithRoleDetails {
  name: String
  email: String
  mobilePhone: String
  title: String
  image: BrokerImage
}

type BrokerEstateForm {
  type: String
  name: String
  link: String
  status: BrokerEstateFormStatus
  relevantForEstateWithProps: RelevantForEstateWithProps
}

type RelevantForEstateWithProps {
  status: Int
  projectRelation: Int
}

type BrokerEstateFormStatus {
  signingFinished: Boolean
  isNotificationSent: Boolean
}

type PostDate {
  date: String!
  timezone_type: Int!
  timezone: String!
}

type InspectionEntry {
  title: String!
  url: String!
  postDate: PostDate
  id: String!
}

type InspectionMetadata {
  count: Int
}

type Inspection {
  success: Boolean
  entries: [InspectionEntry!]!
  metadata: InspectionMetadata
}

type BrokerEstateCampaign {
  packageName: String
  marketingPackage: String
  dateOrdered: DateTime
  orderStartDate: DateTime
  orderEndDate: DateTime
  orderStatus: String
  externalId: String
}

type BrokerEstateLink {
  linkType: Int
  url: String
  text: String
}

type BrokerEstateShowing {
  showingId: String
  start: DateTime
  end: DateTime
}

type BrokerEstateSeller {
  firstName: String
  lastName: String
  email: String
  mobilePhone: String
  mainContact: Boolean
  contactId: String!
  proxyId: String
  # Fields from Prisma
  socialSecurityNumber: String
  address: String
  postCode: String
  city: String
  companyName: String
  contactType: Int
  proxy: SellerProxy
}

type SellerProxy {
  contactId: String!
  firstName: String
  lastName: String
  email: String
  mobilePhone: String
  companyName: String
}

type EstateActivity {
  start: DateTime
  end: DateTime
  type: Int
  typeName: String
  name: String
  performedById: String
  value: String
  done: Boolean
  id: String
}

type BrokerEstateImage {
  category: String
  small: String
  medium: String
  large: String
  id: String
  description: String
  sequence: Int
}

type BrokerEstatePriceHistory {
  id: String
  evPrice: Int
  createdAt: DateTime
  updatedAt: DateTime
  landIdentificationMatrix: LandIdentificationMatrix
  postgresEstateId: String
}

type BrokerEstatePagination {
  offset: Int
  limit: Int
  count: Int
  total: Int
}

type BrokerEstatesResponse {
  items: [BrokerEstate!]!
  pagination: BrokerEstatePagination
}

type BrokerEstateCountResponse {
  tab: EstateTabFilter!
  count: Int!
}

type DepartmentEstateCountResponse {
  tab: EstateTabFilter!
  count: Int!
}

input MinMax {
  min: Int!
  max: Int!
}

input FindEstatesForBrokerFilters {
  longitude: Float
  latitude: Float
  radius: Int
  limit: Int
  offset: Int
  sortBy: String
  statuses: [Int!]
  estateTypeId: [Int!]
  priceRange: MinMax
  assignmentTypeGroup: [Int!]
  sizeRange: MinMax
}

input findEstatesFilters {
  longitude: Float
  latitude: Float
  baseType: [String!]
  estateType: [String!]
  noBedRooms: String
  size: MinMax
  price: MinMax
  radius: Float
  sortBy: String
  statuses: [Int!]
  limit: Int
  offset: Int
  soldDateAfter: DateTime
  city: String
}

enum EstateTabFilter {
  Requested
  Valuation
  InPreparation
  ForSale
  Sold
  Archived
}

enum SortEstateBy {
  changedDate
}

type Query {
  estate(id: String!): BrokerEstate
  estates: [BrokerEstate!]!
  estatePriceHistories: [BrokerEstatePriceHistory!]!
  estatesForBrokerIdCount(
    brokerId: String
    email: String
    tabs: [EstateTabFilter!]!
  ): [BrokerEstateCountResponse!]!
  estatesForBrokerById(
    brokerId: String
    tabs: [EstateTabFilter!]!
    limit: Int
    offset: Int
    archived: Boolean
    email: String
    search: String
    disableCache: Boolean
    assignmentTypeGroup: [Int!]
    sortBy: SortEstateBy
  ): BrokerEstatesResponse!
  estatesForDepartment(
    departmentId: Int!
    tabs: [EstateTabFilter!]!
    limit: Int
    offset: Int
    archived: Boolean
    email: String
    search: String
    assignmentTypeGroup: [Int!]
    brokerIds: [String!]
    sortBy: SortEstateBy
  ): BrokerEstatesResponse!
  estatesForDepartmentCount(
    departmentId: Int!
    tabs: [EstateTabFilter!]!
  ): [DepartmentEstateCountResponse!]!
  syncEstateWithVitec(estateId: String!): Boolean!
  findEstatesForBroker(
    employeeId: String!
    filters: FindEstatesForBrokerFilters
  ): [BrokerEstate!]!
  findEstates(filters: findEstatesFilters!): [BrokerEstate!]!
}
type Mutation {
  resetForm(estateId: String!, formType: String!): Boolean
  upsertEstatePublishDate(estateId: String!, publishDate: DateTime): Boolean!
}
