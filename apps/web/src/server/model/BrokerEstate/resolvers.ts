import { syncEstate } from '@/actions/sync/sync-estate'
import prisma from '@/db/prisma'
import { MINUTE } from '@/db/util'
import { CACHE_KEYS } from '@/lib/cache-keys'
import type {
  GQLMutationResolvers,
  GQLQueryResolvers,
} from '@/server/generated-schema'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { withCache } from '@/utils/with-cache'

// import { getCurrentUserOrThrow } from "@/lib/session";
import {
  estatesForBrokerById,
  estatesForBrokerIdCount,
  estatesForDepartment,
  estatesForDepartmentCount,
  findEstates,
  findEstatesForBroker,
  getEstateById,
  resetForm,
} from './factory'
import Estate from './model'

export function estatesForBrokerIdCountCacheKey(brokerId?: string): string {
  return CACHE_KEYS.ESTATE.FOR_BROKER_COUNT(brokerId)
}

export const Query: GQLQueryResolvers = {
  estates: () => {
    return []
  },
  estate: async (_, args, context) => {
    // const user = await getCurrentUserOrThrow();

    const estate = await getEstateById(args, context)
    // This needs to account for our open pages for customers which are using this query
    // if (!estate?.authorizer.viewerHasAccess({ employeeId: user.employeeId })) {
    //   return undefined;
    // }

    return estate
  },
  estatePriceHistories: () => {
    return []
  },
  estatesForBrokerIdCount: async (_, args) => {
    if (!args.brokerId) {
      return []
    }

    return withCache(
      estatesForBrokerIdCountCacheKey(args.brokerId),
      () => estatesForBrokerIdCount(args),
      MINUTE * 10,
    )
  },
  estatesForBrokerById: (_, args, context) => {
    return estatesForBrokerById(args, context)
  },
  estatesForDepartment: (_, args, ctx) => {
    return estatesForDepartment(args, ctx)
  },
  estatesForDepartmentCount: async (_, args, ctx) => {
    if (!args.departmentId) {
      return []
    }

    return withCache(
      CACHE_KEYS.ESTATE.FOR_DEPARTMENT_COUNT(args.departmentId),
      () => estatesForDepartmentCount(args, ctx),
      MINUTE * 10,
    )
  },
  syncEstateWithVitec: async (_, args) => {
    try {
      const { estate: before } = await nordvikApi.getEstateById({
        estateId: args.estateId,
        statuses: [-1, 0, 1, 2, 3, 4],
      })

      if (!before) {
        return false
      }

      await syncEstate(args.estateId)
      const { estate: after } = await nordvikApi.getEstateById({
        estateId: args.estateId,
        statuses: [-1, 0, 1, 2, 3, 4],
      })
      if (!after) {
        return false
      }
      return before.changedDate !== after.changedDate
    } catch (error) {
      console.error('Error syncing estate with Vitec', error)
      return false
    }
  },
  findEstatesForBroker: async (_, args, context) => {
    const estates = await findEstatesForBroker(args)
    return estates.map((estateData) => new Estate(estateData, context))
  },
  findEstates: async (_, args) => {
    return findEstates(args)
  },
}

export const Mutation: GQLMutationResolvers = {
  resetForm: async (root, args) => {
    return resetForm(args)
  },
  upsertEstatePublishDate: async (_root, args) => {
    const { estateId, publishDate } = args
    if (!estateId || typeof estateId !== 'string') {
      throw new Error('estateId is required')
    }

    // Accept undefined to clear the date
    await prisma.estate_publish_date.upsert({
      where: { estate_id: estateId },
      update: {
        publish_date: publishDate ? new Date(publishDate) : null,
        updated_at: new Date(),
      },
      create: {
        estate_id: estateId,
        publish_date: publishDate ? new Date(publishDate) : null,
      },
    })

    return true
  },
}
