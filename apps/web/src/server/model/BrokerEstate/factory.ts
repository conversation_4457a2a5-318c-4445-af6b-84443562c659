'use server'

import { format, subDays } from 'date-fns'

import {
  GQLEstateTabFilter,
  GQLQueryFindEstatesForBrokerArgs,
} from '@/api/generated-client'
import { get, set } from '@/db/kv'
import { MINUTE } from '@/db/util'
import { getCurrentUserOrThrow } from '@/lib/session'
import { Context } from '@/server/context'
import type {
  GQLMutationResetFormArgs,
  GQLQueryEstateArgs,
  GQLQueryEstatesForBrokerByIdArgs,
  GQLQueryEstatesForBrokerIdCountArgs,
  GQLQueryEstatesForDepartmentArgs,
  GQLQueryFindEstatesArgs,
} from '@/server/generated-schema'
import {
  NordvikNoGQLEstate,
  NordvikNoGQLFindEstatesForBrokerQueryVariables,
  NordvikNoGQLFindEstatesQueryVariables,
} from '@/server/nordvik-client'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { isNotNull } from '@/server/utils'
import { EstateFormType } from '@/utils/forms'
import { isDevelopment } from '@/utils/isDevelopment'
import { withCache } from '@/utils/with-cache'

import Estate from './model'
import type { EstatesForBrokerByIdCache } from './types'
import { getKvKey, getParamsByStatus } from './utils'

export async function getEstateById(
  { id }: GQLQueryEstateArgs,
  context?: Context,
) {
  const rawEstate = await fetchEstateById(id, context)

  if (!rawEstate) {
    return undefined
  }

  return new Estate(rawEstate, context)
}

export const fetchEstateById = async (id: string, context?: Context) => {
  try {
    if (context?.loaders?.estateLoader) {
      const estate = await context.loaders.estateLoader.load(id)
      if (estate) {
        return estate
      }
    }

    const { estate } = await nordvikApi.getEstateById({
      estateId: id,
      statuses: [-1, 0, 1, 2, 3, 4, 5],
    })

    return estate
  } catch (error) {
    console.error('Error fetching estate by id', error)
    return undefined
  }
}

interface EstatesForBrokerIdCountCache {
  tab: GQLEstateTabFilter
  count: number
}
export const estatesForBrokerIdCount = async (
  args: GQLQueryEstatesForBrokerIdCountArgs,
) => {
  const promises = args.tabs.map((tab) => {
    return nordvikApi.employeesEstatesEntriesByIdCount(
      getParamsByStatus({ ...args, tabs: [tab] }),
    )
  })

  if (!promises) {
    return []
  }

  const results = await Promise.all(promises)
  const response: EstatesForBrokerIdCountCache[] = []

  args.tabs.forEach((tab, index) => {
    response.push({
      tab: tab,
      count: results[index]?.employee?.estateEntries?.pagination?.total ?? 0,
    })
  })

  return response
}

export const estatesForDepartment = async (
  { brokerIds, ...args }: GQLQueryEstatesForDepartmentArgs,
  ctx: Context,
) => {
  const isInPreparation = args.tabs?.includes(GQLEstateTabFilter.InPreparation)
  const notInPreparationAndNoBrokerIds =
    !isInPreparation && (!brokerIds || brokerIds.length === 0)

  const { offset = 0, limit = 0 } = args

  if (!notInPreparationAndNoBrokerIds) {
    delete args.limit
    delete args.offset
  }

  const cacheKey = `estatesForDepartment:${JSON.stringify(args)}`

  let { sorted, count } = await withCache(
    cacheKey,
    async () => {
      const estatesEntriesVariables = getParamsByStatus({ ...args })

      const { estates } = await nordvikApi.departmentEstatesEntries(
        estatesEntriesVariables,
      )

      const entries = estates?.estatesEntries?.filter(isNotNull) ?? []

      if (!isInPreparation) {
        return { sorted: entries, count: estates?.count ?? entries.length }
      }

      const result = entries.map((estate) => new Estate(estate, ctx))

      let map = await Promise.all(
        result.map(async (estate) => {
          const publishDate = await estate.marketingStart()
          return { publishDate: publishDate?.date, estateId: estate.estateId }
        }),
      )

      const sevenDaysAgo = subDays(new Date(), 7).getTime()

      map = map.filter(
        (m) => m.publishDate && m.publishDate.getTime() >= sevenDaysAgo,
      )

      const sorted = map.sort((a, b) => {
        if (a.publishDate && b.publishDate) {
          return b.publishDate.getTime() - a.publishDate.getTime()
        }
        if (a.publishDate) {
          return -1
        }
        if (b.publishDate) {
          return 1
        }
        return 0
      })

      return {
        sorted: sorted
          .map((s) => entries.find((e) => e.estateId === s.estateId))
          .filter(isNotNull),
        count: sorted.length,
      }
    },
    10,
  )

  if (brokerIds && brokerIds.length > 0) {
    sorted = sorted.filter((entry) => {
      return (
        entry.brokersIdWithRoles?.some((b) =>
          b?.employeeId ? brokerIds.includes(b.employeeId) : false,
        ) ?? false
      )
    })

    count = sorted.length
  }

  const sliced = notInPreparationAndNoBrokerIds
    ? sorted
    : sorted.slice(offset, limit ? offset + limit : undefined)

  return {
    items: sliced.map((s) => new Estate(s, ctx)),
    pagination: { offset, limit, count: sliced.length, total: count },
  }
}

export const estatesForBrokerById = async (
  { disableCache, ...args }: GQLQueryEstatesForBrokerByIdArgs,
  context?: Context,
) => {
  const user = context?.user ?? (await getCurrentUserOrThrow())

  if (!user.employeeId) {
    return {
      pagination: {
        offset: 0,
        limit: 0,
        count: 0,
        total: 0,
      },
      items: [],
    }
  }

  if (user.employeeId !== args.brokerId) {
    throw new Error('User does not have access to this resource')
  }

  const { tabs, brokerId, email, search, ...rest } = args

  if (!disableCache) {
    const cached = await get<EstatesForBrokerByIdCache>(
      getKvKey((brokerId ?? email)!, tabs, search, rest),
    )

    if (cached?.items.length) {
      console.info('Cache hit for estatesForBrokerById')
      return {
        pagination: cached.pagination,
        items: cached.items.map((estate) => new Estate(estate, context)),
      }
    }
  } else {
    console.info('Cache disabled for estatesForBrokerById')
  }

  const estatesEntriesVariables = getParamsByStatus(args)
  const { employee: result } = await nordvikApi.employeesEstatesEntriesById(
    estatesEntriesVariables,
  )

  const estates = result?.estateEntries?.data?.filter(isNotNull)

  const pagination = {
    offset: result?.estateEntries?.pagination?.offset ?? 0,
    limit: result?.estateEntries?.pagination?.limit ?? 0,
    count: result?.estateEntries?.pagination?.count ?? 0,
    total: result?.estateEntries?.pagination?.total ?? 0,
  }

  if (estates?.length) {
    try {
      void set<EstatesForBrokerByIdCache>(
        getKvKey((brokerId ?? email)!, tabs, search, rest),
        { items: estates, pagination },
        MINUTE,
      )
    } catch (e) {
      console.error(e)
    }
  }

  return {
    pagination,
    items: estates?.map((estate) => new Estate(estate)) ?? [],
  }
}

export const resetForm = async (args: GQLMutationResetFormArgs) => {
  let type = args.formType
  const { estateId, formType } = args
  if (!process.env.NORDVIK_APP_API_URL) {
    throw new Error('NORDVIK_APP_API_URL is not set')
  }
  const nordvikApiUrl = process.env.NORDVIK_APP_API_URL

  if (isDevelopment) {
    // eslint-disable-next-line no-console -- This is a development only log
    console.info('DEVELOPMENT: Reset form resolver', estateId, formType)

    return true
  }
  const searchParams = new URLSearchParams()

  // add extra params and change type to app api for this type
  if ((formType as EstateFormType) === EstateFormType.SettlementBuyerProject) {
    type = EstateFormType.SettlementBuyer
    searchParams.append('estateBaseType', '4')
  }

  const url = new URL(
    `estate/${estateId}/${type}/reset?${searchParams.toString()}`,
    nordvikApiUrl,
  )

  try {
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'x-api-key': 'Q0Rbk136gl',
      },
    })

    if (!response.ok) {
      throw new Error('Failed to reset form')
    }

    return true
  } catch (error) {
    console.error(error)

    return false
  }
}

export async function findEstatesForBroker({
  employeeId,
  filters,
}: GQLQueryFindEstatesForBrokerArgs) {
  const input: NordvikNoGQLFindEstatesForBrokerQueryVariables = {
    assignmentTypeGroup: filters?.assignmentTypeGroup ?? [],
    statuses: filters?.statuses ?? [],
    employeeId,
    radius: filters?.radius ?? undefined,
    estateTypeId: filters?.estateTypeId ?? undefined,
    latitude: filters?.latitude ?? undefined,
    longitude: filters?.longitude ?? undefined,
    sortBy: filters?.sortBy ?? undefined,
  }

  if (filters?.limit) {
    input.pagination = {
      limit: filters.limit,
      offset: filters.offset ?? 0,
    }
  }

  if (filters?.priceRange) {
    input.priceRange = [filters.priceRange.min, filters.priceRange.max]
  }

  if (filters?.sizeRange) {
    input.sizeRange = [filters.sizeRange.min, filters.sizeRange.max]
  }

  try {
    const response = await nordvikApi.findEstatesForBroker(input)
    return response.employee?.estateEntries?.data?.filter(isNotNull) ?? []
  } catch (error) {
    console.error('Failed to fetch estates for broker', error)
    return []
  }
}

export async function findEstates({
  filters,
}: {
  filters: GQLQueryFindEstatesArgs['filters']
}) {
  // Doesn't need to be more specific than the date, works better for caching
  filters.soldDateAfter = filters.soldDateAfter
    ? format(filters.soldDateAfter, 'yyyy-MM-dd')
    : undefined

  const cacheKey = `findEstates:${JSON.stringify(Object.entries(filters).sort())}`

  const cached = await get<NordvikNoGQLEstate[]>(cacheKey)

  if (cached) {
    return cached.map((estate) => new Estate(estate))
  }

  const vars: NordvikNoGQLFindEstatesQueryVariables = {
    ...filters,
    price: filters.price?.min
      ? [filters.price.min, filters.price.max]
      : undefined,
    size: filters.size?.min ? [filters.size.min, filters.size.max] : undefined,
  }

  const response = await nordvikApi.findEstates(vars)

  const estates = response.estates?.estatesEntries?.filter(isNotNull) ?? []

  try {
    await set(cacheKey, estates, 10 * MINUTE)
  } catch (e) {
    console.error(e)
  }

  return estates.map((estate) => new Estate(estate)) ?? []
}
