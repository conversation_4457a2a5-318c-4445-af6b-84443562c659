type EmailAudit {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  subject: String
  fromEmail: String
  templateName: String
  contextType: String
  contextId: String
  globalMetadata: JSON
  recipients: [EmailAuditRecipient!]!
}

type EmailAuditRecipient {
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  emailAuditId: String!
  emailAudit: EmailAudit
  recipientEmail: String!
  contactId: String
  messageId: String!
  status: String!
  sentAt: DateTime!
  openedAt: DateTime
  openCount: Int!
  lastEventAt: DateTime
  bounceType: String
  rejectReason: String
  recipientMetadata: JSON
  rawLastEvent: JSON
}

type Query {
  emailAuditsByEstateId(estateId: String!): [EmailAudit!]!
}
