import { email_audit, email_audit_recipient } from '@nordvik/database'

import prisma from '@/db/prisma'
import { Context } from '@/server/context'
import {
  GQLEmailAudit,
  GQLEmailAuditRecipient,
} from '@/server/generated-schema'

export class EmailAudit implements GQLEmailAudit {
  constructor(
    protected data: email_audit & {
      recipients?: email_audit_recipient[]
    },
    protected context?: Context,
  ) {}

  get id() {
    return this.data.id
  }

  get createdAt() {
    return this.data.created_at
  }

  get updatedAt() {
    return this.data.updated_at
  }

  get subject() {
    return this.data.subject ?? undefined
  }

  get fromEmail() {
    return this.data.from_email ?? undefined
  }

  get templateName() {
    return this.data.template_name ?? undefined
  }

  get contextType() {
    return this.data.context_type ?? undefined
  }

  get contextId() {
    return this.data.context_id ?? undefined
  }

  get globalMetadata() {
    return this.data.global_metadata ?? undefined
  }

  async recipients() {
    if (this.data.recipients) {
      return this.data.recipients.map(
        (recipient) => new EmailAuditRecipient(recipient, this.context),
      )
    }

    const recipients = await prisma.email_audit_recipient.findMany({
      where: {
        email_audit_id: this.data.id,
      },
    })

    return recipients.map(
      (recipient) => new EmailAuditRecipient(recipient, this.context),
    )
  }

  static async findByEstateId(estateId: string, context?: Context) {
    const emailAudits = await prisma.email_audit.findMany({
      where: {
        context_type: 'estate',
        context_id: estateId,
      },
      include: {
        recipients: true,
      },
      orderBy: {
        created_at: 'desc',
      },
    })

    return emailAudits.map((emailAudit) => new EmailAudit(emailAudit, context))
  }
}

export class EmailAuditRecipient implements GQLEmailAuditRecipient {
  constructor(
    protected data: email_audit_recipient,
    protected context?: Context,
  ) {}

  get id() {
    return this.data.id
  }

  get createdAt() {
    return this.data.created_at
  }

  get updatedAt() {
    return this.data.updated_at
  }

  get emailAuditId() {
    return this.data.email_audit_id
  }

  async emailAudit() {
    const emailAudit = await prisma.email_audit.findUnique({
      where: {
        id: this.data.email_audit_id,
      },
    })

    return emailAudit ? new EmailAudit(emailAudit, this.context) : undefined
  }

  get recipientEmail() {
    return this.data.recipient_email
  }

  get contactId() {
    return this.data.contact_id ?? undefined
  }

  get messageId() {
    return this.data.message_id
  }

  get status() {
    return this.data.status
  }

  get sentAt() {
    return this.data.sent_at
  }

  get openedAt() {
    return this.data.opened_at ?? undefined
  }

  get openCount() {
    return this.data.open_count
  }

  get lastEventAt() {
    return this.data.last_event_at ?? undefined
  }

  get bounceType() {
    return this.data.bounce_type ?? undefined
  }

  get rejectReason() {
    return this.data.reject_reason ?? undefined
  }

  get recipientMetadata() {
    return this.data.recipient_metadata ?? undefined
  }

  get rawLastEvent() {
    return this.data.raw_last_event ?? undefined
  }
}

export default EmailAudit
