import { GraphQLClient, RequestOptions } from 'graphql-request';
import gql from 'graphql-tag';
export type Maybe<T> = T | undefined;
export type InputMaybe<T> = T | undefined;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
type GraphQLClientRequestHeaders = RequestOptions['requestHeaders'];
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Date: { input: string; output: string; }
  objectType: { input: any; output: any; }
};

export type NordvikNoGQLArea = {
  __typename?: 'Area';
  areaId?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  postalCodes?: Maybe<Scalars['objectType']['output']>;
};

export type NordvikNoGQLAreaList = {
  __typename?: 'AreaList';
  areasEntries?: Maybe<Array<Maybe<NordvikNoGQLArea>>>;
  count?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLAreaSize = {
  __typename?: 'AreaSize';
  BRABtotal?: Maybe<Scalars['Float']['output']>;
  BRAEtotal?: Maybe<Scalars['Float']['output']>;
  BRAItotal?: Maybe<Scalars['Float']['output']>;
  BRAtotal?: Maybe<Scalars['Float']['output']>;
  TBAtotal?: Maybe<Scalars['Float']['output']>;
};

export type NordvikNoGQLArticle = {
  __typename?: 'Article';
  articleCategory?: Maybe<NordvikNoGQLArticleCategory>;
  categories?: Maybe<Scalars['objectType']['output']>;
  containsVideo?: Maybe<Scalars['String']['output']>;
  excerpt?: Maybe<Scalars['String']['output']>;
  globals?: Maybe<Scalars['objectType']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  mainImage?: Maybe<Scalars['objectType']['output']>;
  mainImageLayout?: Maybe<Scalars['String']['output']>;
  mainImageText?: Maybe<Scalars['String']['output']>;
  mainVideoHd?: Maybe<Scalars['String']['output']>;
  mainVideoSd?: Maybe<Scalars['String']['output']>;
  modules?: Maybe<Scalars['objectType']['output']>;
  postDate?: Maybe<Scalars['objectType']['output']>;
  relatedArticles?: Maybe<Scalars['objectType']['output']>;
  relatedEstate?: Maybe<Scalars['objectType']['output']>;
  sideModules?: Maybe<Scalars['objectType']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  thumbnail?: Maybe<NordvikNoGQLImage>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLArticleCategory = {
  __typename?: 'ArticleCategory';
  id?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLArticleList = {
  __typename?: 'ArticleList';
  articlesEntries?: Maybe<Array<Maybe<NordvikNoGQLArticle>>>;
  count?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLAward = {
  __typename?: 'Award';
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  origin?: Maybe<Scalars['String']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLBanner = {
  __typename?: 'Banner';
  button?: Maybe<Scalars['objectType']['output']>;
  heading?: Maybe<Scalars['String']['output']>;
  label?: Maybe<Scalars['String']['output']>;
  links?: Maybe<Scalars['objectType']['output']>;
  media?: Maybe<Scalars['objectType']['output']>;
  paragraph?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLBrokerWithRole = {
  __typename?: 'BrokerWithRole';
  brokerRole?: Maybe<Scalars['Int']['output']>;
  employee?: Maybe<Scalars['objectType']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLCampaigns = {
  __typename?: 'Campaigns';
  count?: Maybe<Scalars['Int']['output']>;
  entries?: Maybe<Array<Maybe<NordvikNoGQLMarketingPackage>>>;
  items?: Maybe<Array<Maybe<Scalars['objectType']['output']>>>;
};

export type NordvikNoGQLChecklist = {
  __typename?: 'Checklist';
  changedBy?: Maybe<Scalars['String']['output']>;
  changedDate?: Maybe<Scalars['Date']['output']>;
  firstTag?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  value?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLCity = {
  __typename?: 'City';
  areas?: Maybe<Array<Maybe<NordvikNoGQLArea>>>;
  name?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLContact = {
  __typename?: 'Contact';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  companyName?: Maybe<Scalars['String']['output']>;
  contactId?: Maybe<Scalars['String']['output']>;
  contactType?: Maybe<Scalars['Int']['output']>;
  departmentId?: Maybe<Scalars['Int']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  organisationNumber?: Maybe<Scalars['String']['output']>;
  postalAddress?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  privatePhone?: Maybe<Scalars['String']['output']>;
  workPhone?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLCounty = {
  __typename?: 'County';
  active?: Maybe<Scalars['Boolean']['output']>;
  countyNr?: Maybe<Scalars['String']['output']>;
  municipalities?: Maybe<Array<Maybe<NordvikNoGQLMunicipality>>>;
  name?: Maybe<Scalars['String']['output']>;
  noName?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLDepartment = NordvikNoGQLSearchable & {
  __typename?: 'Department';
  aboutDepartment?: Maybe<Scalars['String']['output']>;
  changedDate?: Maybe<Scalars['Date']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  departmentId?: Maybe<Scalars['Int']['output']>;
  departmentNumber?: Maybe<Scalars['Int']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  displayKtiOnEmployee?: Maybe<Scalars['Boolean']['output']>;
  displayKtiRating?: Maybe<Scalars['Boolean']['output']>;
  displayKtiReviews?: Maybe<Scalars['Boolean']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  employees?: Maybe<Array<Maybe<NordvikNoGQLEmployee>>>;
  estates?: Maybe<Array<Maybe<NordvikNoGQLEstate>>>;
  fax?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  image?: Maybe<NordvikNoGQLDepartmentImage>;
  isRegion?: Maybe<Scalars['Boolean']['output']>;
  kti?: Maybe<Scalars['Float']['output']>;
  legalName?: Maybe<Scalars['String']['output']>;
  location?: Maybe<Scalars['objectType']['output']>;
  marketName?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  organisationNumber?: Maybe<Scalars['String']['output']>;
  overrideVitecData?: Maybe<Scalars['Boolean']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  postalAddress?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<NordvikNoGQLDepartmentRating>;
  ratings?: Maybe<Array<Maybe<NordvikNoGQLRating>>>;
  reviews?: Maybe<Array<Maybe<NordvikNoGQLReview>>>;
  roles?: Maybe<Array<Maybe<NordvikNoGQLDepartmentRole>>>;
  slug?: Maybe<Scalars['String']['output']>;
  streetAddress?: Maybe<Scalars['String']['output']>;
  subDepartments?: Maybe<Scalars['objectType']['output']>;
  videoHD?: Maybe<Scalars['String']['output']>;
  videoSD?: Maybe<Scalars['String']['output']>;
  visitCity?: Maybe<Scalars['String']['output']>;
  visitPostalCode?: Maybe<Scalars['String']['output']>;
  webPublish?: Maybe<Scalars['Boolean']['output']>;
};

export type NordvikNoGQLDepartmentAreas = {
  __typename?: 'DepartmentAreas';
  aliases?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  id?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLDepartmentImage = {
  __typename?: 'DepartmentImage';
  large?: Maybe<Scalars['String']['output']>;
  medium?: Maybe<Scalars['String']['output']>;
  small?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLDepartmentList = {
  __typename?: 'DepartmentList';
  count?: Maybe<Scalars['Int']['output']>;
  departmentsEntries?: Maybe<Array<Maybe<NordvikNoGQLDepartment>>>;
};

export type NordvikNoGQLDepartmentRating = {
  __typename?: 'DepartmentRating';
  average?: Maybe<Scalars['Float']['output']>;
  count?: Maybe<Scalars['Int']['output']>;
  reviewsCount?: Maybe<Scalars['Int']['output']>;
  weighted?: Maybe<Scalars['Float']['output']>;
};

export type NordvikNoGQLDepartmentRole = {
  __typename?: 'DepartmentRole';
  active?: Maybe<Scalars['Boolean']['output']>;
  employee?: Maybe<NordvikNoGQLEmployee>;
  employeeId?: Maybe<Scalars['String']['output']>;
  endDate?: Maybe<Scalars['Date']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ownerShare?: Maybe<Scalars['Float']['output']>;
  startDate?: Maybe<Scalars['Date']['output']>;
  typeId?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLDestination = {
  __typename?: 'Destination';
  departmentId?: Maybe<Scalars['objectType']['output']>;
  excerpt?: Maybe<Scalars['String']['output']>;
  heading?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  mainImage?: Maybe<Scalars['objectType']['output']>;
  modules?: Maybe<Scalars['objectType']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLEmployee = NordvikNoGQLSearchable & {
  __typename?: 'Employee';
  aboutMe?: Maybe<Scalars['String']['output']>;
  awards?: Maybe<Array<Maybe<NordvikNoGQLAward>>>;
  birthday?: Maybe<Scalars['String']['output']>;
  changedDate?: Maybe<Scalars['Date']['output']>;
  createdDate?: Maybe<Scalars['Date']['output']>;
  department?: Maybe<NordvikNoGQLDepartment>;
  departmentId?: Maybe<Array<Maybe<Scalars['Int']['output']>>>;
  departmentRoles?: Maybe<Array<Maybe<NordvikNoGQLEmployeeDepartmentRole>>>;
  departments?: Maybe<Array<Maybe<NordvikNoGQLDepartment>>>;
  description?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  employeeActive?: Maybe<Scalars['Boolean']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  endDate?: Maybe<Scalars['String']['output']>;
  estateEntries?: Maybe<NordvikNoGQLEmployeeEstateList>;
  estates?: Maybe<Array<Maybe<NordvikNoGQLEstate>>>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  image?: Maybe<NordvikNoGQLEmployeeImage>;
  imageTimestamp?: Maybe<Scalars['Date']['output']>;
  instagram?: Maybe<Scalars['String']['output']>;
  kti?: Maybe<Scalars['Float']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  nordvikAwards?: Maybe<Array<Maybe<NordvikNoGQLNordvikAward>>>;
  positions?: Maybe<Array<Maybe<NordvikNoGQLEmployeePosition>>>;
  publishStart?: Maybe<Scalars['Date']['output']>;
  rating?: Maybe<NordvikNoGQLEmployeeRating>;
  ratings?: Maybe<NordvikNoGQLEmployeeRatingList>;
  reviews?: Maybe<NordvikNoGQLEmployeeReviewList>;
  roles?: Maybe<Array<Maybe<NordvikNoGQLEmployeeRole>>>;
  salesBudget?: Maybe<Array<Maybe<NordvikNoGQLEmployeeSalesBudget>>>;
  sex?: Maybe<Scalars['Int']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  startDate?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  usp?: Maybe<Array<Maybe<NordvikNoGQLEmployeeUsp>>>;
  videoHD?: Maybe<Scalars['String']['output']>;
  videoSD?: Maybe<Scalars['String']['output']>;
  videoaskId?: Maybe<Scalars['String']['output']>;
  webPublish?: Maybe<Scalars['Boolean']['output']>;
  workPhone?: Maybe<Scalars['String']['output']>;
};


export type NordvikNoGQLEmployeeEstateEntriesArgs = {
  allMarketReady?: InputMaybe<Scalars['Boolean']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  assignmentTypeGroup?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  createdDateAfter?: InputMaybe<Scalars['Date']['input']>;
  estateTypeId?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  expireDateAfter?: InputMaybe<Scalars['Date']['input']>;
  inspectionDateAfter?: InputMaybe<Scalars['Date']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  pagination?: InputMaybe<NordvikNoGQLPaginationArgs>;
  priceRange?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  radius?: InputMaybe<Scalars['Float']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  sizeRange?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  soldDateBefore?: InputMaybe<Scalars['Date']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  takeOverDateAfter?: InputMaybe<Scalars['Date']['input']>;
  takeOverDateBefore?: InputMaybe<Scalars['Date']['input']>;
};


export type NordvikNoGQLEmployeeEstatesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};


export type NordvikNoGQLEmployeeRatingsArgs = {
  dateFrom?: InputMaybe<Scalars['Date']['input']>;
  dateTo?: InputMaybe<Scalars['Date']['input']>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  pagination?: InputMaybe<NordvikNoGQLPaginationArgs>;
  rating?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  sortDir?: InputMaybe<NordvikNoGQLSortDirection>;
};


export type NordvikNoGQLEmployeeReviewsArgs = {
  pagination?: InputMaybe<NordvikNoGQLPaginationArgs>;
};

export type NordvikNoGQLEmployeeDepartmentRole = {
  __typename?: 'EmployeeDepartmentRole';
  departmentId?: Maybe<Scalars['Int']['output']>;
  endDate?: Maybe<Scalars['Date']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ownerShare?: Maybe<Scalars['Float']['output']>;
  startDate?: Maybe<Scalars['Date']['output']>;
  typeId?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLEmployeeEstateList = {
  __typename?: 'EmployeeEstateList';
  data?: Maybe<Array<Maybe<NordvikNoGQLEstate>>>;
  pagination?: Maybe<NordvikNoGQLPagination>;
};

export type NordvikNoGQLEmployeeImage = {
  __typename?: 'EmployeeImage';
  large?: Maybe<Scalars['String']['output']>;
  medium?: Maybe<Scalars['String']['output']>;
  small?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLEmployeeList = {
  __typename?: 'EmployeeList';
  count?: Maybe<Scalars['Int']['output']>;
  employeesEntries?: Maybe<Array<Maybe<NordvikNoGQLEmployee>>>;
};

export type NordvikNoGQLEmployeePosition = {
  __typename?: 'EmployeePosition';
  endDate?: Maybe<Scalars['Date']['output']>;
  position?: Maybe<Scalars['String']['output']>;
  startDate?: Maybe<Scalars['Date']['output']>;
};

export type NordvikNoGQLEmployeeRating = {
  __typename?: 'EmployeeRating';
  average?: Maybe<Scalars['Float']['output']>;
  count?: Maybe<Scalars['Int']['output']>;
  reviewsCount?: Maybe<Scalars['Int']['output']>;
  weighted?: Maybe<Scalars['Float']['output']>;
};

export type NordvikNoGQLEmployeeRatingList = {
  __typename?: 'EmployeeRatingList';
  data?: Maybe<Array<Maybe<NordvikNoGQLRating>>>;
  pagination?: Maybe<NordvikNoGQLPagination>;
};

export type NordvikNoGQLEmployeeReviewList = {
  __typename?: 'EmployeeReviewList';
  data?: Maybe<Array<Maybe<NordvikNoGQLReview>>>;
  pagination?: Maybe<NordvikNoGQLPagination>;
};

export type NordvikNoGQLEmployeeRole = {
  __typename?: 'EmployeeRole';
  name?: Maybe<Scalars['String']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  typeId?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLEmployeeSalesBudget = {
  __typename?: 'EmployeeSalesBudget';
  budget?: Maybe<NordvikNoGQLEmployeeSalesBudgetYear>;
  months?: Maybe<Array<Maybe<NordvikNoGQLEmployeeSalesBudgetMonth>>>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLEmployeeSalesBudgetMonth = {
  __typename?: 'EmployeeSalesBudgetMonth';
  month?: Maybe<Scalars['Int']['output']>;
  new?: Maybe<NordvikNoGQLEmployeeSalesBudgetNew>;
  sum?: Maybe<NordvikNoGQLEmployeeSalesBudgetSum>;
  used?: Maybe<NordvikNoGQLEmployeeSalesBudgetUsedMonth>;
};

export type NordvikNoGQLEmployeeSalesBudgetNew = {
  __typename?: 'EmployeeSalesBudgetNew';
  averageIncomePerUnit?: Maybe<Scalars['Float']['output']>;
  income?: Maybe<Scalars['Float']['output']>;
  numberOfUnitSales?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLEmployeeSalesBudgetSum = {
  __typename?: 'EmployeeSalesBudgetSum';
  income?: Maybe<Scalars['Float']['output']>;
  numberOfSales?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLEmployeeSalesBudgetUsed = {
  __typename?: 'EmployeeSalesBudgetUsed';
  hitRatio?: Maybe<Scalars['String']['output']>;
  income?: Maybe<Scalars['Float']['output']>;
  numberOfCompletedAssignments?: Maybe<Scalars['Int']['output']>;
  numberOfCustomerMeetings?: Maybe<Scalars['Int']['output']>;
  numberOfSignedAssignments?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLEmployeeSalesBudgetUsedMonth = {
  __typename?: 'EmployeeSalesBudgetUsedMonth';
  averageIncomePerAssignment?: Maybe<Scalars['Float']['output']>;
  hitRatio?: Maybe<Scalars['String']['output']>;
  income?: Maybe<Scalars['Float']['output']>;
  numberOfCompletedAssignments?: Maybe<Scalars['Int']['output']>;
  numberOfCustomerMeetings?: Maybe<Scalars['Int']['output']>;
  numberOfSignedAssignments?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLEmployeeSalesBudgetYear = {
  __typename?: 'EmployeeSalesBudgetYear';
  new?: Maybe<NordvikNoGQLEmployeeSalesBudgetNew>;
  sum?: Maybe<NordvikNoGQLEmployeeSalesBudgetSum>;
  used?: Maybe<NordvikNoGQLEmployeeSalesBudgetUsed>;
};

export type NordvikNoGQLEmployeeUsp = {
  __typename?: 'EmployeeUsp';
  description?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLEntry = {
  __typename?: 'Entry';
  bullets?: Maybe<Scalars['objectType']['output']>;
  button?: Maybe<Scalars['objectType']['output']>;
  buttonText?: Maybe<Scalars['String']['output']>;
  excerpt?: Maybe<Scalars['String']['output']>;
  globals?: Maybe<Scalars['objectType']['output']>;
  heading?: Maybe<Scalars['String']['output']>;
  label?: Maybe<Scalars['String']['output']>;
  mainImage?: Maybe<Scalars['objectType']['output']>;
  mainImageText?: Maybe<Scalars['String']['output']>;
  mainVideoHd?: Maybe<Scalars['String']['output']>;
  mainVideoSd?: Maybe<Scalars['String']['output']>;
  modules?: Maybe<Scalars['objectType']['output']>;
  paragraph?: Maybe<Scalars['String']['output']>;
  partnerLogo?: Maybe<Scalars['String']['output']>;
  relatedArticles?: Maybe<Scalars['objectType']['output']>;
  sideModules?: Maybe<Scalars['objectType']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  titleWithColumns?: Maybe<Scalars['objectType']['output']>;
};

export type NordvikNoGQLEstate = NordvikNoGQLSearchable & {
  __typename?: 'Estate';
  activities?: Maybe<Scalars['objectType']['output']>;
  address?: Maybe<NordvikNoGQLEstateAddress>;
  ads?: Maybe<Array<Maybe<NordvikNoGQLEstateAd>>>;
  appraiser?: Maybe<NordvikNoGQLContact>;
  appraiserContact?: Maybe<NordvikNoGQLContact>;
  appraiserContactId?: Maybe<Scalars['String']['output']>;
  archivedDate?: Maybe<Scalars['Date']['output']>;
  area?: Maybe<NordvikNoGQLArea>;
  areaId?: Maybe<Scalars['String']['output']>;
  areaSize?: Maybe<NordvikNoGQLAreaSize>;
  assignmentNum?: Maybe<Scalars['String']['output']>;
  assignmentType?: Maybe<Scalars['Int']['output']>;
  assignmentTypeGroup?: Maybe<Scalars['Int']['output']>;
  brokersIdWithRoles?: Maybe<Array<Maybe<NordvikNoGQLBrokerWithRole>>>;
  buildings?: Maybe<Scalars['objectType']['output']>;
  businessManager?: Maybe<NordvikNoGQLContact>;
  businessManagerContact?: Maybe<NordvikNoGQLContact>;
  buyersEntries?: Maybe<Array<Maybe<NordvikNoGQLEstateContact>>>;
  changedDate?: Maybe<Scalars['Date']['output']>;
  checklist?: Maybe<Array<Maybe<NordvikNoGQLChecklist>>>;
  commercialAreas?: Maybe<Scalars['objectType']['output']>;
  commercialBalanceGroups?: Maybe<Scalars['objectType']['output']>;
  commercialCompanyCertificateDate?: Maybe<Scalars['Date']['output']>;
  commercialCorporationName?: Maybe<Scalars['String']['output']>;
  commercialCorporationOrganisationNumber?: Maybe<Scalars['String']['output']>;
  commercialCorporationShareholders?: Maybe<Scalars['objectType']['output']>;
  commercialEstablishmentDate?: Maybe<Scalars['Date']['output']>;
  commercialHasGarageDoors?: Maybe<Scalars['Boolean']['output']>;
  commercialPricePerDates?: Maybe<Scalars['objectType']['output']>;
  commercialPrices?: Maybe<Scalars['objectType']['output']>;
  commercialSaleItem?: Maybe<Scalars['Int']['output']>;
  commercialTextFields?: Maybe<Scalars['objectType']['output']>;
  commercialWorkingPlaces?: Maybe<Scalars['Int']['output']>;
  commissionAcceptedDate?: Maybe<Scalars['Date']['output']>;
  completionCertificateDate?: Maybe<Scalars['Date']['output']>;
  constructionYear?: Maybe<Scalars['Int']['output']>;
  contractMeetingDate?: Maybe<Scalars['Date']['output']>;
  createdDate?: Maybe<Scalars['Date']['output']>;
  customerPortal?: Maybe<Scalars['Boolean']['output']>;
  department?: Maybe<NordvikNoGQLDepartment>;
  departmentId?: Maybe<Scalars['Int']['output']>;
  employee?: Maybe<NordvikNoGQLEmployee>;
  employeeId?: Maybe<Scalars['String']['output']>;
  energyColorCode?: Maybe<Scalars['Int']['output']>;
  energyLetter?: Maybe<Scalars['Int']['output']>;
  estateBackOfficeStatusGroup?: Maybe<Scalars['Int']['output']>;
  estateBaseType?: Maybe<Scalars['Int']['output']>;
  estateFacilities?: Maybe<Scalars['objectType']['output']>;
  estateId?: Maybe<Scalars['String']['output']>;
  estatePreferences?: Maybe<Scalars['objectType']['output']>;
  estatePrice?: Maybe<Scalars['objectType']['output']>;
  estatePriceModel?: Maybe<NordvikNoGQLEstatePrice>;
  estateSettlementStatusGroup?: Maybe<Scalars['Int']['output']>;
  estateSize?: Maybe<Scalars['objectType']['output']>;
  estateSizeModel?: Maybe<NordvikNoGQLEstateSize>;
  estateType?: Maybe<Scalars['String']['output']>;
  estateTypeExternal?: Maybe<Scalars['Int']['output']>;
  estateTypeId?: Maybe<Scalars['String']['output']>;
  expireDate?: Maybe<Scalars['Date']['output']>;
  facilities?: Maybe<Scalars['objectType']['output']>;
  farm?: Maybe<Scalars['Boolean']['output']>;
  finnCode?: Maybe<Scalars['String']['output']>;
  finnExpireDate?: Maybe<Scalars['Date']['output']>;
  finnPublishDate?: Maybe<Scalars['Date']['output']>;
  firstPublished?: Maybe<Scalars['Date']['output']>;
  firstPublishedFinn?: Maybe<Scalars['Date']['output']>;
  firstPublishedHjem?: Maybe<Scalars['Date']['output']>;
  floor?: Maybe<Scalars['Int']['output']>;
  hasAd?: Maybe<Scalars['Boolean']['output']>;
  heading?: Maybe<Scalars['String']['output']>;
  hjemUrl?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  images?: Maybe<Array<Maybe<NordvikNoGQLEstateImage>>>;
  imagesArchived?: Maybe<Scalars['Date']['output']>;
  inSettlement?: Maybe<Scalars['Boolean']['output']>;
  inspectionDate?: Maybe<Scalars['Date']['output']>;
  landOwnerEstateDocumentDate?: Maybe<Scalars['Date']['output']>;
  lastDocumentChangeDate?: Maybe<Scalars['Date']['output']>;
  lastImageChangeDate?: Maybe<Scalars['Date']['output']>;
  latitude?: Maybe<Scalars['Float']['output']>;
  leasingContractDate?: Maybe<Scalars['Date']['output']>;
  links?: Maybe<Array<Maybe<NordvikNoGQLLink>>>;
  liveAndManagementDuty?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Scalars['objectType']['output']>;
  longitude?: Maybe<Scalars['Float']['output']>;
  managementDuty?: Maybe<Scalars['Boolean']['output']>;
  matrikkel?: Maybe<Array<Maybe<NordvikNoGQLMatrikkel>>>;
  municipality?: Maybe<Scalars['String']['output']>;
  municipalityId?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  noOfBathRooms?: Maybe<Scalars['Int']['output']>;
  noOfBedRooms?: Maybe<Scalars['Int']['output']>;
  noOfRooms?: Maybe<Scalars['Int']['output']>;
  objectFacilities?: Maybe<Scalars['objectType']['output']>;
  odel?: Maybe<Scalars['Boolean']['output']>;
  ownAdvertisementType?: Maybe<Scalars['String']['output']>;
  ownAssignmentType?: Maybe<Scalars['String']['output']>;
  ownership?: Maybe<Scalars['Int']['output']>;
  partOwnership?: Maybe<Scalars['objectType']['output']>;
  plot?: Maybe<Scalars['objectType']['output']>;
  postalCode?: Maybe<NordvikNoGQLPostalCode>;
  preview?: Maybe<Scalars['Boolean']['output']>;
  projectDevelopersReservationCompletedDate?: Maybe<Scalars['Date']['output']>;
  projectDevelopersReservationDueDate?: Maybe<Scalars['Date']['output']>;
  projectDevelopersReservations?: Maybe<Scalars['objectType']['output']>;
  projectId?: Maybe<Scalars['String']['output']>;
  projectName?: Maybe<Scalars['String']['output']>;
  projectRelation?: Maybe<Scalars['Int']['output']>;
  projectTextFields?: Maybe<Scalars['objectType']['output']>;
  projectUnits?: Maybe<Scalars['objectType']['output']>;
  publicApartmentNumber?: Maybe<Scalars['String']['output']>;
  publishEnd?: Maybe<Scalars['Date']['output']>;
  publishStart?: Maybe<Scalars['Date']['output']>;
  requiresConcession?: Maybe<Scalars['Boolean']['output']>;
  sellers?: Maybe<Scalars['objectType']['output']>;
  sellersEntries?: Maybe<Array<Maybe<NordvikNoGQLEstateContact>>>;
  sellersWhenSold?: Maybe<Scalars['objectType']['output']>;
  settleDepartmentId?: Maybe<Scalars['Int']['output']>;
  showingNote?: Maybe<Scalars['String']['output']>;
  showings?: Maybe<Scalars['objectType']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  soldDate?: Maybe<Scalars['Date']['output']>;
  staticMapUrl?: Maybe<Scalars['String']['output']>;
  stats?: Maybe<Scalars['objectType']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  sumArea?: Maybe<Scalars['objectType']['output']>;
  systemId?: Maybe<Scalars['String']['output']>;
  tag?: Maybe<Scalars['String']['output']>;
  takeOverDate?: Maybe<Scalars['Date']['output']>;
  takeoverComment?: Maybe<Scalars['String']['output']>;
  textFields?: Maybe<Scalars['objectType']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  valuationTax?: Maybe<Scalars['objectType']['output']>;
};

export type NordvikNoGQLEstateAd = {
  __typename?: 'EstateAd';
  adStatus?: Maybe<Scalars['Int']['output']>;
  channel?: Maybe<Scalars['Int']['output']>;
  estateBaseType?: Maybe<Scalars['Int']['output']>;
  estateId?: Maybe<Scalars['String']['output']>;
  estateStatus?: Maybe<Scalars['Int']['output']>;
  externalReference?: Maybe<Scalars['String']['output']>;
  finnAdType?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  lastChanged?: Maybe<Scalars['Date']['output']>;
  link?: Maybe<Scalars['String']['output']>;
  ownAdvertisementType?: Maybe<Scalars['String']['output']>;
  publishEnd?: Maybe<Scalars['Date']['output']>;
  publishStart?: Maybe<Scalars['Date']['output']>;
  removed?: Maybe<Scalars['Boolean']['output']>;
  statistics?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  transferTime?: Maybe<Scalars['Date']['output']>;
};

export type NordvikNoGQLEstateAddress = {
  __typename?: 'EstateAddress';
  apartmentNumber?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  streetAdress?: Maybe<Scalars['String']['output']>;
  zipCode?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLEstateContact = {
  __typename?: 'EstateContact';
  companyContactPerson?: Maybe<NordvikNoGQLContact>;
  companyContactPersonId?: Maybe<Scalars['String']['output']>;
  contact?: Maybe<NordvikNoGQLContact>;
  contactId?: Maybe<Scalars['String']['output']>;
  groupId?: Maybe<Scalars['Int']['output']>;
  groupProxy?: Maybe<NordvikNoGQLContact>;
  groupProxyId?: Maybe<Scalars['String']['output']>;
  mainContact?: Maybe<Scalars['Boolean']['output']>;
  proxy?: Maybe<NordvikNoGQLContact>;
  proxyId?: Maybe<Scalars['String']['output']>;
  registered?: Maybe<Scalars['Date']['output']>;
  relationName?: Maybe<Scalars['String']['output']>;
  relationType?: Maybe<Scalars['Int']['output']>;
  singleContact?: Maybe<Scalars['Boolean']['output']>;
};

export type NordvikNoGQLEstateImage = {
  __typename?: 'EstateImage';
  estate?: Maybe<NordvikNoGQLEstate>;
  imageCategoryName?: Maybe<Scalars['String']['output']>;
  imageDescription?: Maybe<Scalars['String']['output']>;
  imageId?: Maybe<Scalars['String']['output']>;
  imageSequence?: Maybe<Scalars['Int']['output']>;
  url?: Maybe<Scalars['objectType']['output']>;
};

export type NordvikNoGQLEstateList = {
  __typename?: 'EstateList';
  count?: Maybe<Scalars['Int']['output']>;
  estatesEntries?: Maybe<Array<Maybe<NordvikNoGQLEstate>>>;
  limit?: Maybe<Scalars['Int']['output']>;
  offset?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLEstatePrice = {
  __typename?: 'EstatePrice';
  additionalAgreementOptions?: Maybe<Scalars['Float']['output']>;
  collectiveAssets?: Maybe<Scalars['Float']['output']>;
  collectiveDebt?: Maybe<Scalars['Float']['output']>;
  communityTax?: Maybe<Scalars['Float']['output']>;
  communityTaxYear?: Maybe<Scalars['Int']['output']>;
  estimatedValue?: Maybe<Scalars['Float']['output']>;
  leasingPartyTransportFee?: Maybe<Scalars['Float']['output']>;
  loanFare?: Maybe<Scalars['Float']['output']>;
  originalAgreementPrice?: Maybe<Scalars['Float']['output']>;
  originalExpensesPrice?: Maybe<Scalars['Float']['output']>;
  otherExpenses?: Maybe<Scalars['String']['output']>;
  priceSuggestion?: Maybe<Scalars['Float']['output']>;
  purchaseCostsAmount?: Maybe<Scalars['Float']['output']>;
  rent?: Maybe<NordvikNoGQLRent>;
  salesCostDescription?: Maybe<Scalars['String']['output']>;
  soldPrice?: Maybe<Scalars['Float']['output']>;
  totalPrice?: Maybe<Scalars['Float']['output']>;
  totalPriceExclusiveCostsAndDebt?: Maybe<Scalars['Float']['output']>;
  transportAgreementCosts?: Maybe<Scalars['Float']['output']>;
  waterRate?: Maybe<Scalars['Float']['output']>;
  waterRateDescription?: Maybe<Scalars['String']['output']>;
  waterRateYear?: Maybe<Scalars['Int']['output']>;
  yearlyLeaseFee?: Maybe<Scalars['Float']['output']>;
  yearlySocietyTax?: Maybe<Scalars['Float']['output']>;
};

export type NordvikNoGQLEstateSize = {
  __typename?: 'EstateSize';
  grossArea?: Maybe<Scalars['Float']['output']>;
  primaryRoomArea?: Maybe<Scalars['Float']['output']>;
  primaryRoomAreaDescription?: Maybe<Scalars['String']['output']>;
  usableArea?: Maybe<Scalars['Float']['output']>;
};

export enum NordvikNoGQLEstateStatus {
  ForSale = 'forSale',
  Preparation = 'preparation',
  Request = 'request',
  Sold = 'sold'
}

export type NordvikNoGQLEstimate = {
  __typename?: 'Estimate';
  value?: Maybe<Scalars['objectType']['output']>;
};

export type NordvikNoGQLFilterCities = {
  __typename?: 'FilterCities';
  id?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLHallOfFame = {
  __typename?: 'HallOfFame';
  entries?: Maybe<Array<Maybe<NordvikNoGQLHallOfFameAward>>>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLHallOfFameAward = {
  __typename?: 'HallOfFameAward';
  awardId?: Maybe<Scalars['Int']['output']>;
  employee?: Maybe<NordvikNoGQLEmployee>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  origin?: Maybe<Scalars['String']['output']>;
  private?: Maybe<Scalars['Boolean']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLImage = {
  __typename?: 'Image';
  focalPoint?: Maybe<Scalars['objectType']['output']>;
  hasFocalPoint?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  large?: Maybe<Scalars['String']['output']>;
  medium?: Maybe<Scalars['String']['output']>;
  roomType?: Maybe<Scalars['objectType']['output']>;
  small?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLImageList = {
  __typename?: 'ImageList';
  count?: Maybe<Scalars['Int']['output']>;
  imageBankEntries?: Maybe<Array<Maybe<NordvikNoGQLImage>>>;
};

export type NordvikNoGQLKti = {
  __typename?: 'Kti';
  averageRating?: Maybe<Scalars['Float']['output']>;
  ratingCount?: Maybe<Scalars['String']['output']>;
  reviewCount?: Maybe<Scalars['Int']['output']>;
  reviews?: Maybe<Scalars['objectType']['output']>;
};

export type NordvikNoGQLKtiNew = {
  __typename?: 'KtiNew';
  average?: Maybe<Scalars['Float']['output']>;
  count?: Maybe<Scalars['Int']['output']>;
  ratings?: Maybe<Array<Maybe<NordvikNoGQLRating>>>;
  reviews?: Maybe<Array<Maybe<NordvikNoGQLReview>>>;
  reviewsCount?: Maybe<Scalars['Int']['output']>;
  weighted?: Maybe<Scalars['Float']['output']>;
};

export type NordvikNoGQLKtiWhitelist = {
  __typename?: 'KtiWhitelist';
  ratings?: Maybe<Array<Maybe<NordvikNoGQLWhitelistRating>>>;
};

export type NordvikNoGQLLead = {
  __typename?: 'Lead';
  date?: Maybe<Scalars['Date']['output']>;
  departmentId?: Maybe<Scalars['String']['output']>;
  direct?: Maybe<Scalars['String']['output']>;
  employeeEmail?: Maybe<Scalars['String']['output']>;
  leadId?: Maybe<Scalars['String']['output']>;
  officeId?: Maybe<Scalars['String']['output']>;
  officeName?: Maybe<Scalars['String']['output']>;
  partner?: Maybe<Scalars['String']['output']>;
  referrer?: Maybe<Scalars['String']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  tipId?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLLeadList = {
  __typename?: 'LeadList';
  count?: Maybe<Scalars['Int']['output']>;
  leadsEntries?: Maybe<Array<Maybe<NordvikNoGQLLead>>>;
};

export type NordvikNoGQLLink = {
  __typename?: 'Link';
  linkType?: Maybe<Scalars['Int']['output']>;
  text?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLMarketingChannel = {
  __typename?: 'MarketingChannel';
  id?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLMarketingPackage = {
  __typename?: 'MarketingPackage';
  dateOrdered?: Maybe<Scalars['Date']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  estateAddress?: Maybe<Scalars['String']['output']>;
  estateId?: Maybe<Scalars['String']['output']>;
  externalId?: Maybe<Scalars['String']['output']>;
  fbAdsetIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  marketingPackage?: Maybe<Scalars['Int']['output']>;
  orderEndDate?: Maybe<Scalars['Date']['output']>;
  orderStartDate?: Maybe<Scalars['Date']['output']>;
  orderStatus?: Maybe<Scalars['String']['output']>;
  orderStatusId?: Maybe<Scalars['Int']['output']>;
  packageName?: Maybe<Scalars['String']['output']>;
  updateIfSold?: Maybe<Scalars['Boolean']['output']>;
};

export type NordvikNoGQLMarketingPackageCms = {
  __typename?: 'MarketingPackageCms';
  active?: Maybe<Scalars['Boolean']['output']>;
  channels?: Maybe<Array<Maybe<NordvikNoGQLMarketingChannel>>>;
  clicks?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  packageId?: Maybe<Scalars['Int']['output']>;
  price?: Maybe<Scalars['Int']['output']>;
  productTag?: Maybe<Scalars['String']['output']>;
  public?: Maybe<Scalars['Boolean']['output']>;
  shortName?: Maybe<Scalars['String']['output']>;
  views?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLMatrikkel = {
  __typename?: 'Matrikkel';
  bnr?: Maybe<Scalars['Int']['output']>;
  fnr?: Maybe<Scalars['Int']['output']>;
  gnr?: Maybe<Scalars['Int']['output']>;
  knr?: Maybe<Scalars['Int']['output']>;
  ownPart?: Maybe<Scalars['String']['output']>;
  snr?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLMunicipality = {
  __typename?: 'Municipality';
  active?: Maybe<Scalars['Boolean']['output']>;
  county?: Maybe<NordvikNoGQLCounty>;
  municipalityNr?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  noName?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLNabolag = {
  __typename?: 'Nabolag';
  id?: Maybe<Scalars['String']['output']>;
  linkPdf?: Maybe<Scalars['String']['output']>;
  profileCards?: Maybe<Scalars['objectType']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLNews = {
  __typename?: 'News';
  excerpt?: Maybe<Scalars['String']['output']>;
  globals?: Maybe<Scalars['objectType']['output']>;
  id?: Maybe<Scalars['Int']['output']>;
  mainImage?: Maybe<Scalars['objectType']['output']>;
  mainImageLayout?: Maybe<Scalars['String']['output']>;
  mainImageText?: Maybe<Scalars['String']['output']>;
  mainVideoHd?: Maybe<Scalars['String']['output']>;
  mainVideoSd?: Maybe<Scalars['String']['output']>;
  modules?: Maybe<Scalars['objectType']['output']>;
  postDate?: Maybe<Scalars['objectType']['output']>;
  sideModules?: Maybe<Scalars['objectType']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  thumbnail?: Maybe<NordvikNoGQLImage>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLNewsList = {
  __typename?: 'NewsList';
  count?: Maybe<Scalars['Int']['output']>;
  newsEntries?: Maybe<Array<Maybe<NordvikNoGQLNews>>>;
};

export type NordvikNoGQLNordvikAward = {
  __typename?: 'NordvikAward';
  awardId: Scalars['ID']['output'];
  hidden?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  origin?: Maybe<Scalars['String']['output']>;
  private?: Maybe<Scalars['Boolean']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLPagination = {
  __typename?: 'Pagination';
  count?: Maybe<Scalars['Int']['output']>;
  limit?: Maybe<Scalars['Int']['output']>;
  offset?: Maybe<Scalars['Int']['output']>;
  total?: Maybe<Scalars['Int']['output']>;
};

export type NordvikNoGQLPaginationArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};

export type NordvikNoGQLPostalCode = {
  __typename?: 'PostalCode';
  bringValid: Scalars['Boolean']['output'];
  category?: Maybe<Scalars['String']['output']>;
  cityArea?: Maybe<Scalars['String']['output']>;
  county?: Maybe<Scalars['String']['output']>;
  dataQuality?: Maybe<Scalars['Int']['output']>;
  geometry?: Maybe<Scalars['objectType']['output']>;
  lastUpdated?: Maybe<Scalars['Date']['output']>;
  location?: Maybe<Scalars['objectType']['output']>;
  municipality?: Maybe<Scalars['String']['output']>;
  municipalityNr?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  postalName?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLPriceIndex = {
  __typename?: 'PriceIndex';
  antallLagtUt?: Maybe<Scalars['Int']['output']>;
  antallLagtUtYTD?: Maybe<Scalars['Int']['output']>;
  antallSolgte?: Maybe<Scalars['Int']['output']>;
  antallSolgteYTD?: Maybe<Scalars['Int']['output']>;
  dato?: Maybe<Scalars['Date']['output']>;
  endringSnittIndeksYTD?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  indeks?: Maybe<Scalars['Float']['output']>;
  indeksEndring1Kvartal?: Maybe<Scalars['Float']['output']>;
  indeksEndring1Mnd?: Maybe<Scalars['Float']['output']>;
  indeksEndring4Kvartal?: Maybe<Scalars['Float']['output']>;
  indeksEndring5Ar?: Maybe<Scalars['Float']['output']>;
  indeksEndring10Ar?: Maybe<Scalars['Float']['output']>;
  indeksEndring12Mnd?: Maybe<Scalars['Float']['output']>;
  indeksPrognoseVSForrigeAr?: Maybe<Scalars['Float']['output']>;
  indeksprognose?: Maybe<Scalars['Float']['output']>;
  kvmPris10prosentPercentil?: Maybe<Scalars['Float']['output']>;
  kvmPris25prosentPercentil?: Maybe<Scalars['Float']['output']>;
  kvmPris50prosentPercentil?: Maybe<Scalars['Float']['output']>;
  kvmPris75prosentPercentil?: Maybe<Scalars['Float']['output']>;
  kvmPris90prosentPercentil?: Maybe<Scalars['Float']['output']>;
  land?: Maybe<Scalars['String']['output']>;
  medianKvmPris?: Maybe<Scalars['Float']['output']>;
  medianPris?: Maybe<Scalars['Float']['output']>;
  medianPrisSiste6mnd?: Maybe<Scalars['Float']['output']>;
  omrade?: Maybe<Scalars['String']['output']>;
  omradeniva?: Maybe<Scalars['Int']['output']>;
  pris10prosentPercentil?: Maybe<Scalars['Float']['output']>;
  pris25prosentPercentil?: Maybe<Scalars['Float']['output']>;
  pris50prosentPercentil?: Maybe<Scalars['Float']['output']>;
  pris75prosentPercentil?: Maybe<Scalars['Float']['output']>;
  pris90prosentPercentil?: Maybe<Scalars['Float']['output']>;
  region?: Maybe<Scalars['String']['output']>;
  snittAvvikPrisUtropspris?: Maybe<Scalars['Float']['output']>;
  snittFormidlingstid?: Maybe<Scalars['Float']['output']>;
  snittIndeksYTD?: Maybe<Scalars['Float']['output']>;
  snittKvmPris?: Maybe<Scalars['Float']['output']>;
  snittKvmPrisSiste6mnd?: Maybe<Scalars['Float']['output']>;
  snittMedianLiggetid?: Maybe<Scalars['Float']['output']>;
  snittPris?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLPriceStatisticsArea = {
  __typename?: 'PriceStatisticsArea';
  areaName?: Maybe<Scalars['String']['output']>;
  areaNameSlug?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  priceIndexes?: Maybe<Array<Maybe<NordvikNoGQLPriceIndex>>>;
  regionName?: Maybe<Scalars['String']['output']>;
  regionNameSlug?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLQuery = {
  __typename?: 'Query';
  area?: Maybe<NordvikNoGQLArea>;
  areas?: Maybe<NordvikNoGQLAreaList>;
  article?: Maybe<NordvikNoGQLArticle>;
  articleCategories?: Maybe<Array<Maybe<NordvikNoGQLArticleCategory>>>;
  articles?: Maybe<NordvikNoGQLArticleList>;
  banner?: Maybe<NordvikNoGQLBanner>;
  campaigns?: Maybe<NordvikNoGQLCampaigns>;
  cities?: Maybe<Array<Maybe<NordvikNoGQLCity>>>;
  contact?: Maybe<NordvikNoGQLContact>;
  counties?: Maybe<Array<Maybe<NordvikNoGQLCounty>>>;
  county?: Maybe<NordvikNoGQLCounty>;
  debug?: Maybe<NordvikNoGQLTest>;
  department?: Maybe<NordvikNoGQLDepartment>;
  departmentAreas?: Maybe<Array<Maybe<NordvikNoGQLDepartmentAreas>>>;
  departments?: Maybe<NordvikNoGQLDepartmentList>;
  destination?: Maybe<NordvikNoGQLDestination>;
  employee?: Maybe<NordvikNoGQLEmployee>;
  employees?: Maybe<NordvikNoGQLEmployeeList>;
  estate?: Maybe<NordvikNoGQLEstate>;
  estates?: Maybe<NordvikNoGQLEstateList>;
  estatesGroup?: Maybe<NordvikNoGQLEstateList>;
  estimateValue?: Maybe<NordvikNoGQLEstimate>;
  filterCities?: Maybe<Array<Maybe<NordvikNoGQLFilterCities>>>;
  getAllEstates?: Maybe<NordvikNoGQLEstateList>;
  getNearestDepartment?: Maybe<Array<Maybe<NordvikNoGQLDepartment>>>;
  hallOfFame?: Maybe<Array<Maybe<NordvikNoGQLHallOfFame>>>;
  image?: Maybe<NordvikNoGQLImage>;
  imageBankImages?: Maybe<NordvikNoGQLImageList>;
  job?: Maybe<NordvikNoGQLEntry>;
  kti?: Maybe<NordvikNoGQLKti>;
  ktiWhitelist?: Maybe<NordvikNoGQLKtiWhitelist>;
  leadSources?: Maybe<NordvikNoGQLSources>;
  leads?: Maybe<NordvikNoGQLLeadList>;
  marketingPackages?: Maybe<Array<Maybe<NordvikNoGQLMarketingPackageCms>>>;
  municipalities?: Maybe<Array<Maybe<NordvikNoGQLMunicipality>>>;
  municipality?: Maybe<NordvikNoGQLMunicipality>;
  nabolag?: Maybe<NordvikNoGQLNabolag>;
  news?: Maybe<NordvikNoGQLNewsList>;
  newsEntry?: Maybe<NordvikNoGQLNews>;
  nordvikEkstraStats?: Maybe<NordvikNoGQLStats>;
  page?: Maybe<NordvikNoGQLEntry>;
  partner?: Maybe<NordvikNoGQLEntry>;
  partnerLeads?: Maybe<NordvikNoGQLLeadList>;
  priceStatistics?: Maybe<NordvikNoGQLStatisticsList>;
  priceStatisticsAreas?: Maybe<Array<Maybe<NordvikNoGQLPriceStatisticsArea>>>;
  ratings?: Maybe<NordvikNoGQLKtiNew>;
  relatedArticleToEstate?: Maybe<NordvikNoGQLRelatedArticle>;
  relatedArticlesToDepartment?: Maybe<NordvikNoGQLArticleList>;
  relatedArticlesToEstate?: Maybe<NordvikNoGQLArticleList>;
  relatedEstateToEstate?: Maybe<NordvikNoGQLEstateList>;
  roomTypes?: Maybe<Array<Maybe<NordvikNoGQLRoomType>>>;
  search: Array<Maybe<NordvikNoGQLSearchable>>;
  searchExtended: Array<Maybe<NordvikNoGQLSearchable>>;
  tips?: Maybe<NordvikNoGQLTipsList>;
};


export type NordvikNoGQLQueryAreaArgs = {
  areaId: Scalars['String']['input'];
};


export type NordvikNoGQLQueryAreasArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryArticleArgs = {
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryArticlesArgs = {
  categorySlug?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryBannerArgs = {
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryCampaignsArgs = {
  estateId?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryContactArgs = {
  contactId?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryCountyArgs = {
  countyNr: Scalars['String']['input'];
};


export type NordvikNoGQLQueryDepartmentArgs = {
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryDepartmentsArgs = {
  all?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryDestinationArgs = {
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryEmployeeArgs = {
  all?: InputMaybe<Scalars['Boolean']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};


export type NordvikNoGQLQueryEmployeesArgs = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
  departmentId?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  ids?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  proxies?: InputMaybe<Scalars['Boolean']['input']>;
  roles?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  status?: InputMaybe<Scalars['Int']['input']>;
  webPublish?: InputMaybe<Scalars['Boolean']['input']>;
};


export type NordvikNoGQLQueryEstateArgs = {
  all?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['String']['input'];
  preview?: InputMaybe<Scalars['Boolean']['input']>;
  signature?: InputMaybe<Scalars['String']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};


export type NordvikNoGQLQueryEstatesArgs = {
  area?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  baseType?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  cities?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  city?: InputMaybe<Scalars['String']['input']>;
  departmentGroup?: InputMaybe<Scalars['String']['input']>;
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  estateType?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  noBedRooms?: InputMaybe<Scalars['String']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  price?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  radius?: InputMaybe<Scalars['Float']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  size?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  soldDateAfter?: InputMaybe<Scalars['Date']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};


export type NordvikNoGQLQueryEstatesGroupArgs = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};


export type NordvikNoGQLQueryEstimateValueArgs = {
  latitude: Scalars['Float']['input'];
  longitude: Scalars['Float']['input'];
};


export type NordvikNoGQLQueryGetAllEstatesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryGetNearestDepartmentArgs = {
  latitude: Scalars['Float']['input'];
  longitude: Scalars['Float']['input'];
  status?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryHallOfFameArgs = {
  awardId?: InputMaybe<Scalars['Int']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryImageArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryImageBankImagesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  roomTypeSlug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryJobArgs = {
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryKtiArgs = {
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  includeReviews?: InputMaybe<Scalars['Boolean']['input']>;
};


export type NordvikNoGQLQueryKtiWhitelistArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryLeadsArgs = {
  all?: InputMaybe<Scalars['Boolean']['input']>;
  departmentId?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Date']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['Date']['input']>;
};


export type NordvikNoGQLQueryMarketingPackagesArgs = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
  publicVisible?: InputMaybe<Scalars['Boolean']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryMunicipalityArgs = {
  municipalityNr: Scalars['String']['input'];
};


export type NordvikNoGQLQueryNabolagArgs = {
  postalCode?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryNewsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};


export type NordvikNoGQLQueryNewsEntryArgs = {
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryNordvikEkstraStatsArgs = {
  estateId: Scalars['String']['input'];
};


export type NordvikNoGQLQueryPageArgs = {
  id?: InputMaybe<Scalars['Int']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryPartnerArgs = {
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryPartnerLeadsArgs = {
  all?: InputMaybe<Scalars['Boolean']['input']>;
  endDate?: InputMaybe<Scalars['Date']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  partner?: InputMaybe<Scalars['String']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['Date']['input']>;
};


export type NordvikNoGQLQueryPriceStatisticsArgs = {
  areaName?: InputMaybe<Scalars['String']['input']>;
  filterYears?: InputMaybe<Scalars['Float']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  regionName?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryRatingsArgs = {
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  from?: InputMaybe<Scalars['Date']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
};


export type NordvikNoGQLQueryRelatedArticleToEstateArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryRelatedArticlesToDepartmentArgs = {
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryRelatedArticlesToEstateArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type NordvikNoGQLQueryRelatedEstateToEstateArgs = {
  id: Scalars['String']['input'];
};


export type NordvikNoGQLQuerySearchArgs = {
  text: Scalars['String']['input'];
};


export type NordvikNoGQLQuerySearchExtendedArgs = {
  query: Scalars['String']['input'];
};


export type NordvikNoGQLQueryTipsArgs = {
  contactId?: InputMaybe<Scalars['String']['input']>;
  createdDateAfter?: InputMaybe<Scalars['Date']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  estateId?: InputMaybe<Scalars['String']['input']>;
  mobilePhone?: InputMaybe<Scalars['String']['input']>;
  originSource?: InputMaybe<Scalars['Int']['input']>;
  originType?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
  tipId?: InputMaybe<Scalars['String']['input']>;
};

export type NordvikNoGQLRating = {
  __typename?: 'Rating';
  created_at?: Maybe<Scalars['Date']['output']>;
  departmentId?: Maybe<Scalars['Int']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  featured?: Maybe<Scalars['Boolean']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Float']['output']>;
  ratingId?: Maybe<Scalars['Int']['output']>;
  review?: Maybe<NordvikNoGQLReview>;
  userEmail?: Maybe<Scalars['String']['output']>;
  userName?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLRelatedArticle = {
  __typename?: 'RelatedArticle';
  id?: Maybe<Scalars['String']['output']>;
  postDate?: Maybe<Scalars['objectType']['output']>;
  postDateFormatted?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLRent = {
  __typename?: 'Rent';
  rentIncludes?: Maybe<Scalars['String']['output']>;
  rentPrMonth?: Maybe<Scalars['Float']['output']>;
};

export type NordvikNoGQLReview = {
  __typename?: 'Review';
  attributes?: Maybe<Array<Maybe<Scalars['objectType']['output']>>>;
  created_at?: Maybe<Scalars['Date']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Float']['output']>;
  reviewId?: Maybe<Scalars['Int']['output']>;
  testimonial?: Maybe<Scalars['Boolean']['output']>;
  text?: Maybe<Scalars['String']['output']>;
  userEmail?: Maybe<Scalars['String']['output']>;
  userName?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLRoomType = {
  __typename?: 'RoomType';
  id?: Maybe<Scalars['String']['output']>;
  image?: Maybe<NordvikNoGQLImage>;
  slug?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLSearchable = {
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
};

export enum NordvikNoGQLSortDirection {
  Asc = 'ASC',
  Desc = 'DESC'
}

export type NordvikNoGQLSources = {
  __typename?: 'Sources';
  sources?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type NordvikNoGQLStatisticsList = {
  __typename?: 'StatisticsList';
  indexes?: Maybe<Array<Maybe<NordvikNoGQLPriceIndex>>>;
  secondaryIndexes?: Maybe<Array<Maybe<NordvikNoGQLPriceIndex>>>;
};

export type NordvikNoGQLStats = {
  __typename?: 'Stats';
  data?: Maybe<Scalars['objectType']['output']>;
  estateId?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLTest = {
  __typename?: 'Test';
  message?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLTip = {
  __typename?: 'Tip';
  contactId?: Maybe<Scalars['String']['output']>;
  created?: Maybe<Scalars['Date']['output']>;
  departmentId?: Maybe<Scalars['Int']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  employeeId?: Maybe<Scalars['String']['output']>;
  estateId?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  mobilePhone?: Maybe<Scalars['String']['output']>;
  modified?: Maybe<Scalars['Date']['output']>;
  originSource?: Maybe<Scalars['Int']['output']>;
  originType?: Maybe<Scalars['Int']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  productId?: Maybe<Scalars['String']['output']>;
  recipientId?: Maybe<Scalars['String']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  streetAdress?: Maybe<Scalars['String']['output']>;
  tipId?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLTipsList = {
  __typename?: 'TipsList';
  count?: Maybe<Scalars['Int']['output']>;
  entries?: Maybe<Array<Maybe<NordvikNoGQLTip>>>;
};

export type NordvikNoGQLTopList = {
  __typename?: 'TopList';
  count?: Maybe<Scalars['Int']['output']>;
  entries?: Maybe<Scalars['objectType']['output']>;
};

export type NordvikNoGQLWhitelistRating = {
  __typename?: 'WhitelistRating';
  date?: Maybe<Scalars['Date']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  rating?: Maybe<Scalars['Float']['output']>;
  text?: Maybe<Scalars['String']['output']>;
};

export type NordvikNoGQLContactQueryVariables = Exact<{
  contactId?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLContactQuery = { __typename?: 'Query', contact?: { __typename?: 'Contact', contactId?: string | undefined, departmentId?: number | undefined, contactType?: number | undefined, companyName?: string | undefined, organisationNumber?: string | undefined, firstName?: string | undefined, lastName?: string | undefined, mobilePhone?: string | undefined, privatePhone?: string | undefined, workPhone?: string | undefined, email?: string | undefined, address?: string | undefined, postalAddress?: string | undefined, postalCode?: string | undefined, city?: string | undefined } | undefined };

export type NordvikNoGQLDashboardKtiQueryVariables = Exact<{
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLDashboardKtiQuery = { __typename?: 'Query', employee?: { __typename?: 'Kti', reviewCount?: number | undefined, averageRating?: number | undefined } | undefined, department?: { __typename?: 'Kti', reviewCount?: number | undefined, averageRating?: number | undefined } | undefined, company?: { __typename?: 'Kti', reviewCount?: number | undefined, averageRating?: number | undefined } | undefined };

export type NordvikNoGQLGetAllDepartmentsQueryVariables = Exact<{ [key: string]: never; }>;


export type NordvikNoGQLGetAllDepartmentsQuery = { __typename?: 'Query', departments?: { __typename?: 'DepartmentList', departmentsEntries?: Array<{ __typename?: 'Department', id: string, departmentId?: number | undefined, departmentNumber?: number | undefined, slug?: string | undefined, name?: string | undefined, marketName?: string | undefined, legalName?: string | undefined, organisationNumber?: string | undefined, phone?: string | undefined, employees?: Array<{ __typename?: 'Employee', id: string, employeeId?: string | undefined, name?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, title?: string | undefined, slug?: string | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLGetDepartmentQueryVariables = Exact<{
  departmentId: Scalars['Int']['input'];
}>;


export type NordvikNoGQLGetDepartmentQuery = { __typename?: 'Query', department?: { __typename?: 'Department', id: string, departmentId?: number | undefined, departmentNumber?: number | undefined, slug?: string | undefined, name?: string | undefined, marketName?: string | undefined, legalName?: string | undefined, organisationNumber?: string | undefined, phone?: string | undefined, employees?: Array<{ __typename?: 'Employee', id: string, employeeId?: string | undefined, name?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, title?: string | undefined, slug?: string | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLDepartmentEstatesQueryVariables = Exact<{
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
}>;


export type NordvikNoGQLDepartmentEstatesQuery = { __typename?: 'Query', department?: { __typename?: 'Department', estates?: Array<{ __typename?: 'Estate', estateId?: string | undefined, status?: number | undefined, employeeId?: string | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLDepartmentEmployeesBudgetsQueryVariables = Exact<{
  departmentId: Scalars['Int']['input'];
}>;


export type NordvikNoGQLDepartmentEmployeesBudgetsQuery = { __typename?: 'Query', department?: { __typename?: 'Department', employees?: Array<{ __typename?: 'Employee', salesBudget?: Array<{ __typename?: 'EmployeeSalesBudget', year?: number | undefined, months?: Array<{ __typename?: 'EmployeeSalesBudgetMonth', month?: number | undefined, sum?: { __typename?: 'EmployeeSalesBudgetSum', income?: number | undefined, numberOfSales?: number | undefined } | undefined } | undefined> | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLAllDepartmentEmployeesBudgetsQueryVariables = Exact<{ [key: string]: never; }>;


export type NordvikNoGQLAllDepartmentEmployeesBudgetsQuery = { __typename?: 'Query', departments?: { __typename?: 'DepartmentList', departmentsEntries?: Array<{ __typename?: 'Department', departmentId?: number | undefined, employees?: Array<{ __typename?: 'Employee', salesBudget?: Array<{ __typename?: 'EmployeeSalesBudget', year?: number | undefined, months?: Array<{ __typename?: 'EmployeeSalesBudgetMonth', month?: number | undefined, sum?: { __typename?: 'EmployeeSalesBudgetSum', income?: number | undefined, numberOfSales?: number | undefined } | undefined } | undefined> | undefined } | undefined> | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLDepartmentEstatesEntriesQueryVariables = Exact<{
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLDepartmentEstatesEntriesQuery = { __typename?: 'Query', estates?: { __typename?: 'EstateList', count?: number | undefined, estatesEntries?: Array<{ __typename?: 'Estate', noOfBedRooms?: number | undefined, assignmentNum?: string | undefined, estateId?: string | undefined, createdDate?: string | undefined, noOfRooms?: number | undefined, soldDate?: string | undefined, estatePrice?: any | undefined, expireDate?: string | undefined, activities?: any | undefined, finnCode?: string | undefined, finnExpireDate?: string | undefined, finnPublishDate?: string | undefined, commissionAcceptedDate?: string | undefined, assignmentTypeGroup?: number | undefined, publishStart?: string | undefined, showings?: any | undefined, id: string, status?: number | undefined, takeOverDate?: string | undefined, sellers?: any | undefined, hasAd?: boolean | undefined, changedDate?: string | undefined, projectRelation?: number | undefined, images?: Array<{ __typename?: 'EstateImage', imageCategoryName?: string | undefined, imageDescription?: string | undefined, imageSequence?: number | undefined, imageId?: string | undefined, url?: any | undefined } | undefined> | undefined, matrikkel?: Array<{ __typename?: 'Matrikkel', knr?: number | undefined, gnr?: number | undefined, bnr?: number | undefined, fnr?: number | undefined, snr?: number | undefined } | undefined> | undefined, checklist?: Array<{ __typename?: 'Checklist', changedBy?: string | undefined, changedDate?: string | undefined, firstTag?: string | undefined, tags?: Array<string | undefined> | undefined, value?: number | undefined } | undefined> | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerWithRole', brokerRole?: number | undefined, employee?: any | undefined, employeeId?: string | undefined } | undefined> | undefined, address?: { __typename?: 'EstateAddress', streetAdress?: string | undefined } | undefined, sellersEntries?: Array<{ __typename?: 'EstateContact', contactId?: string | undefined, mainContact?: boolean | undefined, proxyId?: string | undefined, contact?: { __typename?: 'Contact', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, address?: string | undefined, postalCode?: string | undefined, city?: string | undefined, contactType?: number | undefined } | undefined } | undefined> | undefined, ads?: Array<{ __typename?: 'EstateAd', id: string, finnAdType?: number | undefined, ownAdvertisementType?: string | undefined, link?: string | undefined, channel?: number | undefined } | undefined> | undefined, estatePriceModel?: { __typename?: 'EstatePrice', priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLEmployeeWithRatingAndAwardsQueryVariables = Exact<{
  email?: InputMaybe<Scalars['String']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLEmployeeWithRatingAndAwardsQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', id: string, slug?: string | undefined, employeeId?: string | undefined, title?: string | undefined, name?: string | undefined, email?: string | undefined, departmentId?: Array<number | undefined> | undefined, mobilePhone?: string | undefined, employeeActive?: boolean | undefined, createdDate?: string | undefined, kti?: number | undefined, aboutMe?: string | undefined, instagram?: string | undefined, roles?: Array<{ __typename?: 'EmployeeRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, department?: { __typename?: 'Department', id: string, name?: string | undefined, departmentId?: number | undefined, kti?: number | undefined, displayKtiOnEmployee?: boolean | undefined, rating?: { __typename?: 'DepartmentRating', count?: number | undefined } | undefined } | undefined, image?: { __typename?: 'EmployeeImage', small?: string | undefined, medium?: string | undefined } | undefined, usp?: Array<{ __typename?: 'EmployeeUsp', title?: string | undefined, description?: string | undefined } | undefined> | undefined, awards?: Array<{ __typename?: 'Award', id: string, name?: string | undefined, origin?: string | undefined, year?: number | undefined } | undefined> | undefined, nordvikAwards?: Array<{ __typename?: 'NordvikAward', awardId: string, name?: string | undefined, origin?: string | undefined, year?: number | undefined, private?: boolean | undefined, hidden?: boolean | undefined } | undefined> | undefined, rating?: { __typename?: 'EmployeeRating', average?: number | undefined, count?: number | undefined, reviewsCount?: number | undefined, weighted?: number | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeeSessionInfoQueryVariables = Exact<{
  employeeId?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLEmployeeSessionInfoQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', id: string, employeeId?: string | undefined, slug?: string | undefined, title?: string | undefined, name?: string | undefined, email?: string | undefined, departmentId?: Array<number | undefined> | undefined, mobilePhone?: string | undefined, employeeActive?: boolean | undefined, createdDate?: string | undefined, roles?: Array<{ __typename?: 'EmployeeRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, department?: { __typename?: 'Department', id: string, name?: string | undefined, departmentId?: number | undefined, kti?: number | undefined, displayKtiOnEmployee?: boolean | undefined } | undefined, image?: { __typename?: 'EmployeeImage', small?: string | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeeSessionInfoByEmailQueryVariables = Exact<{
  email?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLEmployeeSessionInfoByEmailQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', id: string, employeeId?: string | undefined, title?: string | undefined, name?: string | undefined, email?: string | undefined, departmentId?: Array<number | undefined> | undefined, mobilePhone?: string | undefined, employeeActive?: boolean | undefined, createdDate?: string | undefined, department?: { __typename?: 'Department', id: string, name?: string | undefined, departmentId?: number | undefined, kti?: number | undefined, displayKtiOnEmployee?: boolean | undefined } | undefined, image?: { __typename?: 'EmployeeImage', small?: string | undefined } | undefined } | undefined };

export type NordvikNoGQLActiveEmployeeByEmailQueryVariables = Exact<{
  email?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLActiveEmployeeByEmailQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', id: string, employeeId?: string | undefined, title?: string | undefined, name?: string | undefined, email?: string | undefined, departmentId?: Array<number | undefined> | undefined, createdDate?: string | undefined, roles?: Array<{ __typename?: 'EmployeeRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, image?: { __typename?: 'EmployeeImage', small?: string | undefined, medium?: string | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeeForAdplentyQueryVariables = Exact<{
  employeeId: Scalars['String']['input'];
}>;


export type NordvikNoGQLEmployeeForAdplentyQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', employeeId?: string | undefined, employeeActive?: boolean | undefined, webPublish?: boolean | undefined, slug?: string | undefined, email?: string | undefined, name?: string | undefined, title?: string | undefined, mobilePhone?: string | undefined, description?: string | undefined, changedDate?: string | undefined, image?: { __typename?: 'EmployeeImage', medium?: string | undefined } | undefined, department?: { __typename?: 'Department', id: string, departmentId?: number | undefined, name?: string | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeesQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  ids?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
}>;


export type NordvikNoGQLEmployeesQuery = { __typename?: 'Query', employees?: { __typename?: 'EmployeeList', count?: number | undefined, employeesEntries?: Array<{ __typename?: 'Employee', employeeId?: string | undefined, id: string, name?: string | undefined, email?: string | undefined, employeeActive?: boolean | undefined, image?: { __typename?: 'EmployeeImage', small?: string | undefined, large?: string | undefined } | undefined, roles?: Array<{ __typename?: 'EmployeeRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, department?: { __typename?: 'Department', id: string, name?: string | undefined, departmentId?: number | undefined } | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLEmployeesEstatesEntriesByIdCountQueryVariables = Exact<{
  employeeId?: InputMaybe<Scalars['String']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  expireDateAfter?: InputMaybe<Scalars['Date']['input']>;
  takeOverDateAfter?: InputMaybe<Scalars['Date']['input']>;
  soldDateBefore?: InputMaybe<Scalars['Date']['input']>;
  inspectionDateAfter?: InputMaybe<Scalars['Date']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  allMarketReady?: InputMaybe<Scalars['Boolean']['input']>;
  assignmentTypeGroup?: InputMaybe<Array<Scalars['Int']['input']> | Scalars['Int']['input']>;
  createdDateAfter?: InputMaybe<Scalars['Date']['input']>;
}>;


export type NordvikNoGQLEmployeesEstatesEntriesByIdCountQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', estateEntries?: { __typename?: 'EmployeeEstateList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeesEstatesEntriesByIdQueryVariables = Exact<{
  employeeId?: InputMaybe<Scalars['String']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  status?: InputMaybe<Scalars['Int']['input']>;
  expireDateAfter?: InputMaybe<Scalars['Date']['input']>;
  takeOverDateAfter?: InputMaybe<Scalars['Date']['input']>;
  assignmentTypeGroup?: InputMaybe<Array<Scalars['Int']['input']> | Scalars['Int']['input']>;
  soldDateBefore?: InputMaybe<Scalars['Date']['input']>;
  inspectionDateAfter?: InputMaybe<Scalars['Date']['input']>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  allMarketReady?: InputMaybe<Scalars['Boolean']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  createdDateAfter?: InputMaybe<Scalars['Date']['input']>;
}>;


export type NordvikNoGQLEmployeesEstatesEntriesByIdQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', estateEntries?: { __typename?: 'EmployeeEstateList', pagination?: { __typename?: 'Pagination', offset?: number | undefined, limit?: number | undefined, count?: number | undefined, total?: number | undefined } | undefined, data?: Array<{ __typename?: 'Estate', noOfBedRooms?: number | undefined, assignmentNum?: string | undefined, estateId?: string | undefined, createdDate?: string | undefined, noOfRooms?: number | undefined, soldDate?: string | undefined, estatePrice?: any | undefined, expireDate?: string | undefined, activities?: any | undefined, finnCode?: string | undefined, finnExpireDate?: string | undefined, finnPublishDate?: string | undefined, commissionAcceptedDate?: string | undefined, assignmentTypeGroup?: number | undefined, publishStart?: string | undefined, showings?: any | undefined, id: string, status?: number | undefined, takeOverDate?: string | undefined, sellers?: any | undefined, hasAd?: boolean | undefined, changedDate?: string | undefined, projectRelation?: number | undefined, images?: Array<{ __typename?: 'EstateImage', imageCategoryName?: string | undefined, imageDescription?: string | undefined, imageSequence?: number | undefined, imageId?: string | undefined, url?: any | undefined } | undefined> | undefined, matrikkel?: Array<{ __typename?: 'Matrikkel', knr?: number | undefined, gnr?: number | undefined, bnr?: number | undefined, fnr?: number | undefined, snr?: number | undefined } | undefined> | undefined, checklist?: Array<{ __typename?: 'Checklist', changedBy?: string | undefined, changedDate?: string | undefined, firstTag?: string | undefined, tags?: Array<string | undefined> | undefined, value?: number | undefined } | undefined> | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerWithRole', brokerRole?: number | undefined, employee?: any | undefined, employeeId?: string | undefined } | undefined> | undefined, address?: { __typename?: 'EstateAddress', streetAdress?: string | undefined } | undefined, sellersEntries?: Array<{ __typename?: 'EstateContact', contactId?: string | undefined, mainContact?: boolean | undefined, proxyId?: string | undefined, contact?: { __typename?: 'Contact', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, address?: string | undefined, postalCode?: string | undefined, city?: string | undefined, contactType?: number | undefined } | undefined } | undefined> | undefined, ads?: Array<{ __typename?: 'EstateAd', id: string, finnAdType?: number | undefined, ownAdvertisementType?: string | undefined, link?: string | undefined, channel?: number | undefined } | undefined> | undefined, estatePriceModel?: { __typename?: 'EstatePrice', priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined } | undefined> | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeeDepartmentsQueryVariables = Exact<{
  email?: InputMaybe<Scalars['String']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLEmployeeDepartmentsQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', department?: { __typename?: 'Department', departmentId?: number | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeesDepartmentIdQueryVariables = Exact<{
  ids?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
}>;


export type NordvikNoGQLEmployeesDepartmentIdQuery = { __typename?: 'Query', employees?: { __typename?: 'EmployeeList', employeesEntries?: Array<{ __typename?: 'Employee', employeeId?: string | undefined, department?: { __typename?: 'Department', departmentId?: number | undefined } | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLActiveEmployeeByPhoneNumberQueryVariables = Exact<{
  phoneNumber: Scalars['String']['input'];
}>;


export type NordvikNoGQLActiveEmployeeByPhoneNumberQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', id: string, employeeId?: string | undefined, title?: string | undefined, name?: string | undefined, email?: string | undefined, departmentId?: Array<number | undefined> | undefined, createdDate?: string | undefined, roles?: Array<{ __typename?: 'EmployeeRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, image?: { __typename?: 'EmployeeImage', small?: string | undefined, medium?: string | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeeByPhoneNumberQueryVariables = Exact<{
  phoneNumber: Scalars['String']['input'];
}>;


export type NordvikNoGQLEmployeeByPhoneNumberQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', id: string, employeeId?: string | undefined, title?: string | undefined, name?: string | undefined, email?: string | undefined, departmentId?: Array<number | undefined> | undefined, createdDate?: string | undefined, roles?: Array<{ __typename?: 'EmployeeRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, image?: { __typename?: 'EmployeeImage', small?: string | undefined, medium?: string | undefined } | undefined } | undefined };

export type NordvikNoGQLEmployeeBudgetQueryVariables = Exact<{
  employeeId: Scalars['String']['input'];
}>;


export type NordvikNoGQLEmployeeBudgetQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', salesBudget?: Array<{ __typename?: 'EmployeeSalesBudget', year?: number | undefined, months?: Array<{ __typename?: 'EmployeeSalesBudgetMonth', month?: number | undefined, sum?: { __typename?: 'EmployeeSalesBudgetSum', income?: number | undefined, numberOfSales?: number | undefined } | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLGetEstateByIdQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
}>;


export type NordvikNoGQLGetEstateByIdQuery = { __typename?: 'Query', estate?: { __typename?: 'Estate', id: string, projectRelation?: number | undefined, assignmentNum?: string | undefined, assignmentType?: number | undefined, assignmentTypeGroup?: number | undefined, ownAssignmentType?: string | undefined, estateId?: string | undefined, status?: number | undefined, activities?: any | undefined, showings?: any | undefined, finnCode?: string | undefined, inspectionDate?: string | undefined, finnExpireDate?: string | undefined, finnPublishDate?: string | undefined, employeeId?: string | undefined, estateType?: string | undefined, estateTypeId?: string | undefined, estateTypeExternal?: number | undefined, url?: string | undefined, soldDate?: string | undefined, changedDate?: string | undefined, commissionAcceptedDate?: string | undefined, ownership?: number | undefined, partOwnership?: any | undefined, createdDate?: string | undefined, municipality?: string | undefined, departmentId?: number | undefined, noOfBedRooms?: number | undefined, noOfRooms?: number | undefined, estatePrice?: any | undefined, sumArea?: any | undefined, publishStart?: string | undefined, publishEnd?: string | undefined, sellers?: any | undefined, hjemUrl?: string | undefined, location?: any | undefined, hasAd?: boolean | undefined, businessManagerContact?: { __typename?: 'Contact', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, address?: string | undefined, city?: string | undefined, companyName?: string | undefined } | undefined, employee?: { __typename?: 'Employee', name?: string | undefined, employeeId?: string | undefined, id: string, email?: string | undefined, mobilePhone?: string | undefined, title?: string | undefined, slug?: string | undefined, roles?: Array<{ __typename?: 'EmployeeRole', source?: string | undefined, typeId?: number | undefined, name?: string | undefined } | undefined> | undefined, image?: { __typename?: 'EmployeeImage', small?: string | undefined } | undefined } | undefined, department?: { __typename?: 'Department', id: string, departmentId?: number | undefined, departmentNumber?: number | undefined, slug?: string | undefined, name?: string | undefined, email?: string | undefined, marketName?: string | undefined, legalName?: string | undefined, organisationNumber?: string | undefined, streetAddress?: string | undefined, postalCode?: string | undefined, phone?: string | undefined, city?: string | undefined } | undefined, estatePriceModel?: { __typename?: 'EstatePrice', priceSuggestion?: number | undefined, soldPrice?: number | undefined, collectiveDebt?: number | undefined, estimatedValue?: number | undefined, totalPrice?: number | undefined } | undefined, estateSizeModel?: { __typename?: 'EstateSize', primaryRoomArea?: number | undefined, usableArea?: number | undefined } | undefined, areaSize?: { __typename?: 'AreaSize', BRAItotal?: number | undefined } | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerWithRole', brokerRole?: number | undefined, employee?: any | undefined, employeeId?: string | undefined } | undefined> | undefined, checklist?: Array<{ __typename?: 'Checklist', changedBy?: string | undefined, changedDate?: string | undefined, firstTag?: string | undefined, tags?: Array<string | undefined> | undefined, value?: number | undefined } | undefined> | undefined, links?: Array<{ __typename?: 'Link', linkType?: number | undefined, text?: string | undefined, url?: string | undefined } | undefined> | undefined, address?: { __typename?: 'EstateAddress', streetAdress?: string | undefined, city?: string | undefined, zipCode?: string | undefined, apartmentNumber?: string | undefined } | undefined, images?: Array<{ __typename?: 'EstateImage', imageCategoryName?: string | undefined, imageDescription?: string | undefined, imageSequence?: number | undefined, imageId?: string | undefined, url?: any | undefined } | undefined> | undefined, sellersEntries?: Array<{ __typename?: 'EstateContact', contactId?: string | undefined, mainContact?: boolean | undefined, proxyId?: string | undefined, contact?: { __typename?: 'Contact', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, address?: string | undefined, postalCode?: string | undefined, city?: string | undefined, contactType?: number | undefined } | undefined } | undefined> | undefined, matrikkel?: Array<{ __typename?: 'Matrikkel', bnr?: number | undefined, gnr?: number | undefined, knr?: number | undefined, snr?: number | undefined, ownPart?: string | undefined } | undefined> | undefined, ads?: Array<{ __typename?: 'EstateAd', id: string, finnAdType?: number | undefined, ownAdvertisementType?: string | undefined, link?: string | undefined, channel?: number | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLCampaignsQueryVariables = Exact<{
  estateId?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLCampaignsQuery = { __typename?: 'Query', campaigns?: { __typename?: 'Campaigns', count?: number | undefined, entries?: Array<{ __typename?: 'MarketingPackage', packageName?: string | undefined, marketingPackage?: number | undefined, dateOrdered?: string | undefined, orderStartDate?: string | undefined, orderEndDate?: string | undefined, externalId?: string | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLEstatesQueryVariables = Exact<{
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  price?: InputMaybe<Array<Scalars['Int']['input']> | Scalars['Int']['input']>;
  size?: InputMaybe<Array<Scalars['Int']['input']> | Scalars['Int']['input']>;
}>;


export type NordvikNoGQLEstatesQuery = { __typename?: 'Query', estates?: { __typename?: 'EstateList', estatesEntries?: Array<{ __typename?: 'Estate', estateId?: string | undefined, employeeId?: string | undefined, status?: number | undefined, departmentId?: number | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerWithRole', employeeId?: string | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLEstatesSoldByDepartmentQueryVariables = Exact<{
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
}>;


export type NordvikNoGQLEstatesSoldByDepartmentQuery = { __typename?: 'Query', estates?: { __typename?: 'EstateList', estatesEntries?: Array<{ __typename?: 'Estate', soldDate?: string | undefined, employeeId?: string | undefined, departmentId?: number | undefined, estateId?: string | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerWithRole', employeeId?: string | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLFindEstatesForBrokerQueryVariables = Exact<{
  employeeId?: InputMaybe<Scalars['String']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  radius?: InputMaybe<Scalars['Float']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  pagination?: InputMaybe<NordvikNoGQLPaginationArgs>;
  assignmentTypeGroup?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  estateTypeId?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  priceRange?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  sizeRange?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
}>;


export type NordvikNoGQLFindEstatesForBrokerQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', estateEntries?: { __typename?: 'EmployeeEstateList', data?: Array<{ __typename?: 'Estate', id: string, heading?: string | undefined, estateId?: string | undefined, employeeId?: string | undefined, finnCode?: string | undefined, hjemUrl?: string | undefined, estateType?: string | undefined, estateTypeId?: string | undefined, soldDate?: string | undefined, estatePrice?: any | undefined, location?: any | undefined, departmentId?: number | undefined, stats?: any | undefined, noOfBedRooms?: number | undefined, estatePriceModel?: { __typename?: 'EstatePrice', priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined, estateSizeModel?: { __typename?: 'EstateSize', primaryRoomArea?: number | undefined, usableArea?: number | undefined } | undefined, areaSize?: { __typename?: 'AreaSize', BRAItotal?: number | undefined } | undefined, address?: { __typename?: 'EstateAddress', streetAdress?: string | undefined, city?: string | undefined, zipCode?: string | undefined, apartmentNumber?: string | undefined } | undefined, images?: Array<{ __typename?: 'EstateImage', url?: any | undefined } | undefined> | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerWithRole', employeeId?: string | undefined } | undefined> | undefined } | undefined> | undefined } | undefined } | undefined };

export type NordvikNoGQLFindEstatesQueryVariables = Exact<{
  longitude?: InputMaybe<Scalars['Float']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  radius?: InputMaybe<Scalars['Float']['input']>;
  statuses?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  baseType?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
  estateType?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
  noBedRooms?: InputMaybe<Scalars['String']['input']>;
  size?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  price?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>> | InputMaybe<Scalars['Int']['input']>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  soldDateAfter?: InputMaybe<Scalars['Date']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLFindEstatesQuery = { __typename?: 'Query', estates?: { __typename?: 'EstateList', count?: number | undefined, estatesEntries?: Array<{ __typename?: 'Estate', id: string, heading?: string | undefined, estateId?: string | undefined, employeeId?: string | undefined, finnCode?: string | undefined, hjemUrl?: string | undefined, estateType?: string | undefined, estateTypeId?: string | undefined, soldDate?: string | undefined, estatePrice?: any | undefined, location?: any | undefined, departmentId?: number | undefined, stats?: any | undefined, noOfBedRooms?: number | undefined, estatePriceModel?: { __typename?: 'EstatePrice', priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerWithRole', employeeId?: string | undefined } | undefined> | undefined, estateSizeModel?: { __typename?: 'EstateSize', primaryRoomArea?: number | undefined, usableArea?: number | undefined } | undefined, areaSize?: { __typename?: 'AreaSize', BRAItotal?: number | undefined } | undefined, address?: { __typename?: 'EstateAddress', streetAdress?: string | undefined, city?: string | undefined, zipCode?: string | undefined, apartmentNumber?: string | undefined } | undefined, images?: Array<{ __typename?: 'EstateImage', url?: any | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLEstateEmployeeIdQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type NordvikNoGQLEstateEmployeeIdQuery = { __typename?: 'Query', estate?: { __typename?: 'Estate', employeeId?: string | undefined } | undefined };

export type NordvikNoGQLEstatesChecklistsQueryVariables = Exact<{
  estateIds: Array<Scalars['String']['input']> | Scalars['String']['input'];
}>;


export type NordvikNoGQLEstatesChecklistsQuery = { __typename?: 'Query', estatesGroup?: { __typename?: 'EstateList', estatesEntries?: Array<{ __typename?: 'Estate', estateId?: string | undefined, checklist?: Array<{ __typename?: 'Checklist', firstTag?: string | undefined, value?: number | undefined, changedDate?: string | undefined } | undefined> | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLEstateChecklistQueryVariables = Exact<{
  estateId: Scalars['String']['input'];
}>;


export type NordvikNoGQLEstateChecklistQuery = { __typename?: 'Query', estate?: { __typename?: 'Estate', checklist?: Array<{ __typename?: 'Checklist', firstTag?: string | undefined, value?: number | undefined, changedDate?: string | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLHallOfFameQueryVariables = Exact<{ [key: string]: never; }>;


export type NordvikNoGQLHallOfFameQuery = { __typename?: 'Query', hallOfFame?: Array<{ __typename?: 'HallOfFame', year?: number | undefined, entries?: Array<{ __typename?: 'HallOfFameAward', name?: string | undefined, year?: number | undefined, awardId?: number | undefined, hidden?: boolean | undefined, origin?: string | undefined, private?: boolean | undefined, employee?: { __typename?: 'Employee', id: string, employeeId?: string | undefined, name?: string | undefined, departmentId?: Array<number | undefined> | undefined, email?: string | undefined, department?: { __typename?: 'Department', id: string, name?: string | undefined } | undefined, image?: { __typename?: 'EmployeeImage', medium?: string | undefined } | undefined } | undefined } | undefined> | undefined } | undefined> | undefined };

export type NordvikNoGQLBrokerRatingsQueryVariables = Exact<{
  employeeId: Scalars['String']['input'];
  pagination?: InputMaybe<NordvikNoGQLPaginationArgs>;
  rating?: InputMaybe<Scalars['Int']['input']>;
  dateFrom?: InputMaybe<Scalars['Date']['input']>;
  dateTo?: InputMaybe<Scalars['Date']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  sortDir?: InputMaybe<NordvikNoGQLSortDirection>;
}>;


export type NordvikNoGQLBrokerRatingsQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', ratings?: { __typename?: 'EmployeeRatingList', data?: Array<{ __typename?: 'Rating', ratingId?: number | undefined, created_at?: string | undefined, departmentId?: number | undefined, employeeId?: string | undefined, rating?: number | undefined, userEmail?: string | undefined, userName?: string | undefined, firstName?: string | undefined, featured?: boolean | undefined, review?: { __typename?: 'Review', reviewId?: number | undefined, rating?: number | undefined, userEmail?: string | undefined, userName?: string | undefined, firstName?: string | undefined, text?: string | undefined, created_at?: string | undefined, attributes?: Array<any | undefined> | undefined, employeeId?: string | undefined, testimonial?: boolean | undefined } | undefined } | undefined> | undefined, pagination?: { __typename?: 'Pagination', offset?: number | undefined, limit?: number | undefined, count?: number | undefined, total?: number | undefined } | undefined } | undefined } | undefined };

export type NordvikNoGQLBrokerUspSuggestionBasisQueryVariables = Exact<{
  employeeId?: InputMaybe<Scalars['String']['input']>;
}>;


export type NordvikNoGQLBrokerUspSuggestionBasisQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', aboutMe?: string | undefined, ratings?: { __typename?: 'EmployeeRatingList', data?: Array<{ __typename?: 'Rating', review?: { __typename?: 'Review', text?: string | undefined } | undefined } | undefined> | undefined } | undefined } | undefined };

export type NordvikNoGQLBrokerRatingsTotalQueryVariables = Exact<{
  employeeId: Scalars['String']['input'];
  last30DaysStart?: InputMaybe<Scalars['Date']['input']>;
  lastYearStart?: InputMaybe<Scalars['Date']['input']>;
  lastYearEnd?: InputMaybe<Scalars['Date']['input']>;
  currentYearStart?: InputMaybe<Scalars['Date']['input']>;
  currentDate?: InputMaybe<Scalars['Date']['input']>;
  dateFrom?: InputMaybe<Scalars['Date']['input']>;
  dateTo?: InputMaybe<Scalars['Date']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
}>;


export type NordvikNoGQLBrokerRatingsTotalQuery = { __typename?: 'Query', employee?: { __typename?: 'Employee', allDates?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, allRatings?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, fiveStar?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, fourStar?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, threeStar?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, twoStar?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, oneStar?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, withReview?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, last30Days?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, lastYear?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined, currentYear?: { __typename?: 'EmployeeRatingList', pagination?: { __typename?: 'Pagination', total?: number | undefined } | undefined } | undefined } | undefined };

export type NordvikNoGQLRatingsQueryVariables = Exact<{
  employeeId?: InputMaybe<Scalars['String']['input']>;
  from?: InputMaybe<Scalars['Date']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
}>;


export type NordvikNoGQLRatingsQuery = { __typename?: 'Query', ratings?: { __typename?: 'KtiNew', average?: number | undefined, count?: number | undefined } | undefined };

export type NordvikNoGQLRatingsSummaryQueryVariables = Exact<{
  employeeId?: InputMaybe<Scalars['String']['input']>;
  departmentId?: InputMaybe<Scalars['Int']['input']>;
}>;


export type NordvikNoGQLRatingsSummaryQuery = { __typename?: 'Query', department?: { __typename?: 'KtiNew', average?: number | undefined, count?: number | undefined } | undefined, employee?: { __typename?: 'KtiNew', average?: number | undefined, count?: number | undefined } | undefined, nordvik?: { __typename?: 'KtiNew', average?: number | undefined, count?: number | undefined } | undefined };

export type NordvikNoGQLNewsArticleFragment = { __typename?: 'News', id?: number | undefined, excerpt?: string | undefined, slug?: string | undefined, title?: string | undefined, postDate?: any | undefined, thumbnail?: { __typename?: 'Image', medium?: string | undefined, large?: string | undefined } | undefined };

export type NordvikNoGQLGetNewsArticlesQueryVariables = Exact<{
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
}>;


export type NordvikNoGQLGetNewsArticlesQuery = { __typename?: 'Query', news?: { __typename?: 'NewsList', count?: number | undefined, newsEntries?: Array<{ __typename?: 'News', id?: number | undefined, excerpt?: string | undefined, slug?: string | undefined, title?: string | undefined, postDate?: any | undefined, thumbnail?: { __typename?: 'Image', medium?: string | undefined, large?: string | undefined } | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLGetNewsBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type NordvikNoGQLGetNewsBySlugQuery = { __typename?: 'Query', newsEntry?: { __typename?: 'News', modules?: any | undefined, id?: number | undefined, excerpt?: string | undefined, slug?: string | undefined, title?: string | undefined, postDate?: any | undefined, thumbnail?: { __typename?: 'Image', medium?: string | undefined, large?: string | undefined } | undefined } | undefined };

export type NordvikNoGQLPriceStatisticsQueryVariables = Exact<{
  postalCode?: InputMaybe<Scalars['String']['input']>;
  filterYears?: InputMaybe<Scalars['Float']['input']>;
}>;


export type NordvikNoGQLPriceStatisticsQuery = { __typename?: 'Query', priceStatistics?: { __typename?: 'StatisticsList', indexes?: Array<{ __typename?: 'PriceIndex', id: string, type?: string | undefined, region?: string | undefined, omrade?: string | undefined, dato?: string | undefined, snittKvmPris?: number | undefined, snittFormidlingstid?: number | undefined, indeksEndring12Mnd?: number | undefined, indeksEndring4Kvartal?: number | undefined, indeksEndring1Kvartal?: number | undefined, indeksEndring5Ar?: number | undefined, indeksEndring10Ar?: number | undefined } | undefined> | undefined, secondaryIndexes?: Array<{ __typename?: 'PriceIndex', id: string, type?: string | undefined, region?: string | undefined, omrade?: string | undefined, dato?: string | undefined, snittKvmPris?: number | undefined, indeksEndring12Mnd?: number | undefined, indeksEndring1Mnd?: number | undefined, snittFormidlingstid?: number | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLTipsQueryVariables = Exact<{
  estateId?: InputMaybe<Scalars['String']['input']>;
  originType?: InputMaybe<Scalars['Int']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  mobilePhone?: InputMaybe<Scalars['String']['input']>;
  contactId?: InputMaybe<Scalars['String']['input']>;
  createdDateAfter?: InputMaybe<Scalars['Date']['input']>;
  tipId?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
  originSource?: InputMaybe<Scalars['Int']['input']>;
}>;


export type NordvikNoGQLTipsQuery = { __typename?: 'Query', tips?: { __typename?: 'TipsList', count?: number | undefined, entries?: Array<{ __typename?: 'Tip', tipId?: string | undefined, estateId?: string | undefined, contactId?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, firstName?: string | undefined, lastName?: string | undefined, originType?: number | undefined, originSource?: number | undefined, status?: number | undefined, created?: string | undefined, modified?: string | undefined, departmentId?: number | undefined, employeeId?: string | undefined, recipientId?: string | undefined, productId?: string | undefined, source?: string | undefined, postalCode?: string | undefined, streetAdress?: string | undefined, userId?: string | undefined } | undefined> | undefined } | undefined };

export type NordvikNoGQLCheckStorebrandTipsQueryVariables = Exact<{
  estateId?: InputMaybe<Scalars['String']['input']>;
  contactId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  mobilePhone?: InputMaybe<Scalars['String']['input']>;
  createdDateAfter?: InputMaybe<Scalars['Date']['input']>;
}>;


export type NordvikNoGQLCheckStorebrandTipsQuery = { __typename?: 'Query', tips?: { __typename?: 'TipsList', count?: number | undefined } | undefined };

export type NordvikNoGQLEstateListEntryFieldsFragment = { __typename?: 'Estate', noOfBedRooms?: number | undefined, assignmentNum?: string | undefined, estateId?: string | undefined, createdDate?: string | undefined, noOfRooms?: number | undefined, soldDate?: string | undefined, estatePrice?: any | undefined, expireDate?: string | undefined, activities?: any | undefined, finnCode?: string | undefined, finnExpireDate?: string | undefined, finnPublishDate?: string | undefined, commissionAcceptedDate?: string | undefined, assignmentTypeGroup?: number | undefined, publishStart?: string | undefined, showings?: any | undefined, id: string, status?: number | undefined, takeOverDate?: string | undefined, sellers?: any | undefined, hasAd?: boolean | undefined, changedDate?: string | undefined, projectRelation?: number | undefined, images?: Array<{ __typename?: 'EstateImage', imageCategoryName?: string | undefined, imageDescription?: string | undefined, imageSequence?: number | undefined, imageId?: string | undefined, url?: any | undefined } | undefined> | undefined, matrikkel?: Array<{ __typename?: 'Matrikkel', knr?: number | undefined, gnr?: number | undefined, bnr?: number | undefined, fnr?: number | undefined, snr?: number | undefined } | undefined> | undefined, checklist?: Array<{ __typename?: 'Checklist', changedBy?: string | undefined, changedDate?: string | undefined, firstTag?: string | undefined, tags?: Array<string | undefined> | undefined, value?: number | undefined } | undefined> | undefined, brokersIdWithRoles?: Array<{ __typename?: 'BrokerWithRole', brokerRole?: number | undefined, employee?: any | undefined, employeeId?: string | undefined } | undefined> | undefined, address?: { __typename?: 'EstateAddress', streetAdress?: string | undefined } | undefined, sellersEntries?: Array<{ __typename?: 'EstateContact', contactId?: string | undefined, mainContact?: boolean | undefined, proxyId?: string | undefined, contact?: { __typename?: 'Contact', firstName?: string | undefined, lastName?: string | undefined, email?: string | undefined, mobilePhone?: string | undefined, address?: string | undefined, postalCode?: string | undefined, city?: string | undefined, contactType?: number | undefined } | undefined } | undefined> | undefined, ads?: Array<{ __typename?: 'EstateAd', id: string, finnAdType?: number | undefined, ownAdvertisementType?: string | undefined, link?: string | undefined, channel?: number | undefined } | undefined> | undefined, estatePriceModel?: { __typename?: 'EstatePrice', priceSuggestion?: number | undefined, soldPrice?: number | undefined } | undefined };

export type NordvikNoGQLMarketingPackagesQueryVariables = Exact<{
  type?: InputMaybe<Scalars['String']['input']>;
  publicVisible?: InputMaybe<Scalars['Boolean']['input']>;
  active?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type NordvikNoGQLMarketingPackagesQuery = { __typename?: 'Query', marketingPackages?: Array<{ __typename?: 'MarketingPackageCms', id?: number | undefined, name?: string | undefined, shortName?: string | undefined, active?: boolean | undefined, public?: boolean | undefined, packageId?: number | undefined, productTag?: string | undefined, price?: number | undefined, views?: string | undefined, clicks?: string | undefined, channels?: Array<{ __typename?: 'MarketingChannel', id?: string | undefined, title?: string | undefined } | undefined> | undefined } | undefined> | undefined };

export const NewsArticleFragmentDoc = gql`
    fragment NewsArticle on News {
  id
  excerpt
  slug
  title
  postDate
  thumbnail {
    medium
    large
  }
}
    `;
export const EstateListEntryFieldsFragmentDoc = gql`
    fragment EstateListEntryFields on Estate {
  images {
    imageCategoryName
    imageDescription
    imageSequence
    imageId
    url
  }
  noOfBedRooms
  assignmentNum
  estateId
  createdDate
  noOfRooms
  soldDate
  estatePrice
  expireDate
  activities
  finnCode
  finnExpireDate
  finnPublishDate
  commissionAcceptedDate
  assignmentTypeGroup
  publishStart
  matrikkel {
    knr
    gnr
    bnr
    fnr
    snr
  }
  showings
  checklist {
    changedBy
    changedDate
    firstTag
    tags
    value
  }
  brokersIdWithRoles {
    brokerRole
    employee
    employeeId
  }
  id
  address {
    streetAdress
  }
  status
  takeOverDate
  sellers
  sellersEntries {
    contactId
    mainContact
    proxyId
    contact {
      firstName
      lastName
      email
      mobilePhone
      address
      postalCode
      city
      contactType
    }
  }
  ads {
    id
    finnAdType
    ownAdvertisementType
    link
    channel
  }
  hasAd
  changedDate
  estatePriceModel {
    priceSuggestion
    soldPrice
  }
  projectRelation
}
    `;
export const ContactDocument = gql`
    query contact($contactId: String) {
  contact(contactId: $contactId) {
    contactId
    departmentId
    contactType
    companyName
    organisationNumber
    firstName
    lastName
    mobilePhone
    privatePhone
    workPhone
    email
    address
    postalAddress
    postalCode
    city
  }
}
    `;
export const DashboardKtiDocument = gql`
    query DashboardKti($departmentId: Int, $employeeId: String) {
  employee: kti(employeeId: $employeeId) {
    reviewCount
    averageRating
  }
  department: kti(departmentId: $departmentId) {
    reviewCount
    averageRating
  }
  company: kti {
    reviewCount
    averageRating
  }
}
    `;
export const GetAllDepartmentsDocument = gql`
    query getAllDepartments {
  departments(limit: 0, offset: 0) {
    departmentsEntries {
      id
      departmentId
      departmentNumber
      slug
      name
      marketName
      legalName
      organisationNumber
      phone
      employees {
        id
        employeeId
        name
        email
        mobilePhone
        title
        slug
      }
    }
  }
}
    `;
export const GetDepartmentDocument = gql`
    query getDepartment($departmentId: Int!) {
  department(departmentId: $departmentId) {
    id
    departmentId
    departmentNumber
    slug
    name
    marketName
    legalName
    organisationNumber
    phone
    employees {
      id
      employeeId
      name
      email
      mobilePhone
      title
      slug
    }
  }
}
    `;
export const DepartmentEstatesDocument = gql`
    query departmentEstates($departmentId: Int, $status: Int) {
  department(departmentId: $departmentId, status: $status) {
    estates {
      estateId
      status
      employeeId
    }
  }
}
    `;
export const DepartmentEmployeesBudgetsDocument = gql`
    query departmentEmployeesBudgets($departmentId: Int!) {
  department(departmentId: $departmentId) {
    employees {
      salesBudget {
        year
        months {
          month
          sum {
            income
            numberOfSales
          }
        }
      }
    }
  }
}
    `;
export const AllDepartmentEmployeesBudgetsDocument = gql`
    query allDepartmentEmployeesBudgets {
  departments(limit: 0, offset: 0) {
    departmentsEntries {
      departmentId
      employees {
        salesBudget {
          year
          months {
            month
            sum {
              income
              numberOfSales
            }
          }
        }
      }
    }
  }
}
    `;
export const DepartmentEstatesEntriesDocument = gql`
    query departmentEstatesEntries($departmentId: Int, $statuses: [Int], $limit: Int, $offset: Int, $sortBy: String, $search: String) {
  estates(
    departmentId: $departmentId
    statuses: $statuses
    limit: $limit
    offset: $offset
    sortBy: $sortBy
    search: $search
  ) {
    estatesEntries {
      ...EstateListEntryFields
    }
    count
  }
}
    ${EstateListEntryFieldsFragmentDoc}`;
export const EmployeeWithRatingAndAwardsDocument = gql`
    query employeeWithRatingAndAwards($email: String, $employeeId: String) {
  employee(email: $email, employeeId: $employeeId, all: true) {
    id
    slug
    employeeId
    title
    name
    email
    departmentId
    mobilePhone
    employeeActive
    roles {
      source
      typeId
      name
    }
    createdDate
    department {
      id
      name
      departmentId
      kti
      rating {
        count
      }
      displayKtiOnEmployee
    }
    image {
      small
      medium
    }
    kti
    aboutMe
    instagram
    usp {
      title
      description
    }
    awards {
      id
      name
      origin
      year
    }
    nordvikAwards {
      awardId
      name
      origin
      year
      private
      hidden
    }
    rating {
      average
      count
      reviewsCount
      weighted
    }
  }
}
    `;
export const EmployeeSessionInfoDocument = gql`
    query employeeSessionInfo($employeeId: String) {
  employee(employeeId: $employeeId, all: true) {
    id
    employeeId
    slug
    title
    name
    email
    departmentId
    mobilePhone
    employeeActive
    roles {
      source
      typeId
      name
    }
    createdDate
    department {
      id
      name
      departmentId
      kti
      displayKtiOnEmployee
    }
    image {
      small
    }
  }
}
    `;
export const EmployeeSessionInfoByEmailDocument = gql`
    query employeeSessionInfoByEmail($email: String) {
  employee(email: $email, all: true) {
    id
    employeeId
    title
    name
    email
    departmentId
    mobilePhone
    employeeActive
    createdDate
    department {
      id
      name
      departmentId
      kti
      displayKtiOnEmployee
    }
    image {
      small
    }
  }
}
    `;
export const ActiveEmployeeByEmailDocument = gql`
    query activeEmployeeByEmail($email: String) {
  employee(email: $email) {
    id
    employeeId
    title
    name
    email
    departmentId
    createdDate
    roles {
      source
      typeId
      name
    }
    image {
      small
      medium
    }
  }
}
    `;
export const EmployeeForAdplentyDocument = gql`
    query employeeForAdplenty($employeeId: String!) {
  employee(employeeId: $employeeId, all: true) {
    employeeId
    employeeActive
    webPublish
    slug
    email
    name
    title
    mobilePhone
    image {
      medium
    }
    department {
      id
      departmentId
      name
    }
    description
    changedDate
  }
}
    `;
export const EmployeesDocument = gql`
    query employees($limit: Int, $offset: Int, $ids: [String!]) {
  employees(limit: $limit, offset: $offset, ids: $ids) {
    count
    employeesEntries {
      employeeId
      id
      name
      email
      employeeActive
      image {
        small
        large
      }
      roles {
        source
        typeId
        name
      }
      department {
        id
        name
        departmentId
      }
    }
  }
}
    `;
export const EmployeesEstatesEntriesByIdCountDocument = gql`
    query employeesEstatesEntriesByIdCount($employeeId: String, $statuses: [Int], $expireDateAfter: Date, $takeOverDateAfter: Date, $soldDateBefore: Date, $inspectionDateAfter: Date, $archived: Boolean, $allMarketReady: Boolean, $assignmentTypeGroup: [Int!], $createdDateAfter: Date) {
  employee(employeeId: $employeeId, all: true) {
    estateEntries(
      statuses: $statuses
      expireDateAfter: $expireDateAfter
      takeOverDateAfter: $takeOverDateAfter
      soldDateBefore: $soldDateBefore
      inspectionDateAfter: $inspectionDateAfter
      archived: $archived
      allMarketReady: $allMarketReady
      assignmentTypeGroup: $assignmentTypeGroup
      createdDateAfter: $createdDateAfter
    ) {
      pagination {
        total
      }
    }
  }
}
    `;
export const EmployeesEstatesEntriesByIdDocument = gql`
    query employeesEstatesEntriesById($employeeId: String, $statuses: [Int], $status: Int, $expireDateAfter: Date, $takeOverDateAfter: Date, $assignmentTypeGroup: [Int!], $soldDateBefore: Date, $inspectionDateAfter: Date, $archived: Boolean, $allMarketReady: Boolean, $offset: Int, $limit: Int, $search: String, $sortBy: String, $createdDateAfter: Date) {
  employee(employeeId: $employeeId, all: true) {
    estateEntries(
      statuses: $statuses
      status: $status
      expireDateAfter: $expireDateAfter
      takeOverDateAfter: $takeOverDateAfter
      soldDateBefore: $soldDateBefore
      inspectionDateAfter: $inspectionDateAfter
      archived: $archived
      allMarketReady: $allMarketReady
      pagination: {offset: $offset, limit: $limit}
      search: $search
      sortBy: $sortBy
      assignmentTypeGroup: $assignmentTypeGroup
      createdDateAfter: $createdDateAfter
    ) {
      pagination {
        offset
        limit
        count
        total
      }
      data {
        ...EstateListEntryFields
      }
    }
  }
}
    ${EstateListEntryFieldsFragmentDoc}`;
export const EmployeeDepartmentsDocument = gql`
    query employeeDepartments($email: String, $employeeId: String) {
  employee(email: $email, employeeId: $employeeId, all: true) {
    department {
      departmentId
    }
  }
}
    `;
export const EmployeesDepartmentIdDocument = gql`
    query employeesDepartmentId($ids: [String!]) {
  employees(ids: $ids) {
    employeesEntries {
      employeeId
      department {
        departmentId
      }
    }
  }
}
    `;
export const ActiveEmployeeByPhoneNumberDocument = gql`
    query activeEmployeeByPhoneNumber($phoneNumber: String!) {
  employee(phone: $phoneNumber) {
    id
    employeeId
    title
    name
    email
    departmentId
    createdDate
    roles {
      source
      typeId
      name
    }
    image {
      small
      medium
    }
  }
}
    `;
export const EmployeeByPhoneNumberDocument = gql`
    query employeeByPhoneNumber($phoneNumber: String!) {
  employee(phone: $phoneNumber, all: true) {
    id
    employeeId
    title
    name
    email
    departmentId
    createdDate
    roles {
      source
      typeId
      name
    }
    image {
      small
      medium
    }
  }
}
    `;
export const EmployeeBudgetDocument = gql`
    query employeeBudget($employeeId: String!) {
  employee(employeeId: $employeeId, all: true) {
    salesBudget {
      year
      months {
        month
        sum {
          income
          numberOfSales
        }
      }
    }
  }
}
    `;
export const GetEstateByIdDocument = gql`
    query getEstateById($estateId: String!, $statuses: [Int]) {
  estate(id: $estateId, all: true, statuses: $statuses) {
    id
    projectRelation
    businessManagerContact {
      firstName
      lastName
      email
      mobilePhone
      address
      city
      address
      companyName
    }
    assignmentNum
    assignmentType
    assignmentTypeGroup
    ownAssignmentType
    estateId
    status
    activities
    showings
    finnCode
    inspectionDate
    finnExpireDate
    finnPublishDate
    employeeId
    estateType
    estateTypeId
    estateTypeExternal
    employee {
      name
      employeeId
    }
    url
    soldDate
    changedDate
    commissionAcceptedDate
    ownership
    partOwnership
    createdDate
    municipality
    departmentId
    department {
      id
      departmentId
      departmentNumber
      slug
      name
      email
      marketName
      legalName
      organisationNumber
      streetAddress
      postalCode
      phone
      city
    }
    noOfBedRooms
    noOfRooms
    estatePrice
    estatePriceModel {
      priceSuggestion
      soldPrice
      collectiveDebt
      estimatedValue
      totalPrice
    }
    estateSizeModel {
      primaryRoomArea
      usableArea
    }
    areaSize {
      BRAItotal
    }
    sumArea
    brokersIdWithRoles {
      brokerRole
      employee
      employeeId
    }
    checklist {
      changedBy
      changedDate
      firstTag
      tags
      value
    }
    publishStart
    publishEnd
    employee {
      id
      name
      employeeId
      email
      mobilePhone
      title
      slug
      roles {
        source
        typeId
        name
      }
      image {
        small
      }
    }
    links {
      linkType
      text
      url
    }
    address {
      streetAdress
      city
      zipCode
      apartmentNumber
    }
    images {
      imageCategoryName
      imageDescription
      imageSequence
      imageId
      url
    }
    sellers
    sellersEntries {
      contactId
      mainContact
      proxyId
      contact {
        firstName
        lastName
        email
        mobilePhone
        address
        postalCode
        city
        contactType
      }
    }
    hjemUrl
    matrikkel {
      bnr
      gnr
      knr
      snr
      ownPart
    }
    location
    ads {
      id
      finnAdType
      ownAdvertisementType
      link
      channel
    }
    hasAd
  }
}
    `;
export const CampaignsDocument = gql`
    query campaigns($estateId: String) {
  campaigns(estateId: $estateId) {
    count
    entries {
      packageName
      marketingPackage
      dateOrdered
      orderStartDate
      orderEndDate
      externalId
    }
  }
}
    `;
export const EstatesDocument = gql`
    query estates($statuses: [Int], $departmentId: Int, $price: [Int!], $size: [Int!]) {
  estates(
    statuses: $statuses
    departmentId: $departmentId
    limit: 9999
    price: $price
    size: $size
  ) {
    estatesEntries {
      estateId
      employeeId
      status
      departmentId
      brokersIdWithRoles {
        employeeId
      }
    }
  }
}
    `;
export const EstatesSoldByDepartmentDocument = gql`
    query estatesSoldByDepartment($departmentId: Int, $limit: Int) {
  estates(
    departmentId: $departmentId
    statuses: [3]
    limit: $limit
    sortBy: "soldDate"
  ) {
    estatesEntries {
      soldDate
      employeeId
      departmentId
      estateId
      brokersIdWithRoles {
        employeeId
      }
    }
  }
}
    `;
export const FindEstatesForBrokerDocument = gql`
    query findEstatesForBroker($employeeId: String, $longitude: Float, $latitude: Float, $radius: Float, $statuses: [Int], $sortBy: String, $pagination: PaginationArgs, $assignmentTypeGroup: [Int], $estateTypeId: [Int], $priceRange: [Int], $sizeRange: [Int]) {
  employee(employeeId: $employeeId) {
    estateEntries(
      longitude: $longitude
      latitude: $latitude
      radius: $radius
      statuses: $statuses
      sortBy: $sortBy
      pagination: $pagination
      assignmentTypeGroup: $assignmentTypeGroup
      estateTypeId: $estateTypeId
      priceRange: $priceRange
      sizeRange: $sizeRange
    ) {
      data {
        id
        heading
        estateId
        employeeId
        finnCode
        hjemUrl
        estateType
        estateTypeId
        soldDate
        estatePrice
        estatePriceModel {
          priceSuggestion
          soldPrice
        }
        estateSizeModel {
          primaryRoomArea
          usableArea
        }
        areaSize {
          BRAItotal
        }
        address {
          streetAdress
          city
          zipCode
          apartmentNumber
        }
        images {
          url
        }
        location
        brokersIdWithRoles {
          employeeId
        }
        employeeId
        departmentId
        stats
        noOfBedRooms
      }
    }
  }
}
    `;
export const FindEstatesDocument = gql`
    query findEstates($longitude: Float, $latitude: Float, $radius: Float, $statuses: [Int], $sortBy: String, $baseType: [String!], $estateType: [String!], $noBedRooms: String, $size: [Int], $price: [Int], $limit: Int, $offset: Int, $soldDateAfter: Date, $city: String) {
  estates(
    longitude: $longitude
    latitude: $latitude
    radius: $radius
    statuses: $statuses
    sortBy: $sortBy
    estateType: $estateType
    price: $price
    size: $size
    noBedRooms: $noBedRooms
    baseType: $baseType
    limit: $limit
    offset: $offset
    soldDateAfter: $soldDateAfter
    city: $city
  ) {
    count
    estatesEntries {
      id
      heading
      estateId
      employeeId
      finnCode
      hjemUrl
      estateType
      estateTypeId
      soldDate
      estatePrice
      estatePriceModel {
        priceSuggestion
        soldPrice
      }
      brokersIdWithRoles {
        employeeId
      }
      employeeId
      estateSizeModel {
        primaryRoomArea
        usableArea
      }
      areaSize {
        BRAItotal
      }
      address {
        streetAdress
        city
        zipCode
        apartmentNumber
      }
      images {
        url
      }
      location
      departmentId
      stats
      noOfBedRooms
    }
  }
}
    `;
export const EstateEmployeeIdDocument = gql`
    query estateEmployeeId($estateId: String!) {
  estate(id: $estateId) {
    employeeId
  }
}
    `;
export const EstatesChecklistsDocument = gql`
    query estatesChecklists($estateIds: [String!]!) {
  estatesGroup(ids: $estateIds) {
    estatesEntries {
      estateId
      checklist {
        firstTag
        value
        changedDate
      }
    }
  }
}
    `;
export const EstateChecklistDocument = gql`
    query estateChecklist($estateId: String!) {
  estate(id: $estateId, statuses: [0, 1, 2, 3, 4, 5, 6, 7]) {
    checklist {
      firstTag
      value
      changedDate
    }
  }
}
    `;
export const HallOfFameDocument = gql`
    query hallOfFame {
  hallOfFame {
    year
    entries {
      name
      employee {
        id
        employeeId
        name
        departmentId
        email
        department {
          id
          name
        }
        image {
          medium
        }
      }
      year
      awardId
      hidden
      origin
      private
    }
  }
}
    `;
export const BrokerRatingsDocument = gql`
    query brokerRatings($employeeId: String!, $pagination: PaginationArgs, $rating: Int, $dateFrom: Date, $dateTo: Date, $sortBy: String, $hasReview: Boolean, $featured: Boolean, $sortDir: SortDirection) {
  employee(employeeId: $employeeId, all: true) {
    ratings(
      pagination: $pagination
      rating: $rating
      dateFrom: $dateFrom
      dateTo: $dateTo
      sortBy: $sortBy
      hasReview: $hasReview
      featured: $featured
      sortDir: $sortDir
    ) {
      data {
        ratingId
        created_at
        departmentId
        employeeId
        rating
        userEmail
        userName
        firstName
        featured
        review {
          reviewId
          rating
          userEmail
          userName
          firstName
          text
          created_at
          attributes
          employeeId
          testimonial
        }
      }
      pagination {
        offset
        limit
        count
        total
      }
    }
  }
}
    `;
export const BrokerUspSuggestionBasisDocument = gql`
    query brokerUSPSuggestionBasis($employeeId: String) {
  employee(employeeId: $employeeId, all: true) {
    aboutMe
    ratings(hasReview: true, rating: 5) {
      data {
        review {
          text
        }
      }
    }
  }
}
    `;
export const BrokerRatingsTotalDocument = gql`
    query brokerRatingsTotal($employeeId: String!, $last30DaysStart: Date, $lastYearStart: Date, $lastYearEnd: Date, $currentYearStart: Date, $currentDate: Date, $dateFrom: Date, $dateTo: Date, $hasReview: Boolean, $featured: Boolean, $rating: Int) {
  employee(all: true, employeeId: $employeeId) {
    allDates: ratings(hasReview: $hasReview, featured: $featured, rating: $rating) {
      pagination {
        total
      }
    }
    allRatings: ratings(
      dateFrom: $dateFrom
      dateTo: $dateTo
      hasReview: $hasReview
      featured: $featured
    ) {
      pagination {
        total
      }
    }
    fiveStar: ratings(
      rating: 5
      dateFrom: $dateFrom
      dateTo: $dateTo
      hasReview: $hasReview
      featured: $featured
    ) {
      pagination {
        total
      }
    }
    fourStar: ratings(
      rating: 4
      dateFrom: $dateFrom
      dateTo: $dateTo
      hasReview: $hasReview
      featured: $featured
    ) {
      pagination {
        total
      }
    }
    threeStar: ratings(
      rating: 3
      dateFrom: $dateFrom
      dateTo: $dateTo
      hasReview: $hasReview
      featured: $featured
    ) {
      pagination {
        total
      }
    }
    twoStar: ratings(
      rating: 2
      dateFrom: $dateFrom
      dateTo: $dateTo
      hasReview: $hasReview
      featured: $featured
    ) {
      pagination {
        total
      }
    }
    oneStar: ratings(
      rating: 1
      dateFrom: $dateFrom
      dateTo: $dateTo
      hasReview: $hasReview
      featured: $featured
    ) {
      pagination {
        total
      }
    }
    withReview: ratings(
      hasReview: true
      dateFrom: $dateFrom
      dateTo: $dateTo
      featured: $featured
      rating: $rating
    ) {
      pagination {
        total
      }
    }
    last30Days: ratings(
      dateFrom: $last30DaysStart
      dateTo: $currentDate
      hasReview: $hasReview
      featured: $featured
      rating: $rating
    ) {
      pagination {
        total
      }
    }
    lastYear: ratings(
      dateFrom: $lastYearStart
      dateTo: $lastYearEnd
      hasReview: $hasReview
      featured: $featured
      rating: $rating
    ) {
      pagination {
        total
      }
    }
    currentYear: ratings(
      dateFrom: $currentYearStart
      dateTo: $currentDate
      hasReview: $hasReview
      featured: $featured
      rating: $rating
    ) {
      pagination {
        total
      }
    }
  }
}
    `;
export const RatingsDocument = gql`
    query ratings($employeeId: String, $from: Date, $to: Date) {
  ratings(employeeId: $employeeId, from: $from, to: $to) {
    average
    count
  }
}
    `;
export const RatingsSummaryDocument = gql`
    query RatingsSummary($employeeId: String, $departmentId: Int) {
  department: ratings(departmentId: $departmentId) {
    average
    count
  }
  employee: ratings(employeeId: $employeeId) {
    average
    count
  }
  nordvik: ratings {
    average
    count
  }
}
    `;
export const GetNewsArticlesDocument = gql`
    query getNewsArticles($offset: Int, $limit: Int) {
  news(offset: $offset, limit: $limit) {
    count
    newsEntries {
      ...NewsArticle
    }
  }
}
    ${NewsArticleFragmentDoc}`;
export const GetNewsBySlugDocument = gql`
    query getNewsBySlug($slug: String!) {
  newsEntry(slug: $slug) {
    ...NewsArticle
    modules
  }
}
    ${NewsArticleFragmentDoc}`;
export const PriceStatisticsDocument = gql`
    query priceStatistics($postalCode: String, $filterYears: Float) {
  priceStatistics(postalCode: $postalCode, filterYears: $filterYears) {
    indexes {
      id
      type
      region
      omrade
      dato
      snittKvmPris
      snittFormidlingstid
      indeksEndring12Mnd
      indeksEndring4Kvartal
      indeksEndring1Kvartal
      indeksEndring5Ar
      indeksEndring10Ar
    }
    secondaryIndexes {
      id
      type
      region
      omrade
      dato
      snittKvmPris
      indeksEndring12Mnd
      indeksEndring1Mnd
      snittFormidlingstid
    }
  }
}
    `;
export const TipsDocument = gql`
    query Tips($estateId: String, $originType: Int, $email: String, $mobilePhone: String, $contactId: String, $createdDateAfter: Date, $tipId: String, $status: Int, $originSource: Int) {
  tips(
    estateId: $estateId
    originType: $originType
    email: $email
    mobilePhone: $mobilePhone
    contactId: $contactId
    createdDateAfter: $createdDateAfter
    tipId: $tipId
    status: $status
    originSource: $originSource
  ) {
    count
    entries {
      tipId
      estateId
      contactId
      email
      mobilePhone
      firstName
      lastName
      originType
      originSource
      status
      created
      modified
      departmentId
      employeeId
      recipientId
      productId
      source
      postalCode
      streetAdress
      userId
    }
  }
}
    `;
export const CheckStorebrandTipsDocument = gql`
    query CheckStorebrandTips($estateId: String, $contactId: String, $email: String, $mobilePhone: String, $createdDateAfter: Date) {
  tips(
    estateId: $estateId
    contactId: $contactId
    email: $email
    mobilePhone: $mobilePhone
    originType: 4
    createdDateAfter: $createdDateAfter
  ) {
    count
  }
}
    `;
export const MarketingPackagesDocument = gql`
    query marketingPackages($type: String, $publicVisible: Boolean, $active: Boolean) {
  marketingPackages(type: $type, publicVisible: $publicVisible, active: $active) {
    id
    name
    shortName
    active
    public
    packageId
    productTag
    price
    views
    clicks
    channels {
      id
      title
    }
  }
}
    `;

export type SdkFunctionWrapper = <T>(action: (requestHeaders?:Record<string, string>) => Promise<T>, operationName: string, operationType?: string, variables?: any) => Promise<T>;


const defaultWrapper: SdkFunctionWrapper = (action, _operationName, _operationType, _variables) => action();

export function getSdk(client: GraphQLClient, withWrapper: SdkFunctionWrapper = defaultWrapper) {
  return {
    contact(variables?: NordvikNoGQLContactQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLContactQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLContactQuery>({ document: ContactDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'contact', 'query', variables);
    },
    DashboardKti(variables?: NordvikNoGQLDashboardKtiQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLDashboardKtiQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLDashboardKtiQuery>({ document: DashboardKtiDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'DashboardKti', 'query', variables);
    },
    getAllDepartments(variables?: NordvikNoGQLGetAllDepartmentsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLGetAllDepartmentsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLGetAllDepartmentsQuery>({ document: GetAllDepartmentsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'getAllDepartments', 'query', variables);
    },
    getDepartment(variables: NordvikNoGQLGetDepartmentQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLGetDepartmentQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLGetDepartmentQuery>({ document: GetDepartmentDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'getDepartment', 'query', variables);
    },
    departmentEstates(variables?: NordvikNoGQLDepartmentEstatesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLDepartmentEstatesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLDepartmentEstatesQuery>({ document: DepartmentEstatesDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'departmentEstates', 'query', variables);
    },
    departmentEmployeesBudgets(variables: NordvikNoGQLDepartmentEmployeesBudgetsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLDepartmentEmployeesBudgetsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLDepartmentEmployeesBudgetsQuery>({ document: DepartmentEmployeesBudgetsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'departmentEmployeesBudgets', 'query', variables);
    },
    allDepartmentEmployeesBudgets(variables?: NordvikNoGQLAllDepartmentEmployeesBudgetsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLAllDepartmentEmployeesBudgetsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLAllDepartmentEmployeesBudgetsQuery>({ document: AllDepartmentEmployeesBudgetsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'allDepartmentEmployeesBudgets', 'query', variables);
    },
    departmentEstatesEntries(variables?: NordvikNoGQLDepartmentEstatesEntriesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLDepartmentEstatesEntriesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLDepartmentEstatesEntriesQuery>({ document: DepartmentEstatesEntriesDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'departmentEstatesEntries', 'query', variables);
    },
    employeeWithRatingAndAwards(variables?: NordvikNoGQLEmployeeWithRatingAndAwardsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeeWithRatingAndAwardsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeeWithRatingAndAwardsQuery>({ document: EmployeeWithRatingAndAwardsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeeWithRatingAndAwards', 'query', variables);
    },
    employeeSessionInfo(variables?: NordvikNoGQLEmployeeSessionInfoQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeeSessionInfoQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeeSessionInfoQuery>({ document: EmployeeSessionInfoDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeeSessionInfo', 'query', variables);
    },
    employeeSessionInfoByEmail(variables?: NordvikNoGQLEmployeeSessionInfoByEmailQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeeSessionInfoByEmailQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeeSessionInfoByEmailQuery>({ document: EmployeeSessionInfoByEmailDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeeSessionInfoByEmail', 'query', variables);
    },
    activeEmployeeByEmail(variables?: NordvikNoGQLActiveEmployeeByEmailQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLActiveEmployeeByEmailQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLActiveEmployeeByEmailQuery>({ document: ActiveEmployeeByEmailDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'activeEmployeeByEmail', 'query', variables);
    },
    employeeForAdplenty(variables: NordvikNoGQLEmployeeForAdplentyQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeeForAdplentyQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeeForAdplentyQuery>({ document: EmployeeForAdplentyDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeeForAdplenty', 'query', variables);
    },
    employees(variables?: NordvikNoGQLEmployeesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeesQuery>({ document: EmployeesDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employees', 'query', variables);
    },
    employeesEstatesEntriesByIdCount(variables?: NordvikNoGQLEmployeesEstatesEntriesByIdCountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeesEstatesEntriesByIdCountQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeesEstatesEntriesByIdCountQuery>({ document: EmployeesEstatesEntriesByIdCountDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeesEstatesEntriesByIdCount', 'query', variables);
    },
    employeesEstatesEntriesById(variables?: NordvikNoGQLEmployeesEstatesEntriesByIdQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeesEstatesEntriesByIdQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeesEstatesEntriesByIdQuery>({ document: EmployeesEstatesEntriesByIdDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeesEstatesEntriesById', 'query', variables);
    },
    employeeDepartments(variables?: NordvikNoGQLEmployeeDepartmentsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeeDepartmentsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeeDepartmentsQuery>({ document: EmployeeDepartmentsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeeDepartments', 'query', variables);
    },
    employeesDepartmentId(variables?: NordvikNoGQLEmployeesDepartmentIdQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeesDepartmentIdQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeesDepartmentIdQuery>({ document: EmployeesDepartmentIdDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeesDepartmentId', 'query', variables);
    },
    activeEmployeeByPhoneNumber(variables: NordvikNoGQLActiveEmployeeByPhoneNumberQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLActiveEmployeeByPhoneNumberQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLActiveEmployeeByPhoneNumberQuery>({ document: ActiveEmployeeByPhoneNumberDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'activeEmployeeByPhoneNumber', 'query', variables);
    },
    employeeByPhoneNumber(variables: NordvikNoGQLEmployeeByPhoneNumberQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeeByPhoneNumberQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeeByPhoneNumberQuery>({ document: EmployeeByPhoneNumberDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeeByPhoneNumber', 'query', variables);
    },
    employeeBudget(variables: NordvikNoGQLEmployeeBudgetQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEmployeeBudgetQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEmployeeBudgetQuery>({ document: EmployeeBudgetDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'employeeBudget', 'query', variables);
    },
    getEstateById(variables: NordvikNoGQLGetEstateByIdQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLGetEstateByIdQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLGetEstateByIdQuery>({ document: GetEstateByIdDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'getEstateById', 'query', variables);
    },
    campaigns(variables?: NordvikNoGQLCampaignsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLCampaignsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLCampaignsQuery>({ document: CampaignsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'campaigns', 'query', variables);
    },
    estates(variables?: NordvikNoGQLEstatesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEstatesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEstatesQuery>({ document: EstatesDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'estates', 'query', variables);
    },
    estatesSoldByDepartment(variables?: NordvikNoGQLEstatesSoldByDepartmentQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEstatesSoldByDepartmentQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEstatesSoldByDepartmentQuery>({ document: EstatesSoldByDepartmentDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'estatesSoldByDepartment', 'query', variables);
    },
    findEstatesForBroker(variables?: NordvikNoGQLFindEstatesForBrokerQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLFindEstatesForBrokerQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLFindEstatesForBrokerQuery>({ document: FindEstatesForBrokerDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'findEstatesForBroker', 'query', variables);
    },
    findEstates(variables?: NordvikNoGQLFindEstatesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLFindEstatesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLFindEstatesQuery>({ document: FindEstatesDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'findEstates', 'query', variables);
    },
    estateEmployeeId(variables: NordvikNoGQLEstateEmployeeIdQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEstateEmployeeIdQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEstateEmployeeIdQuery>({ document: EstateEmployeeIdDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'estateEmployeeId', 'query', variables);
    },
    estatesChecklists(variables: NordvikNoGQLEstatesChecklistsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEstatesChecklistsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEstatesChecklistsQuery>({ document: EstatesChecklistsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'estatesChecklists', 'query', variables);
    },
    estateChecklist(variables: NordvikNoGQLEstateChecklistQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLEstateChecklistQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLEstateChecklistQuery>({ document: EstateChecklistDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'estateChecklist', 'query', variables);
    },
    hallOfFame(variables?: NordvikNoGQLHallOfFameQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLHallOfFameQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLHallOfFameQuery>({ document: HallOfFameDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'hallOfFame', 'query', variables);
    },
    brokerRatings(variables: NordvikNoGQLBrokerRatingsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLBrokerRatingsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLBrokerRatingsQuery>({ document: BrokerRatingsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'brokerRatings', 'query', variables);
    },
    brokerUSPSuggestionBasis(variables?: NordvikNoGQLBrokerUspSuggestionBasisQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLBrokerUspSuggestionBasisQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLBrokerUspSuggestionBasisQuery>({ document: BrokerUspSuggestionBasisDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'brokerUSPSuggestionBasis', 'query', variables);
    },
    brokerRatingsTotal(variables: NordvikNoGQLBrokerRatingsTotalQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLBrokerRatingsTotalQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLBrokerRatingsTotalQuery>({ document: BrokerRatingsTotalDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'brokerRatingsTotal', 'query', variables);
    },
    ratings(variables?: NordvikNoGQLRatingsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLRatingsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLRatingsQuery>({ document: RatingsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'ratings', 'query', variables);
    },
    RatingsSummary(variables?: NordvikNoGQLRatingsSummaryQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLRatingsSummaryQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLRatingsSummaryQuery>({ document: RatingsSummaryDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'RatingsSummary', 'query', variables);
    },
    getNewsArticles(variables?: NordvikNoGQLGetNewsArticlesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLGetNewsArticlesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLGetNewsArticlesQuery>({ document: GetNewsArticlesDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'getNewsArticles', 'query', variables);
    },
    getNewsBySlug(variables: NordvikNoGQLGetNewsBySlugQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLGetNewsBySlugQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLGetNewsBySlugQuery>({ document: GetNewsBySlugDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'getNewsBySlug', 'query', variables);
    },
    priceStatistics(variables?: NordvikNoGQLPriceStatisticsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLPriceStatisticsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLPriceStatisticsQuery>({ document: PriceStatisticsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'priceStatistics', 'query', variables);
    },
    Tips(variables?: NordvikNoGQLTipsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLTipsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLTipsQuery>({ document: TipsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'Tips', 'query', variables);
    },
    CheckStorebrandTips(variables?: NordvikNoGQLCheckStorebrandTipsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLCheckStorebrandTipsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLCheckStorebrandTipsQuery>({ document: CheckStorebrandTipsDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'CheckStorebrandTips', 'query', variables);
    },
    marketingPackages(variables?: NordvikNoGQLMarketingPackagesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders, signal?: RequestInit['signal']): Promise<NordvikNoGQLMarketingPackagesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NordvikNoGQLMarketingPackagesQuery>({ document: MarketingPackagesDocument, variables, requestHeaders: { ...requestHeaders, ...wrappedRequestHeaders }, signal }), 'marketingPackages', 'query', variables);
    }
  };
}
export type Sdk = ReturnType<typeof getSdk>;