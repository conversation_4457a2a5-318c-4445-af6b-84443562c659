import { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';
import { Context } from './context';
export type Maybe<T> = T | undefined;
export type InputMaybe<T> = T | undefined;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
export type EntireFieldWrapper<T> = T | (() => Promise<T>) | (() => T);
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Date: { input: any; output: any; }
  DateTime: { input: any; output: any; }
  JSON: { input: any; output: any; }
  NodeID: { input: any; output: any; }
};

export type GQLAccessToken = {
  __typename?: 'AccessToken';
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  id: EntireFieldWrapper<Scalars['NodeID']['output']>;
  token?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLAccordion = {
  __typename?: 'Accordion';
  header?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  text?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLActivitySummary = {
  __typename?: 'ActivitySummary';
  recipientsCount: EntireFieldWrapper<Scalars['Int']['output']>;
  signedCount: EntireFieldWrapper<Scalars['Int']['output']>;
  signersCount: EntireFieldWrapper<Scalars['Int']['output']>;
  totalTimeSpent: EntireFieldWrapper<Scalars['Float']['output']>;
  visitorCount: EntireFieldWrapper<Scalars['Int']['output']>;
  visitsCount: EntireFieldWrapper<Scalars['Int']['output']>;
};

export type GQLAreaSize = {
  __typename?: 'AreaSize';
  BRAItotal?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
};

export enum GQLAssignmentDocumentState {
  Complete = 'COMPLETE',
  Error = 'ERROR',
  InProgress = 'IN_PROGRESS',
  Missing = 'MISSING',
  Pending = 'PENDING'
}

export type GQLAssignmentDocumentStatusItem = {
  __typename?: 'AssignmentDocumentStatusItem';
  message?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  state: EntireFieldWrapper<GQLAssignmentDocumentState>;
  type: EntireFieldWrapper<GQLAssignmentDocumentType>;
  updatedAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
};

export type GQLAssignmentDocumentStatuses = {
  __typename?: 'AssignmentDocumentStatuses';
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  generatedAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
  items: EntireFieldWrapper<Array<GQLAssignmentDocumentStatusItem>>;
  partialFailure: EntireFieldWrapper<Scalars['Boolean']['output']>;
};

export enum GQLAssignmentDocumentType {
  EnergyCertificate = 'ENERGY_CERTIFICATE',
  ListingAgreement = 'LISTING_AGREEMENT',
  Photos = 'PHOTOS',
  SafetyDeclaration = 'SAFETY_DECLARATION',
  SellerInterview = 'SELLER_INTERVIEW',
  SurveyReport = 'SURVEY_REPORT'
}

export type GQLAuditExtraData = {
  __typename?: 'AuditExtraData';
  recipients?: EntireFieldWrapper<Maybe<Array<GQLAuditRecipient>>>;
  template?: EntireFieldWrapper<Maybe<GQLAuditTemplate>>;
};

export type GQLAuditRecipient = {
  __typename?: 'AuditRecipient';
  contactId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  firstName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  lastName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  mobilePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLAuditTemplate = {
  __typename?: 'AuditTemplate';
  modified?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  templateId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLAverageCommissionEntry = {
  __typename?: 'AverageCommissionEntry';
  commission?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  price: EntireFieldWrapper<Scalars['Float']['output']>;
  salesCount?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  type: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLAverageCommissionResponse = {
  __typename?: 'AverageCommissionResponse';
  compare: EntireFieldWrapper<GQLAverageCommissionEntry>;
  departmentName: EntireFieldWrapper<Scalars['String']['output']>;
  reference: EntireFieldWrapper<GQLAverageCommissionEntry>;
};

export type GQLAward = {
  __typename?: 'Award';
  id?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  origin?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  year?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLBroker = {
  __typename?: 'Broker';
  aboutMe?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  awards?: EntireFieldWrapper<Maybe<Array<GQLAward>>>;
  createdDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  department?: EntireFieldWrapper<Maybe<GQLDepartment>>;
  email: EntireFieldWrapper<Scalars['String']['output']>;
  employeeActive?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  employeeId: EntireFieldWrapper<Scalars['String']['output']>;
  employeeRoles?: EntireFieldWrapper<Maybe<Array<Maybe<GQLBrokerRole>>>>;
  id: EntireFieldWrapper<Scalars['NodeID']['output']>;
  image?: EntireFieldWrapper<Maybe<GQLBrokerImage>>;
  instagram?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  kti?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  links?: EntireFieldWrapper<Maybe<GQLBrokerProfileLinks>>;
  mobilePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  nordvikAwards?: EntireFieldWrapper<Maybe<Array<GQLNordvikAward>>>;
  rating?: EntireFieldWrapper<Maybe<GQLRating>>;
  ratings?: EntireFieldWrapper<Maybe<GQLBrokerRatingsList>>;
  reviews: EntireFieldWrapper<Array<GQLBrokerRating>>;
  slug?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  team?: EntireFieldWrapper<Maybe<Array<GQLBrokerPartner>>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  usp?: EntireFieldWrapper<Maybe<Array<GQLUsp>>>;
};


export type GQLBrokerReviewsArgs = {
  input: GQLBrokerReviewsInput;
};

export type GQLBrokerAddress = {
  __typename?: 'BrokerAddress';
  apartmentNumber?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  city?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  municipality?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  streetAddress?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  zipCode?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerEstate = {
  __typename?: 'BrokerEstate';
  activities: EntireFieldWrapper<Array<GQLEstateActivity>>;
  address?: EntireFieldWrapper<Maybe<GQLBrokerAddress>>;
  ads: EntireFieldWrapper<Array<GQLEstateAd>>;
  areaSize?: EntireFieldWrapper<Maybe<GQLAreaSize>>;
  assignmentNumber?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  assignmentType?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  assignmentTypeGroup?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  assistantBroker?: EntireFieldWrapper<Maybe<GQLBroker>>;
  broker?: EntireFieldWrapper<Maybe<GQLBroker>>;
  brokerId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  brokers?: EntireFieldWrapper<Maybe<Array<Maybe<GQLEstateBroker>>>>;
  brokersIdWithRoles?: EntireFieldWrapper<Maybe<Array<Maybe<GQLBrokerIdWithRole>>>>;
  businessManagerContact?: EntireFieldWrapper<Maybe<GQLBusinessManagerContact>>;
  campaigns: EntireFieldWrapper<Array<GQLBrokerEstateCampaign>>;
  changedDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  checklist: EntireFieldWrapper<Array<GQLEstateChecklistItem>>;
  commissionAcceptedDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  companyContacts: EntireFieldWrapper<Array<GQLContact>>;
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  department?: EntireFieldWrapper<Maybe<GQLDepartment>>;
  departmentId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  documents: EntireFieldWrapper<Array<GQLNextDocument>>;
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  estatePrice?: EntireFieldWrapper<Maybe<GQLEstatePrice>>;
  estatePriceModel?: EntireFieldWrapper<Maybe<GQLEstatePriceModel>>;
  estateSizeModel?: EntireFieldWrapper<Maybe<GQLEstateSizeModel>>;
  estateType?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  estateTypeExternal?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  estateTypeId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  etakst?: EntireFieldWrapper<Maybe<GQLNextDocument>>;
  expireDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  extraContacts: EntireFieldWrapper<Array<GQLContact>>;
  finn?: EntireFieldWrapper<Maybe<GQLFinnData>>;
  forms: EntireFieldWrapper<Array<GQLBrokerEstateForm>>;
  hasCompanySeller?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  hasInspection?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  heading?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  hjemUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id: EntireFieldWrapper<Scalars['NodeID']['output']>;
  images?: EntireFieldWrapper<Maybe<Array<GQLBrokerEstateImage>>>;
  inspection?: EntireFieldWrapper<Maybe<GQLInspection>>;
  inspectionDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  inspectionEvents?: EntireFieldWrapper<Maybe<Array<GQLInspectionEvent>>>;
  inspectionFolder?: EntireFieldWrapper<Maybe<GQLInspectionFolder>>;
  isEtakstPublished?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  isValuation?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  isWithdrawn: EntireFieldWrapper<Scalars['Boolean']['output']>;
  landIdentificationMatrix?: EntireFieldWrapper<Maybe<GQLLandIdentificationMatrix>>;
  latitude?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  linkToNext?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  links: EntireFieldWrapper<Array<GQLBrokerEstateLink>>;
  listingAgreement?: EntireFieldWrapper<Maybe<GQLListingAgreement>>;
  location?: EntireFieldWrapper<Maybe<GQLLocation>>;
  longitude?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  mainBroker?: EntireFieldWrapper<Maybe<GQLBroker>>;
  mainImage?: EntireFieldWrapper<Maybe<GQLBrokerEstateImage>>;
  mainSeller?: EntireFieldWrapper<Maybe<GQLBrokerEstateSeller>>;
  marketingStart?: EntireFieldWrapper<Maybe<GQLMarketingStart>>;
  matrikkel: EntireFieldWrapper<Array<Maybe<GQLLandIdentificationMatrix>>>;
  noOfBedRooms?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  noOfRooms?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  numberOfBedrooms?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  ownAssignmentType?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  ownership?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  ownershipType?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  partOwnership?: EntireFieldWrapper<Maybe<GQLPartOwnership>>;
  placeholderImage?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  priceHistory?: EntireFieldWrapper<Maybe<Array<Maybe<GQLBrokerEstatePriceHistory>>>>;
  projectRelation?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  propertyType?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  riskCheckmark?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  sellPreference?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  sellers: EntireFieldWrapper<Array<GQLBrokerEstateSeller>>;
  showings: EntireFieldWrapper<Array<GQLBrokerEstateShowing>>;
  soldDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  stats?: EntireFieldWrapper<Maybe<GQLEstateStats>>;
  status: EntireFieldWrapper<Scalars['Int']['output']>;
  sumArea?: EntireFieldWrapper<Maybe<GQLSumArea>>;
  takeOverDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  type?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  upcomingEvents: EntireFieldWrapper<Array<GQLEstateActivity>>;
  updatedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  userID?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};


export type GQLBrokerEstateExtraContactsArgs = {
  source?: InputMaybe<GQLSource>;
};

export type GQLBrokerEstateCampaign = {
  __typename?: 'BrokerEstateCampaign';
  dateOrdered?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  externalId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  marketingPackage?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  orderEndDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  orderStartDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  orderStatus?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  packageName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerEstateCountResponse = {
  __typename?: 'BrokerEstateCountResponse';
  count: EntireFieldWrapper<Scalars['Int']['output']>;
  tab: EntireFieldWrapper<GQLEstateTabFilter>;
};

export type GQLBrokerEstateForm = {
  __typename?: 'BrokerEstateForm';
  link?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  relevantForEstateWithProps?: EntireFieldWrapper<Maybe<GQLRelevantForEstateWithProps>>;
  status?: EntireFieldWrapper<Maybe<GQLBrokerEstateFormStatus>>;
  type?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerEstateFormStatus = {
  __typename?: 'BrokerEstateFormStatus';
  isNotificationSent?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  signingFinished?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
};

export type GQLBrokerEstateImage = {
  __typename?: 'BrokerEstateImage';
  category?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  description?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  large?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  medium?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  sequence?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  small?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerEstateLink = {
  __typename?: 'BrokerEstateLink';
  linkType?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  text?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  url?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerEstatePagination = {
  __typename?: 'BrokerEstatePagination';
  count?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  limit?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  offset?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  total?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLBrokerEstatePriceHistory = {
  __typename?: 'BrokerEstatePriceHistory';
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  evPrice?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  id?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  landIdentificationMatrix?: EntireFieldWrapper<Maybe<GQLLandIdentificationMatrix>>;
  postgresEstateId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  updatedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
};

export type GQLBrokerEstateSeller = {
  __typename?: 'BrokerEstateSeller';
  address?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  city?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  companyName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactId: EntireFieldWrapper<Scalars['String']['output']>;
  contactType?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  firstName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  lastName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  mainContact?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  mobilePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  postCode?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  proxy?: EntireFieldWrapper<Maybe<GQLSellerProxy>>;
  proxyId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  socialSecurityNumber?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerEstateShowing = {
  __typename?: 'BrokerEstateShowing';
  end?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  showingId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  start?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
};

export type GQLBrokerEstatesResponse = {
  __typename?: 'BrokerEstatesResponse';
  items: EntireFieldWrapper<Array<GQLBrokerEstate>>;
  pagination?: EntireFieldWrapper<Maybe<GQLBrokerEstatePagination>>;
};

export type GQLBrokerIdWithRole = {
  __typename?: 'BrokerIdWithRole';
  brokerRole: EntireFieldWrapper<Scalars['Int']['output']>;
  employee: EntireFieldWrapper<GQLBrokerIdWithRoleDetails>;
  employeeId: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLBrokerIdWithRoleDetails = {
  __typename?: 'BrokerIdWithRoleDetails';
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  image?: EntireFieldWrapper<Maybe<GQLBrokerImage>>;
  mobilePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerImage = {
  __typename?: 'BrokerImage';
  large?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  medium?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  small?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerKtiResponse = {
  __typename?: 'BrokerKtiResponse';
  broker?: EntireFieldWrapper<Maybe<GQLKtiResponse>>;
  brokerDepartment?: EntireFieldWrapper<Maybe<GQLKtiResponse>>;
  nordvik?: EntireFieldWrapper<Maybe<GQLKtiResponse>>;
};

export type GQLBrokerPartner = {
  __typename?: 'BrokerPartner';
  category?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  description?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  hidden?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  images: EntireFieldWrapper<Array<Scalars['String']['output']>>;
  instagram?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
  profilePicture?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  updatedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  website?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerPartnerCreateInput = {
  category?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  images?: InputMaybe<Array<Scalars['String']['input']>>;
  instagram?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  profilePicture?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type GQLBrokerPartnerUpdateInput = {
  category?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  images?: InputMaybe<Array<Scalars['String']['input']>>;
  instagram?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  profilePicture?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type GQLBrokerProfileLinks = {
  __typename?: 'BrokerProfileLinks';
  adLinks: EntireFieldWrapper<Array<Scalars['String']['output']>>;
  mediaLinks: EntireFieldWrapper<Array<Scalars['String']['output']>>;
};

export type GQLBrokerProfileLinksPayload = {
  adLinks: Array<Scalars['String']['input']>;
  mediaLinks: Array<Scalars['String']['input']>;
};

export type GQLBrokerRating = {
  __typename?: 'BrokerRating';
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  employeeId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  featured?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  rating?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  ratingId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  review?: EntireFieldWrapper<Maybe<GQLBrokerReview>>;
  userName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export enum GQLBrokerRatingSortBy {
  CreatedDate = 'CREATED_DATE',
  Rating = 'RATING'
}

export type GQLBrokerRatingsList = {
  __typename?: 'BrokerRatingsList';
  data: EntireFieldWrapper<Array<Maybe<GQLBrokerRating>>>;
  pagination: EntireFieldWrapper<GQLPagination>;
};

export type GQLBrokerReview = {
  __typename?: 'BrokerReview';
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  reviewId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  text?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLBrokerReviewsInput = {
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
};

export type GQLBrokerRole = {
  __typename?: 'BrokerRole';
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  source?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  typeId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLBrokersResponse = {
  __typename?: 'BrokersResponse';
  items: EntireFieldWrapper<Array<GQLBroker>>;
  totalCount: EntireFieldWrapper<Scalars['Int']['output']>;
};

export type GQLBudgetPost = {
  __typename?: 'BudgetPost';
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  deletedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  description?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id: EntireFieldWrapper<Scalars['NodeID']['output']>;
  initialPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  listingAgreementId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  price?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  updatedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
};

export type GQLBudgetPostDescription = {
  __typename?: 'BudgetPostDescription';
  description?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  heading: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLBusinessManagerContact = {
  __typename?: 'BusinessManagerContact';
  address?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  city?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  companyName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  firstName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  lastName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLCmsArticle = {
  __typename?: 'CmsArticle';
  author?: EntireFieldWrapper<Maybe<GQLCmsArticleAuthor>>;
  canUserViewArticle?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  categories: EntireFieldWrapper<Array<GQLCmsArticleCategory>>;
  departments?: EntireFieldWrapper<Maybe<Array<GQLCmsArticleDepartment>>>;
  eventDate?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  excerpt?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  externalUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  image?: EntireFieldWrapper<Maybe<GQLCmsArticleImage>>;
  important?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  modules?: EntireFieldWrapper<Maybe<Array<Maybe<GQLModule>>>>;
  postDate?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  slug: EntireFieldWrapper<Scalars['String']['output']>;
  targetRoles: EntireFieldWrapper<Array<GQLTargetRole>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  type?: EntireFieldWrapper<Maybe<GQLCmsArticleType>>;
  viewerHasRead?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
};


export type GQLCmsArticleViewerHasReadArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type GQLCmsArticleAuthor = {
  __typename?: 'CmsArticleAuthor';
  avatarUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLCmsArticleCategory = {
  __typename?: 'CmsArticleCategory';
  id: EntireFieldWrapper<Scalars['String']['output']>;
  slug: EntireFieldWrapper<Scalars['String']['output']>;
  title: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLCmsArticleDepartment = {
  __typename?: 'CmsArticleDepartment';
  departmentId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLCmsArticleImage = {
  __typename?: 'CmsArticleImage';
  large?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  medium?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  small?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLCmsArticleResponse = {
  __typename?: 'CmsArticleResponse';
  items: EntireFieldWrapper<Array<GQLCmsArticle>>;
  meta: EntireFieldWrapper<GQLCmsMeta>;
};

export enum GQLCmsArticleType {
  Calendar = 'calendar',
  Changelog = 'changelog',
  News = 'news',
  Resource = 'resource',
  Updates = 'updates'
}

export type GQLCmsIncidentsResponse = {
  __typename?: 'CmsIncidentsResponse';
  items: EntireFieldWrapper<Array<GQLCraftCmsIncident>>;
  meta: EntireFieldWrapper<GQLCmsMeta>;
};

export type GQLCmsMeta = {
  __typename?: 'CmsMeta';
  currentPage: EntireFieldWrapper<Scalars['Int']['output']>;
  total: EntireFieldWrapper<Scalars['Int']['output']>;
  totalPages: EntireFieldWrapper<Scalars['Int']['output']>;
};

export type GQLContact = {
  __typename?: 'Contact';
  address?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  city?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  companyName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactId: EntireFieldWrapper<Scalars['String']['output']>;
  contactType: EntireFieldWrapper<Scalars['Int']['output']>;
  deletedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  departmentId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  firstName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  lastName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  mobilePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  organisationNumber?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  postalAddress?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  postalCode?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  privatePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  relationName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  relationType?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  roleName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  source?: EntireFieldWrapper<Maybe<GQLSource>>;
  workPhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLCraftCmsIncident = {
  __typename?: 'CraftCmsIncident';
  active: EntireFieldWrapper<Scalars['Boolean']['output']>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  level: EntireFieldWrapper<GQLCraftCmsIncidentLevel>;
  postDate: EntireFieldWrapper<Scalars['String']['output']>;
  resolved: EntireFieldWrapper<Scalars['Boolean']['output']>;
  resolvedComment?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  resolvedDate?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  slug: EntireFieldWrapper<Scalars['String']['output']>;
  title: EntireFieldWrapper<Scalars['String']['output']>;
  updates?: EntireFieldWrapper<Maybe<Array<GQLCraftCmsIncidentUpdate>>>;
};

export enum GQLCraftCmsIncidentLevel {
  Critical = 'critical',
  Error = 'error',
  Serious = 'serious'
}

export type GQLCraftCmsIncidentUpdate = {
  __typename?: 'CraftCmsIncidentUpdate';
  text: EntireFieldWrapper<Scalars['String']['output']>;
  time: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLCraftCmsJob = {
  __typename?: 'CraftCmsJob';
  author?: EntireFieldWrapper<Maybe<GQLCmsArticleAuthor>>;
  excerpt?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  image?: EntireFieldWrapper<Maybe<GQLCmsArticleImage>>;
  modules?: EntireFieldWrapper<Maybe<Array<Maybe<GQLModule>>>>;
  postDate: EntireFieldWrapper<Scalars['String']['output']>;
  slug: EntireFieldWrapper<Scalars['String']['output']>;
  title: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLCraftCmsJobListingItem = {
  __typename?: 'CraftCmsJobListingItem';
  author?: EntireFieldWrapper<Maybe<GQLCmsArticleAuthor>>;
  excerpt?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  image?: EntireFieldWrapper<Maybe<GQLCmsArticleImage>>;
  postDate: EntireFieldWrapper<Scalars['String']['output']>;
  slug: EntireFieldWrapper<Scalars['String']['output']>;
  title: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLCraftCmsJobsListingResponse = {
  __typename?: 'CraftCmsJobsListingResponse';
  items: EntireFieldWrapper<Array<GQLCraftCmsJobListingItem>>;
  meta: EntireFieldWrapper<GQLCmsMeta>;
};

export type GQLCreateAward = {
  name?: InputMaybe<Scalars['String']['input']>;
  origin?: InputMaybe<Scalars['String']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
};

export type GQLCreatePageVisitInput = {
  browser?: InputMaybe<Scalars['String']['input']>;
  contactId?: InputMaybe<Scalars['String']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  endTime?: InputMaybe<Scalars['DateTime']['input']>;
  estateId: Scalars['String']['input'];
  lastHeartbeat: Scalars['DateTime']['input'];
  location?: InputMaybe<Scalars['String']['input']>;
  pageId: Scalars['String']['input'];
  source?: InputMaybe<Scalars['String']['input']>;
  startTime: Scalars['DateTime']['input'];
};

export type GQLCurrentBrokerRatingsTotal = {
  __typename?: 'CurrentBrokerRatingsTotal';
  allDates?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  allRatings?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  currentYear?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  fiveStar?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  fourStar?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  last30Days?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  lastYear?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  oneStar?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  threeStar?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  twoStar?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  withReview?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLDashboardActivity = {
  __typename?: 'DashboardActivity';
  done?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  end?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  estateAddress?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  id?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  performedById?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  start?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  type?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  typeName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  value?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLDashboardKtiCurrent = {
  __typename?: 'DashboardKtiCurrent';
  name: EntireFieldWrapper<Scalars['String']['output']>;
  ratings: EntireFieldWrapper<Scalars['Int']['output']>;
  value: EntireFieldWrapper<Scalars['Float']['output']>;
};

export type GQLDashboardKtiOthers = {
  __typename?: 'DashboardKtiOthers';
  name: EntireFieldWrapper<Scalars['String']['output']>;
  ratings: EntireFieldWrapper<Scalars['Int']['output']>;
  value: EntireFieldWrapper<Scalars['Float']['output']>;
};

export type GQLDashboardKtiResponse = {
  __typename?: 'DashboardKtiResponse';
  current: EntireFieldWrapper<GQLDashboardKtiCurrent>;
  others: EntireFieldWrapper<Array<GQLDashboardKtiOthers>>;
};

export type GQLDashboardLeadsResponse = {
  __typename?: 'DashboardLeadsResponse';
  budget: EntireFieldWrapper<Scalars['Float']['output']>;
  entries: EntireFieldWrapper<Array<GQLLeadsEntry>>;
};

export type GQLDashboardStatusEntry = {
  __typename?: 'DashboardStatusEntry';
  count: EntireFieldWrapper<Scalars['Int']['output']>;
  status: EntireFieldWrapper<Scalars['Int']['output']>;
  value: EntireFieldWrapper<Scalars['Float']['output']>;
};

export type GQLDashboardToplistResponse = {
  __typename?: 'DashboardToplistResponse';
  current: EntireFieldWrapper<GQLToplistEntryCurrent>;
  section: EntireFieldWrapper<GQLToplistSection>;
  topEntries: EntireFieldWrapper<Array<GQLToplistEntry>>;
};

export enum GQLDashboardType {
  Department = 'department',
  Personal = 'personal'
}

export type GQLDepartment = {
  __typename?: 'Department';
  city?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  departmentId: EntireFieldWrapper<Scalars['Int']['output']>;
  departmentNumber?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  displayKtiOnEmployee?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  employees?: EntireFieldWrapper<Maybe<Array<Maybe<GQLDepartmentEmployee>>>>;
  id: EntireFieldWrapper<Scalars['NodeID']['output']>;
  kti?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  legalName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  marketName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
  organisationNumber?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  phone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  postalCode?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  rating?: EntireFieldWrapper<Maybe<GQLDepartmentRating>>;
  slug?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  streetAddress?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLDepartmentEmployee = {
  __typename?: 'DepartmentEmployee';
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  employeeId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  mobilePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  slug?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLDepartmentRating = {
  __typename?: 'DepartmentRating';
  average?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  count?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  reviewsCount?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  weighted?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
};

export type GQLDepartmentsResponse = {
  __typename?: 'DepartmentsResponse';
  items: EntireFieldWrapper<Array<GQLDepartment>>;
  totalCount: EntireFieldWrapper<Scalars['Int']['output']>;
};

export type GQLEmailAudit = {
  __typename?: 'EmailAudit';
  contextId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contextType?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  createdAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
  fromEmail?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  globalMetadata?: EntireFieldWrapper<Maybe<Scalars['JSON']['output']>>;
  id: EntireFieldWrapper<Scalars['ID']['output']>;
  recipients: EntireFieldWrapper<Array<GQLEmailAuditRecipient>>;
  subject?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  templateName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  updatedAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
};

export type GQLEmailAuditRecipient = {
  __typename?: 'EmailAuditRecipient';
  bounceType?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  createdAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
  emailAudit?: EntireFieldWrapper<Maybe<GQLEmailAudit>>;
  emailAuditId: EntireFieldWrapper<Scalars['String']['output']>;
  id: EntireFieldWrapper<Scalars['ID']['output']>;
  lastEventAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  messageId: EntireFieldWrapper<Scalars['String']['output']>;
  openCount: EntireFieldWrapper<Scalars['Int']['output']>;
  openedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  rawLastEvent?: EntireFieldWrapper<Maybe<Scalars['JSON']['output']>>;
  recipientEmail: EntireFieldWrapper<Scalars['String']['output']>;
  recipientMetadata?: EntireFieldWrapper<Maybe<Scalars['JSON']['output']>>;
  rejectReason?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  sentAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
  status: EntireFieldWrapper<Scalars['String']['output']>;
  updatedAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
};

export type GQLEmailInteraction = {
  __typename?: 'EmailInteraction';
  bounceType?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  emailAuditId: EntireFieldWrapper<Scalars['String']['output']>;
  eventTimestamp: EntireFieldWrapper<Scalars['DateTime']['output']>;
  eventType: EntireFieldWrapper<Scalars['String']['output']>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  messageId: EntireFieldWrapper<Scalars['String']['output']>;
  openCount?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  recipientEmail: EntireFieldWrapper<Scalars['String']['output']>;
  rejectReason?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLEstateActivity = {
  __typename?: 'EstateActivity';
  done?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  end?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  id?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  performedById?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  start?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  type?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  typeName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  value?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLEstateAd = {
  __typename?: 'EstateAd';
  link?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  source?: EntireFieldWrapper<Maybe<GQLEstateAdSource>>;
};

export enum GQLEstateAdSource {
  Finn = 'FINN',
  Hjem = 'HJEM',
  Nordvikbolig = 'NORDVIKBOLIG'
}

export type GQLEstateBroker = {
  __typename?: 'EstateBroker';
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  employeeId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  employeeRoles?: EntireFieldWrapper<Maybe<Array<Maybe<GQLBrokerRole>>>>;
  image?: EntireFieldWrapper<Maybe<GQLBrokerImage>>;
  mobilePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  role?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  slug?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  workPhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLEstateChecklistItem = {
  __typename?: 'EstateChecklistItem';
  changedBy?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  changedDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  firstTag?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  tags?: EntireFieldWrapper<Maybe<Array<Maybe<Scalars['String']['output']>>>>;
  value?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLEstatePrice = {
  __typename?: 'EstatePrice';
  collectiveDebt?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  priceSuggestion?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  soldPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  totalPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
};

export type GQLEstatePriceModel = {
  __typename?: 'EstatePriceModel';
  additionalAgreementOptions?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  collectiveAssets?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  collectiveDebt?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  communityTax?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  communityTaxYear?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  estimatedValue?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  leasingPartyTransportFee?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  loanFare?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  originalAgreementPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  originalExpensesPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  otherExpenses?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  priceSuggestion?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  purchaseCostsAmount?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  salesCostDescription?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  soldPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  totalPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  totalPriceExclusiveCostsAndDebt?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  transportAgreementCosts?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  waterRate?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  waterRateDescription?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  waterRateYear?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  yearlyLeaseFee?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  yearlySocietyTax?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
};

export type GQLEstateProps = {
  projectRelation?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['Int']['input']>;
};

export type GQLEstateSizeModel = {
  __typename?: 'EstateSizeModel';
  grossArea?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  primaryRoomArea?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  primaryRoomAreaDescription?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  usableArea?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
};

export type GQLEstateStats = {
  __typename?: 'EstateStats';
  bidders?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  bids?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  followUp?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  interested?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  privateShowingsCount?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  showingParticipants?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  showingRegistrations?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  showingRegistrationsTotal?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  showings?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  showingsTotal?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export enum GQLEstateTabFilter {
  Archived = 'Archived',
  ForSale = 'ForSale',
  InPreparation = 'InPreparation',
  Requested = 'Requested',
  Sold = 'Sold',
  Valuation = 'Valuation'
}

export type GQLFindEstatesForBrokerFilters = {
  assignmentTypeGroup?: InputMaybe<Array<Scalars['Int']['input']>>;
  estateTypeId?: InputMaybe<Array<Scalars['Int']['input']>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  priceRange?: InputMaybe<GQLMinMax>;
  radius?: InputMaybe<Scalars['Int']['input']>;
  sizeRange?: InputMaybe<GQLMinMax>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  statuses?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export type GQLFinnData = {
  __typename?: 'FinnData';
  finnCode?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  finnExpireDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  finnPublishDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
};

export type GQLHallOfFameEntry = {
  __typename?: 'HallOfFameEntry';
  entries?: EntireFieldWrapper<Maybe<Array<GQLHallOfFameEntryAward>>>;
  year?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLHallOfFameEntryAward = {
  __typename?: 'HallOfFameEntryAward';
  awardId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  employee?: EntireFieldWrapper<Maybe<GQLBroker>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  year?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLImportantTask = {
  __typename?: 'ImportantTask';
  amlUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  eiendomsverdiUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  estateAddress: EntireFieldWrapper<Scalars['String']['output']>;
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  mainBrokerId: EntireFieldWrapper<Scalars['String']['output']>;
  signUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  type: EntireFieldWrapper<GQLImportantTaskType>;
};

export enum GQLImportantTaskType {
  AmlCheckIncomplete = 'aml_check_incomplete',
  EtakstChecksIncomplete = 'etakst_checks_incomplete',
  SignListingAgreement = 'sign_listing_agreement'
}

export type GQLInspection = {
  __typename?: 'Inspection';
  entries: EntireFieldWrapper<Array<GQLInspectionEntry>>;
  metadata?: EntireFieldWrapper<Maybe<GQLInspectionMetadata>>;
  success?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
};

export type GQLInspectionActivity = {
  __typename?: 'InspectionActivity';
  contacts: EntireFieldWrapper<Array<GQLSimpleContact>>;
  description?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  employeeId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id: EntireFieldWrapper<Scalars['ID']['output']>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
  source?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  timestamp: EntireFieldWrapper<Scalars['DateTime']['output']>;
  type: EntireFieldWrapper<Scalars['String']['output']>;
  userAgent?: EntireFieldWrapper<Maybe<GQLUserAgent>>;
};

export type GQLInspectionEntry = {
  __typename?: 'InspectionEntry';
  id: EntireFieldWrapper<Scalars['String']['output']>;
  postDate?: EntireFieldWrapper<Maybe<GQLPostDate>>;
  title: EntireFieldWrapper<Scalars['String']['output']>;
  url: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLInspectionEvent = {
  __typename?: 'InspectionEvent';
  description?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  end?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  start: EntireFieldWrapper<Scalars['DateTime']['output']>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  type: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLInspectionEventInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  end?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  start: Scalars['String']['input'];
  title?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};

export type GQLInspectionFolder = {
  __typename?: 'InspectionFolder';
  audit?: EntireFieldWrapper<Maybe<Array<GQLInspectionFolderAudit>>>;
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  createdBy?: EntireFieldWrapper<Maybe<GQLBroker>>;
  deletedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  excludedEmployees: EntireFieldWrapper<Array<Scalars['String']['output']>>;
  excludedPartners: EntireFieldWrapper<Array<GQLBrokerPartner>>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  listingAgreement?: EntireFieldWrapper<Maybe<GQLListingAgreement>>;
  listingAgreementActive: EntireFieldWrapper<Scalars['Boolean']['output']>;
  listingAgreementSentAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  notes?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  publishedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  publishedBy?: EntireFieldWrapper<Maybe<GQLBroker>>;
  recipients?: EntireFieldWrapper<Maybe<Array<Scalars['String']['output']>>>;
  relevantLinks: EntireFieldWrapper<Array<Scalars['String']['output']>>;
  sentAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  sentBy?: EntireFieldWrapper<Maybe<GQLBroker>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  updatedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  updatedBy?: EntireFieldWrapper<Maybe<GQLBroker>>;
};

export type GQLInspectionFolderAudit = {
  __typename?: 'InspectionFolderAudit';
  channels: EntireFieldWrapper<Array<Scalars['String']['output']>>;
  emailAuditId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  estateId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  extraData?: EntireFieldWrapper<Maybe<Scalars['JSON']['output']>>;
  id: EntireFieldWrapper<Scalars['Int']['output']>;
  listingAgreementActive?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  modifiedTemplate?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  recipientContactIds: EntireFieldWrapper<Array<Scalars['String']['output']>>;
  recipients?: EntireFieldWrapper<Maybe<Array<GQLAuditRecipient>>>;
  sentAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  sentBy?: EntireFieldWrapper<Maybe<GQLBroker>>;
  templateId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLInspectionLead = {
  __typename?: 'InspectionLead';
  comment?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactId: EntireFieldWrapper<Scalars['String']['output']>;
  createdAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
  createdByContactId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  createdByEmployeeId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  id: EntireFieldWrapper<Scalars['String']['output']>;
  leadType: EntireFieldWrapper<GQLInspectionLeadType>;
  successful: EntireFieldWrapper<Scalars['Boolean']['output']>;
  updatedAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
};

export enum GQLInspectionLeadType {
  Financing = 'FINANCING'
}

export type GQLInspectionMetadata = {
  __typename?: 'InspectionMetadata';
  count?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLInteractionFilter = {
  employeeIds?: InputMaybe<Array<Scalars['String']['input']>>;
  estateIds?: InputMaybe<Array<Scalars['String']['input']>>;
  hasEventTypes?: InputMaybe<Array<GQLListingAgreementInteractionType>>;
  hasInteractionSince?: InputMaybe<Scalars['DateTime']['input']>;
  hasNotEventTypes?: InputMaybe<Array<GQLListingAgreementInteractionType>>;
  signed?: InputMaybe<Scalars['Boolean']['input']>;
};

export type GQLKeyFigureEntry = {
  __typename?: 'KeyFigureEntry';
  format?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  label: EntireFieldWrapper<Scalars['String']['output']>;
  lastValue?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  type?: EntireFieldWrapper<Maybe<GQLToplistSection>>;
  value: EntireFieldWrapper<Scalars['Float']['output']>;
};

export type GQLKtiResponse = {
  __typename?: 'KtiResponse';
  averageRating?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  ratingCount?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  reviewCount?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  reviews?: EntireFieldWrapper<Maybe<Array<GQLReview>>>;
};

export type GQLLandIdentificationMatrix = {
  __typename?: 'LandIdentificationMatrix';
  bnr?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  fnr?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  gnr?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  knr?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  ownPart?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  snr?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLLeadsEntry = {
  __typename?: 'LeadsEntry';
  actual: EntireFieldWrapper<Scalars['Float']['output']>;
  budget: EntireFieldWrapper<Scalars['Float']['output']>;
  current: EntireFieldWrapper<Scalars['Boolean']['output']>;
  departmentId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  employeeCount: EntireFieldWrapper<Scalars['Int']['output']>;
  id?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
  value: EntireFieldWrapper<Scalars['Float']['output']>;
};

export type GQLListingAgreement = {
  __typename?: 'ListingAgreement';
  accessTokens?: EntireFieldWrapper<Maybe<Array<Maybe<GQLAccessToken>>>>;
  brokerSigners?: EntireFieldWrapper<Maybe<Array<GQLSigner>>>;
  budgetPosts: EntireFieldWrapper<Array<GQLBudgetPost>>;
  canStartSigning?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  commission?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  createdAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  deadline?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  deadlineHasBeenExceeded?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  deletedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  feePercentage?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  generalTerms?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  hasStorebrandLead: EntireFieldWrapper<Scalars['Boolean']['output']>;
  id: EntireFieldWrapper<Scalars['NodeID']['output']>;
  initiatedSigningAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  interactions: EntireFieldWrapper<Array<GQLListingAgreementInteraction>>;
  marketingPackage?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  offerSellerLink?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  sellerInsurance?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  sellerSigners?: EntireFieldWrapper<Maybe<Array<GQLSigner>>>;
  sentToClientAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  signedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  signers: EntireFieldWrapper<Array<GQLSigner>>;
  signicatDocument?: EntireFieldWrapper<Maybe<GQLSignicatDocument>>;
  signicatDocumentId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  status?: EntireFieldWrapper<Maybe<GQLListingAgreementStatus>>;
  suggestedPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  updatedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
};

export type GQLListingAgreementInteraction = {
  __typename?: 'ListingAgreementInteraction';
  broker?: EntireFieldWrapper<Maybe<GQLBroker>>;
  employeeId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  eventTimestamp: EntireFieldWrapper<Scalars['DateTime']['output']>;
  eventType: EntireFieldWrapper<GQLListingAgreementInteractionType>;
  extraData?: EntireFieldWrapper<Maybe<Scalars['JSON']['output']>>;
  id: EntireFieldWrapper<Scalars['Int']['output']>;
  listingAgreementsId: EntireFieldWrapper<Scalars['String']['output']>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  seller?: EntireFieldWrapper<Maybe<GQLListingAgreementInteractionSeller>>;
  sellerId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLListingAgreementInteractionSeller = {
  __typename?: 'ListingAgreementInteractionSeller';
  contactId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  firstName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  lastName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLListingAgreementInteractionSummary = {
  __typename?: 'ListingAgreementInteractionSummary';
  estate?: EntireFieldWrapper<Maybe<GQLBrokerEstate>>;
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  interactions: EntireFieldWrapper<Array<GQLListingAgreementInteraction>>;
};

export enum GQLListingAgreementInteractionType {
  Created = 'created',
  EmailOpened = 'email_opened',
  EmailSent = 'email_sent',
  EtakstClicked = 'etakst_clicked',
  EtakstSent = 'etakst_sent',
  Expired = 'expired',
  FinancingRequested = 'financing_requested',
  SentToSeller = 'sent_to_seller',
  Signed = 'signed',
  SignedBySeller = 'signed_by_seller',
  StartSigning = 'start_signing',
  TipSent = 'tip_sent',
  Viewed = 'viewed',
  Withdrawn = 'withdrawn'
}

export type GQLListingAgreementInteractions = {
  __typename?: 'ListingAgreementInteractions';
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  interactions: EntireFieldWrapper<Array<GQLListingAgreementInteraction>>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
};

export enum GQLListingAgreementStatus {
  Created = 'Created',
  Expired = 'Expired',
  PartialSigned = 'PartialSigned',
  Sent = 'Sent',
  Signed = 'Signed',
  SignedViaNext = 'SignedViaNext',
  Signing = 'Signing'
}

export type GQLLocation = {
  __typename?: 'Location';
  coordinates?: EntireFieldWrapper<Maybe<Array<Scalars['Float']['output']>>>;
  type?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLMarketingChannel = {
  __typename?: 'MarketingChannel';
  id: EntireFieldWrapper<Scalars['String']['output']>;
  title: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLMarketingPackage = {
  __typename?: 'MarketingPackage';
  active?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  channels: EntireFieldWrapper<Array<GQLMarketingChannel>>;
  clicks?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id: EntireFieldWrapper<Scalars['NodeID']['output']>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  packageId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  price?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  productTag?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  public?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  shortName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  views?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLMarketingStart = {
  __typename?: 'MarketingStart';
  date?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  source?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLMinMax = {
  max: Scalars['Int']['input'];
  min: Scalars['Int']['input'];
};

export type GQLModule = {
  __typename?: 'Module';
  accordion?: EntireFieldWrapper<Maybe<Array<Maybe<GQLAccordion>>>>;
  body?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  type: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLMutation = {
  __typename?: 'Mutation';
  addAward: EntireFieldWrapper<Scalars['Boolean']['output']>;
  addInspectionEvents: EntireFieldWrapper<Scalars['Boolean']['output']>;
  clearInspectionEvents: EntireFieldWrapper<Scalars['Boolean']['output']>;
  createBrokerPartner: EntireFieldWrapper<GQLBrokerPartner>;
  createPageVisit?: EntireFieldWrapper<Maybe<GQLPageVisit>>;
  deleteBrokerPartner: EntireFieldWrapper<Scalars['Boolean']['output']>;
  deleteInspectionEvent: EntireFieldWrapper<Scalars['Boolean']['output']>;
  endPageVisit: EntireFieldWrapper<Scalars['Boolean']['output']>;
  hideAward: EntireFieldWrapper<Scalars['Boolean']['output']>;
  hideBrokerPartner?: EntireFieldWrapper<Maybe<GQLBrokerPartner>>;
  markNewsAsRead?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  pageVisitHeartbeat?: EntireFieldWrapper<Maybe<GQLPageVisit>>;
  removeAward: EntireFieldWrapper<Scalars['Boolean']['output']>;
  reorderBrokerPartners: EntireFieldWrapper<Scalars['Boolean']['output']>;
  resetFlagForAllUsers: EntireFieldWrapper<Scalars['Boolean']['output']>;
  resetForm?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  sendInspectionLead: EntireFieldWrapper<GQLInspectionLead>;
  updateAward: EntireFieldWrapper<Scalars['Boolean']['output']>;
  updateBroker: EntireFieldWrapper<Scalars['Boolean']['output']>;
  updateBrokerPartner?: EntireFieldWrapper<Maybe<GQLBrokerPartner>>;
  updateBrokerProfileLinks: EntireFieldWrapper<Scalars['Boolean']['output']>;
  updateInspectionEvent: EntireFieldWrapper<Scalars['Boolean']['output']>;
  updateInspectionEvents: EntireFieldWrapper<Scalars['Boolean']['output']>;
  updateInspectionFolderNotes?: EntireFieldWrapper<Maybe<GQLInspectionFolder>>;
  upsertEstatePublishDate: EntireFieldWrapper<Scalars['Boolean']['output']>;
  userResetAllFlags: EntireFieldWrapper<Scalars['Boolean']['output']>;
  userResetFlag: EntireFieldWrapper<Scalars['Boolean']['output']>;
  userSetFlag: EntireFieldWrapper<Scalars['Boolean']['output']>;
};


export type GQLMutationAddAwardArgs = {
  input: GQLCreateAward;
};


export type GQLMutationAddInspectionEventsArgs = {
  estateId: Scalars['String']['input'];
  events: Array<GQLInspectionEventInput>;
};


export type GQLMutationClearInspectionEventsArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLMutationCreateBrokerPartnerArgs = {
  input: GQLBrokerPartnerCreateInput;
};


export type GQLMutationCreatePageVisitArgs = {
  input: GQLCreatePageVisitInput;
};


export type GQLMutationDeleteBrokerPartnerArgs = {
  id: Scalars['String']['input'];
};


export type GQLMutationDeleteInspectionEventArgs = {
  eventId: Scalars['String']['input'];
};


export type GQLMutationEndPageVisitArgs = {
  pageId: Scalars['String']['input'];
};


export type GQLMutationHideAwardArgs = {
  hidden: Scalars['Boolean']['input'];
  id: Scalars['String']['input'];
};


export type GQLMutationHideBrokerPartnerArgs = {
  hidden: Scalars['Boolean']['input'];
  id: Scalars['String']['input'];
};


export type GQLMutationMarkNewsAsReadArgs = {
  newsId: Scalars['String']['input'];
};


export type GQLMutationPageVisitHeartbeatArgs = {
  estateId: Scalars['String']['input'];
  pageId: Scalars['String']['input'];
};


export type GQLMutationRemoveAwardArgs = {
  id: Scalars['String']['input'];
};


export type GQLMutationReorderBrokerPartnersArgs = {
  ids: Array<Scalars['String']['input']>;
};


export type GQLMutationResetFlagForAllUsersArgs = {
  flag: Scalars['String']['input'];
};


export type GQLMutationResetFormArgs = {
  estateId: Scalars['String']['input'];
  formType: Scalars['String']['input'];
};


export type GQLMutationSendInspectionLeadArgs = {
  contactId: Scalars['String']['input'];
  estateId: Scalars['String']['input'];
  leadType: GQLInspectionLeadType;
  source?: InputMaybe<Scalars['String']['input']>;
};


export type GQLMutationUpdateAwardArgs = {
  input: GQLUpdateAward;
};


export type GQLMutationUpdateBrokerArgs = {
  input: GQLUpdateBrokerPayload;
};


export type GQLMutationUpdateBrokerPartnerArgs = {
  input: GQLBrokerPartnerUpdateInput;
};


export type GQLMutationUpdateBrokerProfileLinksArgs = {
  input: GQLBrokerProfileLinksPayload;
};


export type GQLMutationUpdateInspectionEventArgs = {
  event: GQLInspectionEventInput;
  eventId: Scalars['String']['input'];
};


export type GQLMutationUpdateInspectionEventsArgs = {
  estateId: Scalars['String']['input'];
  events: Array<GQLInspectionEventInput>;
};


export type GQLMutationUpdateInspectionFolderNotesArgs = {
  estateId: Scalars['String']['input'];
  notes: Scalars['String']['input'];
};


export type GQLMutationUpsertEstatePublishDateArgs = {
  estateId: Scalars['String']['input'];
  publishDate?: InputMaybe<Scalars['DateTime']['input']>;
};


export type GQLMutationUserResetFlagArgs = {
  flag: Scalars['String']['input'];
};


export type GQLMutationUserSetFlagArgs = {
  flag: Scalars['String']['input'];
  value: Scalars['Boolean']['input'];
};

export type GQLNextDocument = {
  __typename?: 'NextDocument';
  approvalDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  approvedBy?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  docType: EntireFieldWrapper<Scalars['Int']['output']>;
  documentId: EntireFieldWrapper<Scalars['String']['output']>;
  extension: EntireFieldWrapper<Scalars['String']['output']>;
  head: EntireFieldWrapper<Scalars['String']['output']>;
  lastChanged: EntireFieldWrapper<Scalars['DateTime']['output']>;
  signStatus?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLNordvikAward = {
  __typename?: 'NordvikAward';
  awardId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  hidden?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  origin?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  private?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  year?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLPageVisit = {
  __typename?: 'PageVisit';
  browser?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  employeeId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  endTime?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  estateId: EntireFieldWrapper<Scalars['String']['output']>;
  id: EntireFieldWrapper<Scalars['Int']['output']>;
  lastHeartbeat: EntireFieldWrapper<Scalars['DateTime']['output']>;
  location?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  pageId: EntireFieldWrapper<Scalars['String']['output']>;
  source?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  startTime: EntireFieldWrapper<Scalars['DateTime']['output']>;
  totalTimeSpent: EntireFieldWrapper<Scalars['Int']['output']>;
  userAgent?: EntireFieldWrapper<Maybe<GQLUserAgent>>;
};

export type GQLPagination = {
  __typename?: 'Pagination';
  count?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  limit?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  offset?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  total?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLPartOwnership = {
  __typename?: 'PartOwnership';
  businessManagerContactId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  estateHousingCooperativeStockHousingUnitNumber?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  estateHousingCooperativeStockNumber?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  partAbout?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  partName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  partNumber?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  partOrgNumber?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export enum GQLPersonalInfoOrigin {
  Eid = 'eid',
  Unknown = 'unknown',
  UserFormInput = 'userFormInput'
}

export type GQLPostDate = {
  __typename?: 'PostDate';
  date: EntireFieldWrapper<Scalars['String']['output']>;
  timezone: EntireFieldWrapper<Scalars['String']['output']>;
  timezone_type: EntireFieldWrapper<Scalars['Int']['output']>;
};

export type GQLPriceIndex = {
  __typename?: 'PriceIndex';
  area?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  avgSalesTime?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  avgSqmPrice?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  date: EntireFieldWrapper<Scalars['DateTime']['output']>;
  id: EntireFieldWrapper<Scalars['ID']['output']>;
  indexChange1Month?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  indexChange1Quarter?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  indexChange4Quarter?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  indexChange5Years?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  indexChange10Years?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  indexChange12Months?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  region?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  type: EntireFieldWrapper<Scalars['String']['output']>;
};

export type GQLPriceStatistics = {
  __typename?: 'PriceStatistics';
  indexes: EntireFieldWrapper<Array<GQLPriceIndex>>;
  secondaryIndexes: EntireFieldWrapper<Array<GQLPriceIndex>>;
};

export type GQLQuery = {
  __typename?: 'Query';
  activitySummary: EntireFieldWrapper<GQLActivitySummary>;
  allDepartments?: EntireFieldWrapper<Maybe<GQLDepartmentsResponse>>;
  allListingAgreementInteractions: EntireFieldWrapper<Array<GQLListingAgreementInteractionSummary>>;
  assignmentDocumentStatus: EntireFieldWrapper<GQLAssignmentDocumentStatusItem>;
  brokerByEmail?: EntireFieldWrapper<Maybe<GQLBroker>>;
  brokerByEmployeeId?: EntireFieldWrapper<Maybe<GQLBroker>>;
  brokerPartner?: EntireFieldWrapper<Maybe<GQLBrokerPartner>>;
  brokerPartners: EntireFieldWrapper<Array<GQLBrokerPartner>>;
  brokers?: EntireFieldWrapper<Maybe<GQLBrokersResponse>>;
  budgetPostDescriptions: EntireFieldWrapper<Array<GQLBudgetPostDescription>>;
  cmsArticleBySlug?: EntireFieldWrapper<Maybe<GQLCmsArticle>>;
  cmsArticleCategories: EntireFieldWrapper<Array<GQLCmsArticleCategory>>;
  cmsArticles: EntireFieldWrapper<GQLCmsArticleResponse>;
  cmsChangelogs: EntireFieldWrapper<GQLCmsArticleResponse>;
  cmsIncidentBySlug?: EntireFieldWrapper<Maybe<GQLCraftCmsIncident>>;
  cmsIncidents: EntireFieldWrapper<GQLCmsIncidentsResponse>;
  cmsJobBySlug?: EntireFieldWrapper<Maybe<GQLCraftCmsJob>>;
  cmsJobsListing: EntireFieldWrapper<GQLCraftCmsJobsListingResponse>;
  contact?: EntireFieldWrapper<Maybe<GQLContact>>;
  currentBroker?: EntireFieldWrapper<Maybe<GQLBroker>>;
  currentBrokerKti: EntireFieldWrapper<GQLBrokerKtiResponse>;
  currentBrokerPartners: EntireFieldWrapper<Array<GQLBrokerPartner>>;
  currentBrokerProfileLinks?: EntireFieldWrapper<Maybe<GQLBrokerProfileLinks>>;
  currentBrokerRatings?: EntireFieldWrapper<Maybe<GQLBrokerRatingsList>>;
  currentBrokerRatingsTotal?: EntireFieldWrapper<Maybe<GQLCurrentBrokerRatingsTotal>>;
  dashboardAverageCommission: EntireFieldWrapper<GQLAverageCommissionResponse>;
  dashboardCache: EntireFieldWrapper<Scalars['Boolean']['output']>;
  dashboardCacheForEmployee: EntireFieldWrapper<Scalars['Boolean']['output']>;
  dashboardExpectedRevenue: EntireFieldWrapper<Array<GQLDashboardStatusEntry>>;
  dashboardImportantTasks: EntireFieldWrapper<Array<GQLImportantTask>>;
  dashboardKeyFigures: EntireFieldWrapper<Array<GQLKeyFigureEntry>>;
  dashboardKti: EntireFieldWrapper<GQLDashboardKtiResponse>;
  dashboardLeads: EntireFieldWrapper<GQLDashboardLeadsResponse>;
  dashboardRevenue: EntireFieldWrapper<GQLRevenueResponse>;
  dashboardToplist: EntireFieldWrapper<GQLDashboardToplistResponse>;
  dashboardUpcomingActivities: EntireFieldWrapper<Array<GQLDashboardActivity>>;
  department?: EntireFieldWrapper<Maybe<GQLDepartment>>;
  emailAuditsByEstateId: EntireFieldWrapper<Array<GQLEmailAudit>>;
  emailInteractionsForEstate: EntireFieldWrapper<Array<GQLEmailInteraction>>;
  estate?: EntireFieldWrapper<Maybe<GQLBrokerEstate>>;
  estateFormsByEstateId: EntireFieldWrapper<Array<GQLBrokerEstateForm>>;
  estatePriceHistories: EntireFieldWrapper<Array<GQLBrokerEstatePriceHistory>>;
  estates: EntireFieldWrapper<Array<GQLBrokerEstate>>;
  estatesForBrokerById: EntireFieldWrapper<GQLBrokerEstatesResponse>;
  estatesForBrokerIdCount: EntireFieldWrapper<Array<GQLBrokerEstateCountResponse>>;
  estatesForDepartment: EntireFieldWrapper<GQLBrokerEstatesResponse>;
  findEstates: EntireFieldWrapper<Array<GQLBrokerEstate>>;
  findEstatesForBroker: EntireFieldWrapper<Array<GQLBrokerEstate>>;
  hallOfFame: EntireFieldWrapper<Array<GQLHallOfFameEntry>>;
  inspectionEvents: EntireFieldWrapper<Array<GQLInspectionEvent>>;
  inspectionFolder?: EntireFieldWrapper<Maybe<GQLInspectionFolder>>;
  inspectionLeadsForEstate: EntireFieldWrapper<Array<GQLInspectionLead>>;
  listingAgreementByDocumentId?: EntireFieldWrapper<Maybe<GQLListingAgreement>>;
  listingAgreementByEstateId?: EntireFieldWrapper<Maybe<GQLListingAgreement>>;
  listingAgreementInteractions: EntireFieldWrapper<Array<GQLListingAgreementInteraction>>;
  listingAgreementInteractionsForBroker: EntireFieldWrapper<Array<GQLListingAgreementInteractions>>;
  listingAgreementInteractionsForCurrentBroker: EntireFieldWrapper<Array<GQLListingAgreementInteractions>>;
  listingAgreementInteractionsForEstate: EntireFieldWrapper<Array<GQLListingAgreementInteraction>>;
  mainBrokerPartners: EntireFieldWrapper<Array<GQLBrokerPartner>>;
  marketingPackages: EntireFieldWrapper<Array<GQLMarketingPackage>>;
  pageVisit?: EntireFieldWrapper<Maybe<GQLPageVisit>>;
  pageVisitHeartbeat?: EntireFieldWrapper<Maybe<GQLPageVisit>>;
  pageVisits: EntireFieldWrapper<Array<GQLPageVisit>>;
  priceStatistics: EntireFieldWrapper<GQLPriceStatistics>;
  ratings: EntireFieldWrapper<GQLRatingResponse>;
  readArticles: EntireFieldWrapper<Array<GQLReadArticle>>;
  storebrandDuplicateCheck: EntireFieldWrapper<GQLStorebrandDuplicateCheckResult>;
  syncEstateWithVitec: EntireFieldWrapper<Scalars['Boolean']['output']>;
  toplist: EntireFieldWrapper<GQLToplistResponse>;
  userHasFlag: EntireFieldWrapper<Scalars['Boolean']['output']>;
  userNotifications: EntireFieldWrapper<GQLUserNotificationsResponse>;
};


export type GQLQueryActivitySummaryArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryAllListingAgreementInteractionsArgs = {
  input?: InputMaybe<GQLInteractionFilter>;
};


export type GQLQueryAssignmentDocumentStatusArgs = {
  estateId: Scalars['String']['input'];
  type: GQLAssignmentDocumentType;
};


export type GQLQueryBrokerByEmailArgs = {
  email: Scalars['String']['input'];
};


export type GQLQueryBrokerByEmployeeIdArgs = {
  employeeId: Scalars['String']['input'];
};


export type GQLQueryBrokerPartnerArgs = {
  id: Scalars['String']['input'];
};


export type GQLQueryBrokerPartnersArgs = {
  employeeId: Scalars['String']['input'];
};


export type GQLQueryCmsArticleBySlugArgs = {
  slug: Scalars['String']['input'];
};


export type GQLQueryCmsArticleCategoriesArgs = {
  type?: InputMaybe<GQLCmsArticleType>;
};


export type GQLQueryCmsArticlesArgs = {
  categorySlug?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<GQLCmsArticleType>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type GQLQueryCmsChangelogsArgs = {
  categorySlug?: InputMaybe<Scalars['String']['input']>;
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
};


export type GQLQueryCmsIncidentBySlugArgs = {
  slug: Scalars['String']['input'];
};


export type GQLQueryCmsIncidentsArgs = {
  active?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GQLQueryCmsJobBySlugArgs = {
  slug: Scalars['String']['input'];
};


export type GQLQueryContactArgs = {
  contactId: Scalars['String']['input'];
  source?: InputMaybe<GQLSource>;
};


export type GQLQueryCurrentBrokerKtiArgs = {
  includeDepartment?: InputMaybe<Scalars['Boolean']['input']>;
  includeNordvik?: InputMaybe<Scalars['Boolean']['input']>;
  includeReviews?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GQLQueryCurrentBrokerRatingsArgs = {
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
  sortBy?: InputMaybe<GQLBrokerRatingSortBy>;
  sortDir?: InputMaybe<GQLSortDirection>;
};


export type GQLQueryCurrentBrokerRatingsTotalArgs = {
  dateFrom?: InputMaybe<Scalars['DateTime']['input']>;
  dateTo?: InputMaybe<Scalars['DateTime']['input']>;
  featured?: InputMaybe<Scalars['Boolean']['input']>;
  hasReview?: InputMaybe<Scalars['Boolean']['input']>;
  rating?: InputMaybe<Scalars['Int']['input']>;
};


export type GQLQueryDashboardAverageCommissionArgs = {
  type: GQLDashboardType;
};


export type GQLQueryDashboardCacheForEmployeeArgs = {
  employeeId: Scalars['String']['input'];
  section: GQLSection;
};


export type GQLQueryDashboardExpectedRevenueArgs = {
  type: GQLDashboardType;
};


export type GQLQueryDashboardKeyFiguresArgs = {
  period: Scalars['String']['input'];
  type: GQLDashboardType;
};


export type GQLQueryDashboardLeadsArgs = {
  period?: InputMaybe<Scalars['String']['input']>;
  type: GQLDashboardType;
};


export type GQLQueryDashboardRevenueArgs = {
  type: GQLDashboardType;
};


export type GQLQueryDashboardToplistArgs = {
  amountOfEntries?: InputMaybe<Scalars['Int']['input']>;
  excludeCurrent?: InputMaybe<Scalars['Boolean']['input']>;
  period?: InputMaybe<Scalars['String']['input']>;
  section: GQLToplistSection;
  type: GQLDashboardType;
};


export type GQLQueryDashboardUpcomingActivitiesArgs = {
  type: GQLDashboardType;
};


export type GQLQueryDepartmentArgs = {
  departmentId: Scalars['Int']['input'];
};


export type GQLQueryEmailAuditsByEstateIdArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryEmailInteractionsForEstateArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryEstateArgs = {
  id: Scalars['String']['input'];
};


export type GQLQueryEstateFormsByEstateIdArgs = {
  estateId: Scalars['String']['input'];
  estateProps?: InputMaybe<GQLEstateProps>;
};


export type GQLQueryEstatesForBrokerByIdArgs = {
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  assignmentTypeGroup?: InputMaybe<Array<Scalars['Int']['input']>>;
  brokerId?: InputMaybe<Scalars['String']['input']>;
  disableCache?: InputMaybe<Scalars['Boolean']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  sortBy?: InputMaybe<GQLSortEstateBy>;
  tabs: Array<GQLEstateTabFilter>;
};


export type GQLQueryEstatesForBrokerIdCountArgs = {
  brokerId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  tabs: Array<GQLEstateTabFilter>;
};


export type GQLQueryEstatesForDepartmentArgs = {
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  assignmentTypeGroup?: InputMaybe<Array<Scalars['Int']['input']>>;
  brokerIds?: InputMaybe<Array<Scalars['String']['input']>>;
  departmentId: Scalars['Int']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  sortBy?: InputMaybe<GQLSortEstateBy>;
  tabs: Array<GQLEstateTabFilter>;
};


export type GQLQueryFindEstatesArgs = {
  filters: GQLFindEstatesFilters;
};


export type GQLQueryFindEstatesForBrokerArgs = {
  employeeId: Scalars['String']['input'];
  filters?: InputMaybe<GQLFindEstatesForBrokerFilters>;
};


export type GQLQueryInspectionEventsArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryInspectionFolderArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryInspectionLeadsForEstateArgs = {
  estateId: Scalars['String']['input'];
  leadType?: InputMaybe<GQLInspectionLeadType>;
};


export type GQLQueryListingAgreementByDocumentIdArgs = {
  documentId: Scalars['String']['input'];
};


export type GQLQueryListingAgreementByEstateIdArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryListingAgreementInteractionsArgs = {
  listingAgreementId: Scalars['String']['input'];
};


export type GQLQueryListingAgreementInteractionsForBrokerArgs = {
  employeeId: Scalars['String']['input'];
};


export type GQLQueryListingAgreementInteractionsForEstateArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryMainBrokerPartnersArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryMarketingPackagesArgs = {
  active: Scalars['Boolean']['input'];
  publicVisible?: InputMaybe<Scalars['Boolean']['input']>;
  type: Scalars['String']['input'];
};


export type GQLQueryPageVisitArgs = {
  id: Scalars['Int']['input'];
};


export type GQLQueryPageVisitHeartbeatArgs = {
  estateId: Scalars['String']['input'];
  pageId: Scalars['String']['input'];
};


export type GQLQueryPageVisitsArgs = {
  estateId: Scalars['String']['input'];
  includeSubPages?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GQLQueryPriceStatisticsArgs = {
  postalCode: Scalars['String']['input'];
  years?: InputMaybe<Scalars['Float']['input']>;
};


export type GQLQueryRatingsArgs = {
  employeeId?: InputMaybe<Scalars['String']['input']>;
  ytd?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GQLQueryStorebrandDuplicateCheckArgs = {
  input: GQLStorebrandDuplicateCheckInput;
};


export type GQLQuerySyncEstateWithVitecArgs = {
  estateId: Scalars['String']['input'];
};


export type GQLQueryToplistArgs = {
  cron?: InputMaybe<Scalars['Boolean']['input']>;
  departmentId?: InputMaybe<Scalars['Int']['input']>;
  employeeId?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Date']['input']>;
  estateType?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  marketingPackage?: InputMaybe<Scalars['String']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  partner?: InputMaybe<Scalars['String']['input']>;
  period?: InputMaybe<Scalars['String']['input']>;
  roles?: InputMaybe<Array<GQLToplistRole>>;
  section?: InputMaybe<GQLToplistSection>;
  startDate?: InputMaybe<Scalars['Date']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};


export type GQLQueryUserHasFlagArgs = {
  flag: Scalars['String']['input'];
};

export type GQLRating = {
  __typename?: 'Rating';
  average?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  count?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  reviewsCount?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  weighted?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
};

export type GQLRatingResponse = {
  __typename?: 'RatingResponse';
  average?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  count?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLReadArticle = {
  __typename?: 'ReadArticle';
  id: EntireFieldWrapper<Scalars['String']['output']>;
  readAt: EntireFieldWrapper<Scalars['DateTime']['output']>;
};

export type GQLRelevantForEstateWithProps = {
  __typename?: 'RelevantForEstateWithProps';
  projectRelation?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  status?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
};

export type GQLRevenueEntry = {
  __typename?: 'RevenueEntry';
  value: EntireFieldWrapper<Scalars['Float']['output']>;
  year: EntireFieldWrapper<Scalars['Int']['output']>;
};

export type GQLRevenueMonth = {
  __typename?: 'RevenueMonth';
  budget?: EntireFieldWrapper<Maybe<GQLRevenueEntry>>;
  current?: EntireFieldWrapper<Maybe<GQLRevenueEntry>>;
  month: EntireFieldWrapper<Scalars['Int']['output']>;
  previous: EntireFieldWrapper<GQLRevenueEntry>;
};

export type GQLRevenueResponse = {
  __typename?: 'RevenueResponse';
  budgetTotal: EntireFieldWrapper<Scalars['Float']['output']>;
  currentYearBudget: EntireFieldWrapper<Scalars['Float']['output']>;
  currentYearTotal: EntireFieldWrapper<Scalars['Int']['output']>;
  entries: EntireFieldWrapper<Array<GQLRevenueMonth>>;
  hitBudgetPercentage: EntireFieldWrapper<Scalars['Float']['output']>;
  percentageChange: EntireFieldWrapper<Scalars['Float']['output']>;
  previousYearTotal?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
  previousYearUntilNow?: EntireFieldWrapper<Maybe<Scalars['Float']['output']>>;
};

export type GQLReview = {
  __typename?: 'Review';
  agentSatisfaction?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  createdAt?: EntireFieldWrapper<Maybe<Scalars['Date']['output']>>;
  id?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  lang?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  rating?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  recommendation?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  reviewerShortName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  text?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export enum GQLSection {
  AverageCommission = 'averageCommission',
  KeyFigures = 'keyFigures',
  Revenue = 'revenue'
}

export type GQLSellerProxy = {
  __typename?: 'SellerProxy';
  companyName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  contactId: EntireFieldWrapper<Scalars['String']['output']>;
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  firstName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  lastName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  mobilePhone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export enum GQLSignatureMechanism {
  Handwritten = 'handwritten',
  HandwrittenWithIdentification = 'handwritten_with_identification',
  Identification = 'identification',
  Pkisignature = 'pkisignature'
}

export type GQLSigner = {
  __typename?: 'Signer';
  email: EntireFieldWrapper<Scalars['String']['output']>;
  externalSignerId: EntireFieldWrapper<Scalars['String']['output']>;
  firstName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  id: EntireFieldWrapper<Scalars['NodeID']['output']>;
  lastName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  listingAgreementId: EntireFieldWrapper<Scalars['String']['output']>;
  phone?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  signedAt?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  url?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLSignerSummary = {
  __typename?: 'SignerSummary';
  contactId: EntireFieldWrapper<Scalars['String']['output']>;
  lastHeartbeat?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
  totalTimeSpent: EntireFieldWrapper<Scalars['Int']['output']>;
  visitsCount: EntireFieldWrapper<Scalars['Int']['output']>;
};

export type GQLSignicatDocument = {
  __typename?: 'SignicatDocument';
  accountId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  attachments?: EntireFieldWrapper<Maybe<Array<Maybe<Scalars['String']['output']>>>>;
  created?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  currentSignatures?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  deadline?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  description?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  documentId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  documentSignatures?: EntireFieldWrapper<Maybe<Array<Maybe<GQLSignicatExtendedDocumentSignature>>>>;
  externalId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  lastUpdated?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  requiredSignatures?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  signedDate?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  signers?: EntireFieldWrapper<Maybe<Array<Maybe<Scalars['String']['output']>>>>;
  status?: EntireFieldWrapper<Maybe<GQLSignicatDocumentStatus>>;
  tags?: EntireFieldWrapper<Maybe<Array<Maybe<Scalars['String']['output']>>>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLSignicatDocumentStatus = {
  __typename?: 'SignicatDocumentStatus';
  documentStatus?: EntireFieldWrapper<Maybe<GQLSignicatDocumentStatusEnum>>;
};

export enum GQLSignicatDocumentStatusEnum {
  Canceled = 'canceled',
  Expired = 'expired',
  Partialsigned = 'partialsigned',
  Signed = 'signed',
  Unsigned = 'unsigned',
  WaitingForAttachments = 'waiting_for_attachments'
}

export type GQLSignicatExtendedDocumentSignature = {
  __typename?: 'SignicatExtendedDocumentSignature';
  clientIp?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  dateOfBirth?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  externalSignerId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  firstName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  fullName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  lastName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  mechanism?: EntireFieldWrapper<Maybe<GQLSignatureMechanism>>;
  middleName?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  personalInfoOrigin?: EntireFieldWrapper<Maybe<GQLPersonalInfoOrigin>>;
  signatureMethod?: EntireFieldWrapper<Maybe<GQLSignicatSignatureMethod>>;
  signatureMethodUniqueId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  signedTime?: EntireFieldWrapper<Maybe<Scalars['DateTime']['output']>>;
  signerId?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  socialSecurityNumber?: EntireFieldWrapper<Maybe<GQLSignicatSocialSecurityNumber>>;
};

export enum GQLSignicatSignatureMethod {
  DkNemid = 'dk_nemid',
  FiEid = 'fi_eid',
  Mitid = 'mitid',
  NoBankidMobile = 'no_bankid_mobile',
  NoBankidNetcentric = 'no_bankid_netcentric',
  NoBankidOidc = 'no_bankid_oidc',
  NoBuypass = 'no_buypass',
  SeBankid = 'se_bankid',
  SmsOtp = 'sms_otp',
  Unknown = 'unknown'
}

export type GQLSignicatSocialSecurityNumber = {
  __typename?: 'SignicatSocialSecurityNumber';
  countryCode?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  value?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLSimpleContact = {
  __typename?: 'SimpleContact';
  email?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
};

export enum GQLSortDirection {
  Asc = 'Asc',
  Desc = 'Desc'
}

export enum GQLSortEstateBy {
  ChangedDate = 'changedDate'
}

export enum GQLSortOrder {
  Ascending = 'Ascending',
  Descending = 'Descending'
}

export enum GQLSource {
  Next = 'Next',
  Nordvik = 'Nordvik'
}

export type GQLStorebrandDuplicateCheckInput = {
  estateId: Scalars['String']['input'];
};

export type GQLStorebrandDuplicateCheckResult = {
  __typename?: 'StorebrandDuplicateCheckResult';
  hasDuplicates: EntireFieldWrapper<Scalars['Boolean']['output']>;
};

export type GQLSumArea = {
  __typename?: 'SumArea';
  bra: EntireFieldWrapper<Scalars['Float']['output']>;
  braB: EntireFieldWrapper<Scalars['Float']['output']>;
  braE: EntireFieldWrapper<Scalars['Float']['output']>;
  braI: EntireFieldWrapper<Scalars['Float']['output']>;
  braS: EntireFieldWrapper<Scalars['Float']['output']>;
  bta: EntireFieldWrapper<Scalars['Float']['output']>;
  pRom: EntireFieldWrapper<Scalars['Float']['output']>;
  sRom: EntireFieldWrapper<Scalars['Float']['output']>;
  tba: EntireFieldWrapper<Scalars['Float']['output']>;
};

export type GQLTargetRole = {
  __typename?: 'TargetRole';
  id?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  roleTypeId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  slug?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLToplistEntry = {
  __typename?: 'ToplistEntry';
  avatarUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  count?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  department?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  departmentId?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  id?: EntireFieldWrapper<Maybe<Scalars['NodeID']['output']>>;
  imageUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
  reviews?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  value: EntireFieldWrapper<Scalars['Float']['output']>;
};

export type GQLToplistEntryCurrent = {
  __typename?: 'ToplistEntryCurrent';
  imageUrl?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name: EntireFieldWrapper<Scalars['String']['output']>;
  position?: EntireFieldWrapper<Maybe<Scalars['Int']['output']>>;
  value: EntireFieldWrapper<Scalars['Float']['output']>;
};

export type GQLToplistResponse = {
  __typename?: 'ToplistResponse';
  items: EntireFieldWrapper<Array<GQLToplistEntry>>;
  totalCount: EntireFieldWrapper<Scalars['Int']['output']>;
};

export enum GQLToplistRole {
  PowerOfAttorney = 'powerOfAttorney',
  Realtor = 'realtor'
}

export enum GQLToplistSection {
  Commission = 'commission',
  Inspection = 'inspection',
  Kti = 'kti',
  Marketing = 'marketing',
  Partner = 'partner',
  Signed = 'signed',
  Sold = 'sold',
  Valuation = 'valuation'
}

export type GQLUpdateAward = {
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  origin?: InputMaybe<Scalars['String']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
};

export type GQLUpdateUsp = {
  description?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type GQLUserAgent = {
  __typename?: 'UserAgent';
  browser?: EntireFieldWrapper<Maybe<GQLUserAgentBrowser>>;
  cpu?: EntireFieldWrapper<Maybe<Scalars['JSON']['output']>>;
  device?: EntireFieldWrapper<Maybe<GQLUserAgentDevice>>;
  engine?: EntireFieldWrapper<Maybe<GQLUserAgentEngine>>;
  isBot?: EntireFieldWrapper<Maybe<Scalars['Boolean']['output']>>;
  os?: EntireFieldWrapper<Maybe<GQLUserAgentOs>>;
  ua?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLUserAgentBrowser = {
  __typename?: 'UserAgentBrowser';
  major?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  version?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLUserAgentDevice = {
  __typename?: 'UserAgentDevice';
  model?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  vendor?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLUserAgentEngine = {
  __typename?: 'UserAgentEngine';
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  version?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLUserAgentOs = {
  __typename?: 'UserAgentOS';
  name?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  version?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLUserNotificationsResponse = {
  __typename?: 'UserNotificationsResponse';
  newsCount: EntireFieldWrapper<Scalars['Int']['output']>;
  totalCount: EntireFieldWrapper<Scalars['Int']['output']>;
};

export type GQLUsp = {
  __typename?: 'Usp';
  description?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
  title?: EntireFieldWrapper<Maybe<Scalars['String']['output']>>;
};

export type GQLFindEstatesFilters = {
  baseType?: InputMaybe<Array<Scalars['String']['input']>>;
  city?: InputMaybe<Scalars['String']['input']>;
  estateType?: InputMaybe<Array<Scalars['String']['input']>>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  noBedRooms?: InputMaybe<Scalars['String']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  price?: InputMaybe<GQLMinMax>;
  radius?: InputMaybe<Scalars['Float']['input']>;
  size?: InputMaybe<GQLMinMax>;
  soldDateAfter?: InputMaybe<Scalars['DateTime']['input']>;
  sortBy?: InputMaybe<Scalars['String']['input']>;
  statuses?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export type GQLUpdateBrokerPayload = {
  awards?: InputMaybe<Array<GQLUpdateAward>>;
  instagram?: InputMaybe<Scalars['String']['input']>;
  usp?: InputMaybe<Array<GQLUpdateUsp>>;
};



export type ResolverTypeWrapper<T> = Promise<T> | T;


export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = Record<PropertyKey, never>, TContext = Record<PropertyKey, never>, TArgs = Record<PropertyKey, never>> = ResolverFn<TResult, TParent, TContext, TArgs> | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = Record<PropertyKey, never>, TContext = Record<PropertyKey, never>, TArgs = Record<PropertyKey, never>> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = Record<PropertyKey, never>, TContext = Record<PropertyKey, never>> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = Record<PropertyKey, never>, TContext = Record<PropertyKey, never>> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = Record<PropertyKey, never>, TParent = Record<PropertyKey, never>, TContext = Record<PropertyKey, never>, TArgs = Record<PropertyKey, never>> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;





/** Mapping between all available schema types and the resolvers types */
export type GQLResolversTypes = {
  AccessToken: ResolverTypeWrapper<GQLAccessToken>;
  Accordion: ResolverTypeWrapper<GQLAccordion>;
  ActivitySummary: ResolverTypeWrapper<GQLActivitySummary>;
  AreaSize: ResolverTypeWrapper<GQLAreaSize>;
  AssignmentDocumentState: GQLAssignmentDocumentState;
  AssignmentDocumentStatusItem: ResolverTypeWrapper<GQLAssignmentDocumentStatusItem>;
  AssignmentDocumentStatuses: ResolverTypeWrapper<GQLAssignmentDocumentStatuses>;
  AssignmentDocumentType: GQLAssignmentDocumentType;
  AuditExtraData: ResolverTypeWrapper<GQLAuditExtraData>;
  AuditRecipient: ResolverTypeWrapper<GQLAuditRecipient>;
  AuditTemplate: ResolverTypeWrapper<GQLAuditTemplate>;
  AverageCommissionEntry: ResolverTypeWrapper<GQLAverageCommissionEntry>;
  AverageCommissionResponse: ResolverTypeWrapper<GQLAverageCommissionResponse>;
  Award: ResolverTypeWrapper<GQLAward>;
  Boolean: ResolverTypeWrapper<Scalars['Boolean']['output']>;
  Broker: ResolverTypeWrapper<GQLBroker>;
  BrokerAddress: ResolverTypeWrapper<GQLBrokerAddress>;
  BrokerEstate: ResolverTypeWrapper<GQLBrokerEstate>;
  BrokerEstateCampaign: ResolverTypeWrapper<GQLBrokerEstateCampaign>;
  BrokerEstateCountResponse: ResolverTypeWrapper<GQLBrokerEstateCountResponse>;
  BrokerEstateForm: ResolverTypeWrapper<GQLBrokerEstateForm>;
  BrokerEstateFormStatus: ResolverTypeWrapper<GQLBrokerEstateFormStatus>;
  BrokerEstateImage: ResolverTypeWrapper<GQLBrokerEstateImage>;
  BrokerEstateLink: ResolverTypeWrapper<GQLBrokerEstateLink>;
  BrokerEstatePagination: ResolverTypeWrapper<GQLBrokerEstatePagination>;
  BrokerEstatePriceHistory: ResolverTypeWrapper<GQLBrokerEstatePriceHistory>;
  BrokerEstateSeller: ResolverTypeWrapper<GQLBrokerEstateSeller>;
  BrokerEstateShowing: ResolverTypeWrapper<GQLBrokerEstateShowing>;
  BrokerEstatesResponse: ResolverTypeWrapper<GQLBrokerEstatesResponse>;
  BrokerIdWithRole: ResolverTypeWrapper<GQLBrokerIdWithRole>;
  BrokerIdWithRoleDetails: ResolverTypeWrapper<GQLBrokerIdWithRoleDetails>;
  BrokerImage: ResolverTypeWrapper<GQLBrokerImage>;
  BrokerKtiResponse: ResolverTypeWrapper<GQLBrokerKtiResponse>;
  BrokerPartner: ResolverTypeWrapper<GQLBrokerPartner>;
  BrokerPartnerCreateInput: GQLBrokerPartnerCreateInput;
  BrokerPartnerUpdateInput: GQLBrokerPartnerUpdateInput;
  BrokerProfileLinks: ResolverTypeWrapper<GQLBrokerProfileLinks>;
  BrokerProfileLinksPayload: GQLBrokerProfileLinksPayload;
  BrokerRating: ResolverTypeWrapper<GQLBrokerRating>;
  BrokerRatingSortBy: GQLBrokerRatingSortBy;
  BrokerRatingsList: ResolverTypeWrapper<GQLBrokerRatingsList>;
  BrokerReview: ResolverTypeWrapper<GQLBrokerReview>;
  BrokerReviewsInput: GQLBrokerReviewsInput;
  BrokerRole: ResolverTypeWrapper<GQLBrokerRole>;
  BrokersResponse: ResolverTypeWrapper<GQLBrokersResponse>;
  BudgetPost: ResolverTypeWrapper<GQLBudgetPost>;
  BudgetPostDescription: ResolverTypeWrapper<GQLBudgetPostDescription>;
  BusinessManagerContact: ResolverTypeWrapper<GQLBusinessManagerContact>;
  CmsArticle: ResolverTypeWrapper<GQLCmsArticle>;
  CmsArticleAuthor: ResolverTypeWrapper<GQLCmsArticleAuthor>;
  CmsArticleCategory: ResolverTypeWrapper<GQLCmsArticleCategory>;
  CmsArticleDepartment: ResolverTypeWrapper<GQLCmsArticleDepartment>;
  CmsArticleImage: ResolverTypeWrapper<GQLCmsArticleImage>;
  CmsArticleResponse: ResolverTypeWrapper<GQLCmsArticleResponse>;
  CmsArticleType: GQLCmsArticleType;
  CmsIncidentsResponse: ResolverTypeWrapper<GQLCmsIncidentsResponse>;
  CmsMeta: ResolverTypeWrapper<GQLCmsMeta>;
  Contact: ResolverTypeWrapper<GQLContact>;
  CraftCmsIncident: ResolverTypeWrapper<GQLCraftCmsIncident>;
  CraftCmsIncidentLevel: GQLCraftCmsIncidentLevel;
  CraftCmsIncidentUpdate: ResolverTypeWrapper<GQLCraftCmsIncidentUpdate>;
  CraftCmsJob: ResolverTypeWrapper<GQLCraftCmsJob>;
  CraftCmsJobListingItem: ResolverTypeWrapper<GQLCraftCmsJobListingItem>;
  CraftCmsJobsListingResponse: ResolverTypeWrapper<GQLCraftCmsJobsListingResponse>;
  CreateAward: GQLCreateAward;
  CreatePageVisitInput: GQLCreatePageVisitInput;
  CurrentBrokerRatingsTotal: ResolverTypeWrapper<GQLCurrentBrokerRatingsTotal>;
  DashboardActivity: ResolverTypeWrapper<GQLDashboardActivity>;
  DashboardKtiCurrent: ResolverTypeWrapper<GQLDashboardKtiCurrent>;
  DashboardKtiOthers: ResolverTypeWrapper<GQLDashboardKtiOthers>;
  DashboardKtiResponse: ResolverTypeWrapper<GQLDashboardKtiResponse>;
  DashboardLeadsResponse: ResolverTypeWrapper<GQLDashboardLeadsResponse>;
  DashboardStatusEntry: ResolverTypeWrapper<GQLDashboardStatusEntry>;
  DashboardToplistResponse: ResolverTypeWrapper<GQLDashboardToplistResponse>;
  DashboardType: GQLDashboardType;
  Date: ResolverTypeWrapper<Scalars['Date']['output']>;
  DateTime: ResolverTypeWrapper<Scalars['DateTime']['output']>;
  Department: ResolverTypeWrapper<GQLDepartment>;
  DepartmentEmployee: ResolverTypeWrapper<GQLDepartmentEmployee>;
  DepartmentRating: ResolverTypeWrapper<GQLDepartmentRating>;
  DepartmentsResponse: ResolverTypeWrapper<GQLDepartmentsResponse>;
  EmailAudit: ResolverTypeWrapper<GQLEmailAudit>;
  EmailAuditRecipient: ResolverTypeWrapper<GQLEmailAuditRecipient>;
  EmailInteraction: ResolverTypeWrapper<GQLEmailInteraction>;
  EstateActivity: ResolverTypeWrapper<GQLEstateActivity>;
  EstateAd: ResolverTypeWrapper<GQLEstateAd>;
  EstateAdSource: GQLEstateAdSource;
  EstateBroker: ResolverTypeWrapper<GQLEstateBroker>;
  EstateChecklistItem: ResolverTypeWrapper<GQLEstateChecklistItem>;
  EstatePrice: ResolverTypeWrapper<GQLEstatePrice>;
  EstatePriceModel: ResolverTypeWrapper<GQLEstatePriceModel>;
  EstateProps: GQLEstateProps;
  EstateSizeModel: ResolverTypeWrapper<GQLEstateSizeModel>;
  EstateStats: ResolverTypeWrapper<GQLEstateStats>;
  EstateTabFilter: GQLEstateTabFilter;
  FindEstatesForBrokerFilters: GQLFindEstatesForBrokerFilters;
  FinnData: ResolverTypeWrapper<GQLFinnData>;
  Float: ResolverTypeWrapper<Scalars['Float']['output']>;
  HallOfFameEntry: ResolverTypeWrapper<GQLHallOfFameEntry>;
  HallOfFameEntryAward: ResolverTypeWrapper<GQLHallOfFameEntryAward>;
  ID: ResolverTypeWrapper<Scalars['ID']['output']>;
  ImportantTask: ResolverTypeWrapper<GQLImportantTask>;
  ImportantTaskType: GQLImportantTaskType;
  Inspection: ResolverTypeWrapper<GQLInspection>;
  InspectionActivity: ResolverTypeWrapper<GQLInspectionActivity>;
  InspectionEntry: ResolverTypeWrapper<GQLInspectionEntry>;
  InspectionEvent: ResolverTypeWrapper<GQLInspectionEvent>;
  InspectionEventInput: GQLInspectionEventInput;
  InspectionFolder: ResolverTypeWrapper<GQLInspectionFolder>;
  InspectionFolderAudit: ResolverTypeWrapper<GQLInspectionFolderAudit>;
  InspectionLead: ResolverTypeWrapper<GQLInspectionLead>;
  InspectionLeadType: GQLInspectionLeadType;
  InspectionMetadata: ResolverTypeWrapper<GQLInspectionMetadata>;
  Int: ResolverTypeWrapper<Scalars['Int']['output']>;
  InteractionFilter: GQLInteractionFilter;
  JSON: ResolverTypeWrapper<Scalars['JSON']['output']>;
  KeyFigureEntry: ResolverTypeWrapper<GQLKeyFigureEntry>;
  KtiResponse: ResolverTypeWrapper<GQLKtiResponse>;
  LandIdentificationMatrix: ResolverTypeWrapper<GQLLandIdentificationMatrix>;
  LeadsEntry: ResolverTypeWrapper<GQLLeadsEntry>;
  ListingAgreement: ResolverTypeWrapper<GQLListingAgreement>;
  ListingAgreementInteraction: ResolverTypeWrapper<GQLListingAgreementInteraction>;
  ListingAgreementInteractionSeller: ResolverTypeWrapper<GQLListingAgreementInteractionSeller>;
  ListingAgreementInteractionSummary: ResolverTypeWrapper<GQLListingAgreementInteractionSummary>;
  ListingAgreementInteractionType: GQLListingAgreementInteractionType;
  ListingAgreementInteractions: ResolverTypeWrapper<GQLListingAgreementInteractions>;
  ListingAgreementStatus: GQLListingAgreementStatus;
  Location: ResolverTypeWrapper<GQLLocation>;
  MarketingChannel: ResolverTypeWrapper<GQLMarketingChannel>;
  MarketingPackage: ResolverTypeWrapper<GQLMarketingPackage>;
  MarketingStart: ResolverTypeWrapper<GQLMarketingStart>;
  MinMax: GQLMinMax;
  Module: ResolverTypeWrapper<GQLModule>;
  Mutation: ResolverTypeWrapper<Record<PropertyKey, never>>;
  NextDocument: ResolverTypeWrapper<GQLNextDocument>;
  NodeID: ResolverTypeWrapper<Scalars['NodeID']['output']>;
  NordvikAward: ResolverTypeWrapper<GQLNordvikAward>;
  PageVisit: ResolverTypeWrapper<GQLPageVisit>;
  Pagination: ResolverTypeWrapper<GQLPagination>;
  PartOwnership: ResolverTypeWrapper<GQLPartOwnership>;
  PersonalInfoOrigin: GQLPersonalInfoOrigin;
  PostDate: ResolverTypeWrapper<GQLPostDate>;
  PriceIndex: ResolverTypeWrapper<GQLPriceIndex>;
  PriceStatistics: ResolverTypeWrapper<GQLPriceStatistics>;
  Query: ResolverTypeWrapper<Record<PropertyKey, never>>;
  Rating: ResolverTypeWrapper<GQLRating>;
  RatingResponse: ResolverTypeWrapper<GQLRatingResponse>;
  ReadArticle: ResolverTypeWrapper<GQLReadArticle>;
  RelevantForEstateWithProps: ResolverTypeWrapper<GQLRelevantForEstateWithProps>;
  RevenueEntry: ResolverTypeWrapper<GQLRevenueEntry>;
  RevenueMonth: ResolverTypeWrapper<GQLRevenueMonth>;
  RevenueResponse: ResolverTypeWrapper<GQLRevenueResponse>;
  Review: ResolverTypeWrapper<GQLReview>;
  Section: GQLSection;
  SellerProxy: ResolverTypeWrapper<GQLSellerProxy>;
  SignatureMechanism: GQLSignatureMechanism;
  Signer: ResolverTypeWrapper<GQLSigner>;
  SignerSummary: ResolverTypeWrapper<GQLSignerSummary>;
  SignicatDocument: ResolverTypeWrapper<GQLSignicatDocument>;
  SignicatDocumentStatus: ResolverTypeWrapper<GQLSignicatDocumentStatus>;
  SignicatDocumentStatusEnum: GQLSignicatDocumentStatusEnum;
  SignicatExtendedDocumentSignature: ResolverTypeWrapper<GQLSignicatExtendedDocumentSignature>;
  SignicatSignatureMethod: GQLSignicatSignatureMethod;
  SignicatSocialSecurityNumber: ResolverTypeWrapper<GQLSignicatSocialSecurityNumber>;
  SimpleContact: ResolverTypeWrapper<GQLSimpleContact>;
  SortDirection: GQLSortDirection;
  SortEstateBy: GQLSortEstateBy;
  SortOrder: GQLSortOrder;
  Source: GQLSource;
  StorebrandDuplicateCheckInput: GQLStorebrandDuplicateCheckInput;
  StorebrandDuplicateCheckResult: ResolverTypeWrapper<GQLStorebrandDuplicateCheckResult>;
  String: ResolverTypeWrapper<Scalars['String']['output']>;
  SumArea: ResolverTypeWrapper<GQLSumArea>;
  TargetRole: ResolverTypeWrapper<GQLTargetRole>;
  ToplistEntry: ResolverTypeWrapper<GQLToplistEntry>;
  ToplistEntryCurrent: ResolverTypeWrapper<GQLToplistEntryCurrent>;
  ToplistResponse: ResolverTypeWrapper<GQLToplistResponse>;
  ToplistRole: GQLToplistRole;
  ToplistSection: GQLToplistSection;
  UpdateAward: GQLUpdateAward;
  UpdateUsp: GQLUpdateUsp;
  UserAgent: ResolverTypeWrapper<GQLUserAgent>;
  UserAgentBrowser: ResolverTypeWrapper<GQLUserAgentBrowser>;
  UserAgentDevice: ResolverTypeWrapper<GQLUserAgentDevice>;
  UserAgentEngine: ResolverTypeWrapper<GQLUserAgentEngine>;
  UserAgentOS: ResolverTypeWrapper<GQLUserAgentOs>;
  UserNotificationsResponse: ResolverTypeWrapper<GQLUserNotificationsResponse>;
  Usp: ResolverTypeWrapper<GQLUsp>;
  findEstatesFilters: GQLFindEstatesFilters;
  updateBrokerPayload: GQLUpdateBrokerPayload;
};

/** Mapping between all available schema types and the resolvers parents */
export type GQLResolversParentTypes = {
  AccessToken: GQLAccessToken;
  Accordion: GQLAccordion;
  ActivitySummary: GQLActivitySummary;
  AreaSize: GQLAreaSize;
  AssignmentDocumentStatusItem: GQLAssignmentDocumentStatusItem;
  AssignmentDocumentStatuses: GQLAssignmentDocumentStatuses;
  AuditExtraData: GQLAuditExtraData;
  AuditRecipient: GQLAuditRecipient;
  AuditTemplate: GQLAuditTemplate;
  AverageCommissionEntry: GQLAverageCommissionEntry;
  AverageCommissionResponse: GQLAverageCommissionResponse;
  Award: GQLAward;
  Boolean: Scalars['Boolean']['output'];
  Broker: GQLBroker;
  BrokerAddress: GQLBrokerAddress;
  BrokerEstate: GQLBrokerEstate;
  BrokerEstateCampaign: GQLBrokerEstateCampaign;
  BrokerEstateCountResponse: GQLBrokerEstateCountResponse;
  BrokerEstateForm: GQLBrokerEstateForm;
  BrokerEstateFormStatus: GQLBrokerEstateFormStatus;
  BrokerEstateImage: GQLBrokerEstateImage;
  BrokerEstateLink: GQLBrokerEstateLink;
  BrokerEstatePagination: GQLBrokerEstatePagination;
  BrokerEstatePriceHistory: GQLBrokerEstatePriceHistory;
  BrokerEstateSeller: GQLBrokerEstateSeller;
  BrokerEstateShowing: GQLBrokerEstateShowing;
  BrokerEstatesResponse: GQLBrokerEstatesResponse;
  BrokerIdWithRole: GQLBrokerIdWithRole;
  BrokerIdWithRoleDetails: GQLBrokerIdWithRoleDetails;
  BrokerImage: GQLBrokerImage;
  BrokerKtiResponse: GQLBrokerKtiResponse;
  BrokerPartner: GQLBrokerPartner;
  BrokerPartnerCreateInput: GQLBrokerPartnerCreateInput;
  BrokerPartnerUpdateInput: GQLBrokerPartnerUpdateInput;
  BrokerProfileLinks: GQLBrokerProfileLinks;
  BrokerProfileLinksPayload: GQLBrokerProfileLinksPayload;
  BrokerRating: GQLBrokerRating;
  BrokerRatingsList: GQLBrokerRatingsList;
  BrokerReview: GQLBrokerReview;
  BrokerReviewsInput: GQLBrokerReviewsInput;
  BrokerRole: GQLBrokerRole;
  BrokersResponse: GQLBrokersResponse;
  BudgetPost: GQLBudgetPost;
  BudgetPostDescription: GQLBudgetPostDescription;
  BusinessManagerContact: GQLBusinessManagerContact;
  CmsArticle: GQLCmsArticle;
  CmsArticleAuthor: GQLCmsArticleAuthor;
  CmsArticleCategory: GQLCmsArticleCategory;
  CmsArticleDepartment: GQLCmsArticleDepartment;
  CmsArticleImage: GQLCmsArticleImage;
  CmsArticleResponse: GQLCmsArticleResponse;
  CmsIncidentsResponse: GQLCmsIncidentsResponse;
  CmsMeta: GQLCmsMeta;
  Contact: GQLContact;
  CraftCmsIncident: GQLCraftCmsIncident;
  CraftCmsIncidentUpdate: GQLCraftCmsIncidentUpdate;
  CraftCmsJob: GQLCraftCmsJob;
  CraftCmsJobListingItem: GQLCraftCmsJobListingItem;
  CraftCmsJobsListingResponse: GQLCraftCmsJobsListingResponse;
  CreateAward: GQLCreateAward;
  CreatePageVisitInput: GQLCreatePageVisitInput;
  CurrentBrokerRatingsTotal: GQLCurrentBrokerRatingsTotal;
  DashboardActivity: GQLDashboardActivity;
  DashboardKtiCurrent: GQLDashboardKtiCurrent;
  DashboardKtiOthers: GQLDashboardKtiOthers;
  DashboardKtiResponse: GQLDashboardKtiResponse;
  DashboardLeadsResponse: GQLDashboardLeadsResponse;
  DashboardStatusEntry: GQLDashboardStatusEntry;
  DashboardToplistResponse: GQLDashboardToplistResponse;
  Date: Scalars['Date']['output'];
  DateTime: Scalars['DateTime']['output'];
  Department: GQLDepartment;
  DepartmentEmployee: GQLDepartmentEmployee;
  DepartmentRating: GQLDepartmentRating;
  DepartmentsResponse: GQLDepartmentsResponse;
  EmailAudit: GQLEmailAudit;
  EmailAuditRecipient: GQLEmailAuditRecipient;
  EmailInteraction: GQLEmailInteraction;
  EstateActivity: GQLEstateActivity;
  EstateAd: GQLEstateAd;
  EstateBroker: GQLEstateBroker;
  EstateChecklistItem: GQLEstateChecklistItem;
  EstatePrice: GQLEstatePrice;
  EstatePriceModel: GQLEstatePriceModel;
  EstateProps: GQLEstateProps;
  EstateSizeModel: GQLEstateSizeModel;
  EstateStats: GQLEstateStats;
  FindEstatesForBrokerFilters: GQLFindEstatesForBrokerFilters;
  FinnData: GQLFinnData;
  Float: Scalars['Float']['output'];
  HallOfFameEntry: GQLHallOfFameEntry;
  HallOfFameEntryAward: GQLHallOfFameEntryAward;
  ID: Scalars['ID']['output'];
  ImportantTask: GQLImportantTask;
  Inspection: GQLInspection;
  InspectionActivity: GQLInspectionActivity;
  InspectionEntry: GQLInspectionEntry;
  InspectionEvent: GQLInspectionEvent;
  InspectionEventInput: GQLInspectionEventInput;
  InspectionFolder: GQLInspectionFolder;
  InspectionFolderAudit: GQLInspectionFolderAudit;
  InspectionLead: GQLInspectionLead;
  InspectionMetadata: GQLInspectionMetadata;
  Int: Scalars['Int']['output'];
  InteractionFilter: GQLInteractionFilter;
  JSON: Scalars['JSON']['output'];
  KeyFigureEntry: GQLKeyFigureEntry;
  KtiResponse: GQLKtiResponse;
  LandIdentificationMatrix: GQLLandIdentificationMatrix;
  LeadsEntry: GQLLeadsEntry;
  ListingAgreement: GQLListingAgreement;
  ListingAgreementInteraction: GQLListingAgreementInteraction;
  ListingAgreementInteractionSeller: GQLListingAgreementInteractionSeller;
  ListingAgreementInteractionSummary: GQLListingAgreementInteractionSummary;
  ListingAgreementInteractions: GQLListingAgreementInteractions;
  Location: GQLLocation;
  MarketingChannel: GQLMarketingChannel;
  MarketingPackage: GQLMarketingPackage;
  MarketingStart: GQLMarketingStart;
  MinMax: GQLMinMax;
  Module: GQLModule;
  Mutation: Record<PropertyKey, never>;
  NextDocument: GQLNextDocument;
  NodeID: Scalars['NodeID']['output'];
  NordvikAward: GQLNordvikAward;
  PageVisit: GQLPageVisit;
  Pagination: GQLPagination;
  PartOwnership: GQLPartOwnership;
  PostDate: GQLPostDate;
  PriceIndex: GQLPriceIndex;
  PriceStatistics: GQLPriceStatistics;
  Query: Record<PropertyKey, never>;
  Rating: GQLRating;
  RatingResponse: GQLRatingResponse;
  ReadArticle: GQLReadArticle;
  RelevantForEstateWithProps: GQLRelevantForEstateWithProps;
  RevenueEntry: GQLRevenueEntry;
  RevenueMonth: GQLRevenueMonth;
  RevenueResponse: GQLRevenueResponse;
  Review: GQLReview;
  SellerProxy: GQLSellerProxy;
  Signer: GQLSigner;
  SignerSummary: GQLSignerSummary;
  SignicatDocument: GQLSignicatDocument;
  SignicatDocumentStatus: GQLSignicatDocumentStatus;
  SignicatExtendedDocumentSignature: GQLSignicatExtendedDocumentSignature;
  SignicatSocialSecurityNumber: GQLSignicatSocialSecurityNumber;
  SimpleContact: GQLSimpleContact;
  StorebrandDuplicateCheckInput: GQLStorebrandDuplicateCheckInput;
  StorebrandDuplicateCheckResult: GQLStorebrandDuplicateCheckResult;
  String: Scalars['String']['output'];
  SumArea: GQLSumArea;
  TargetRole: GQLTargetRole;
  ToplistEntry: GQLToplistEntry;
  ToplistEntryCurrent: GQLToplistEntryCurrent;
  ToplistResponse: GQLToplistResponse;
  UpdateAward: GQLUpdateAward;
  UpdateUsp: GQLUpdateUsp;
  UserAgent: GQLUserAgent;
  UserAgentBrowser: GQLUserAgentBrowser;
  UserAgentDevice: GQLUserAgentDevice;
  UserAgentEngine: GQLUserAgentEngine;
  UserAgentOS: GQLUserAgentOs;
  UserNotificationsResponse: GQLUserNotificationsResponse;
  Usp: GQLUsp;
  findEstatesFilters: GQLFindEstatesFilters;
  updateBrokerPayload: GQLUpdateBrokerPayload;
};

export type GQLAccessTokenResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AccessToken'] = GQLResolversParentTypes['AccessToken']> = {
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['NodeID'], ParentType, ContextType>;
  token?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLAccordionResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Accordion'] = GQLResolversParentTypes['Accordion']> = {
  header?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  text?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLActivitySummaryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ActivitySummary'] = GQLResolversParentTypes['ActivitySummary']> = {
  recipientsCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  signedCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  signersCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  totalTimeSpent?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  visitorCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  visitsCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLAreaSizeResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AreaSize'] = GQLResolversParentTypes['AreaSize']> = {
  BRAItotal?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
};

export type GQLAssignmentDocumentStatusItemResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AssignmentDocumentStatusItem'] = GQLResolversParentTypes['AssignmentDocumentStatusItem']> = {
  message?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  state?: Resolver<GQLResolversTypes['AssignmentDocumentState'], ParentType, ContextType>;
  type?: Resolver<GQLResolversTypes['AssignmentDocumentType'], ParentType, ContextType>;
  updatedAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
};

export type GQLAssignmentDocumentStatusesResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AssignmentDocumentStatuses'] = GQLResolversParentTypes['AssignmentDocumentStatuses']> = {
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  generatedAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  items?: Resolver<Array<GQLResolversTypes['AssignmentDocumentStatusItem']>, ParentType, ContextType>;
  partialFailure?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
};

export type GQLAuditExtraDataResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AuditExtraData'] = GQLResolversParentTypes['AuditExtraData']> = {
  recipients?: Resolver<Maybe<Array<GQLResolversTypes['AuditRecipient']>>, ParentType, ContextType>;
  template?: Resolver<Maybe<GQLResolversTypes['AuditTemplate']>, ParentType, ContextType>;
};

export type GQLAuditRecipientResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AuditRecipient'] = GQLResolversParentTypes['AuditRecipient']> = {
  contactId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  mobilePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLAuditTemplateResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AuditTemplate'] = GQLResolversParentTypes['AuditTemplate']> = {
  modified?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  templateId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLAverageCommissionEntryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AverageCommissionEntry'] = GQLResolversParentTypes['AverageCommissionEntry']> = {
  commission?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  price?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  salesCount?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  type?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLAverageCommissionResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['AverageCommissionResponse'] = GQLResolversParentTypes['AverageCommissionResponse']> = {
  compare?: Resolver<GQLResolversTypes['AverageCommissionEntry'], ParentType, ContextType>;
  departmentName?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  reference?: Resolver<GQLResolversTypes['AverageCommissionEntry'], ParentType, ContextType>;
};

export type GQLAwardResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Award'] = GQLResolversParentTypes['Award']> = {
  id?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  origin?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  year?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLBrokerResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Broker'] = GQLResolversParentTypes['Broker']> = {
  aboutMe?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  awards?: Resolver<Maybe<Array<GQLResolversTypes['Award']>>, ParentType, ContextType>;
  createdDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  department?: Resolver<Maybe<GQLResolversTypes['Department']>, ParentType, ContextType>;
  email?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  employeeActive?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  employeeId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  employeeRoles?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['BrokerRole']>>>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['NodeID'], ParentType, ContextType>;
  image?: Resolver<Maybe<GQLResolversTypes['BrokerImage']>, ParentType, ContextType>;
  instagram?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  kti?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  links?: Resolver<Maybe<GQLResolversTypes['BrokerProfileLinks']>, ParentType, ContextType>;
  mobilePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  nordvikAwards?: Resolver<Maybe<Array<GQLResolversTypes['NordvikAward']>>, ParentType, ContextType>;
  rating?: Resolver<Maybe<GQLResolversTypes['Rating']>, ParentType, ContextType>;
  ratings?: Resolver<Maybe<GQLResolversTypes['BrokerRatingsList']>, ParentType, ContextType>;
  reviews?: Resolver<Array<GQLResolversTypes['BrokerRating']>, ParentType, ContextType, RequireFields<GQLBrokerReviewsArgs, 'input'>>;
  slug?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  team?: Resolver<Maybe<Array<GQLResolversTypes['BrokerPartner']>>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  usp?: Resolver<Maybe<Array<GQLResolversTypes['Usp']>>, ParentType, ContextType>;
};

export type GQLBrokerAddressResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerAddress'] = GQLResolversParentTypes['BrokerAddress']> = {
  apartmentNumber?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  city?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  municipality?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  streetAddress?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  zipCode?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerEstateResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstate'] = GQLResolversParentTypes['BrokerEstate']> = {
  activities?: Resolver<Array<GQLResolversTypes['EstateActivity']>, ParentType, ContextType>;
  address?: Resolver<Maybe<GQLResolversTypes['BrokerAddress']>, ParentType, ContextType>;
  ads?: Resolver<Array<GQLResolversTypes['EstateAd']>, ParentType, ContextType>;
  areaSize?: Resolver<Maybe<GQLResolversTypes['AreaSize']>, ParentType, ContextType>;
  assignmentNumber?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  assignmentType?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  assignmentTypeGroup?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  assistantBroker?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  broker?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  brokerId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  brokers?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['EstateBroker']>>>, ParentType, ContextType>;
  brokersIdWithRoles?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['BrokerIdWithRole']>>>, ParentType, ContextType>;
  businessManagerContact?: Resolver<Maybe<GQLResolversTypes['BusinessManagerContact']>, ParentType, ContextType>;
  campaigns?: Resolver<Array<GQLResolversTypes['BrokerEstateCampaign']>, ParentType, ContextType>;
  changedDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  checklist?: Resolver<Array<GQLResolversTypes['EstateChecklistItem']>, ParentType, ContextType>;
  commissionAcceptedDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  companyContacts?: Resolver<Array<GQLResolversTypes['Contact']>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  department?: Resolver<Maybe<GQLResolversTypes['Department']>, ParentType, ContextType>;
  departmentId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  documents?: Resolver<Array<GQLResolversTypes['NextDocument']>, ParentType, ContextType>;
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  estatePrice?: Resolver<Maybe<GQLResolversTypes['EstatePrice']>, ParentType, ContextType>;
  estatePriceModel?: Resolver<Maybe<GQLResolversTypes['EstatePriceModel']>, ParentType, ContextType>;
  estateSizeModel?: Resolver<Maybe<GQLResolversTypes['EstateSizeModel']>, ParentType, ContextType>;
  estateType?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  estateTypeExternal?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  estateTypeId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  etakst?: Resolver<Maybe<GQLResolversTypes['NextDocument']>, ParentType, ContextType>;
  expireDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  extraContacts?: Resolver<Array<GQLResolversTypes['Contact']>, ParentType, ContextType, Partial<GQLBrokerEstateExtraContactsArgs>>;
  finn?: Resolver<Maybe<GQLResolversTypes['FinnData']>, ParentType, ContextType>;
  forms?: Resolver<Array<GQLResolversTypes['BrokerEstateForm']>, ParentType, ContextType>;
  hasCompanySeller?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  hasInspection?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  heading?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  hjemUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['NodeID'], ParentType, ContextType>;
  images?: Resolver<Maybe<Array<GQLResolversTypes['BrokerEstateImage']>>, ParentType, ContextType>;
  inspection?: Resolver<Maybe<GQLResolversTypes['Inspection']>, ParentType, ContextType>;
  inspectionDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  inspectionEvents?: Resolver<Maybe<Array<GQLResolversTypes['InspectionEvent']>>, ParentType, ContextType>;
  inspectionFolder?: Resolver<Maybe<GQLResolversTypes['InspectionFolder']>, ParentType, ContextType>;
  isEtakstPublished?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  isValuation?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  isWithdrawn?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  landIdentificationMatrix?: Resolver<Maybe<GQLResolversTypes['LandIdentificationMatrix']>, ParentType, ContextType>;
  latitude?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  linkToNext?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  links?: Resolver<Array<GQLResolversTypes['BrokerEstateLink']>, ParentType, ContextType>;
  listingAgreement?: Resolver<Maybe<GQLResolversTypes['ListingAgreement']>, ParentType, ContextType>;
  location?: Resolver<Maybe<GQLResolversTypes['Location']>, ParentType, ContextType>;
  longitude?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  mainBroker?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  mainImage?: Resolver<Maybe<GQLResolversTypes['BrokerEstateImage']>, ParentType, ContextType>;
  mainSeller?: Resolver<Maybe<GQLResolversTypes['BrokerEstateSeller']>, ParentType, ContextType>;
  marketingStart?: Resolver<Maybe<GQLResolversTypes['MarketingStart']>, ParentType, ContextType>;
  matrikkel?: Resolver<Array<Maybe<GQLResolversTypes['LandIdentificationMatrix']>>, ParentType, ContextType>;
  noOfBedRooms?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  noOfRooms?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  numberOfBedrooms?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  ownAssignmentType?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  ownership?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  ownershipType?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  partOwnership?: Resolver<Maybe<GQLResolversTypes['PartOwnership']>, ParentType, ContextType>;
  placeholderImage?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  priceHistory?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['BrokerEstatePriceHistory']>>>, ParentType, ContextType>;
  projectRelation?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  propertyType?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  riskCheckmark?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  sellPreference?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  sellers?: Resolver<Array<GQLResolversTypes['BrokerEstateSeller']>, ParentType, ContextType>;
  showings?: Resolver<Array<GQLResolversTypes['BrokerEstateShowing']>, ParentType, ContextType>;
  soldDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  stats?: Resolver<Maybe<GQLResolversTypes['EstateStats']>, ParentType, ContextType>;
  status?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  sumArea?: Resolver<Maybe<GQLResolversTypes['SumArea']>, ParentType, ContextType>;
  takeOverDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  type?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  upcomingEvents?: Resolver<Array<GQLResolversTypes['EstateActivity']>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  userID?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerEstateCampaignResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstateCampaign'] = GQLResolversParentTypes['BrokerEstateCampaign']> = {
  dateOrdered?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  externalId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  marketingPackage?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  orderEndDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  orderStartDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  orderStatus?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  packageName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerEstateCountResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstateCountResponse'] = GQLResolversParentTypes['BrokerEstateCountResponse']> = {
  count?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  tab?: Resolver<GQLResolversTypes['EstateTabFilter'], ParentType, ContextType>;
};

export type GQLBrokerEstateFormResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstateForm'] = GQLResolversParentTypes['BrokerEstateForm']> = {
  link?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  relevantForEstateWithProps?: Resolver<Maybe<GQLResolversTypes['RelevantForEstateWithProps']>, ParentType, ContextType>;
  status?: Resolver<Maybe<GQLResolversTypes['BrokerEstateFormStatus']>, ParentType, ContextType>;
  type?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerEstateFormStatusResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstateFormStatus'] = GQLResolversParentTypes['BrokerEstateFormStatus']> = {
  isNotificationSent?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  signingFinished?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
};

export type GQLBrokerEstateImageResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstateImage'] = GQLResolversParentTypes['BrokerEstateImage']> = {
  category?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  description?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  large?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  medium?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  sequence?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  small?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerEstateLinkResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstateLink'] = GQLResolversParentTypes['BrokerEstateLink']> = {
  linkType?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  text?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  url?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerEstatePaginationResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstatePagination'] = GQLResolversParentTypes['BrokerEstatePagination']> = {
  count?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  limit?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  offset?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  total?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLBrokerEstatePriceHistoryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstatePriceHistory'] = GQLResolversParentTypes['BrokerEstatePriceHistory']> = {
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  evPrice?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  landIdentificationMatrix?: Resolver<Maybe<GQLResolversTypes['LandIdentificationMatrix']>, ParentType, ContextType>;
  postgresEstateId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
};

export type GQLBrokerEstateSellerResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstateSeller'] = GQLResolversParentTypes['BrokerEstateSeller']> = {
  address?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  city?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  companyName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  contactType?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  mainContact?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  mobilePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  postCode?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  proxy?: Resolver<Maybe<GQLResolversTypes['SellerProxy']>, ParentType, ContextType>;
  proxyId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  socialSecurityNumber?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerEstateShowingResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstateShowing'] = GQLResolversParentTypes['BrokerEstateShowing']> = {
  end?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  showingId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  start?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
};

export type GQLBrokerEstatesResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerEstatesResponse'] = GQLResolversParentTypes['BrokerEstatesResponse']> = {
  items?: Resolver<Array<GQLResolversTypes['BrokerEstate']>, ParentType, ContextType>;
  pagination?: Resolver<Maybe<GQLResolversTypes['BrokerEstatePagination']>, ParentType, ContextType>;
};

export type GQLBrokerIdWithRoleResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerIdWithRole'] = GQLResolversParentTypes['BrokerIdWithRole']> = {
  brokerRole?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  employee?: Resolver<GQLResolversTypes['BrokerIdWithRoleDetails'], ParentType, ContextType>;
  employeeId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLBrokerIdWithRoleDetailsResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerIdWithRoleDetails'] = GQLResolversParentTypes['BrokerIdWithRoleDetails']> = {
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  image?: Resolver<Maybe<GQLResolversTypes['BrokerImage']>, ParentType, ContextType>;
  mobilePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerImageResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerImage'] = GQLResolversParentTypes['BrokerImage']> = {
  large?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  medium?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  small?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerKtiResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerKtiResponse'] = GQLResolversParentTypes['BrokerKtiResponse']> = {
  broker?: Resolver<Maybe<GQLResolversTypes['KtiResponse']>, ParentType, ContextType>;
  brokerDepartment?: Resolver<Maybe<GQLResolversTypes['KtiResponse']>, ParentType, ContextType>;
  nordvik?: Resolver<Maybe<GQLResolversTypes['KtiResponse']>, ParentType, ContextType>;
};

export type GQLBrokerPartnerResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerPartner'] = GQLResolversParentTypes['BrokerPartner']> = {
  category?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  description?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  hidden?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  images?: Resolver<Array<GQLResolversTypes['String']>, ParentType, ContextType>;
  instagram?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  profilePicture?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  website?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerProfileLinksResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerProfileLinks'] = GQLResolversParentTypes['BrokerProfileLinks']> = {
  adLinks?: Resolver<Array<GQLResolversTypes['String']>, ParentType, ContextType>;
  mediaLinks?: Resolver<Array<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerRatingResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerRating'] = GQLResolversParentTypes['BrokerRating']> = {
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  employeeId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  featured?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  rating?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  ratingId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  review?: Resolver<Maybe<GQLResolversTypes['BrokerReview']>, ParentType, ContextType>;
  userName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerRatingsListResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerRatingsList'] = GQLResolversParentTypes['BrokerRatingsList']> = {
  data?: Resolver<Array<Maybe<GQLResolversTypes['BrokerRating']>>, ParentType, ContextType>;
  pagination?: Resolver<GQLResolversTypes['Pagination'], ParentType, ContextType>;
};

export type GQLBrokerReviewResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerReview'] = GQLResolversParentTypes['BrokerReview']> = {
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  reviewId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  text?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLBrokerRoleResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokerRole'] = GQLResolversParentTypes['BrokerRole']> = {
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  source?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  typeId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLBrokersResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BrokersResponse'] = GQLResolversParentTypes['BrokersResponse']> = {
  items?: Resolver<Array<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  totalCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLBudgetPostResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BudgetPost'] = GQLResolversParentTypes['BudgetPost']> = {
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  deletedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  description?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['NodeID'], ParentType, ContextType>;
  initialPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  listingAgreementId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  price?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
};

export type GQLBudgetPostDescriptionResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BudgetPostDescription'] = GQLResolversParentTypes['BudgetPostDescription']> = {
  description?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  heading?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLBusinessManagerContactResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['BusinessManagerContact'] = GQLResolversParentTypes['BusinessManagerContact']> = {
  address?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  city?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  companyName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLCmsArticleResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CmsArticle'] = GQLResolversParentTypes['CmsArticle']> = {
  author?: Resolver<Maybe<GQLResolversTypes['CmsArticleAuthor']>, ParentType, ContextType>;
  canUserViewArticle?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  categories?: Resolver<Array<GQLResolversTypes['CmsArticleCategory']>, ParentType, ContextType>;
  departments?: Resolver<Maybe<Array<GQLResolversTypes['CmsArticleDepartment']>>, ParentType, ContextType>;
  eventDate?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  excerpt?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  externalUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  image?: Resolver<Maybe<GQLResolversTypes['CmsArticleImage']>, ParentType, ContextType>;
  important?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  modules?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['Module']>>>, ParentType, ContextType>;
  postDate?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  slug?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  targetRoles?: Resolver<Array<GQLResolversTypes['TargetRole']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<Maybe<GQLResolversTypes['CmsArticleType']>, ParentType, ContextType>;
  viewerHasRead?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType, Partial<GQLCmsArticleViewerHasReadArgs>>;
};

export type GQLCmsArticleAuthorResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CmsArticleAuthor'] = GQLResolversParentTypes['CmsArticleAuthor']> = {
  avatarUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLCmsArticleCategoryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CmsArticleCategory'] = GQLResolversParentTypes['CmsArticleCategory']> = {
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  slug?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  title?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLCmsArticleDepartmentResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CmsArticleDepartment'] = GQLResolversParentTypes['CmsArticleDepartment']> = {
  departmentId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLCmsArticleImageResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CmsArticleImage'] = GQLResolversParentTypes['CmsArticleImage']> = {
  large?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  medium?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  small?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLCmsArticleResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CmsArticleResponse'] = GQLResolversParentTypes['CmsArticleResponse']> = {
  items?: Resolver<Array<GQLResolversTypes['CmsArticle']>, ParentType, ContextType>;
  meta?: Resolver<GQLResolversTypes['CmsMeta'], ParentType, ContextType>;
};

export type GQLCmsIncidentsResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CmsIncidentsResponse'] = GQLResolversParentTypes['CmsIncidentsResponse']> = {
  items?: Resolver<Array<GQLResolversTypes['CraftCmsIncident']>, ParentType, ContextType>;
  meta?: Resolver<GQLResolversTypes['CmsMeta'], ParentType, ContextType>;
};

export type GQLCmsMetaResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CmsMeta'] = GQLResolversParentTypes['CmsMeta']> = {
  currentPage?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  total?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  totalPages?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLContactResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Contact'] = GQLResolversParentTypes['Contact']> = {
  address?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  city?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  companyName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  contactType?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  deletedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  departmentId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  mobilePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  organisationNumber?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  postalAddress?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  postalCode?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  privatePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  relationName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  relationType?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  roleName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  source?: Resolver<Maybe<GQLResolversTypes['Source']>, ParentType, ContextType>;
  workPhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLCraftCmsIncidentResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CraftCmsIncident'] = GQLResolversParentTypes['CraftCmsIncident']> = {
  active?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  level?: Resolver<GQLResolversTypes['CraftCmsIncidentLevel'], ParentType, ContextType>;
  postDate?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  resolved?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  resolvedComment?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  resolvedDate?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  slug?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  title?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  updates?: Resolver<Maybe<Array<GQLResolversTypes['CraftCmsIncidentUpdate']>>, ParentType, ContextType>;
};

export type GQLCraftCmsIncidentUpdateResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CraftCmsIncidentUpdate'] = GQLResolversParentTypes['CraftCmsIncidentUpdate']> = {
  text?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  time?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLCraftCmsJobResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CraftCmsJob'] = GQLResolversParentTypes['CraftCmsJob']> = {
  author?: Resolver<Maybe<GQLResolversTypes['CmsArticleAuthor']>, ParentType, ContextType>;
  excerpt?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  image?: Resolver<Maybe<GQLResolversTypes['CmsArticleImage']>, ParentType, ContextType>;
  modules?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['Module']>>>, ParentType, ContextType>;
  postDate?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  slug?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  title?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLCraftCmsJobListingItemResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CraftCmsJobListingItem'] = GQLResolversParentTypes['CraftCmsJobListingItem']> = {
  author?: Resolver<Maybe<GQLResolversTypes['CmsArticleAuthor']>, ParentType, ContextType>;
  excerpt?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  image?: Resolver<Maybe<GQLResolversTypes['CmsArticleImage']>, ParentType, ContextType>;
  postDate?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  slug?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  title?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLCraftCmsJobsListingResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CraftCmsJobsListingResponse'] = GQLResolversParentTypes['CraftCmsJobsListingResponse']> = {
  items?: Resolver<Array<GQLResolversTypes['CraftCmsJobListingItem']>, ParentType, ContextType>;
  meta?: Resolver<GQLResolversTypes['CmsMeta'], ParentType, ContextType>;
};

export type GQLCurrentBrokerRatingsTotalResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['CurrentBrokerRatingsTotal'] = GQLResolversParentTypes['CurrentBrokerRatingsTotal']> = {
  allDates?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  allRatings?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  currentYear?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  fiveStar?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  fourStar?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  last30Days?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  lastYear?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  oneStar?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  threeStar?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  twoStar?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  withReview?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLDashboardActivityResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DashboardActivity'] = GQLResolversParentTypes['DashboardActivity']> = {
  done?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  end?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  estateAddress?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  performedById?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  start?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  type?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  typeName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLDashboardKtiCurrentResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DashboardKtiCurrent'] = GQLResolversParentTypes['DashboardKtiCurrent']> = {
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  ratings?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  value?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
};

export type GQLDashboardKtiOthersResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DashboardKtiOthers'] = GQLResolversParentTypes['DashboardKtiOthers']> = {
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  ratings?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  value?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
};

export type GQLDashboardKtiResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DashboardKtiResponse'] = GQLResolversParentTypes['DashboardKtiResponse']> = {
  current?: Resolver<GQLResolversTypes['DashboardKtiCurrent'], ParentType, ContextType>;
  others?: Resolver<Array<GQLResolversTypes['DashboardKtiOthers']>, ParentType, ContextType>;
};

export type GQLDashboardLeadsResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DashboardLeadsResponse'] = GQLResolversParentTypes['DashboardLeadsResponse']> = {
  budget?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  entries?: Resolver<Array<GQLResolversTypes['LeadsEntry']>, ParentType, ContextType>;
};

export type GQLDashboardStatusEntryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DashboardStatusEntry'] = GQLResolversParentTypes['DashboardStatusEntry']> = {
  count?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  status?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  value?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
};

export type GQLDashboardToplistResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DashboardToplistResponse'] = GQLResolversParentTypes['DashboardToplistResponse']> = {
  current?: Resolver<GQLResolversTypes['ToplistEntryCurrent'], ParentType, ContextType>;
  section?: Resolver<GQLResolversTypes['ToplistSection'], ParentType, ContextType>;
  topEntries?: Resolver<Array<GQLResolversTypes['ToplistEntry']>, ParentType, ContextType>;
};

export interface GQLDateScalarConfig extends GraphQLScalarTypeConfig<GQLResolversTypes['Date'], any> {
  name: 'Date';
}

export interface GQLDateTimeScalarConfig extends GraphQLScalarTypeConfig<GQLResolversTypes['DateTime'], any> {
  name: 'DateTime';
}

export type GQLDepartmentResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Department'] = GQLResolversParentTypes['Department']> = {
  city?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  departmentId?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  departmentNumber?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  displayKtiOnEmployee?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  employees?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['DepartmentEmployee']>>>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['NodeID'], ParentType, ContextType>;
  kti?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  legalName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  marketName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  organisationNumber?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  phone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  postalCode?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  rating?: Resolver<Maybe<GQLResolversTypes['DepartmentRating']>, ParentType, ContextType>;
  slug?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  streetAddress?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLDepartmentEmployeeResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DepartmentEmployee'] = GQLResolversParentTypes['DepartmentEmployee']> = {
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  employeeId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  mobilePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  slug?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLDepartmentRatingResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DepartmentRating'] = GQLResolversParentTypes['DepartmentRating']> = {
  average?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  count?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  reviewsCount?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  weighted?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
};

export type GQLDepartmentsResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['DepartmentsResponse'] = GQLResolversParentTypes['DepartmentsResponse']> = {
  items?: Resolver<Array<GQLResolversTypes['Department']>, ParentType, ContextType>;
  totalCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLEmailAuditResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EmailAudit'] = GQLResolversParentTypes['EmailAudit']> = {
  contextId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contextType?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  createdAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  fromEmail?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  globalMetadata?: Resolver<Maybe<GQLResolversTypes['JSON']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['ID'], ParentType, ContextType>;
  recipients?: Resolver<Array<GQLResolversTypes['EmailAuditRecipient']>, ParentType, ContextType>;
  subject?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  templateName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  updatedAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
};

export type GQLEmailAuditRecipientResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EmailAuditRecipient'] = GQLResolversParentTypes['EmailAuditRecipient']> = {
  bounceType?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  createdAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  emailAudit?: Resolver<Maybe<GQLResolversTypes['EmailAudit']>, ParentType, ContextType>;
  emailAuditId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['ID'], ParentType, ContextType>;
  lastEventAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  messageId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  openCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  openedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  rawLastEvent?: Resolver<Maybe<GQLResolversTypes['JSON']>, ParentType, ContextType>;
  recipientEmail?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  recipientMetadata?: Resolver<Maybe<GQLResolversTypes['JSON']>, ParentType, ContextType>;
  rejectReason?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  sentAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  status?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  updatedAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
};

export type GQLEmailInteractionResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EmailInteraction'] = GQLResolversParentTypes['EmailInteraction']> = {
  bounceType?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  emailAuditId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  eventTimestamp?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  eventType?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  messageId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  openCount?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  recipientEmail?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  rejectReason?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLEstateActivityResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EstateActivity'] = GQLResolversParentTypes['EstateActivity']> = {
  done?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  end?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  performedById?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  start?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  type?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  typeName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLEstateAdResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EstateAd'] = GQLResolversParentTypes['EstateAd']> = {
  link?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  source?: Resolver<Maybe<GQLResolversTypes['EstateAdSource']>, ParentType, ContextType>;
};

export type GQLEstateBrokerResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EstateBroker'] = GQLResolversParentTypes['EstateBroker']> = {
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  employeeId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  employeeRoles?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['BrokerRole']>>>, ParentType, ContextType>;
  image?: Resolver<Maybe<GQLResolversTypes['BrokerImage']>, ParentType, ContextType>;
  mobilePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  role?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  slug?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  workPhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLEstateChecklistItemResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EstateChecklistItem'] = GQLResolversParentTypes['EstateChecklistItem']> = {
  changedBy?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  changedDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  firstTag?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  tags?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['String']>>>, ParentType, ContextType>;
  value?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLEstatePriceResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EstatePrice'] = GQLResolversParentTypes['EstatePrice']> = {
  collectiveDebt?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  priceSuggestion?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  soldPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  totalPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
};

export type GQLEstatePriceModelResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EstatePriceModel'] = GQLResolversParentTypes['EstatePriceModel']> = {
  additionalAgreementOptions?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  collectiveAssets?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  collectiveDebt?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  communityTax?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  communityTaxYear?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  estimatedValue?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  leasingPartyTransportFee?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  loanFare?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  originalAgreementPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  originalExpensesPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  otherExpenses?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  priceSuggestion?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  purchaseCostsAmount?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  salesCostDescription?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  soldPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  totalPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  totalPriceExclusiveCostsAndDebt?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  transportAgreementCosts?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  waterRate?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  waterRateDescription?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  waterRateYear?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  yearlyLeaseFee?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  yearlySocietyTax?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
};

export type GQLEstateSizeModelResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EstateSizeModel'] = GQLResolversParentTypes['EstateSizeModel']> = {
  grossArea?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  primaryRoomArea?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  primaryRoomAreaDescription?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  usableArea?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
};

export type GQLEstateStatsResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['EstateStats'] = GQLResolversParentTypes['EstateStats']> = {
  bidders?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  bids?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  followUp?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  interested?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  privateShowingsCount?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  showingParticipants?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  showingRegistrations?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  showingRegistrationsTotal?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  showings?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  showingsTotal?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLFinnDataResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['FinnData'] = GQLResolversParentTypes['FinnData']> = {
  finnCode?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  finnExpireDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  finnPublishDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
};

export type GQLHallOfFameEntryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['HallOfFameEntry'] = GQLResolversParentTypes['HallOfFameEntry']> = {
  entries?: Resolver<Maybe<Array<GQLResolversTypes['HallOfFameEntryAward']>>, ParentType, ContextType>;
  year?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLHallOfFameEntryAwardResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['HallOfFameEntryAward'] = GQLResolversParentTypes['HallOfFameEntryAward']> = {
  awardId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  employee?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  year?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLImportantTaskResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ImportantTask'] = GQLResolversParentTypes['ImportantTask']> = {
  amlUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  eiendomsverdiUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  estateAddress?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  mainBrokerId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  signUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<GQLResolversTypes['ImportantTaskType'], ParentType, ContextType>;
};

export type GQLInspectionResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Inspection'] = GQLResolversParentTypes['Inspection']> = {
  entries?: Resolver<Array<GQLResolversTypes['InspectionEntry']>, ParentType, ContextType>;
  metadata?: Resolver<Maybe<GQLResolversTypes['InspectionMetadata']>, ParentType, ContextType>;
  success?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
};

export type GQLInspectionActivityResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['InspectionActivity'] = GQLResolversParentTypes['InspectionActivity']> = {
  contacts?: Resolver<Array<GQLResolversTypes['SimpleContact']>, ParentType, ContextType>;
  description?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  employeeId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  source?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  timestamp?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  type?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  userAgent?: Resolver<Maybe<GQLResolversTypes['UserAgent']>, ParentType, ContextType>;
};

export type GQLInspectionEntryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['InspectionEntry'] = GQLResolversParentTypes['InspectionEntry']> = {
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  postDate?: Resolver<Maybe<GQLResolversTypes['PostDate']>, ParentType, ContextType>;
  title?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  url?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLInspectionEventResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['InspectionEvent'] = GQLResolversParentTypes['InspectionEvent']> = {
  description?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  end?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  start?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLInspectionFolderResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['InspectionFolder'] = GQLResolversParentTypes['InspectionFolder']> = {
  audit?: Resolver<Maybe<Array<GQLResolversTypes['InspectionFolderAudit']>>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  createdBy?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  deletedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  excludedEmployees?: Resolver<Array<GQLResolversTypes['String']>, ParentType, ContextType>;
  excludedPartners?: Resolver<Array<GQLResolversTypes['BrokerPartner']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  listingAgreement?: Resolver<Maybe<GQLResolversTypes['ListingAgreement']>, ParentType, ContextType>;
  listingAgreementActive?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  listingAgreementSentAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  notes?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  publishedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  publishedBy?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  recipients?: Resolver<Maybe<Array<GQLResolversTypes['String']>>, ParentType, ContextType>;
  relevantLinks?: Resolver<Array<GQLResolversTypes['String']>, ParentType, ContextType>;
  sentAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  sentBy?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  updatedBy?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
};

export type GQLInspectionFolderAuditResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['InspectionFolderAudit'] = GQLResolversParentTypes['InspectionFolderAudit']> = {
  channels?: Resolver<Array<GQLResolversTypes['String']>, ParentType, ContextType>;
  emailAuditId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  estateId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  extraData?: Resolver<Maybe<GQLResolversTypes['JSON']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  listingAgreementActive?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  modifiedTemplate?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  recipientContactIds?: Resolver<Array<GQLResolversTypes['String']>, ParentType, ContextType>;
  recipients?: Resolver<Maybe<Array<GQLResolversTypes['AuditRecipient']>>, ParentType, ContextType>;
  sentAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  sentBy?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  templateId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLInspectionLeadResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['InspectionLead'] = GQLResolversParentTypes['InspectionLead']> = {
  comment?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  createdAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  createdByContactId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  createdByEmployeeId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  leadType?: Resolver<GQLResolversTypes['InspectionLeadType'], ParentType, ContextType>;
  successful?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  updatedAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
};

export type GQLInspectionMetadataResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['InspectionMetadata'] = GQLResolversParentTypes['InspectionMetadata']> = {
  count?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export interface GQLJsonScalarConfig extends GraphQLScalarTypeConfig<GQLResolversTypes['JSON'], any> {
  name: 'JSON';
}

export type GQLKeyFigureEntryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['KeyFigureEntry'] = GQLResolversParentTypes['KeyFigureEntry']> = {
  format?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  label?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  lastValue?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  type?: Resolver<Maybe<GQLResolversTypes['ToplistSection']>, ParentType, ContextType>;
  value?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
};

export type GQLKtiResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['KtiResponse'] = GQLResolversParentTypes['KtiResponse']> = {
  averageRating?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  ratingCount?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  reviewCount?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  reviews?: Resolver<Maybe<Array<GQLResolversTypes['Review']>>, ParentType, ContextType>;
};

export type GQLLandIdentificationMatrixResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['LandIdentificationMatrix'] = GQLResolversParentTypes['LandIdentificationMatrix']> = {
  bnr?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  fnr?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  gnr?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  knr?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  ownPart?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  snr?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLLeadsEntryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['LeadsEntry'] = GQLResolversParentTypes['LeadsEntry']> = {
  actual?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  budget?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  current?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  departmentId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  employeeCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  id?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  value?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
};

export type GQLListingAgreementResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ListingAgreement'] = GQLResolversParentTypes['ListingAgreement']> = {
  accessTokens?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['AccessToken']>>>, ParentType, ContextType>;
  brokerSigners?: Resolver<Maybe<Array<GQLResolversTypes['Signer']>>, ParentType, ContextType>;
  budgetPosts?: Resolver<Array<GQLResolversTypes['BudgetPost']>, ParentType, ContextType>;
  canStartSigning?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  commission?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  deadline?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  deadlineHasBeenExceeded?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  deletedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  feePercentage?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  generalTerms?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  hasStorebrandLead?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['NodeID'], ParentType, ContextType>;
  initiatedSigningAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  interactions?: Resolver<Array<GQLResolversTypes['ListingAgreementInteraction']>, ParentType, ContextType>;
  marketingPackage?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  offerSellerLink?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  sellerInsurance?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  sellerSigners?: Resolver<Maybe<Array<GQLResolversTypes['Signer']>>, ParentType, ContextType>;
  sentToClientAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  signedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  signers?: Resolver<Array<GQLResolversTypes['Signer']>, ParentType, ContextType>;
  signicatDocument?: Resolver<Maybe<GQLResolversTypes['SignicatDocument']>, ParentType, ContextType>;
  signicatDocumentId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  status?: Resolver<Maybe<GQLResolversTypes['ListingAgreementStatus']>, ParentType, ContextType>;
  suggestedPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
};

export type GQLListingAgreementInteractionResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ListingAgreementInteraction'] = GQLResolversParentTypes['ListingAgreementInteraction']> = {
  broker?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  employeeId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  eventTimestamp?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  eventType?: Resolver<GQLResolversTypes['ListingAgreementInteractionType'], ParentType, ContextType>;
  extraData?: Resolver<Maybe<GQLResolversTypes['JSON']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  listingAgreementsId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  seller?: Resolver<Maybe<GQLResolversTypes['ListingAgreementInteractionSeller']>, ParentType, ContextType>;
  sellerId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLListingAgreementInteractionSellerResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ListingAgreementInteractionSeller'] = GQLResolversParentTypes['ListingAgreementInteractionSeller']> = {
  contactId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLListingAgreementInteractionSummaryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ListingAgreementInteractionSummary'] = GQLResolversParentTypes['ListingAgreementInteractionSummary']> = {
  estate?: Resolver<Maybe<GQLResolversTypes['BrokerEstate']>, ParentType, ContextType>;
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  interactions?: Resolver<Array<GQLResolversTypes['ListingAgreementInteraction']>, ParentType, ContextType>;
};

export type GQLListingAgreementInteractionsResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ListingAgreementInteractions'] = GQLResolversParentTypes['ListingAgreementInteractions']> = {
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  interactions?: Resolver<Array<GQLResolversTypes['ListingAgreementInteraction']>, ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLLocationResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Location'] = GQLResolversParentTypes['Location']> = {
  coordinates?: Resolver<Maybe<Array<GQLResolversTypes['Float']>>, ParentType, ContextType>;
  type?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLMarketingChannelResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['MarketingChannel'] = GQLResolversParentTypes['MarketingChannel']> = {
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  title?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLMarketingPackageResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['MarketingPackage'] = GQLResolversParentTypes['MarketingPackage']> = {
  active?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  channels?: Resolver<Array<GQLResolversTypes['MarketingChannel']>, ParentType, ContextType>;
  clicks?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['NodeID'], ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  packageId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  price?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  productTag?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  public?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  shortName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  views?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLMarketingStartResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['MarketingStart'] = GQLResolversParentTypes['MarketingStart']> = {
  date?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  source?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLModuleResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Module'] = GQLResolversParentTypes['Module']> = {
  accordion?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['Accordion']>>>, ParentType, ContextType>;
  body?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLMutationResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Mutation'] = GQLResolversParentTypes['Mutation']> = {
  addAward?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationAddAwardArgs, 'input'>>;
  addInspectionEvents?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationAddInspectionEventsArgs, 'estateId' | 'events'>>;
  clearInspectionEvents?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationClearInspectionEventsArgs, 'estateId'>>;
  createBrokerPartner?: Resolver<GQLResolversTypes['BrokerPartner'], ParentType, ContextType, RequireFields<GQLMutationCreateBrokerPartnerArgs, 'input'>>;
  createPageVisit?: Resolver<Maybe<GQLResolversTypes['PageVisit']>, ParentType, ContextType, RequireFields<GQLMutationCreatePageVisitArgs, 'input'>>;
  deleteBrokerPartner?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationDeleteBrokerPartnerArgs, 'id'>>;
  deleteInspectionEvent?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationDeleteInspectionEventArgs, 'eventId'>>;
  endPageVisit?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationEndPageVisitArgs, 'pageId'>>;
  hideAward?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationHideAwardArgs, 'hidden' | 'id'>>;
  hideBrokerPartner?: Resolver<Maybe<GQLResolversTypes['BrokerPartner']>, ParentType, ContextType, RequireFields<GQLMutationHideBrokerPartnerArgs, 'hidden' | 'id'>>;
  markNewsAsRead?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType, RequireFields<GQLMutationMarkNewsAsReadArgs, 'newsId'>>;
  pageVisitHeartbeat?: Resolver<Maybe<GQLResolversTypes['PageVisit']>, ParentType, ContextType, RequireFields<GQLMutationPageVisitHeartbeatArgs, 'estateId' | 'pageId'>>;
  removeAward?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationRemoveAwardArgs, 'id'>>;
  reorderBrokerPartners?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationReorderBrokerPartnersArgs, 'ids'>>;
  resetFlagForAllUsers?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationResetFlagForAllUsersArgs, 'flag'>>;
  resetForm?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType, RequireFields<GQLMutationResetFormArgs, 'estateId' | 'formType'>>;
  sendInspectionLead?: Resolver<GQLResolversTypes['InspectionLead'], ParentType, ContextType, RequireFields<GQLMutationSendInspectionLeadArgs, 'contactId' | 'estateId' | 'leadType'>>;
  updateAward?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationUpdateAwardArgs, 'input'>>;
  updateBroker?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationUpdateBrokerArgs, 'input'>>;
  updateBrokerPartner?: Resolver<Maybe<GQLResolversTypes['BrokerPartner']>, ParentType, ContextType, RequireFields<GQLMutationUpdateBrokerPartnerArgs, 'input'>>;
  updateBrokerProfileLinks?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationUpdateBrokerProfileLinksArgs, 'input'>>;
  updateInspectionEvent?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationUpdateInspectionEventArgs, 'event' | 'eventId'>>;
  updateInspectionEvents?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationUpdateInspectionEventsArgs, 'estateId' | 'events'>>;
  updateInspectionFolderNotes?: Resolver<Maybe<GQLResolversTypes['InspectionFolder']>, ParentType, ContextType, RequireFields<GQLMutationUpdateInspectionFolderNotesArgs, 'estateId' | 'notes'>>;
  upsertEstatePublishDate?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationUpsertEstatePublishDateArgs, 'estateId'>>;
  userResetAllFlags?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  userResetFlag?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationUserResetFlagArgs, 'flag'>>;
  userSetFlag?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLMutationUserSetFlagArgs, 'flag' | 'value'>>;
};

export type GQLNextDocumentResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['NextDocument'] = GQLResolversParentTypes['NextDocument']> = {
  approvalDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  approvedBy?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  docType?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  documentId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  extension?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  head?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  lastChanged?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  signStatus?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export interface GQLNodeIdScalarConfig extends GraphQLScalarTypeConfig<GQLResolversTypes['NodeID'], any> {
  name: 'NodeID';
}

export type GQLNordvikAwardResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['NordvikAward'] = GQLResolversParentTypes['NordvikAward']> = {
  awardId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  hidden?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  origin?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  private?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  year?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLPageVisitResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['PageVisit'] = GQLResolversParentTypes['PageVisit']> = {
  browser?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  employeeId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  endTime?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  estateId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  lastHeartbeat?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  location?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  pageId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  source?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  startTime?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  totalTimeSpent?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  userAgent?: Resolver<Maybe<GQLResolversTypes['UserAgent']>, ParentType, ContextType>;
};

export type GQLPaginationResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Pagination'] = GQLResolversParentTypes['Pagination']> = {
  count?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  limit?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  offset?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  total?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLPartOwnershipResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['PartOwnership'] = GQLResolversParentTypes['PartOwnership']> = {
  businessManagerContactId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  estateHousingCooperativeStockHousingUnitNumber?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  estateHousingCooperativeStockNumber?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  partAbout?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  partName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  partNumber?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  partOrgNumber?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLPostDateResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['PostDate'] = GQLResolversParentTypes['PostDate']> = {
  date?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  timezone?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  timezone_type?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLPriceIndexResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['PriceIndex'] = GQLResolversParentTypes['PriceIndex']> = {
  area?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  avgSalesTime?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  avgSqmPrice?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  date?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['ID'], ParentType, ContextType>;
  indexChange1Month?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  indexChange1Quarter?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  indexChange4Quarter?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  indexChange5Years?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  indexChange10Years?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  indexChange12Months?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  region?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLPriceStatisticsResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['PriceStatistics'] = GQLResolversParentTypes['PriceStatistics']> = {
  indexes?: Resolver<Array<GQLResolversTypes['PriceIndex']>, ParentType, ContextType>;
  secondaryIndexes?: Resolver<Array<GQLResolversTypes['PriceIndex']>, ParentType, ContextType>;
};

export type GQLQueryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Query'] = GQLResolversParentTypes['Query']> = {
  activitySummary?: Resolver<GQLResolversTypes['ActivitySummary'], ParentType, ContextType, RequireFields<GQLQueryActivitySummaryArgs, 'estateId'>>;
  allDepartments?: Resolver<Maybe<GQLResolversTypes['DepartmentsResponse']>, ParentType, ContextType>;
  allListingAgreementInteractions?: Resolver<Array<GQLResolversTypes['ListingAgreementInteractionSummary']>, ParentType, ContextType, Partial<GQLQueryAllListingAgreementInteractionsArgs>>;
  assignmentDocumentStatus?: Resolver<GQLResolversTypes['AssignmentDocumentStatusItem'], ParentType, ContextType, RequireFields<GQLQueryAssignmentDocumentStatusArgs, 'estateId' | 'type'>>;
  brokerByEmail?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType, RequireFields<GQLQueryBrokerByEmailArgs, 'email'>>;
  brokerByEmployeeId?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType, RequireFields<GQLQueryBrokerByEmployeeIdArgs, 'employeeId'>>;
  brokerPartner?: Resolver<Maybe<GQLResolversTypes['BrokerPartner']>, ParentType, ContextType, RequireFields<GQLQueryBrokerPartnerArgs, 'id'>>;
  brokerPartners?: Resolver<Array<GQLResolversTypes['BrokerPartner']>, ParentType, ContextType, RequireFields<GQLQueryBrokerPartnersArgs, 'employeeId'>>;
  brokers?: Resolver<Maybe<GQLResolversTypes['BrokersResponse']>, ParentType, ContextType>;
  budgetPostDescriptions?: Resolver<Array<GQLResolversTypes['BudgetPostDescription']>, ParentType, ContextType>;
  cmsArticleBySlug?: Resolver<Maybe<GQLResolversTypes['CmsArticle']>, ParentType, ContextType, RequireFields<GQLQueryCmsArticleBySlugArgs, 'slug'>>;
  cmsArticleCategories?: Resolver<Array<GQLResolversTypes['CmsArticleCategory']>, ParentType, ContextType, Partial<GQLQueryCmsArticleCategoriesArgs>>;
  cmsArticles?: Resolver<GQLResolversTypes['CmsArticleResponse'], ParentType, ContextType, Partial<GQLQueryCmsArticlesArgs>>;
  cmsChangelogs?: Resolver<GQLResolversTypes['CmsArticleResponse'], ParentType, ContextType, Partial<GQLQueryCmsChangelogsArgs>>;
  cmsIncidentBySlug?: Resolver<Maybe<GQLResolversTypes['CraftCmsIncident']>, ParentType, ContextType, RequireFields<GQLQueryCmsIncidentBySlugArgs, 'slug'>>;
  cmsIncidents?: Resolver<GQLResolversTypes['CmsIncidentsResponse'], ParentType, ContextType, Partial<GQLQueryCmsIncidentsArgs>>;
  cmsJobBySlug?: Resolver<Maybe<GQLResolversTypes['CraftCmsJob']>, ParentType, ContextType, RequireFields<GQLQueryCmsJobBySlugArgs, 'slug'>>;
  cmsJobsListing?: Resolver<GQLResolversTypes['CraftCmsJobsListingResponse'], ParentType, ContextType>;
  contact?: Resolver<Maybe<GQLResolversTypes['Contact']>, ParentType, ContextType, RequireFields<GQLQueryContactArgs, 'contactId'>>;
  currentBroker?: Resolver<Maybe<GQLResolversTypes['Broker']>, ParentType, ContextType>;
  currentBrokerKti?: Resolver<GQLResolversTypes['BrokerKtiResponse'], ParentType, ContextType, Partial<GQLQueryCurrentBrokerKtiArgs>>;
  currentBrokerPartners?: Resolver<Array<GQLResolversTypes['BrokerPartner']>, ParentType, ContextType>;
  currentBrokerProfileLinks?: Resolver<Maybe<GQLResolversTypes['BrokerProfileLinks']>, ParentType, ContextType>;
  currentBrokerRatings?: Resolver<Maybe<GQLResolversTypes['BrokerRatingsList']>, ParentType, ContextType, Partial<GQLQueryCurrentBrokerRatingsArgs>>;
  currentBrokerRatingsTotal?: Resolver<Maybe<GQLResolversTypes['CurrentBrokerRatingsTotal']>, ParentType, ContextType, Partial<GQLQueryCurrentBrokerRatingsTotalArgs>>;
  dashboardAverageCommission?: Resolver<GQLResolversTypes['AverageCommissionResponse'], ParentType, ContextType, RequireFields<GQLQueryDashboardAverageCommissionArgs, 'type'>>;
  dashboardCache?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
  dashboardCacheForEmployee?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLQueryDashboardCacheForEmployeeArgs, 'employeeId' | 'section'>>;
  dashboardExpectedRevenue?: Resolver<Array<GQLResolversTypes['DashboardStatusEntry']>, ParentType, ContextType, RequireFields<GQLQueryDashboardExpectedRevenueArgs, 'type'>>;
  dashboardImportantTasks?: Resolver<Array<GQLResolversTypes['ImportantTask']>, ParentType, ContextType>;
  dashboardKeyFigures?: Resolver<Array<GQLResolversTypes['KeyFigureEntry']>, ParentType, ContextType, RequireFields<GQLQueryDashboardKeyFiguresArgs, 'period' | 'type'>>;
  dashboardKti?: Resolver<GQLResolversTypes['DashboardKtiResponse'], ParentType, ContextType>;
  dashboardLeads?: Resolver<GQLResolversTypes['DashboardLeadsResponse'], ParentType, ContextType, RequireFields<GQLQueryDashboardLeadsArgs, 'type'>>;
  dashboardRevenue?: Resolver<GQLResolversTypes['RevenueResponse'], ParentType, ContextType, RequireFields<GQLQueryDashboardRevenueArgs, 'type'>>;
  dashboardToplist?: Resolver<GQLResolversTypes['DashboardToplistResponse'], ParentType, ContextType, RequireFields<GQLQueryDashboardToplistArgs, 'section' | 'type'>>;
  dashboardUpcomingActivities?: Resolver<Array<GQLResolversTypes['DashboardActivity']>, ParentType, ContextType, RequireFields<GQLQueryDashboardUpcomingActivitiesArgs, 'type'>>;
  department?: Resolver<Maybe<GQLResolversTypes['Department']>, ParentType, ContextType, RequireFields<GQLQueryDepartmentArgs, 'departmentId'>>;
  emailAuditsByEstateId?: Resolver<Array<GQLResolversTypes['EmailAudit']>, ParentType, ContextType, RequireFields<GQLQueryEmailAuditsByEstateIdArgs, 'estateId'>>;
  emailInteractionsForEstate?: Resolver<Array<GQLResolversTypes['EmailInteraction']>, ParentType, ContextType, RequireFields<GQLQueryEmailInteractionsForEstateArgs, 'estateId'>>;
  estate?: Resolver<Maybe<GQLResolversTypes['BrokerEstate']>, ParentType, ContextType, RequireFields<GQLQueryEstateArgs, 'id'>>;
  estateFormsByEstateId?: Resolver<Array<GQLResolversTypes['BrokerEstateForm']>, ParentType, ContextType, RequireFields<GQLQueryEstateFormsByEstateIdArgs, 'estateId'>>;
  estatePriceHistories?: Resolver<Array<GQLResolversTypes['BrokerEstatePriceHistory']>, ParentType, ContextType>;
  estates?: Resolver<Array<GQLResolversTypes['BrokerEstate']>, ParentType, ContextType>;
  estatesForBrokerById?: Resolver<GQLResolversTypes['BrokerEstatesResponse'], ParentType, ContextType, RequireFields<GQLQueryEstatesForBrokerByIdArgs, 'tabs'>>;
  estatesForBrokerIdCount?: Resolver<Array<GQLResolversTypes['BrokerEstateCountResponse']>, ParentType, ContextType, RequireFields<GQLQueryEstatesForBrokerIdCountArgs, 'tabs'>>;
  estatesForDepartment?: Resolver<GQLResolversTypes['BrokerEstatesResponse'], ParentType, ContextType, RequireFields<GQLQueryEstatesForDepartmentArgs, 'departmentId' | 'tabs'>>;
  findEstates?: Resolver<Array<GQLResolversTypes['BrokerEstate']>, ParentType, ContextType, RequireFields<GQLQueryFindEstatesArgs, 'filters'>>;
  findEstatesForBroker?: Resolver<Array<GQLResolversTypes['BrokerEstate']>, ParentType, ContextType, RequireFields<GQLQueryFindEstatesForBrokerArgs, 'employeeId'>>;
  hallOfFame?: Resolver<Array<GQLResolversTypes['HallOfFameEntry']>, ParentType, ContextType>;
  inspectionEvents?: Resolver<Array<GQLResolversTypes['InspectionEvent']>, ParentType, ContextType, RequireFields<GQLQueryInspectionEventsArgs, 'estateId'>>;
  inspectionFolder?: Resolver<Maybe<GQLResolversTypes['InspectionFolder']>, ParentType, ContextType, RequireFields<GQLQueryInspectionFolderArgs, 'estateId'>>;
  inspectionLeadsForEstate?: Resolver<Array<GQLResolversTypes['InspectionLead']>, ParentType, ContextType, RequireFields<GQLQueryInspectionLeadsForEstateArgs, 'estateId'>>;
  listingAgreementByDocumentId?: Resolver<Maybe<GQLResolversTypes['ListingAgreement']>, ParentType, ContextType, RequireFields<GQLQueryListingAgreementByDocumentIdArgs, 'documentId'>>;
  listingAgreementByEstateId?: Resolver<Maybe<GQLResolversTypes['ListingAgreement']>, ParentType, ContextType, RequireFields<GQLQueryListingAgreementByEstateIdArgs, 'estateId'>>;
  listingAgreementInteractions?: Resolver<Array<GQLResolversTypes['ListingAgreementInteraction']>, ParentType, ContextType, RequireFields<GQLQueryListingAgreementInteractionsArgs, 'listingAgreementId'>>;
  listingAgreementInteractionsForBroker?: Resolver<Array<GQLResolversTypes['ListingAgreementInteractions']>, ParentType, ContextType, RequireFields<GQLQueryListingAgreementInteractionsForBrokerArgs, 'employeeId'>>;
  listingAgreementInteractionsForCurrentBroker?: Resolver<Array<GQLResolversTypes['ListingAgreementInteractions']>, ParentType, ContextType>;
  listingAgreementInteractionsForEstate?: Resolver<Array<GQLResolversTypes['ListingAgreementInteraction']>, ParentType, ContextType, RequireFields<GQLQueryListingAgreementInteractionsForEstateArgs, 'estateId'>>;
  mainBrokerPartners?: Resolver<Array<GQLResolversTypes['BrokerPartner']>, ParentType, ContextType, RequireFields<GQLQueryMainBrokerPartnersArgs, 'estateId'>>;
  marketingPackages?: Resolver<Array<GQLResolversTypes['MarketingPackage']>, ParentType, ContextType, RequireFields<GQLQueryMarketingPackagesArgs, 'active' | 'type'>>;
  pageVisit?: Resolver<Maybe<GQLResolversTypes['PageVisit']>, ParentType, ContextType, RequireFields<GQLQueryPageVisitArgs, 'id'>>;
  pageVisitHeartbeat?: Resolver<Maybe<GQLResolversTypes['PageVisit']>, ParentType, ContextType, RequireFields<GQLQueryPageVisitHeartbeatArgs, 'estateId' | 'pageId'>>;
  pageVisits?: Resolver<Array<GQLResolversTypes['PageVisit']>, ParentType, ContextType, RequireFields<GQLQueryPageVisitsArgs, 'estateId'>>;
  priceStatistics?: Resolver<GQLResolversTypes['PriceStatistics'], ParentType, ContextType, RequireFields<GQLQueryPriceStatisticsArgs, 'postalCode'>>;
  ratings?: Resolver<GQLResolversTypes['RatingResponse'], ParentType, ContextType, Partial<GQLQueryRatingsArgs>>;
  readArticles?: Resolver<Array<GQLResolversTypes['ReadArticle']>, ParentType, ContextType>;
  storebrandDuplicateCheck?: Resolver<GQLResolversTypes['StorebrandDuplicateCheckResult'], ParentType, ContextType, RequireFields<GQLQueryStorebrandDuplicateCheckArgs, 'input'>>;
  syncEstateWithVitec?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLQuerySyncEstateWithVitecArgs, 'estateId'>>;
  toplist?: Resolver<GQLResolversTypes['ToplistResponse'], ParentType, ContextType, Partial<GQLQueryToplistArgs>>;
  userHasFlag?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType, RequireFields<GQLQueryUserHasFlagArgs, 'flag'>>;
  userNotifications?: Resolver<GQLResolversTypes['UserNotificationsResponse'], ParentType, ContextType>;
};

export type GQLRatingResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Rating'] = GQLResolversParentTypes['Rating']> = {
  average?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  count?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  reviewsCount?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  weighted?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
};

export type GQLRatingResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['RatingResponse'] = GQLResolversParentTypes['RatingResponse']> = {
  average?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  count?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLReadArticleResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ReadArticle'] = GQLResolversParentTypes['ReadArticle']> = {
  id?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  readAt?: Resolver<GQLResolversTypes['DateTime'], ParentType, ContextType>;
};

export type GQLRelevantForEstateWithPropsResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['RelevantForEstateWithProps'] = GQLResolversParentTypes['RelevantForEstateWithProps']> = {
  projectRelation?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  status?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
};

export type GQLRevenueEntryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['RevenueEntry'] = GQLResolversParentTypes['RevenueEntry']> = {
  value?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  year?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLRevenueMonthResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['RevenueMonth'] = GQLResolversParentTypes['RevenueMonth']> = {
  budget?: Resolver<Maybe<GQLResolversTypes['RevenueEntry']>, ParentType, ContextType>;
  current?: Resolver<Maybe<GQLResolversTypes['RevenueEntry']>, ParentType, ContextType>;
  month?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  previous?: Resolver<GQLResolversTypes['RevenueEntry'], ParentType, ContextType>;
};

export type GQLRevenueResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['RevenueResponse'] = GQLResolversParentTypes['RevenueResponse']> = {
  budgetTotal?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  currentYearBudget?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  currentYearTotal?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  entries?: Resolver<Array<GQLResolversTypes['RevenueMonth']>, ParentType, ContextType>;
  hitBudgetPercentage?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  percentageChange?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  previousYearTotal?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
  previousYearUntilNow?: Resolver<Maybe<GQLResolversTypes['Float']>, ParentType, ContextType>;
};

export type GQLReviewResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Review'] = GQLResolversParentTypes['Review']> = {
  agentSatisfaction?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  createdAt?: Resolver<Maybe<GQLResolversTypes['Date']>, ParentType, ContextType>;
  id?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  lang?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  rating?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  recommendation?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  reviewerShortName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  text?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLSellerProxyResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['SellerProxy'] = GQLResolversParentTypes['SellerProxy']> = {
  companyName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  contactId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  mobilePhone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLSignerResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Signer'] = GQLResolversParentTypes['Signer']> = {
  email?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  externalSignerId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  firstName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GQLResolversTypes['NodeID'], ParentType, ContextType>;
  lastName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  listingAgreementId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  phone?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  signedAt?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  url?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLSignerSummaryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['SignerSummary'] = GQLResolversParentTypes['SignerSummary']> = {
  contactId?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  lastHeartbeat?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  totalTimeSpent?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  visitsCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLSignicatDocumentResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['SignicatDocument'] = GQLResolversParentTypes['SignicatDocument']> = {
  accountId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  attachments?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['String']>>>, ParentType, ContextType>;
  created?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  currentSignatures?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  deadline?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  description?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  documentId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  documentSignatures?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['SignicatExtendedDocumentSignature']>>>, ParentType, ContextType>;
  externalId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  lastUpdated?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  requiredSignatures?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  signedDate?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  signers?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['String']>>>, ParentType, ContextType>;
  status?: Resolver<Maybe<GQLResolversTypes['SignicatDocumentStatus']>, ParentType, ContextType>;
  tags?: Resolver<Maybe<Array<Maybe<GQLResolversTypes['String']>>>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLSignicatDocumentStatusResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['SignicatDocumentStatus'] = GQLResolversParentTypes['SignicatDocumentStatus']> = {
  documentStatus?: Resolver<Maybe<GQLResolversTypes['SignicatDocumentStatusEnum']>, ParentType, ContextType>;
};

export type GQLSignicatExtendedDocumentSignatureResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['SignicatExtendedDocumentSignature'] = GQLResolversParentTypes['SignicatExtendedDocumentSignature']> = {
  clientIp?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  dateOfBirth?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  externalSignerId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  fullName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  mechanism?: Resolver<Maybe<GQLResolversTypes['SignatureMechanism']>, ParentType, ContextType>;
  middleName?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  personalInfoOrigin?: Resolver<Maybe<GQLResolversTypes['PersonalInfoOrigin']>, ParentType, ContextType>;
  signatureMethod?: Resolver<Maybe<GQLResolversTypes['SignicatSignatureMethod']>, ParentType, ContextType>;
  signatureMethodUniqueId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  signedTime?: Resolver<Maybe<GQLResolversTypes['DateTime']>, ParentType, ContextType>;
  signerId?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  socialSecurityNumber?: Resolver<Maybe<GQLResolversTypes['SignicatSocialSecurityNumber']>, ParentType, ContextType>;
};

export type GQLSignicatSocialSecurityNumberResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['SignicatSocialSecurityNumber'] = GQLResolversParentTypes['SignicatSocialSecurityNumber']> = {
  countryCode?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLSimpleContactResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['SimpleContact'] = GQLResolversParentTypes['SimpleContact']> = {
  email?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
};

export type GQLStorebrandDuplicateCheckResultResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['StorebrandDuplicateCheckResult'] = GQLResolversParentTypes['StorebrandDuplicateCheckResult']> = {
  hasDuplicates?: Resolver<GQLResolversTypes['Boolean'], ParentType, ContextType>;
};

export type GQLSumAreaResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['SumArea'] = GQLResolversParentTypes['SumArea']> = {
  bra?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  braB?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  braE?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  braI?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  braS?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  bta?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  pRom?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  sRom?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
  tba?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
};

export type GQLTargetRoleResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['TargetRole'] = GQLResolversParentTypes['TargetRole']> = {
  id?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  roleTypeId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  slug?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLToplistEntryResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ToplistEntry'] = GQLResolversParentTypes['ToplistEntry']> = {
  avatarUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  count?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  department?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  departmentId?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<Maybe<GQLResolversTypes['NodeID']>, ParentType, ContextType>;
  imageUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  reviews?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  value?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
};

export type GQLToplistEntryCurrentResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ToplistEntryCurrent'] = GQLResolversParentTypes['ToplistEntryCurrent']> = {
  imageUrl?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<GQLResolversTypes['String'], ParentType, ContextType>;
  position?: Resolver<Maybe<GQLResolversTypes['Int']>, ParentType, ContextType>;
  value?: Resolver<GQLResolversTypes['Float'], ParentType, ContextType>;
};

export type GQLToplistResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['ToplistResponse'] = GQLResolversParentTypes['ToplistResponse']> = {
  items?: Resolver<Array<GQLResolversTypes['ToplistEntry']>, ParentType, ContextType>;
  totalCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLUserAgentResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['UserAgent'] = GQLResolversParentTypes['UserAgent']> = {
  browser?: Resolver<Maybe<GQLResolversTypes['UserAgentBrowser']>, ParentType, ContextType>;
  cpu?: Resolver<Maybe<GQLResolversTypes['JSON']>, ParentType, ContextType>;
  device?: Resolver<Maybe<GQLResolversTypes['UserAgentDevice']>, ParentType, ContextType>;
  engine?: Resolver<Maybe<GQLResolversTypes['UserAgentEngine']>, ParentType, ContextType>;
  isBot?: Resolver<Maybe<GQLResolversTypes['Boolean']>, ParentType, ContextType>;
  os?: Resolver<Maybe<GQLResolversTypes['UserAgentOS']>, ParentType, ContextType>;
  ua?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLUserAgentBrowserResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['UserAgentBrowser'] = GQLResolversParentTypes['UserAgentBrowser']> = {
  major?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  version?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLUserAgentDeviceResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['UserAgentDevice'] = GQLResolversParentTypes['UserAgentDevice']> = {
  model?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  vendor?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLUserAgentEngineResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['UserAgentEngine'] = GQLResolversParentTypes['UserAgentEngine']> = {
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  version?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLUserAgentOsResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['UserAgentOS'] = GQLResolversParentTypes['UserAgentOS']> = {
  name?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  version?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLUserNotificationsResponseResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['UserNotificationsResponse'] = GQLResolversParentTypes['UserNotificationsResponse']> = {
  newsCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
  totalCount?: Resolver<GQLResolversTypes['Int'], ParentType, ContextType>;
};

export type GQLUspResolvers<ContextType = Context, ParentType extends GQLResolversParentTypes['Usp'] = GQLResolversParentTypes['Usp']> = {
  description?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GQLResolversTypes['String']>, ParentType, ContextType>;
};

export type GQLResolvers<ContextType = Context> = {
  AccessToken?: GQLAccessTokenResolvers<ContextType>;
  Accordion?: GQLAccordionResolvers<ContextType>;
  ActivitySummary?: GQLActivitySummaryResolvers<ContextType>;
  AreaSize?: GQLAreaSizeResolvers<ContextType>;
  AssignmentDocumentStatusItem?: GQLAssignmentDocumentStatusItemResolvers<ContextType>;
  AssignmentDocumentStatuses?: GQLAssignmentDocumentStatusesResolvers<ContextType>;
  AuditExtraData?: GQLAuditExtraDataResolvers<ContextType>;
  AuditRecipient?: GQLAuditRecipientResolvers<ContextType>;
  AuditTemplate?: GQLAuditTemplateResolvers<ContextType>;
  AverageCommissionEntry?: GQLAverageCommissionEntryResolvers<ContextType>;
  AverageCommissionResponse?: GQLAverageCommissionResponseResolvers<ContextType>;
  Award?: GQLAwardResolvers<ContextType>;
  Broker?: GQLBrokerResolvers<ContextType>;
  BrokerAddress?: GQLBrokerAddressResolvers<ContextType>;
  BrokerEstate?: GQLBrokerEstateResolvers<ContextType>;
  BrokerEstateCampaign?: GQLBrokerEstateCampaignResolvers<ContextType>;
  BrokerEstateCountResponse?: GQLBrokerEstateCountResponseResolvers<ContextType>;
  BrokerEstateForm?: GQLBrokerEstateFormResolvers<ContextType>;
  BrokerEstateFormStatus?: GQLBrokerEstateFormStatusResolvers<ContextType>;
  BrokerEstateImage?: GQLBrokerEstateImageResolvers<ContextType>;
  BrokerEstateLink?: GQLBrokerEstateLinkResolvers<ContextType>;
  BrokerEstatePagination?: GQLBrokerEstatePaginationResolvers<ContextType>;
  BrokerEstatePriceHistory?: GQLBrokerEstatePriceHistoryResolvers<ContextType>;
  BrokerEstateSeller?: GQLBrokerEstateSellerResolvers<ContextType>;
  BrokerEstateShowing?: GQLBrokerEstateShowingResolvers<ContextType>;
  BrokerEstatesResponse?: GQLBrokerEstatesResponseResolvers<ContextType>;
  BrokerIdWithRole?: GQLBrokerIdWithRoleResolvers<ContextType>;
  BrokerIdWithRoleDetails?: GQLBrokerIdWithRoleDetailsResolvers<ContextType>;
  BrokerImage?: GQLBrokerImageResolvers<ContextType>;
  BrokerKtiResponse?: GQLBrokerKtiResponseResolvers<ContextType>;
  BrokerPartner?: GQLBrokerPartnerResolvers<ContextType>;
  BrokerProfileLinks?: GQLBrokerProfileLinksResolvers<ContextType>;
  BrokerRating?: GQLBrokerRatingResolvers<ContextType>;
  BrokerRatingsList?: GQLBrokerRatingsListResolvers<ContextType>;
  BrokerReview?: GQLBrokerReviewResolvers<ContextType>;
  BrokerRole?: GQLBrokerRoleResolvers<ContextType>;
  BrokersResponse?: GQLBrokersResponseResolvers<ContextType>;
  BudgetPost?: GQLBudgetPostResolvers<ContextType>;
  BudgetPostDescription?: GQLBudgetPostDescriptionResolvers<ContextType>;
  BusinessManagerContact?: GQLBusinessManagerContactResolvers<ContextType>;
  CmsArticle?: GQLCmsArticleResolvers<ContextType>;
  CmsArticleAuthor?: GQLCmsArticleAuthorResolvers<ContextType>;
  CmsArticleCategory?: GQLCmsArticleCategoryResolvers<ContextType>;
  CmsArticleDepartment?: GQLCmsArticleDepartmentResolvers<ContextType>;
  CmsArticleImage?: GQLCmsArticleImageResolvers<ContextType>;
  CmsArticleResponse?: GQLCmsArticleResponseResolvers<ContextType>;
  CmsIncidentsResponse?: GQLCmsIncidentsResponseResolvers<ContextType>;
  CmsMeta?: GQLCmsMetaResolvers<ContextType>;
  Contact?: GQLContactResolvers<ContextType>;
  CraftCmsIncident?: GQLCraftCmsIncidentResolvers<ContextType>;
  CraftCmsIncidentUpdate?: GQLCraftCmsIncidentUpdateResolvers<ContextType>;
  CraftCmsJob?: GQLCraftCmsJobResolvers<ContextType>;
  CraftCmsJobListingItem?: GQLCraftCmsJobListingItemResolvers<ContextType>;
  CraftCmsJobsListingResponse?: GQLCraftCmsJobsListingResponseResolvers<ContextType>;
  CurrentBrokerRatingsTotal?: GQLCurrentBrokerRatingsTotalResolvers<ContextType>;
  DashboardActivity?: GQLDashboardActivityResolvers<ContextType>;
  DashboardKtiCurrent?: GQLDashboardKtiCurrentResolvers<ContextType>;
  DashboardKtiOthers?: GQLDashboardKtiOthersResolvers<ContextType>;
  DashboardKtiResponse?: GQLDashboardKtiResponseResolvers<ContextType>;
  DashboardLeadsResponse?: GQLDashboardLeadsResponseResolvers<ContextType>;
  DashboardStatusEntry?: GQLDashboardStatusEntryResolvers<ContextType>;
  DashboardToplistResponse?: GQLDashboardToplistResponseResolvers<ContextType>;
  Date?: GraphQLScalarType;
  DateTime?: GraphQLScalarType;
  Department?: GQLDepartmentResolvers<ContextType>;
  DepartmentEmployee?: GQLDepartmentEmployeeResolvers<ContextType>;
  DepartmentRating?: GQLDepartmentRatingResolvers<ContextType>;
  DepartmentsResponse?: GQLDepartmentsResponseResolvers<ContextType>;
  EmailAudit?: GQLEmailAuditResolvers<ContextType>;
  EmailAuditRecipient?: GQLEmailAuditRecipientResolvers<ContextType>;
  EmailInteraction?: GQLEmailInteractionResolvers<ContextType>;
  EstateActivity?: GQLEstateActivityResolvers<ContextType>;
  EstateAd?: GQLEstateAdResolvers<ContextType>;
  EstateBroker?: GQLEstateBrokerResolvers<ContextType>;
  EstateChecklistItem?: GQLEstateChecklistItemResolvers<ContextType>;
  EstatePrice?: GQLEstatePriceResolvers<ContextType>;
  EstatePriceModel?: GQLEstatePriceModelResolvers<ContextType>;
  EstateSizeModel?: GQLEstateSizeModelResolvers<ContextType>;
  EstateStats?: GQLEstateStatsResolvers<ContextType>;
  FinnData?: GQLFinnDataResolvers<ContextType>;
  HallOfFameEntry?: GQLHallOfFameEntryResolvers<ContextType>;
  HallOfFameEntryAward?: GQLHallOfFameEntryAwardResolvers<ContextType>;
  ImportantTask?: GQLImportantTaskResolvers<ContextType>;
  Inspection?: GQLInspectionResolvers<ContextType>;
  InspectionActivity?: GQLInspectionActivityResolvers<ContextType>;
  InspectionEntry?: GQLInspectionEntryResolvers<ContextType>;
  InspectionEvent?: GQLInspectionEventResolvers<ContextType>;
  InspectionFolder?: GQLInspectionFolderResolvers<ContextType>;
  InspectionFolderAudit?: GQLInspectionFolderAuditResolvers<ContextType>;
  InspectionLead?: GQLInspectionLeadResolvers<ContextType>;
  InspectionMetadata?: GQLInspectionMetadataResolvers<ContextType>;
  JSON?: GraphQLScalarType;
  KeyFigureEntry?: GQLKeyFigureEntryResolvers<ContextType>;
  KtiResponse?: GQLKtiResponseResolvers<ContextType>;
  LandIdentificationMatrix?: GQLLandIdentificationMatrixResolvers<ContextType>;
  LeadsEntry?: GQLLeadsEntryResolvers<ContextType>;
  ListingAgreement?: GQLListingAgreementResolvers<ContextType>;
  ListingAgreementInteraction?: GQLListingAgreementInteractionResolvers<ContextType>;
  ListingAgreementInteractionSeller?: GQLListingAgreementInteractionSellerResolvers<ContextType>;
  ListingAgreementInteractionSummary?: GQLListingAgreementInteractionSummaryResolvers<ContextType>;
  ListingAgreementInteractions?: GQLListingAgreementInteractionsResolvers<ContextType>;
  Location?: GQLLocationResolvers<ContextType>;
  MarketingChannel?: GQLMarketingChannelResolvers<ContextType>;
  MarketingPackage?: GQLMarketingPackageResolvers<ContextType>;
  MarketingStart?: GQLMarketingStartResolvers<ContextType>;
  Module?: GQLModuleResolvers<ContextType>;
  Mutation?: GQLMutationResolvers<ContextType>;
  NextDocument?: GQLNextDocumentResolvers<ContextType>;
  NodeID?: GraphQLScalarType;
  NordvikAward?: GQLNordvikAwardResolvers<ContextType>;
  PageVisit?: GQLPageVisitResolvers<ContextType>;
  Pagination?: GQLPaginationResolvers<ContextType>;
  PartOwnership?: GQLPartOwnershipResolvers<ContextType>;
  PostDate?: GQLPostDateResolvers<ContextType>;
  PriceIndex?: GQLPriceIndexResolvers<ContextType>;
  PriceStatistics?: GQLPriceStatisticsResolvers<ContextType>;
  Query?: GQLQueryResolvers<ContextType>;
  Rating?: GQLRatingResolvers<ContextType>;
  RatingResponse?: GQLRatingResponseResolvers<ContextType>;
  ReadArticle?: GQLReadArticleResolvers<ContextType>;
  RelevantForEstateWithProps?: GQLRelevantForEstateWithPropsResolvers<ContextType>;
  RevenueEntry?: GQLRevenueEntryResolvers<ContextType>;
  RevenueMonth?: GQLRevenueMonthResolvers<ContextType>;
  RevenueResponse?: GQLRevenueResponseResolvers<ContextType>;
  Review?: GQLReviewResolvers<ContextType>;
  SellerProxy?: GQLSellerProxyResolvers<ContextType>;
  Signer?: GQLSignerResolvers<ContextType>;
  SignerSummary?: GQLSignerSummaryResolvers<ContextType>;
  SignicatDocument?: GQLSignicatDocumentResolvers<ContextType>;
  SignicatDocumentStatus?: GQLSignicatDocumentStatusResolvers<ContextType>;
  SignicatExtendedDocumentSignature?: GQLSignicatExtendedDocumentSignatureResolvers<ContextType>;
  SignicatSocialSecurityNumber?: GQLSignicatSocialSecurityNumberResolvers<ContextType>;
  SimpleContact?: GQLSimpleContactResolvers<ContextType>;
  StorebrandDuplicateCheckResult?: GQLStorebrandDuplicateCheckResultResolvers<ContextType>;
  SumArea?: GQLSumAreaResolvers<ContextType>;
  TargetRole?: GQLTargetRoleResolvers<ContextType>;
  ToplistEntry?: GQLToplistEntryResolvers<ContextType>;
  ToplistEntryCurrent?: GQLToplistEntryCurrentResolvers<ContextType>;
  ToplistResponse?: GQLToplistResponseResolvers<ContextType>;
  UserAgent?: GQLUserAgentResolvers<ContextType>;
  UserAgentBrowser?: GQLUserAgentBrowserResolvers<ContextType>;
  UserAgentDevice?: GQLUserAgentDeviceResolvers<ContextType>;
  UserAgentEngine?: GQLUserAgentEngineResolvers<ContextType>;
  UserAgentOS?: GQLUserAgentOsResolvers<ContextType>;
  UserNotificationsResponse?: GQLUserNotificationsResponseResolvers<ContextType>;
  Usp?: GQLUspResolvers<ContextType>;
};

