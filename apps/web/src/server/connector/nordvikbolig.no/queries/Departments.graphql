query getAllDepartments {
  departments(limit: 0, offset: 0) {
    departmentsEntries {
      id
      departmentId
      departmentNumber
      slug
      name
      marketName
      legalName
      organisationNumber
      phone
      employees {
        id
        employeeId
        name
        email
        mobilePhone
        title
        slug
      }
    }
  }
}

query getDepartment($departmentId: Int!) {
  department(departmentId: $departmentId) {
    id
    departmentId
    departmentNumber
    slug
    name
    marketName
    legalName
    organisationNumber
    phone
    employees {
      id
      employeeId
      name
      email
      mobilePhone
      title
      slug
    }
  }
}

query departmentEstates($departmentId: Int, $status: Int) {
  department(departmentId: $departmentId, status: $status) {
    estates {
      estateId
      status
      employeeId
    }
  }
}

query departmentEmployeesBudgets($departmentId: Int!) {
  department(departmentId: $departmentId) {
    employees {
      salesBudget {
        year
        months {
          month
          sum {
            income
            numberOfSales
          }
        }
      }
    }
  }
}

query allDepartmentEmployeesBudgets {
  departments(limit: 0, offset: 0) {
    departmentsEntries {
      departmentId
      employees {
        salesBudget {
          year
          months {
            month
            sum {
              income
              numberOfSales
            }
          }
        }
      }
    }
  }
}

query departmentEstatesEntries(
  $departmentId: Int
  $statuses: [Int]
  $limit: Int
  $offset: Int
  $sortBy: String
  $search: String
) {
  estates(
    departmentId: $departmentId
    statuses: $statuses
    limit: $limit
    offset: $offset
    sortBy: $sortBy
    search: $search
  ) {
    estatesEntries {
      ...EstateListEntryFields
    }
    count
  }
}

query departmentEstatesCount($departmentId: Int, $statuses: [Int]) {
  estates(departmentId: $departmentId, statuses: $statuses) {
    estatesEntries {
      estateId
      ads {
        id
        finnAdType
        ownAdvertisementType
        link
        channel
      }
      hasAd
      publishStart
    }
    count
  }
}
