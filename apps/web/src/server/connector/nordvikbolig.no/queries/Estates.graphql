query getEstateById($estateId: String!, $statuses: [Int]) {
  estate(id: $estateId, all: true, statuses: $statuses) {
    id
    projectRelation
    businessManagerContact {
      firstName
      lastName
      email
      mobilePhone
      address
      city
      address
      companyName
    }
    assignmentNum
    assignmentType
    assignmentTypeGroup
    ownAssignmentType
    estateId
    status
    activities
    showings
    finnCode
    inspectionDate
    finnExpireDate

    finnPublishDate
    employeeId
    estateType
    estateTypeId
    estateTypeExternal
    employee {
      name
      employeeId
    }
    url
    soldDate
    changedDate
    commissionAcceptedDate
    ownership
    partOwnership
    createdDate
    municipality
    departmentId

    department {
      id
      departmentId
      departmentNumber
      slug
      name
      email
      marketName
      legalName
      organisationNumber
      streetAddress
      postalCode
      phone
      city
    }

    noOfBedRooms
    noOfRooms
    estatePrice
    estatePriceModel {
      priceSuggestion
      soldPrice
      collectiveDebt
      estimatedValue
      totalPrice
    }

    estateSizeModel {
      primaryRoomArea
      usableArea
    }

    areaSize {
      BRAItotal
    }
    sumArea

    brokersIdWithRoles {
      brokerRole
      employee
      employeeId
    }
    checklist {
      changedBy
      changedDate
      firstTag
      tags
      value
    }

    publishStart
    publishEnd

    employee {
      id
      name
      employeeId
      email
      mobilePhone
      title
      slug
      roles {
        source
        typeId
        name
      }
      image {
        small
      }
    }

    links {
      linkType
      text
      url
    }
    address {
      streetAdress
      city
      zipCode
      apartmentNumber
    }
    images {
      imageCategoryName
      imageDescription
      imageSequence
      imageId
      url
    }
    sellers
    sellersEntries {
      contactId
      mainContact
      proxyId
      contact {
        firstName
        lastName
        email
        mobilePhone
        address
        postalCode
        city
        contactType
      }
    }
    hjemUrl
    matrikkel {
      bnr
      gnr
      knr
      snr
      ownPart
    }
    location

    ads {
      id
      finnAdType
      ownAdvertisementType
      link
      channel
    }
    hasAd
    preview
  }
}

query campaigns($estateId: String) {
  campaigns(estateId: $estateId) {
    count
    entries {
      packageName
      marketingPackage
      dateOrdered
      orderStartDate
      orderEndDate
      externalId
    }
  }
}

query estates(
  $statuses: [Int]
  $departmentId: Int
  $price: [Int!]
  $size: [Int!]
) {
  estates(
    statuses: $statuses
    departmentId: $departmentId
    limit: 9999
    price: $price
    size: $size
  ) {
    estatesEntries {
      estateId
      employeeId
      status
      departmentId
      brokersIdWithRoles {
        employeeId
      }
    }
  }
}

query estatesSoldByDepartment($departmentId: Int, $limit: Int) {
  estates(
    departmentId: $departmentId
    statuses: [3]
    limit: $limit
    sortBy: "soldDate"
  ) {
    estatesEntries {
      soldDate
      employeeId
      departmentId
      estateId
      brokersIdWithRoles {
        employeeId
      }
    }
  }
}

query findEstatesForBroker(
  $employeeId: String
  $longitude: Float
  $latitude: Float
  $radius: Float
  $statuses: [Int]
  $sortBy: String
  $pagination: PaginationArgs
  $assignmentTypeGroup: [Int]
  $estateTypeId: [Int]
  $priceRange: [Int]
  $sizeRange: [Int]
) {
  employee(employeeId: $employeeId) {
    estateEntries(
      longitude: $longitude
      latitude: $latitude
      radius: $radius
      statuses: $statuses
      sortBy: $sortBy
      pagination: $pagination
      assignmentTypeGroup: $assignmentTypeGroup
      estateTypeId: $estateTypeId
      priceRange: $priceRange
      sizeRange: $sizeRange
    ) {
      data {
        id
        heading
        estateId
        employeeId
        finnCode
        hjemUrl
        estateType
        estateTypeId
        soldDate
        estatePrice
        estatePriceModel {
          priceSuggestion
          soldPrice
        }
        estateSizeModel {
          primaryRoomArea
          usableArea
        }
        areaSize {
          BRAItotal
        }
        address {
          streetAdress
          city
          zipCode
          apartmentNumber
        }
        images {
          url
        }
        location
        brokersIdWithRoles {
          employeeId
        }
        employeeId
        departmentId
        stats
        noOfBedRooms
      }
    }
  }
}

query findEstates(
  $longitude: Float
  $latitude: Float
  $radius: Float
  $statuses: [Int]
  $sortBy: String
  $baseType: [String!]
  $estateType: [String!]
  $noBedRooms: String
  $size: [Int]
  $price: [Int]
  $limit: Int
  $offset: Int
  $soldDateAfter: Date
  $city: String
) {
  estates(
    longitude: $longitude
    latitude: $latitude
    radius: $radius
    statuses: $statuses
    sortBy: $sortBy
    estateType: $estateType
    price: $price
    size: $size
    noBedRooms: $noBedRooms
    baseType: $baseType
    limit: $limit
    offset: $offset
    soldDateAfter: $soldDateAfter
    city: $city
  ) {
    count
    estatesEntries {
      id
      heading
      estateId
      employeeId
      finnCode
      hjemUrl
      estateType
      estateTypeId
      soldDate
      estatePrice
      estatePriceModel {
        priceSuggestion
        soldPrice
      }
      brokersIdWithRoles {
        employeeId
      }
      employeeId
      estateSizeModel {
        primaryRoomArea
        usableArea
      }
      areaSize {
        BRAItotal
      }
      address {
        streetAdress
        city
        zipCode
        apartmentNumber
      }
      images {
        url
      }
      location
      departmentId
      stats
      noOfBedRooms
    }
  }
}

query estateEmployeeId($estateId: String!) {
  estate(id: $estateId) {
    employeeId
  }
}

query estatesChecklists($estateIds: [String!]!) {
  estatesGroup(ids: $estateIds) {
    estatesEntries {
      estateId
      checklist {
        firstTag
        value
        changedDate
      }
    }
  }
}

query estateChecklist($estateId: String!) {
  estate(id: $estateId, statuses: [0, 1, 2, 3, 4, 5, 6, 7]) {
    checklist {
      firstTag
      value
      changedDate
    }
  }
}
