query Tips(
  $estateId: String
  $originType: Int
  $email: String
  $mobilePhone: String
  $contactId: String
  $createdDateAfter: Date
  $tipId: String
  $status: Int
  $originSource: Int
) {
  tips(
    estateId: $estateId
    originType: $originType
    email: $email
    mobilePhone: $mobilePhone
    contactId: $contactId
    createdDateAfter: $createdDateAfter
    tipId: $tipId
    status: $status
    originSource: $originSource
  ) {
    count
    entries {
      tipId
      estateId
      contactId
      email
      mobilePhone
      firstName
      lastName
      originType
      originSource
      status
      created
      modified
      departmentId
      employeeId
      recipientId
      productId
      source
      postalCode
      streetAdress
      userId
    }
  }
}

query CheckStorebrandTips(
  $estateId: String
  $contactId: String
  $email: String
  $mobilePhone: String
  $createdDateAfter: Date
) {
  tips(
    estateId: $estateId
    contactId: $contactId
    email: $email
    mobilePhone: $mobilePhone
    originType: 4
    createdDateAfter: $createdDateAfter
  ) {
    count
  }
}
