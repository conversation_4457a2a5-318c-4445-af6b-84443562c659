'use server'

import { simpleFetch } from '@/utils/fetching'

export async function fetchNordvikApi<ResponseType>(
  endPoint: string,
  options?: RequestInit & {
    useDedicatedStatsApi?: boolean
  },
) {
  const apiUrl = options?.useDedicatedStatsApi
    ? process.env.NORDVIK_NO_STATS_API_URL
    : process.env.NORDVIK_NO_API_URL

  const headers = new Headers({
    'Content-Type': 'application/json',
    'x-api-key': process.env.NORDVIK_NO_API_KEY!,
    ...options?.headers,
  })

  const url = endPoint.startsWith('http') ? endPoint : `${apiUrl}/${endPoint}`

  return simpleFetch<ResponseType>(url, {
    ...options,
    headers,
  })
}
