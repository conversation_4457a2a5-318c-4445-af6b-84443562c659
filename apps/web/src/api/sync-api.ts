'server only'

import { simpleFetch } from '@/utils/fetching'

const SYNC_API_URL = process.env.SYNC_API_URL
const SYNC_API_KEY = process.env.SYNC_API_KEY

if (!SYNC_API_URL || !SYNC_API_KEY) {
  throw new Error('Missing sync api environment variables')
}

export async function fetchSyncApi<ResponseType>(
  endPoint: string,
  options?: RequestInit,
) {
  const headers = new Headers({
    'Content-Type': 'application/json',
    'x-api-key': SYNC_API_KEY!,
    ...options?.headers,
  })

  const url = endPoint.startsWith('http')
    ? endPoint
    : `${SYNC_API_URL}/${endPoint}`

  return simpleFetch<ResponseType>(url, {
    ...options,
    headers,
  })
}
