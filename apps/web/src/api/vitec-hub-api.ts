import 'server-only'

import { simpleFetch } from '@/utils/fetching'

const VITEC_URL = process.env.VITEC_URL
const VITEC_INSTALLATION_ID = process.env.VITEC_INSTALLATION_ID
const VITEC_USER = process.env.VITEC_USER
const VITEC_PASSWORD = process.env.VITEC_PASSWORD

if (!VITEC_URL || !VITEC_INSTALLATION_ID || !VITEC_USER || !VITEC_PASSWORD) {
  throw new Error('Missing VITEC environment variables')
}

const inFlightGetMap = new Map<string, Promise<unknown>>()

export const vitecHubApiBaseUrl = `${VITEC_URL}/${VITEC_INSTALLATION_ID}`

export async function fetchVitecHubApi<ResponseType>(
  endPoint: string, // example: Estates or https://hub.megler.vitec.net/1234/Estates
  options?: RequestInit,
) {
  const authorization = getVitecHubApiAuth()
  const headers = new Headers({
    Authorization: authorization,
    'Content-Type': 'application/json',
    ...options?.headers,
  })

  const url = endPoint.startsWith('http')
    ? endPoint
    : `${vitecHubApiBaseUrl}/${endPoint}`

  const method = options?.method?.toUpperCase() || 'GET'
  if (method !== 'GET') {
    return simpleFetch<ResponseType>(url, {
      ...options,
      headers,
    })
  }

  if (inFlightGetMap.has(url)) {
    return inFlightGetMap.get(url) as Promise<ResponseType>
  }

  const promise = simpleFetch<ResponseType>(url, {
    ...options,
    headers,
  }).finally(() => inFlightGetMap.delete(url))

  inFlightGetMap.set(url, promise)
  return promise
}

export function getVitecHubApiAuth() {
  const user = VITEC_USER
  const password = VITEC_PASSWORD
  return `Basic ${btoa(`${user}:${password}`)}`
}
