export enum EmailEventType {
  OPENED = 'email_opened',
  BOUNCED = 'email_bounced',
  REJECTED = 'email_rejected',
  FAILED = 'email_failed',
}

/**
 * Converts a string to EmailEventType enum with fallback
 */
export function toEmailEventType(eventType: string): EmailEventType {
  switch (eventType) {
    case 'email_opened':
      return EmailEventType.OPENED
    case 'email_bounced':
      return EmailEventType.BOUNCED
    case 'email_rejected':
      return EmailEventType.REJECTED
    case 'email_failed':
      return EmailEventType.FAILED
    default:
      // Default fallback for unknown event types
      return EmailEventType.FAILED
  }
}
