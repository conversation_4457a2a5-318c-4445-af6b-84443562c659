import type { DefaultSession } from 'next-auth'

declare module 'next-auth' {
  interface Session {
    id: number
    user: {
      id: string
      email?: string
      isImpersonated: boolean
      impersonatedByUserId: string
      role: string
      image?: string
      employeeId?: string
      department?: {
        departmentId?: number
        name?: string
        id?: string
      }
    } & DefaultSession['user']
  }
}
