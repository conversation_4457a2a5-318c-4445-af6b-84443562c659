#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

/**
 * Bundle Analysis Script
 * 
 * This script analyzes the Next.js bundle and provides insights about:
 * - Bundle sizes
 * - Largest chunks
 * - Potential optimizations
 * - Size comparisons with previous builds
 */

const BUNDLE_ANALYSIS_DIR = '.next/analyze'
const PREVIOUS_ANALYSIS_FILE = path.join(BUNDLE_ANALYSIS_DIR, 'previous-analysis.json')
const CURRENT_ANALYSIS_FILE = path.join(BUNDLE_ANALYSIS_DIR, 'current-analysis.json')

// Ensure analysis directory exists
if (!fs.existsSync(BUNDLE_ANALYSIS_DIR)) {
  fs.mkdirSync(BUNDLE_ANALYSIS_DIR, { recursive: true })
}

function getFileSizeInKB(filePath) {
  try {
    const stats = fs.statSync(filePath)
    return Math.round(stats.size / 1024 * 100) / 100
  } catch (error) {
    return 0
  }
}

function analyzeBundleFiles() {
  const staticDir = '.next/static'
  const analysis = {
    timestamp: new Date().toISOString(),
    totalSize: 0,
    jsFiles: [],
    cssFiles: [],
    chunks: []
  }

  if (!fs.existsSync(staticDir)) {
    console.error('❌ No .next/static directory found. Run `npm run build` first.')
    process.exit(1)
  }

  // Analyze JS files
  const jsDir = path.join(staticDir, 'js')
  if (fs.existsSync(jsDir)) {
    const jsFiles = fs.readdirSync(jsDir)
    jsFiles.forEach(file => {
      if (file.endsWith('.js')) {
        const filePath = path.join(jsDir, file)
        const size = getFileSizeInKB(filePath)
        analysis.jsFiles.push({ name: file, size })
        analysis.totalSize += size
      }
    })
  }

  // Analyze CSS files
  const cssDir = path.join(staticDir, 'css')
  if (fs.existsSync(cssDir)) {
    const cssFiles = fs.readdirSync(cssDir)
    cssFiles.forEach(file => {
      if (file.endsWith('.css')) {
        const filePath = path.join(cssDir, file)
        const size = getFileSizeInKB(filePath)
        analysis.cssFiles.push({ name: file, size })
        analysis.totalSize += size
      }
    })
  }

  // Sort by size (largest first)
  analysis.jsFiles.sort((a, b) => b.size - a.size)
  analysis.cssFiles.sort((a, b) => b.size - a.size)

  return analysis
}

function compareWithPrevious(current) {
  if (!fs.existsSync(PREVIOUS_ANALYSIS_FILE)) {
    console.log('📊 No previous analysis found. This will be the baseline.')
    return null
  }

  try {
    const previous = JSON.parse(fs.readFileSync(PREVIOUS_ANALYSIS_FILE, 'utf8'))
    const comparison = {
      totalSizeDiff: current.totalSize - previous.totalSize,
      totalSizePercent: ((current.totalSize - previous.totalSize) / previous.totalSize * 100).toFixed(2),
      jsFilesDiff: current.jsFiles.length - previous.jsFiles.length,
      cssFilesDiff: current.cssFiles.length - previous.cssFiles.length
    }
    return { previous, comparison }
  } catch (error) {
    console.warn('⚠️  Could not parse previous analysis file')
    return null
  }
}

function generateReport(analysis, comparisonData) {
  console.log('\n🔍 Bundle Analysis Report')
  console.log('=' .repeat(50))
  
  console.log(`\n📦 Total Bundle Size: ${analysis.totalSize.toFixed(2)} KB`)
  console.log(`📅 Analysis Date: ${new Date(analysis.timestamp).toLocaleString()}`)
  
  if (comparisonData) {
    const { comparison } = comparisonData
    const sizeChange = comparison.totalSizeDiff > 0 ? '📈' : '📉'
    const changeColor = comparison.totalSizeDiff > 0 ? '\x1b[31m' : '\x1b[32m' // Red for increase, green for decrease
    const resetColor = '\x1b[0m'
    
    console.log(`${sizeChange} Size Change: ${changeColor}${comparison.totalSizeDiff > 0 ? '+' : ''}${comparison.totalSizeDiff.toFixed(2)} KB (${comparison.totalSizePercent}%)${resetColor}`)
  }

  // Top 5 largest JS files
  console.log('\n🟨 Largest JavaScript Files:')
  analysis.jsFiles.slice(0, 5).forEach((file, index) => {
    console.log(`  ${index + 1}. ${file.name} - ${file.size} KB`)
  })

  // Top 5 largest CSS files
  if (analysis.cssFiles.length > 0) {
    console.log('\n🟦 Largest CSS Files:')
    analysis.cssFiles.slice(0, 5).forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.name} - ${file.size} KB`)
    })
  }

  // Warnings for large files
  const largeJsFiles = analysis.jsFiles.filter(file => file.size > 500)
  const largeCssFiles = analysis.cssFiles.filter(file => file.size > 100)
  
  if (largeJsFiles.length > 0 || largeCssFiles.length > 0) {
    console.log('\n⚠️  Large Files Detected:')
    largeJsFiles.forEach(file => {
      console.log(`  🔴 ${file.name} (${file.size} KB) - Consider code splitting`)
    })
    largeCssFiles.forEach(file => {
      console.log(`  🔴 ${file.name} (${file.size} KB) - Consider CSS optimization`)
    })
  }

  // Recommendations
  console.log('\n💡 Optimization Recommendations:')
  if (analysis.totalSize > 1000) {
    console.log('  • Consider implementing dynamic imports for large components')
    console.log('  • Review and remove unused dependencies')
  }
  if (analysis.jsFiles.length > 20) {
    console.log('  • Consider consolidating smaller chunks')
  }
  if (largeJsFiles.length > 0) {
    console.log('  • Implement code splitting for large JavaScript files')
  }
  
  console.log('\n📋 Commands to investigate further:')
  console.log('  • npm run build:analyze - Open interactive bundle analyzer')
  console.log('  • npm run bundle:size - Check against size limits')
  
  console.log('\n' + '='.repeat(50))
}

function savePreviousAnalysis(analysis) {
  // Move current to previous
  if (fs.existsSync(CURRENT_ANALYSIS_FILE)) {
    fs.copyFileSync(CURRENT_ANALYSIS_FILE, PREVIOUS_ANALYSIS_FILE)
  }
  
  // Save current analysis
  fs.writeFileSync(CURRENT_ANALYSIS_FILE, JSON.stringify(analysis, null, 2))
}

function main() {
  console.log('🚀 Starting bundle analysis...')
  
  const analysis = analyzeBundleFiles()
  const comparisonData = compareWithPrevious(analysis)
  
  generateReport(analysis, comparisonData)
  savePreviousAnalysis(analysis)
  
  // Exit with error code if bundle is too large
  const MAX_TOTAL_SIZE = 2000 // 2MB
  if (analysis.totalSize > MAX_TOTAL_SIZE) {
    console.error(`\n❌ Bundle size (${analysis.totalSize} KB) exceeds maximum allowed size (${MAX_TOTAL_SIZE} KB)`)
    process.exit(1)
  }
  
  console.log('\n✅ Bundle analysis completed successfully!')
}

if (require.main === module) {
  main()
}

module.exports = { analyzeBundleFiles, generateReport }
