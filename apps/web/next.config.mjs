import bundleAnalyzer from '@next/bundle-analyzer'
import { withSentryConfig } from '@sentry/nextjs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
})

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  outputFileTracingRoot: path.join(__dirname, '../../'),
  output: 'standalone',
  experimental: {
    reactCompiler: true,
    serverActions: {
      bodySizeLimit: '10mb',
    },
  },
  serverExternalPackages: ['pdf-parse'],
  turbopack: {
    rules: {
      '*.graphql': {
        loaders: ['raw-loader'],
        as: '*.js',
      },
    },
    root: path.join(__dirname, '../..'),
  },
  transpilePackages: ['@nordvik/ui', '@nordvik/utils', '@nordvik/database'],
  async headers() {
    return [
      {
        // matching all API routes
        // https://vercel.com/guides/how-to-enable-cors
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: '*',
          },
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
        ],
      },
      {
        source: '/.well-known/apple-app-site-association',
        headers: [{ key: 'content-type', value: 'application/json' }],
      },
    ]
  },

  async redirects() {
    return [
      {
        source: '/oppdrag',
        destination: '/oppdrag/status/innsalg',
        permanent: true,
      },
      {
        source: '/news/hjem-no',
        destination: '/hjelpesenter/hjem-no',
        permanent: true,
      },
      {
        source: '/news/drift-og-status-next/:subpage*',
        destination: '/driftsmeldinger',
        permanent: true,
      },
      {
        source: '/news/toppliste-v2/:subpage*',
        destination: '/toppliste',
        permanent: true,
      },
      {
        source: '/befaring',
        destination: '/api/old-megler-redirect?module=befaring',
        permanent: false,
      },
      {
        source: '/verdivurdering/:estateId/start',
        destination: '/verdivurdering/:estateId/din-bolig',
        permanent: true,
      },
      {
        source: '/verdivurdering/:estateId/suksess',
        destination: '/verdivurdering/:estateId/din-bolig?success=true',
        permanent: true,
      },
    ]
  },
  async rewrites() {
    return [
      {
        // Rewrite all short url routes to /api
        source: '/l/:path*',
        destination: '/api/url/:path*',
      },
    ]
  },
  images: {
    // cache images for 30 minutes
    minimumCacheTTL: 60 * 30,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.cloudfront.net',
      },
      {
        protocol: 'https',
        hostname: 'nordvik-vitec-images.*.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 's3.eu-west-1.amazonaws.com',
      },
      // Remove once we can remove the news mock data
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
      {
        protocol: 'https',
        hostname: 'nordvik-prod.s3.eu-north-1.amazonaws.com',
        pathname: '/broker-partner/**',
      },
      {
        protocol: 'https',
        hostname: 'nordvik-omega.s3.eu-north-1.amazonaws.com',
        pathname: '/**',
      },
    ],
  },
  webpack: (config) => {
    config.module.rules.push({
      test: /\.(graphql|gql)$/,
      exclude: /node_modules/,
      loader: 'graphql-tag/loader',
    })
    return config
  },
}

export default withBundleAnalyzer(
  withSentryConfig(nextConfig, {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options
    authToken: process.env.SENTRY_AUTH_TOKEN,

    org: 'nordvik',
    project: 'nordvik-megler',

    // Only print logs for uploading source maps in CI
    silent: !process.env.CI,

    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    // tunnelRoute: "/monitoring",

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: true,
  }),
)
