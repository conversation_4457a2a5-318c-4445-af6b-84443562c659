'use client'

import * as DialogPrimitive from '@radix-ui/react-dialog'
import { X } from 'lucide-react'
import * as React from 'react'

import { cn } from '@nordvik/theme/cn'

import { ScrollArea } from './scroll-area'

export const Dialog = DialogPrimitive.Root

export const DialogTrigger = DialogPrimitive.Trigger

export const DialogPortal = DialogPrimitive.Portal

export const DialogClose = DialogPrimitive.Close

export const DialogOverlay = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    className={cn(
      'fixed inset-0 z-50 bg-[black] opacity-50 duration-500 animate-in fade-in-0',
      className,
    )}
    ref={ref}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

// TODO You can't compose content with overlay and portal, then it's not composable for others
// @deprecated Use DialogContent from dialog instead
export const DialogContent = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
    classNameWrapper?: string
    withCloseButton?: boolean
    closeOnClickOutside?: boolean
    unpadded?: boolean
    rounded?: boolean
    classNameScrollArea?: string
  }
>(
  (
    {
      className,
      classNameWrapper,
      classNameScrollArea,
      children,
      withCloseButton = true,
      closeOnClickOutside = true,
      unpadded = false,
      rounded = true,
      title,
      ...props
    },
    ref,
  ) => (
    <DialogPortal>
      <DialogOverlay />
      <DialogPrimitive.Content
        className={cn(
          'fixed top-1/2 left-1/2 translate-x-[-50%] translate-y-[-50%] z-50 grid w-full max-w-screen-sm  bg-root',
          'darky:outline-muted darky:outline-1 darky:outline overflow-clip',
          rounded && 'sm:rounded-lg',
          className,
        )}
        ref={ref}
        onInteractOutside={(e) => {
          if (!closeOnClickOutside) {
            e.preventDefault()
          }
        }}
        {...props}
      >
        {title && (
          <DialogPrimitive.Title className="sr-only">
            {title}
          </DialogPrimitive.Title>
        )}
        <ScrollArea
          className={cn(
            'max-h-dvh sm:max-h-[calc(100dvh-4rem)] md::max-h-[calc(100dvh-8rem)]',
            rounded && 'sm:rounded-md',
            classNameScrollArea,
          )}
        >
          <div className={cn('h-full', !unpadded && 'p-6', classNameWrapper)}>
            {children}
          </div>
        </ScrollArea>
        {withCloseButton && (
          <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background-root-muted transition-opacity focus:outline-none focus:ring-2 focus:ring-focus focus:ring-offset-2 hover:opacity-100 disabled:pointer-events-none">
            <X className="size-5" />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        )}
      </DialogPrimitive.Content>
    </DialogPortal>
  ),
)
DialogContent.displayName = DialogPrimitive.Content.displayName

export function DialogHeader({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        'flex flex-col space-y-1.5 text-center sm:text-left',
        className,
      )}
      {...props}
    />
  )
}
DialogHeader.displayName = 'DialogHeader'

export function DialogFooter({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        'flex mt-6 flex-col-reverse sm:flex-row sm:justify-end gap-x-2 gap-y-4',
        className,
      )}
      {...props}
    />
  )
}
DialogFooter.displayName = 'DialogFooter'

export const DialogTitle = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title> & {
    size?: 'md' | 'sm'
  }
>(({ className, size = 'default', ...props }, ref) => (
  <DialogPrimitive.Title
    className={cn(
      'typo-title-md mb-4',
      size === 'sm' && 'typo-title-sm',
      className,
    )}
    ref={ref}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

export const DialogDescription = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    className={cn('typo-body-md text-pretty ink-default', className)}
    ref={ref}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName
