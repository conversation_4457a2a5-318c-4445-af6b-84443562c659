import { XIcon } from 'lucide-react'
import * as React from 'react'

import { cn } from '@nordvik/theme/cn'

import { Button } from './button'

export type DialogContainerProps = React.HTMLAttributes<HTMLDivElement> & {
  size?: 'sm' | 'md' | 'lg'
  ref?: React.RefObject<HTMLDivElement | null>
}

export function DialogContainer({
  children,
  size = 'md',
  className,
  ...rest
}: DialogContainerProps) {
  return (
    <div
      className={cn(
        'bg-float outline outline-[0.5px] darky:outline-1 outline-muted overflow-clip rounded-lg shadow-xl w-full relative',
        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
        {
          sm: 'max-w-[28rem] [--dialog-padding-x:1rem]',
          md: 'max-w-[37.5rem] [--dialog-padding-x:1.25rem]',
          lg: 'max-w-[56rem] [--dialog-padding-x:1.5rem]',
        }[size],
        className,
      )}
      {...rest}
    >
      {children}
    </div>
  )
}

type DialogHeaderProps = React.HTMLAttributes<HTMLDivElement> & {
  title: React.ReactNode
  subtitle?: React.ReactNode
  ref?: React.RefObject<HTMLDivElement | null>
}

export function DialogHeader({
  title,
  subtitle,
  className,
  ...rest
}: DialogHeaderProps) {
  return (
    <div
      className={cn(
        'flex flex-col mr-5 px-[--dialog-padding-x] py-4',
        className,
      )}
      {...rest}
    >
      {/* We set tabIndex to make the title the first thing focused when opening the dialog. Prevents ugly focus rings */}
      <h1 tabIndex={0} className="typo-title-sm focus:outline-none text-pretty">
        {title}
      </h1>{' '}
      {subtitle && <p className="typo-body-sm ink-subtle">{subtitle}</p>}
    </div>
  )
}

type DialogCloseButtonProps = React.HTMLAttributes<HTMLButtonElement> & {
  ref?: React.RefObject<HTMLButtonElement | null>
}

export function DialogCloseButton({
  children,
  className,
  ...rest
}: DialogCloseButtonProps) {
  return (
    <Button
      size="sm"
      variant="ghost"
      tooltip={false}
      iconOnly={<XIcon />}
      className={cn(
        'absolute top-3 -mt-px transition-all opacity-40 hover:opacity-100 right-[--dialog-padding-x] -mr-2',
        className,
      )}
      {...rest}
    >
      {children}
    </Button>
  )
}

export type DialogDescriptionProps = React.HTMLAttributes<HTMLDivElement> & {
  ref?: React.RefObject<HTMLDivElement | null>
  divider?: boolean
}

export function DialogDescription({
  children,
  className,
  divider,
  ...rest
}: DialogDescriptionProps) {
  const localRef = React.useRef<HTMLDivElement>(null)

  // Track content overflow to show/hide scroll indicators
  React.useLayoutEffect(() => {
    const element = localRef.current
    if (!element) return

    const checkOverflow = () => {
      element.dataset.overflow = (
        element.scrollHeight > element.clientHeight
      ).toString()
    }

    checkOverflow()
    const resizeObserver = new ResizeObserver(checkOverflow)
    resizeObserver.observe(element)

    return () => resizeObserver.disconnect()
  }, [])

  return (
    <div
      ref={localRef}
      onScroll={(event) => {
        const atStart = event.currentTarget.scrollTop <= 0
        const atScrollBottom =
          event.currentTarget.scrollHeight -
            event.currentTarget.scrollTop -
            event.currentTarget.clientHeight <
          1
        event.currentTarget.dataset.scrolled = (!atStart).toString()
        event.currentTarget.dataset.scrolledBottom = atScrollBottom.toString()
      }}
      className={cn(
        'typo-body-md overflow-y-auto px-[--dialog-padding-x] subtle-scrollbar pb-[--dialog-padding-x]',
        'subtle-scrollbar focus-visible:outline-none',
        'border-y -my-px border-y-transparent transition-[border-color] duration-200',
        divider
          ? 'border-y-muted pt-[--dialog-padding-x] last:border-b-transparent'
          : cn(
              'data-[scrolled-bottom=true]:border-b-transparent',
              'data-[overflow=true]:border-b-muted',
              'data-[scrolled=true]:border-t-muted',
            ),
        className,
      )}
      {...rest}
    >
      {children}
    </div>
  )
}

type DialogFooterProps = React.HTMLAttributes<HTMLDivElement> & {
  secondary?: React.ReactNode
  ref?: React.RefObject<HTMLDivElement | null>
}

export function DialogFooter({
  children,
  className,
  secondary,
  ...rest
}: DialogFooterProps) {
  return (
    <div
      className={cn(
        'flex justify-end gap-1 px-[--dialog-padding-x] py-4',
        className,
      )}
      {...rest}
    >
      {secondary && <div className="flex-1">{secondary}</div>}
      {children}
    </div>
  )
}
