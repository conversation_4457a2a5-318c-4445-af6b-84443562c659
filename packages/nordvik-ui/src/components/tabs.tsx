'use client'

import * as TabsPrimitive from '@radix-ui/react-tabs'
import * as React from 'react'

import { cn } from '@nordvik/theme/cn'

export const Tabs = TabsPrimitive.Root

export const TabsList = React.forwardRef<
  React.ComponentRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> & {
    border?: boolean
  }
>(({ className, children, border = true, ...props }, ref) => {
  return (
    <TabsPrimitive.List
      className={cn(
        'overflow-x-auto flex max-w-full hide-scrollbar',
        className,
      )}
      ref={ref}
      {...props}
    >
      <div
        className={cn(
          'inline-flex grow items-center gap-6',
          border &&
            'relative after:absolute after:inset-x-0 after:bottom-0 after:block after:border-b after:border-muted',
        )}
      >
        {children}
      </div>
    </TabsPrimitive.List>
  )
})

TabsList.displayName = TabsPrimitive.List.displayName

export const TabsTrigger = React.forwardRef<
  React.ComponentRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    className={cn(
      // Base Styles
      'typo-label-lg relative inline-flex h-9 pb-2.5 items-center whitespace-nowrap',
      'after:absolute after:bottom-0 after:block',
      'after:inset-x-0 after:border-b-[3px]',

      // Inactive State
      'data-[state=inactive]:ink-subtle hover:data-[state=inactive]:ink-default',
      'after:data-[state=inactive]:border-[transparent] hover:after:data-[state=inactive]:border-subtle',

      // Active State
      'data-[state=active]:font-medium data-[state=active]:ink-default',
      'after:data-[state=active]:border-stroke-gold-emphasis',

      // Accessibility and Interaction Styles
      'focus-visible:outline-none focus-visible:ring-2 ring-offset-background-root rounded-sm',
      'focus-visible:ring-emphasis focus-visible:ring-offset-0 ring-inset',
      'disabled:pointer-events-none disabled:opacity-50',
      className,
    )}
    ref={ref}
    {...props}
  />
))

TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

export const TabsContent = React.forwardRef<
  React.ComponentRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    className={cn(
      'mt-2 ring-offset-background-root focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-8',
      className,
    )}
    ref={ref}
    {...props}
  />
))

TabsContent.displayName = TabsPrimitive.Content.displayName
