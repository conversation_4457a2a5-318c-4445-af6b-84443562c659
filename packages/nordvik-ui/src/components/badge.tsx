import { type VariantProps, cva } from 'class-variance-authority'
import * as React from 'react'

import { cn } from '@nordvik/theme/cn'

export const _variants = [
  'light-green',
  'beige',
  'red',
  'dark-green',
  'blue',
  'grey',
  'bright-green',
  'outline',
] as const

export const sizes = ['md', 'lg'] as const

export const badgeVariants = cva(
  cn(
    ' typo-label-sm inline-flex items-center whitespace-nowrap rounded-sm ink-default',
  ),
  {
    variants: {
      variant: {
        beige: cn('bg-gold-subtle ink-on-gold'),
        red: cn('bg-danger-subtle ink-on-danger-subtle-emphasis'),
        'dark-green': cn('bg-brand-subtle ink-on-brand'),
        blue: cn('bg-info-subtle ink-on-info'),
        grey: cn('bg-gray-subtle ink-on-gray-subtle'),
        'bright-green': cn('bg-success-subtle ink-on-success'),
        'light-green': cn('bg-light-green-subtle ink-on-light-green-subtle'),
        outline: cn('border-stroke-muted border ink-default'),
      },
      size: {
        md: 'px-[6px] py-[2px]',
        lg: 'px-[8px] py-[5.5px]',
      },
    },
    defaultVariants: {
      variant: 'bright-green',
      size: 'md',
    },
  },
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

export function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div
      className={cn(badgeVariants({ variant, size }), className)}
      {...props}
    />
  )
}
