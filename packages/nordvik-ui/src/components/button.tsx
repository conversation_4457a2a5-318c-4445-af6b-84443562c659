'use client'

import { type VariantProps, cva } from 'class-variance-authority'
import { Loader2 } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import * as Tooltip from '@nordvik/ui/tooltip'

import { Link, type LinkProps } from './global-navigation-progress/link'

export const variants = {
  variant: {
    default: cn(
      'bg-button-primary-fill text-button-primary-ink hover:bg-button-primary-fill-hover disabled:bg-button-primary-fill-disabled disabled:text-button-primary-ink-disabled',
    ),
    outline: cn(
      'border border-emphasis bg-transparent text-button-secondary-ink hover:border-button-secondary-border-hover hover:bg-transparent',
      'disabled:border-button-secondary-border-disabled disabled:text-button-secondary-ink-disabled disabled:hover:border-button-secondary-border-hover',
    ),
    tertiary: cn(
      'bg-button-tertiary-fill ink-default hover:bg-button-tertiary-fill-hover disabled:bg-button-tertiary-fill-disabled disabled:text-button-tertiary-ink-disabled',
    ),
    gold: cn(
      'bg-button-gold-fill text-button-gold-ink hover:bg-button-gold-fill-hover disabled:bg-button-gold-fill-disabled disabled:text-button-gold-ink-disabled',
    ),
    ghost: cn(
      'text-button-ghost-ink hover:bg-button-ghost-fill-hover disabled:text-button-ghost-ink-disabled',
    ),
    unstyled: cn('bg-transparent ink-default'),
    'unstyled-for-real': cn(''),
  },
  square: {
    true: cn('rounded-full aspect-square !px-0'),
    false: '',
  },
  size: {
    xs: cn('typo-label-sm [--icon-size:0.75rem] px-3 h-6'),
    sm: cn('typo-label-md [--icon-size:1rem] px-4 h-8'),
    md: cn('typo-label-md [--icon-size:1rem] px-5 h-9'),
    lg: cn('typo-label-lg [--icon-size:1.25rem] px-6 h-11'),
  },
}

export const createButtonClassName = cva(
  'flex items-center disabled:cursor-default justify-center gap-1.5 whitespace-nowrap rounded-full font-grotesk font-medium ring-offset-background-root transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-focus focus-visible:ring-offset-2',
  { variants },
)

type ButtonVariants = VariantProps<typeof createButtonClassName>

type BaseButtonProps = {
  ref?: React.Ref<HTMLButtonElement | HTMLLinkElement>
  variant?: ButtonVariants['variant']
  size: ButtonVariants['size']
  tooltip?: React.ReactNode
  tooltipPosition?: Tooltip.TooltipPosition
  loading?: boolean
} & (
  | {
      iconEnd?: React.ReactElement | null
      iconStart?: React.ReactElement | null
      iconOnly?: never
    }
  | {
      iconEnd?: never
      iconStart?: never
      iconOnly?: React.ReactElement | null
    }
)

export type ButtonLinkProps = BaseButtonProps &
  ({ href: string } & React.ComponentPropsWithoutRef<'a'> & LinkProps)

export type ButtonButtonProps = BaseButtonProps &
  ({ href?: never } & React.ComponentPropsWithoutRef<'button'>)

export type ButtonProps =
  | ButtonLinkProps
  | (ButtonButtonProps & {
      onClick?: React.MouseEventHandler<HTMLButtonElement | HTMLAnchorElement>
    })

export function Button({
  children,
  variant = 'default',
  className,
  iconStart,
  iconEnd,
  iconOnly,
  size,
  loading,
  tooltip,
  tooltipPosition,
  ref,
  ...rest
}: ButtonProps) {
  const buttonClassName = cn(
    createButtonClassName({ variant, size, square: Boolean(iconOnly) }),
    className,
  )

  const inner = (
    <InnerButton
      loading={loading}
      iconStart={iconStart}
      iconEnd={iconEnd}
      iconOnly={iconOnly}
    >
      {children}
    </InnerButton>
  )

  const hasHref = 'href' in rest && !!rest.href

  if (!hasHref) {
    delete (rest as ButtonButtonProps).href
  }

  const outer =
    'href' in rest ? (
      <Link
        className={buttonClassName}
        ref={ref as React.ForwardedRef<HTMLAnchorElement>}
        {...(rest as React.ComponentPropsWithoutRef<'a'> & LinkProps)}
      >
        {inner}
      </Link>
    ) : (
      <button
        type="button"
        className={buttonClassName}
        disabled={rest.disabled ?? loading}
        ref={ref as React.ForwardedRef<HTMLButtonElement>}
        aria-busy={loading}
        {...rest}
      >
        {inner}
      </button>
    )

  const tooltipText = tooltip ?? (iconOnly ? children : undefined)

  if (tooltipText) {
    return (
      <Tooltip.Tooltip>
        <Tooltip.TooltipTrigger asChild>{outer}</Tooltip.TooltipTrigger>
        <Tooltip.TooltipPortal>
          <Tooltip.TooltipContent side={tooltipPosition}>
            {tooltipText}
          </Tooltip.TooltipContent>
        </Tooltip.TooltipPortal>
      </Tooltip.Tooltip>
    )
  }

  return outer
}

function InnerButton({
  children,
  loading,
  iconStart,
  iconEnd,
  iconOnly,
}: Pick<
  ButtonProps,
  'iconStart' | 'iconEnd' | 'iconOnly' | 'loading' | 'children'
>) {
  return (
    <span className="relative inline-flex">
      <span
        className={cn(
          'inline-flex items-center gap-1.5 transition-opacity duration-75',
          loading && 'opacity-0',
        )}
      >
        {iconOnly ? (
          <>
            <span className="size-[--icon-size] [&_>*]:size-full">
              {iconOnly}
            </span>
            <span className="sr-only">{children}</span>
          </>
        ) : (
          <>
            {iconStart && (
              <span className="size-[--icon-size] [&_>*]:size-full">
                {iconStart}
              </span>
            )}
            {children}
            {iconEnd && (
              <span className="size-[--icon-size] [&_>*]:size-full">
                {iconEnd}
              </span>
            )}
          </>
        )}
      </span>
      {loading && (
        <div className="absolute inset-0 flex justify-center items-center animate-in fade-in">
          <Loader2
            data-chromatic="ignore"
            className="size-[1.2em] animate-spin"
          />
        </div>
      )}
    </span>
  )
}
