{"name": "@nordvik/database", "type": "module", "version": "1.0.0", "exports": {".": "./src/index.ts"}, "scripts": {"db:migrate:deploy": "prisma migrate deploy", "db:migrate:dev": "prisma migrate dev", "format": "prisma format", "generate": "dotenv -e .env -e .env.local -- prisma generate", "lint": "eslint . --max-warnings 0", "prisma:generate": "dotenv -e .env -e .env.local -- prisma generate"}, "dependencies": {"@prisma/adapter-pg": "6.16.0", "@prisma/client": "6.16.0"}, "devDependencies": {"prisma": "6.16.0", "typescript": "5.8.3", "dotenv": "17.2.1"}}