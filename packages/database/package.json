{"name": "@nordvik/database", "type": "module", "version": "1.0.0", "exports": {".": "./src/index.ts"}, "scripts": {"db:migrate:deploy": "prisma migrate deploy", "db:migrate:dev": "prisma migrate dev", "format": "prisma format", "generate": "dotenv -e .env -e .env.local -- prisma generate", "lint": "eslint . --ext .ts,.tsx --max-warnings 0", "prisma:generate": "dotenv -e .env -e .env.local -- prisma generate"}, "dependencies": {"@nordvik/eslint-config": "workspace:*", "@prisma/adapter-pg": "6.16.3", "@prisma/client": "6.16.3"}, "devDependencies": {"prisma": "6.16.3", "eslint": "8.57.0", "typescript": "5.9.3", "dotenv": "17.2.1"}}