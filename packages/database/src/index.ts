import { PrismaPg } from '@prisma/adapter-pg'

import { PrismaClient } from './generated/prisma/client'

export type GetDbParams = {
  connectionString: string
}

export function getDb({ connectionString }: GetDbParams): PrismaClient {
  const pool = new PrismaPg({
    connectionString,
    // Accept self-signed certificates (similar to Sequelize's rejectUnauthorized: false)
    ssl: { rejectUnauthorized: false },
  })
  const prisma = new PrismaClient({
    adapter: pool,
    transactionOptions: { maxWait: 10000, timeout: 15000 },
  })

  return prisma
}

export * from './generated/prisma/client'
export type { Prisma } from './generated/prisma/client'
export { Decimal } from './generated/prisma/internal/prismaNamespace'
