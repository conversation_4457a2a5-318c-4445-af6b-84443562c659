-- CreateTable
CREATE TABLE "company_sign_rights" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "company_contact_id" UUID NOT NULL,
    "listing_agreement_id" UUID NOT NULL,
    "signers" TEXT[],
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ(6),

    CONSTRAINT "company_sign_rights_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "company_sign_rights_company_contact_id_listing_agreement_id_key" ON "company_sign_rights"("company_contact_id", "listing_agreement_id");

-- Add<PERSON><PERSON>ign<PERSON><PERSON>
ALTER TABLE "company_sign_rights" ADD CONSTRAINT "company_sign_rights_listing_agreement_id_fkey" FOREIGN KEY ("listing_agreement_id") REFERENCES "listing_agreements"("id") ON DELETE CASCADE ON UPDATE CASCADE;
