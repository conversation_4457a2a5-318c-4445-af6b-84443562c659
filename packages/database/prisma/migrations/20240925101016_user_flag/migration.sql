-- CreateTable
CREATE TABLE "user_flag" (
    "id" SERIAL NOT NULL,
    "userId" UUID NOT NULL,
    "flagKey" TEXT NOT NULL,
    "flagValue" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_flag_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_flag_userId_flagKey_idx" ON "user_flag"("userId", "flagKey");

-- CreateIndex
CREATE UNIQUE INDEX "user_flag_userId_flagKey_key" ON "user_flag"("userId", "flagKey");
