/*
  Warnings:

  - You are about to drop the column `employee_id` on the `broker_partner` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[employeeId]` on the table `magic_link_users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `userId` to the `broker_partner` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "broker_partner" DROP COLUMN "employee_id",
ADD COLUMN     "userId" UUID NOT NULL;

-- CreateTable
CREATE TABLE "broker_profile_links" (
    "id" SERIAL NOT NULL,
    "urls" TEXT[],
    "userId" UUID NOT NULL,

    CONSTRAINT "broker_profile_links_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "broker_profile_links_userId_key" ON "broker_profile_links"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "magic_link_users_employeeId_key" ON "magic_link_users"("employeeId");

-- AddForeignKey
ALTER TABLE "broker_profile_links" ADD CONSTRAINT "broker_profile_links_userId_fkey" FOREIGN KEY ("userId") REFERENCES "magic_link_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_partner" ADD CONSTRAINT "broker_partner_userId_fkey" FOREIGN KEY ("userId") REFERENCES "magic_link_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
