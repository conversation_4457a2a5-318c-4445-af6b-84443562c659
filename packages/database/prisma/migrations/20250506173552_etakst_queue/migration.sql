-- CreateTable
CREATE TABLE "etakst_check_queue" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_checked_at" TIMESTAMPTZ(6),
    "check_count" INTEGER NOT NULL DEFAULT 0,
    "is_complete" BOOLEAN NOT NULL DEFAULT false,
    "completed_at" TIMESTAMPTZ(6),
    "document_id" TEXT,
    CONSTRAINT "etakst_check_queue_pkey" PRIMARY KEY ("id")
);
-- CreateIndex
CREATE UNIQUE INDEX "etakst_check_queue_estate_id_key" ON "etakst_check_queue"("estate_id");