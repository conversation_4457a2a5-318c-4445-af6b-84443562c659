-- CreateTable
CREATE TABLE "listing_agreement_interactions" (
    "id" SERIAL NOT NULL,
    "listing_agreements_id" UUID NOT NULL,
    "seller_id" TEXT,
    "employee_id" TEXT,
    "event_type" TEXT NOT NULL,
    "event_timestamp" TIMESTAMPTZ(6) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "extra_data" JSONB,

    CONSTRAINT "listing_agreement_interactions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "listing_agreement_interactions_listing_agreements_id_seller_key" ON "listing_agreement_interactions"("listing_agreements_id", "seller_id", "event_timestamp");

-- AddForeignKey
ALTER TABLE "listing_agreement_interactions" ADD CONSTRAINT "listing_agreement_interactions_listing_agreements_id_fkey" FOREIGN KEY ("listing_agreements_id") REFERENCES "listing_agreements"("id") ON DELETE CASCADE ON UPDATE CASCADE;
