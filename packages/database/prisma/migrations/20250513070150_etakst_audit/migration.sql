-- CreateTable
CREATE TABLE "etakst_audit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "document_id" TEXT,
    "employee_id" TEXT,
    "seller_id" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notification_type" VARCHAR(255),
    "is_successful" BOOLEAN NOT NULL DEFAULT false,
    "data" JSONB,
    "response" JSONB,

    CONSTRAINT "etakst_audit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "etakst_audit_estate_id_idx" ON "etakst_audit"("estate_id");
