-- CreateTable
CREATE TABLE "seller_interview_submissions" (
    "id" BIGSERIAL PRIMARY KEY,
    "form_version" INT NOT NULL,
    "user_id" UUID,
    "metadata" JSONB NOT NULL DEFAULT '{}'::jsonb,
    "answers" JSONB NOT NULL,
    "submitted_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "user_agent" TEXT,
    "ip_address" INET,
    CONSTRAINT "chk_answers_is_object" CHECK (jsonb_typeof(answers) = 'object')
);

-- Foreign key added separately to match existing style
ALTER TABLE "seller_interview_submissions" ADD CONSTRAINT "seller_interview_submissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Index on submitted_at (descending)
CREATE INDEX "idx_submissions_submitted_at" ON "seller_interview_submissions" ("submitted_at" DESC);

-- GIN index on answers using jsonb_path_ops for efficient path and containment queries
CREATE INDEX "idx_submissions_answers_gin" ON "seller_interview_submissions" USING GIN ("answers" jsonb_path_ops);
