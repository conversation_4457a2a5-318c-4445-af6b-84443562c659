CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- CreateEnum
CREATE TYPE "broker_activity_type" AS ENUM ('signup', 'login', 'getStatistics', 'getSellers', 'getEstates');

-- CreateEnum
CREATE TYPE "coupon_type" AS ENUM ('hmh_box', 'kolonial_250', 'kolonial_400', 'kolonial_500');

-- CreateEnum
CREATE TYPE "enum_OvertakeProtocolMeter_type" AS ENUM ('electricity', 'water');

-- CreateEnum
CREATE TYPE "enum_OvertakeProtocolParticipant_belongsTo" AS ENUM ('SELLER', 'BUYER');

-- CreateEnum
CREATE TYPE "enum_OvertakeProtocol_electricityProviderSelected" AS ENUM ('fortum', 'norgesEnergy', 'hafslund', 'none', 'hafslundFortum', 'fjordkraft', 'trondelagSpot', 'trondelagTobbSpot');

-- CreateEnum
CREATE TYPE "enum_OvertakeProtocol_numberOfKeys" AS ENUM ('1', '2', '3', '4', '5+');

-- CreateEnum
CREATE TYPE "enum_PEPForm_pepType" AS ENUM ('SELLER', 'BUYER');

-- CreateEnum
CREATE TYPE "estate_checklist_id_enum" AS ENUM ('appraiser', 'contractSigning', 'handover', 'informationGathering', 'photographer', 'viewing', 'buyerContracSigning', 'takeover', 'move-in');

-- CreateEnum
CREATE TYPE "estate_checklist_type_enum" AS ENUM ('buyer', 'seller');

-- CreateEnum
CREATE TYPE "estate_event_id_enum" AS ENUM ('interiorGuidance', 'photographer', 'appraiser', 'adLiveInCoreChannels', 'viewing', 'biddingRound', 'contractSigning', 'takeOver');

-- CreateEnum
CREATE TYPE "estate_event_type_enum" AS ENUM ('user', 'vitec');

-- CreateEnum
CREATE TYPE "estate_type_enum" AS ENUM ('forSale', 'owned');

-- CreateEnum
CREATE TYPE "mail_audit_type" AS ENUM ('sendMail', 'sendTemplate', 'otp', 'settlement', 'pep');

-- CreateEnum
CREATE TYPE "mortgage_options_enum" AS ENUM ('no', 'yes', 'preApproved');

-- CreateEnum
CREATE TYPE "popup_state_enum" AS ENUM ('firstShow', 'neverShow', 'showAfterDay', 'showAfterWeek');

-- CreateEnum
CREATE TYPE "popup_type_enum" AS ENUM ('referral', 'first_friend_referred', 'third_friend_referred', 'hmh_box_sent_confirm');

-- CreateEnum
CREATE TYPE "register_type_enum" AS ENUM ('BANKID', 'PHONE_NUMBER', 'VIPPS');

-- CreateEnum
CREATE TYPE "reward_type" AS ENUM ('third_friend_referred');

-- CreateEnum
CREATE TYPE "storebrand_audit_type" AS ENUM ('clientJwtCreate', 'serverJwtExchange', 'clientLeadCreate', 'serverLeadSend');

-- CreateEnum
CREATE TYPE "undefined" AS ENUM ('minister', 'national_assembly_member', 'government_member', 'court_member', 'national_audit_office_member', 'ambassador_of_military_member', 'state_enterprise_member', 'international_boss', 'none');

-- CreateEnum
CREATE TYPE "user_preference_enum" AS ENUM ('now', 'rightPrice', 'withinSixMonths', 'dont');

-- CreateTable
CREATE TABLE "Areas" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-09-24 15:20:06.951+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-09-24 15:20:06.951+00'::timestamp with time zone,
    "name" TEXT,
    "parentId" UUID,
    "weight" INTEGER DEFAULT 0,
    "postalCodes" TEXT[] DEFAULT ARRAY[]::TEXT[],

    CONSTRAINT "Areas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AverageSalePrice" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "averageTimeForPropertySale" DECIMAL(32,4),
    "averagePricePerSquareMeter" DECIMAL(32,4),
    "averagePriceAbsolute" DECIMAL(32,4),
    "postalCode" VARCHAR(255),
    "averageTimeForPropertySaleCount" INTEGER NOT NULL DEFAULT 0,
    "averagePricePerSquareMeterCount" INTEGER NOT NULL DEFAULT 0,
    "averagePriceAbsoluteCount" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "AverageSalePrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BrokerActivity" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "brokerID" UUID NOT NULL,
    "type" "broker_activity_type",
    "data" JSONB,

    CONSTRAINT "BrokerActivity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Brokers" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "email" TEXT NOT NULL,
    "password" TEXT,
    "vitecID" TEXT NOT NULL,
    "passwordCode" TEXT NOT NULL DEFAULT uuid_generate_v4(),
    "verifyPasswordCode" TEXT,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "verifyEmailCode" UUID,
    "referralCode" VARCHAR(255) NOT NULL DEFAULT '',

    CONSTRAINT "Brokers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Coupons" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "code" TEXT NOT NULL,
    "type" "coupon_type" NOT NULL,
    "rewardID" UUID,

    CONSTRAINT "Coupons_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DismissedActions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "actionID" TEXT NOT NULL,
    "brokerID" TEXT NOT NULL,
    "estateID" TEXT NOT NULL,

    CONSTRAINT "DismissedActions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EiendomsverdiAudit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "userID" UUID,
    "response" TEXT NOT NULL,

    CONSTRAINT "EiendomsverdiAudit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EiendomsverdiEstateCache" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "query" TEXT,
    "cacheType" TEXT,
    "lastSynced" TIMESTAMPTZ(6) NOT NULL,
    "data" JSONB NOT NULL,
    "queryHash" CHAR(64) NOT NULL,

    CONSTRAINT "EiendomsverdiEstateCache_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EstateChecklistConnection" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "vitecID" TEXT,
    "userID" UUID NOT NULL,
    "checklistID" "estate_checklist_id_enum" NOT NULL,
    "doneTodoID" INTEGER NOT NULL,

    CONSTRAINT "EstateChecklistConnection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EstateChecklists" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "checklistID" "estate_checklist_id_enum",
    "title" TEXT,
    "type" "estate_checklist_type_enum",
    "logName" VARCHAR(255),
    "sortOrder" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "EstateChecklists_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EstateEventConnection" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "vitecID" TEXT,
    "userID" UUID NOT NULL,
    "eventID" "estate_event_id_enum" NOT NULL,
    "date" TIMESTAMPTZ(6) NOT NULL,

    CONSTRAINT "EstateEventConnection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EstateEvents" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "eventID" "estate_event_id_enum",
    "title" TEXT,
    "type" "estate_event_type_enum",
    "logName" VARCHAR(255),

    CONSTRAINT "EstateEvents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EstateImagesFromUsers" (
    "userID" UUID NOT NULL,
    "landIdentificationMatrix" JSONB NOT NULL,
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "imagePublicUrl" TEXT,

    CONSTRAINT "EstateImagesFromUsers_pkey" PRIMARY KEY ("userID","landIdentificationMatrix")
);

-- CreateTable
CREATE TABLE "EstatePriceHistories" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "postgresEstateId" UUID NOT NULL,
    "landIdentificationMatrix" JSONB NOT NULL,
    "evPrice" INTEGER NOT NULL,
    "actualPriceWithVitecOffset" INTEGER NOT NULL,

    CONSTRAINT "EstatePriceHistories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EstateTodos" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "checklistID" "estate_checklist_id_enum" NOT NULL,
    "todoID" SERIAL NOT NULL,
    "title" TEXT,
    "description" TEXT,
    "type" "estate_checklist_type_enum",
    "logName" VARCHAR(255),

    CONSTRAINT "EstateTodos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Estates" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "userID" UUID NOT NULL,
    "type" "estate_type_enum" NOT NULL DEFAULT 'owned',
    "address" TEXT,
    "numberOfBedrooms" INTEGER,
    "sellPreference" "user_preference_enum",
    "propertyType" TEXT,
    "landIdentificationMatrix" JSONB,
    "ownership" TEXT,
    "livingArea" INTEGER,
    "buildYear" INTEGER,
    "floor" INTEGER,
    "connectToBroker" BOOLEAN DEFAULT false,
    "EVEstateID" VARCHAR(255),
    "EVAddressID" VARCHAR(255),
    "loanAmount" INTEGER,
    "interestRate" DOUBLE PRECISION,
    "originalMortgage" INTEGER,
    "imageUrl" VARCHAR(255),
    "OrganizationNumber" VARCHAR(255),
    "ShareNumber" VARCHAR(255),
    "mortgageYears" INTEGER,
    "isArchived" BOOLEAN DEFAULT false,
    "estimationOffset" REAL,

    CONSTRAINT "Estates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Estates_audit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "operation" TEXT NOT NULL,
    "oldData" JSONB,
    "newData" JSONB,
    "changedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "userID" UUID,

    CONSTRAINT "Estates_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ExternalLeadAudit" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "leadType" VARCHAR(255) NOT NULL,
    "isSuccessful" BOOLEAN,
    "externalLeadId" VARCHAR(255),
    "name" VARCHAR(255),
    "email" VARCHAR(255),
    "phone" VARCHAR(255),
    "address" TEXT,
    "postalCode" VARCHAR(255),
    "brokerId" VARCHAR(255),
    "departmentOfBroker" VARCHAR(255),
    "data" JSONB,

    CONSTRAINT "ExternalLeadAudit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Favorites" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "estateID" TEXT,
    "userID" UUID,

    CONSTRAINT "Favorites_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Feeds" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "userID" UUID NOT NULL,
    "text" TEXT,
    "iconName" VARCHAR(255),
    "deletedAt" TIMESTAMPTZ(6),
    "redirectUrl" TEXT,

    CONSTRAINT "Feeds_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "File" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "type" VARCHAR(255) NOT NULL,
    "binaryContent" BYTEA,
    "fileName" VARCHAR(255) NOT NULL,
    "mimeType" VARCHAR(255) NOT NULL,
    "s3FileUrl" VARCHAR(255),

    CONSTRAINT "File_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LeadAudit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "userID" UUID NOT NULL,
    "data" JSONB,
    "response" JSONB,

    CONSTRAINT "LeadAudit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LipscoreAudit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "targetEmail" VARCHAR(255) NOT NULL,
    "brokerId" VARCHAR(255) NOT NULL,
    "estateVitecId" VARCHAR(255) NOT NULL,
    "data" JSONB,
    "response" JSONB,
    "isSuccessful" BOOLEAN,

    CONSTRAINT "LipscoreAudit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LipscoreBrokerRating" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "lipscoreId" INTEGER,
    "vitecBrokerEmployeeId" VARCHAR(255),
    "averageRating" DOUBLE PRECISION,
    "reviewCount" INTEGER,
    "reviews" JSONB,
    "ratingCount" INTEGER,
    "ratings" JSONB,

    CONSTRAINT "LipscoreBrokerRating_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MailAudit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "type" "mail_audit_type",
    "from" VARCHAR(255),
    "to" VARCHAR(255),
    "data" JSONB,

    CONSTRAINT "MailAudit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Offices" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-09-24 15:20:06.951+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-09-24 15:20:06.951+00'::timestamp with time zone,
    "name" TEXT,
    "zip" TEXT,

    CONSTRAINT "Offices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OvertakeProtocol" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "estateVitecId" VARCHAR(255),
    "moneyTransferred" BOOLEAN,
    "sellerNewAddress" TEXT,
    "sellerNewPostcode" VARCHAR(255),
    "sellerNewCity" VARCHAR(255),
    "propertyCleaned" BOOLEAN,
    "propertyCleanedComment" TEXT,
    "sellerPaidCosts" BOOLEAN,
    "sellerPaidCostsComment" TEXT,
    "handedOverAllKeys" BOOLEAN,
    "handedOverAllKeysComment" TEXT,
    "numberOfKeys" "enum_OvertakeProtocol_numberOfKeys",
    "smokeAlarmAvailable" BOOLEAN,
    "fireExtinguisherAvailable" BOOLEAN,
    "fireSafetyComment" TEXT,
    "signingStarted" TIMESTAMPTZ(6),
    "signingFinished" TIMESTAMPTZ(6),
    "idfyDocumentId" TEXT,
    "waterInfoProvided" BOOLEAN,
    "electricityInfoProvided" BOOLEAN,
    "electricityProviderSelected" "enum_OvertakeProtocol_electricityProviderSelected",
    "finalSettlement" BOOLEAN,
    "finalSettlementComment" TEXT,
    "address" TEXT,
    "postCode" VARCHAR(255),
    "city" VARCHAR(255),
    "billingBuyerContactId" VARCHAR(255),
    "finalSettlementWithholding" BOOLEAN,
    "finalSettlementWithholdAmount" VARCHAR(255),
    "finalSettlementWithholdReason" TEXT,
    "finalSettlementWithholdingComment" TEXT,
    "locked" BOOLEAN NOT NULL DEFAULT false,
    "handoverComment" TEXT,
    "fileId" UUID,

    CONSTRAINT "OvertakeProtocol_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OvertakeProtocolMeter" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "overtakeProtocolId" UUID NOT NULL,
    "type" "enum_OvertakeProtocolMeter_type",
    "meterReading" VARCHAR(255),
    "meterNumber" VARCHAR(255),
    "fileId" UUID,
    "meterName" VARCHAR(255),

    CONSTRAINT "OvertakeProtocolMeter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OvertakeProtocolParticipant" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "overtakeProtocolId" UUID NOT NULL,
    "name" VARCHAR(255),
    "email" VARCHAR(255),
    "phoneNumber" VARCHAR(255),
    "isPowerOfAttorney" BOOLEAN,
    "belongsTo" "enum_OvertakeProtocolParticipant_belongsTo",
    "fileId" UUID,

    CONSTRAINT "OvertakeProtocolParticipant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PEPForm" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "estateVitecId" VARCHAR(255) NOT NULL,
    "isNotificationSent" BOOLEAN,
    "type" VARCHAR(255),
    "saleInfoTransactionReason" TEXT,
    "saleInfoInMyNameOrProxy" TEXT,
    "saleInfoUseOrInvestment" TEXT,
    "propertyInfoOwnedForTime" TEXT,
    "propertyInfoIsRenovatedByOwner" BOOLEAN,
    "propertyInfoRenovator" TEXT,
    "propertyInfoRenovationFinance" TEXT,
    "propertyInfoRenovationDocumentation" TEXT,
    "equityInfoPercent" TEXT,
    "equityInfoSource" TEXT,
    "idfyDocumentId" VARCHAR(255),
    "signingStarted" TIMESTAMPTZ(6),
    "signingFinished" TIMESTAMPTZ(6),
    "financing" JSONB DEFAULT '{"newHome": null, "doYouWantFinancing": null, "participantsSelected": null}',
    "locked" BOOLEAN NOT NULL DEFAULT false,
    "estateAssignmentTypeGroup" INTEGER,
    "valuationPurpose" VARCHAR(255),
    "valuationDescriptionOfOther" TEXT,
    "valuationSincePurchase" BOOLEAN,
    "valuationYear" INTEGER,
    "fileId" UUID,

    CONSTRAINT "PEPForm_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PEPFormParticipant" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "contactPersonVitecId" VARCHAR(255) NOT NULL,
    "name" VARCHAR(255),
    "email" VARCHAR(255),
    "phoneNumber" VARCHAR(255),
    "profession" VARCHAR(255),
    "employer" VARCHAR(255),
    "selectedCountry" VARCHAR(255),
    "typedCountry" VARCHAR(255),
    "estateCount" VARCHAR(255),
    "citizenshipDescription" JSONB,
    "ownPepType" VARCHAR(255),
    "ownDescriptionsOfSelected" JSONB DEFAULT '{"countries": null, "description": null, "dateWhenPracticed": null, "isLessThanAYearAgo": null}',
    "ownComment" TEXT,
    "employerPepType" VARCHAR(255),
    "employerDescriptionsOfSelected" JSONB DEFAULT '{"name": null, "countries": null, "description": null, "relationship": null, "dateWhenPracticed": null, "isLessThanAYearAgo": null}',
    "employerComment" TEXT,
    "familyPepType" VARCHAR(255),
    "familyDescriptionsOfSelected" JSONB DEFAULT '{"name": null, "countries": null, "description": null, "relationship": null, "dateWhenPracticed": null, "isLessThanAYearAgo": null}',
    "familyComment" TEXT,
    "pepFormId" UUID,
    "settlementBuyerParticipantId" UUID,
    "settlementBuyerFormIdForAccountManagers" UUID,
    "settlementSellerFormIdForAccountManagers" UUID,

    CONSTRAINT "PEPFormParticipant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Popups" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "userID" UUID NOT NULL,
    "type" "popup_type_enum" NOT NULL DEFAULT 'referral',
    "state" "popup_state_enum" NOT NULL DEFAULT 'firstShow',

    CONSTRAINT "Popups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PriceGuessingEstates" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "estateVitecId" VARCHAR(255),
    "dateOfGuessing" TIMESTAMPTZ(6),
    "priceSnapshot" INTEGER,

    CONSTRAINT "PriceGuessingEstates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PriceGuessingUserGuesses" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "userId" UUID NOT NULL,
    "priceGuessingEstateId" UUID NOT NULL,
    "guessedPrice" INTEGER,

    CONSTRAINT "PriceGuessingUserGuesses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReferralCodes" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "code" VARCHAR(255) NOT NULL,
    "free" BOOLEAN DEFAULT true,
    "comment" TEXT,

    CONSTRAINT "ReferralCodes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReferralInvites" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "referralID" UUID NOT NULL,
    "invitedUserID" UUID NOT NULL,

    CONSTRAINT "ReferralInvites_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RemovedUsers" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-09-24 15:20:06.951+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-09-24 15:20:06.951+00'::timestamp with time zone,
    "email" TEXT NOT NULL,
    "reason" TEXT NOT NULL,

    CONSTRAINT "RemovedUsers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RewardTypes" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "type" TEXT NOT NULL,
    "name" VARCHAR(255) NOT NULL,

    CONSTRAINT "RewardTypes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Rewards" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "referralCodeID" UUID,
    "rewardTypeID" UUID NOT NULL,
    "isRedeemed" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Rewards_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SequelizeMeta" (
    "name" VARCHAR(255) NOT NULL,

    CONSTRAINT "SequelizeMeta_pkey" PRIMARY KEY ("name")
);

-- CreateTable
CREATE TABLE "SettlementBuyer" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "estateVitecId" VARCHAR(255) NOT NULL,
    "hasEquity" BOOLEAN,
    "financeAccountNumber" VARCHAR(255),
    "financedWithLoan" VARCHAR(255),
    "idfyDocumentId" TEXT,
    "signingStarted" TIMESTAMPTZ(6),
    "signingFinished" TIMESTAMPTZ(6),
    "estateAssignmentNumber" VARCHAR(255),
    "estateAddress" TEXT,
    "fileId" UUID,
    "hasFinanceAccountManagers" BOOLEAN,
    "financeAccountManagers" JSONB,
    "isNotificationSent" BOOLEAN DEFAULT false,
    "financeAccountOwnerName" VARCHAR(255),
    "equityAccountNumber" VARCHAR(255),
    "equityAccountHolderName" VARCHAR(255),
    "hasEquityAccountManagers" BOOLEAN,
    "equityAccountManagers" JSONB,
    "isFinanceAndEquityAccountsTheSame" BOOLEAN,
    "leads" JSONB,
    "saleInfoTransactionReason" VARCHAR(255),
    "saleInfoInMyNameOrProxy" VARCHAR(255),
    "saleInfoUseOrInvestment" VARCHAR(255),
    "estateBaseType" SMALLINT,
    "locked" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "SettlementBuyer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SettlementBuyerEquity" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "settlementBuyerParticipantId" UUID NOT NULL,
    "amountOfEquity" VARCHAR(255),
    "bankOfEquity" VARCHAR(255),
    "originOfEquity" VARCHAR(255),
    "originOfEquityComment" TEXT,

    CONSTRAINT "SettlementBuyerEquity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SettlementBuyerLoan" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "settlementBuyerId" UUID NOT NULL,
    "loanBank" TEXT,
    "bankContactName" TEXT,
    "bankContactPhone" TEXT,
    "bankContactEmail" TEXT,
    "loanTaker" TEXT,

    CONSTRAINT "SettlementBuyerLoan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SettlementBuyerParticipant" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "settlementBuyerId" UUID NOT NULL,
    "name" VARCHAR(255),
    "email" VARCHAR(255),
    "phoneNumber" VARCHAR(255),
    "hasEquity" BOOLEAN,

    CONSTRAINT "SettlementBuyerParticipant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SettlementSeller" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "estateVitecId" VARCHAR(255) NOT NULL,
    "isMortgaged" BOOLEAN,
    "idfyDocumentId" TEXT,
    "signingStarted" TIMESTAMPTZ(6),
    "signingFinished" TIMESTAMPTZ(6),
    "estateAssignmentNumber" VARCHAR(255),
    "estateAddress" TEXT,
    "fileId" UUID,
    "isNotificationSent" BOOLEAN DEFAULT false,
    "accountsComment" TEXT,
    "leads" JSONB,
    "locked" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "SettlementSeller_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SettlementSellerAccount" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "settlementSellerId" UUID NOT NULL,
    "hasFinanceAccountManagers" BOOLEAN,
    "accountNumber" VARCHAR(255),
    "accountOwnerName" VARCHAR(255),
    "distributionInPercentage" INTEGER,
    "isAccountShared" BOOLEAN,
    "financeAccountManagers" JSONB,

    CONSTRAINT "SettlementSellerAccount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SettlementSellerLoan" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "settlementSellerId" UUID NOT NULL,
    "loanBank" TEXT,
    "loanTaker" TEXT,
    "residualDebt" TEXT,
    "bankContactName" TEXT,
    "bankContactPhone" TEXT,
    "bankContactEmail" TEXT,
    "shouldMortgageLoanBeRepaid" BOOLEAN,
    "loanIdNumber" VARCHAR(255),

    CONSTRAINT "SettlementSellerLoan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SettlementSellerParticipant" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "settlementSellerId" UUID NOT NULL,
    "name" VARCHAR(255),
    "email" VARCHAR(255),
    "phoneNumber" VARCHAR(255),

    CONSTRAINT "SettlementSellerParticipant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SmsAudit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "type" TEXT,
    "phoneNumber" VARCHAR(255),
    "data" JSONB,
    "response" JSONB,

    CONSTRAINT "SmsAudit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StorebrandAudit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "userOrParticipantID" UUID,
    "type" "storebrand_audit_type",
    "data" JSONB,

    CONSTRAINT "StorebrandAudit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Urls" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "shortUrl" VARCHAR(32) NOT NULL,
    "longUrl" TEXT NOT NULL,
    "deletedAt" TIMESTAMPTZ(6),
    "firstClickedAt" TIMESTAMPTZ(6),

    CONSTRAINT "Urls_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserActivity" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "userID" UUID,
    "type" VARCHAR(255),
    "data" JSONB,

    CONSTRAINT "UserActivity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserArea" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "userID" UUID,
    "areaID" UUID,

    CONSTRAINT "UserArea_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserFirebaseToken" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6),
    "updatedAt" TIMESTAMPTZ(6),
    "userID" UUID,
    "token" TEXT,
    "os" TEXT DEFAULT 'ios',

    CONSTRAINT "UserIosToken_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserOptions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "userID" UUID NOT NULL,
    "type" JSONB,
    "livingArea" INTEGER,
    "area" JSONB,
    "price" JSONB,
    "profile" JSONB,
    "values" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "mortgage" "mortgage_options_enum",
    "contactPreferences" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "contactHours" TEXT[] DEFAULT ARRAY[]::TEXT[],

    CONSTRAINT "UserOptions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Users" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-06-25 13:06:51.511+00'::timestamp with time zone,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phoneNumber" TEXT,
    "password" TEXT,
    "bankIdVerified" BOOLEAN,
    "birthday" VARCHAR(255),
    "passwordCode" TEXT NOT NULL DEFAULT uuid_generate_v4(),
    "verifyPasswordCode" TEXT,
    "annualIncome" INTEGER,
    "sumOfOtherLoans" INTEGER,
    "referralCode" VARCHAR(255) NOT NULL DEFAULT '',
    "visitedChecklist" BOOLEAN,
    "visitedSalesProcess" BOOLEAN,
    "registeredWith" "register_type_enum" NOT NULL DEFAULT 'BANKID',
    "closedTutorialAt" TIMESTAMPTZ(6),
    "pushNotificationSettings" JSONB DEFAULT '{"quiz": true, "journey": true, "prospect": true, "propertyValue": true}',
    "consentSettings" JSONB DEFAULT '{"financing": false, "searchProfile": false, "emailMarketing": false}',

    CONSTRAINT "Users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VitecEstateExtensions" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "createdAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT '2020-10-20 19:10:45.44+00'::timestamp with time zone,
    "vitecID" VARCHAR(255) NOT NULL,
    "loanAmount" INTEGER,
    "interestRate" DOUBLE PRECISION,
    "originalMortgage" INTEGER,
    "mapImageUrl" VARCHAR(255),
    "triedToDownloadImage" BOOLEAN NOT NULL DEFAULT false,
    "mortgageYears" INTEGER,

    CONSTRAINT "VitecEstateExtensions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "commission_cache" (
    "cache_id" SERIAL NOT NULL,
    "cache_date" DATE NOT NULL,
    "employee_id" VARCHAR(255) NOT NULL,
    "department" VARCHAR(255) NOT NULL,
    "commission_amount" DECIMAL(10,2) NOT NULL,
    "signed_count" INTEGER DEFAULT 0,
    "sold_count" INTEGER DEFAULT 0,
    "last_updated" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "commission_cache_pkey" PRIMARY KEY ("cache_id")
);

-- CreateTable
CREATE TABLE "estate_matrix_updates_log" (
    "log_id" SERIAL NOT NULL,
    "estate_id" INTEGER NOT NULL,
    "old_matrix" JSONB,
    "new_matrix" JSONB NOT NULL,
    "update_timestamp" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "pg_estate_id" UUID,

    CONSTRAINT "estate_matrix_updates_log_pkey" PRIMARY KEY ("log_id")
);

-- CreateTable
CREATE TABLE "magic_link_users" (
    "name" VARCHAR(255),
    "email" VARCHAR(255) NOT NULL,
    "emailVerified" TIMESTAMPTZ(6),
    "image" TEXT,
    "role" VARCHAR(255) DEFAULT 'user',
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),

    CONSTRAINT "magic_link_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sessions" (
    "id" SERIAL NOT NULL,
    "expires" TIMESTAMPTZ(6) NOT NULL,
    "sessionToken" VARCHAR(255) NOT NULL,
    "isimpersonated" BOOLEAN DEFAULT false,
    "userId" UUID NOT NULL,
    "impersonatedbyuserid" UUID,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "verification_token" (
    "identifier" TEXT NOT NULL,
    "expires" TIMESTAMPTZ(6) NOT NULL,
    "token" TEXT NOT NULL,

    CONSTRAINT "verification_token_pkey" PRIMARY KEY ("identifier","token")
);

-- CreateIndex
CREATE UNIQUE INDEX "areas_name_parent_id" ON "Areas"("name", "parentId");

-- CreateIndex
CREATE UNIQUE INDEX "Brokers_email_key" ON "Brokers"("email");

-- CreateIndex
CREATE UNIQUE INDEX "EiendomsverdiEstateCache_queryHash_key" ON "EiendomsverdiEstateCache"("queryHash");

-- CreateIndex
CREATE UNIQUE INDEX "EstateChecklists_checklistID_key" ON "EstateChecklists"("checklistID");

-- CreateIndex
CREATE UNIQUE INDEX "EstateEvents_eventID_key" ON "EstateEvents"("eventID");

-- CreateIndex
CREATE INDEX "idx_postgres_estate_id" ON "EstatePriceHistories"("postgresEstateId");

-- CreateIndex
CREATE UNIQUE INDEX "EstateTodos_todoID_key" ON "EstateTodos"("todoID");

-- CreateIndex
CREATE UNIQUE INDEX "ReferralCodes_code_key" ON "ReferralCodes"("code");

-- CreateIndex
CREATE UNIQUE INDEX "Urls_shortUrl_key" ON "Urls"("shortUrl");

-- CreateIndex
CREATE INDEX "user_activity_user_i_d" ON "UserActivity"("userID");

-- CreateIndex
CREATE UNIQUE INDEX "user_area_user_i_d_area_i_d" ON "UserArea"("userID", "areaID");

-- CreateIndex
CREATE UNIQUE INDEX "user_ios_token_user_i_d_token" ON "UserFirebaseToken"("userID", "token");

-- CreateIndex
CREATE UNIQUE INDEX "Users_email_key" ON "Users"("email");

-- CreateIndex
CREATE INDEX "idx_commission_cache_date_employee_amount" ON "commission_cache"("cache_date", "employee_id", "commission_amount");

-- CreateIndex
CREATE UNIQUE INDEX "commission_cache_cache_date_employee_id_key" ON "commission_cache"("cache_date", "employee_id");

-- AddForeignKey
ALTER TABLE "Areas" ADD CONSTRAINT "Areas_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Areas"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BrokerActivity" ADD CONSTRAINT "BrokerActivity_brokerID_fkey" FOREIGN KEY ("brokerID") REFERENCES "Brokers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Coupons" ADD CONSTRAINT "Coupons_rewardID_fkey" FOREIGN KEY ("rewardID") REFERENCES "Rewards"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "EiendomsverdiAudit" ADD CONSTRAINT "EiendomsverdiAudit_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EstateChecklistConnection" ADD CONSTRAINT "EstateChecklistConnection_checklistID_fkey" FOREIGN KEY ("checklistID") REFERENCES "EstateChecklists"("checklistID") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "EstateChecklistConnection" ADD CONSTRAINT "EstateChecklistConnection_doneTodoID_fkey" FOREIGN KEY ("doneTodoID") REFERENCES "EstateTodos"("todoID") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "EstateChecklistConnection" ADD CONSTRAINT "EstateChecklistConnection_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EstateEventConnection" ADD CONSTRAINT "EstateEventConnection_eventID_fkey" FOREIGN KEY ("eventID") REFERENCES "EstateEvents"("eventID") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "EstateEventConnection" ADD CONSTRAINT "EstateEventConnection_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EstateImagesFromUsers" ADD CONSTRAINT "EstateImagesFromUsers_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "EstateTodos" ADD CONSTRAINT "EstateTodos_checklistID_fkey" FOREIGN KEY ("checklistID") REFERENCES "EstateChecklists"("checklistID") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Estates" ADD CONSTRAINT "Estates_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Estates_audit" ADD CONSTRAINT "Estates_audit_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Favorites" ADD CONSTRAINT "Favorites_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Feeds" ADD CONSTRAINT "Feeds_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LeadAudit" ADD CONSTRAINT "LeadAudit_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OvertakeProtocol" ADD CONSTRAINT "OvertakeProtocol_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "OvertakeProtocolMeter" ADD CONSTRAINT "OvertakeProtocolMeter_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "OvertakeProtocolMeter" ADD CONSTRAINT "OvertakeProtocolMeter_overtakeProtocolId_fkey" FOREIGN KEY ("overtakeProtocolId") REFERENCES "OvertakeProtocol"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "OvertakeProtocolParticipant" ADD CONSTRAINT "OvertakeProtocolParticipant_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "OvertakeProtocolParticipant" ADD CONSTRAINT "OvertakeProtocolParticipant_overtakeProtocolId_fkey" FOREIGN KEY ("overtakeProtocolId") REFERENCES "OvertakeProtocol"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "PEPFormParticipant" ADD CONSTRAINT "PEPFormParticipant_pepFormId_fkey" FOREIGN KEY ("pepFormId") REFERENCES "PEPForm"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "PEPFormParticipant" ADD CONSTRAINT "PEPFormParticipant_settlementBuyerFormIdForAccountManagers_fkey" FOREIGN KEY ("settlementBuyerFormIdForAccountManagers") REFERENCES "SettlementBuyer"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "PEPFormParticipant" ADD CONSTRAINT "PEPFormParticipant_settlementBuyerParticipantId_fkey" FOREIGN KEY ("settlementBuyerParticipantId") REFERENCES "SettlementBuyerParticipant"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "PEPFormParticipant" ADD CONSTRAINT "PEPFormParticipant_settlementSellerFormIdForAccountManager_fkey" FOREIGN KEY ("settlementSellerFormIdForAccountManagers") REFERENCES "SettlementSeller"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Popups" ADD CONSTRAINT "Popups_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PriceGuessingUserGuesses" ADD CONSTRAINT "PriceGuessingUserGuesses_priceGuessingEstateId_fkey" FOREIGN KEY ("priceGuessingEstateId") REFERENCES "PriceGuessingEstates"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "PriceGuessingUserGuesses" ADD CONSTRAINT "PriceGuessingUserGuesses_userId_fkey" FOREIGN KEY ("userId") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ReferralInvites" ADD CONSTRAINT "ReferralInvites_invitedUserID_fkey" FOREIGN KEY ("invitedUserID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReferralInvites" ADD CONSTRAINT "ReferralInvites_referralID_fkey" FOREIGN KEY ("referralID") REFERENCES "ReferralCodes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Rewards" ADD CONSTRAINT "Rewards_rewardTypeID_fkey" FOREIGN KEY ("rewardTypeID") REFERENCES "RewardTypes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SettlementBuyer" ADD CONSTRAINT "SettlementBuyer_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SettlementBuyerEquity" ADD CONSTRAINT "SettlementBuyerEquity_settlementBuyerParticipantId_fkey" FOREIGN KEY ("settlementBuyerParticipantId") REFERENCES "SettlementBuyerParticipant"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SettlementBuyerLoan" ADD CONSTRAINT "SettlementBuyerLoan_settlementBuyerId_fkey" FOREIGN KEY ("settlementBuyerId") REFERENCES "SettlementBuyer"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SettlementBuyerParticipant" ADD CONSTRAINT "SettlementBuyerParticipant_settlementBuyerId_fkey" FOREIGN KEY ("settlementBuyerId") REFERENCES "SettlementBuyer"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SettlementSeller" ADD CONSTRAINT "SettlementSeller_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SettlementSellerAccount" ADD CONSTRAINT "SettlementSellerAccount_settlementSellerId_fkey" FOREIGN KEY ("settlementSellerId") REFERENCES "SettlementSeller"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SettlementSellerLoan" ADD CONSTRAINT "SettlementSellerLoan_settlementSellerId_fkey" FOREIGN KEY ("settlementSellerId") REFERENCES "SettlementSeller"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SettlementSellerParticipant" ADD CONSTRAINT "SettlementSellerParticipant_settlementSellerId_fkey" FOREIGN KEY ("settlementSellerId") REFERENCES "SettlementSeller"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "UserActivity" ADD CONSTRAINT "UserActivity_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserArea" ADD CONSTRAINT "UserArea_areaID_fkey" FOREIGN KEY ("areaID") REFERENCES "Areas"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "UserArea" ADD CONSTRAINT "UserArea_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserFirebaseToken" ADD CONSTRAINT "UserIosToken_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserOptions" ADD CONSTRAINT "UserOptions_userID_fkey" FOREIGN KEY ("userID") REFERENCES "Users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_impersonatedbyuserid_fkey" FOREIGN KEY ("userId") REFERENCES "magic_link_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

