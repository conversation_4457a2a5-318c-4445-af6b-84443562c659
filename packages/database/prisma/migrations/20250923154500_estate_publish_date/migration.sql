-- CreateTable
CREATE TABLE "estate_publish_date" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "publish_date" TIMESTAMPTZ(6),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "estate_publish_date_pkey" PRIMARY KEY ("id")
);

-- Unique index for estate_id (matches Prisma @unique)
CREATE UNIQUE INDEX "estate_publish_date_estate_id_key" ON "estate_publish_date"("estate_id");

-- Additional index declared in Prisma (@@index([estate_id]))
CREATE INDEX "estate_publish_date_estate_id_idx" ON "estate_publish_date"("estate_id");
