-- CreateTable
CREATE TABLE "message_template_override" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ(6),
    "employee_id" VARCHAR(255),
    "estate_id" TEXT,
    "template_id" TEXT NOT NULL,
    "email_subject" TEXT NOT NULL,
    "email_content" JSONB NOT NULL,
    "email_html" TEXT NOT NULL,
    "magic_link_users_id" UUID NOT NULL,

    CONSTRAINT "message_template_override_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "message_template_override_employee_id_idx" ON "message_template_override"("employee_id");

-- CreateIndex
CREATE INDEX "message_template_override_estate_id_idx" ON "message_template_override"("estate_id");

-- CreateIndex
CREATE INDEX "message_template_override_template_id_idx" ON "message_template_override"("template_id");

-- CreateIndex
CREATE INDEX "message_template_override_employee_id_estate_id_idx" ON "message_template_override"("employee_id", "estate_id");

-- CreateIndex
CREATE INDEX "message_template_override_employee_id_template_id_idx" ON "message_template_override"("employee_id", "template_id");

-- CreateIndex
CREATE INDEX "message_template_override_estate_id_template_id_idx" ON "message_template_override"("estate_id", "template_id");

-- CreateIndex
CREATE UNIQUE INDEX "message_template_override_employee_id_estate_id_template_id_key" ON "message_template_override"("employee_id", "estate_id", "template_id");

-- AddForeignKey
ALTER TABLE "message_template_override" ADD CONSTRAINT "message_template_override_magic_link_users_id_fkey" FOREIGN KEY ("magic_link_users_id") REFERENCES "magic_link_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
