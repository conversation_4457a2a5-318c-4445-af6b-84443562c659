-- CreateTable
CREATE TABLE "estate_sellers" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "seller_id" TEXT NOT NULL,
    "listing_agreement_id" UUID NOT NULL,
    "social_security_number" TEXT,
    "address" TEXT,
    "post_code" TEXT,
    "city" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ(6),

    CONSTRAINT "estate_sellers_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "estate_sellers_estate_id_key" ON "estate_sellers"("estate_id");

-- CreateIndex
CREATE UNIQUE INDEX "estate_sellers_seller_id_key" ON "estate_sellers"("seller_id");

-- AddForeignKey
ALTER TABLE "estate_sellers" ADD CONSTRAINT "estate_sellers_listing_agreement_id_fkey" FOREIGN KEY ("listing_agreement_id") REFERENCES "listing_agreements"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
