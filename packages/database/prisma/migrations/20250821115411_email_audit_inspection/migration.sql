/*
  Migration note:
  We are inverting the relation between inspection_folders_audit and email_audit.
  Previously: inspection_folders_audit.email_audit_id -> email_audit.id
  Now:        email_audit.inspection_folders_audit_id -> inspection_folders_audit.id

  Backfill strategy:
  1. Add the new nullable column on email_audit
  2. Copy over existing relations
  3. Drop old FK + column
  4. Add new FK constraint

  Assumption: At most one inspection_folders_audit row referenced a given email_audit.id.
  If multiple existed, this UPDATE would arbitrarily pick one (due to standard join semantics) – adjust if needed.
*/

-- 1. Drop old foreign key to allow structural changes (still can read the column value)
ALTER TABLE "inspection_folders_audit" DROP CONSTRAINT IF EXISTS "inspection_folders_audit_email_audit_id_fkey";

-- 2. Add new column on email_audit
ALTER TABLE "email_audit" ADD COLUMN IF NOT EXISTS "inspection_folders_audit_id" INTEGER;

-- 3. Backfill new column using existing data before dropping old column
UPDATE "email_audit" ea
SET "inspection_folders_audit_id" = ifa.id
FROM "inspection_folders_audit" ifa
WHERE ifa.email_audit_id = ea.id
  AND ea."inspection_folders_audit_id" IS NULL;

-- 4. Drop old column
ALTER TABLE "inspection_folders_audit" DROP COLUMN IF EXISTS "email_audit_id";

-- 5. Add new foreign key constraint
ALTER TABLE "email_audit" ADD CONSTRAINT "email_audit_inspection_folders_audit_id_fkey" FOREIGN KEY ("inspection_folders_audit_id") REFERENCES "inspection_folders_audit"("id") ON DELETE SET NULL ON UPDATE CASCADE;
