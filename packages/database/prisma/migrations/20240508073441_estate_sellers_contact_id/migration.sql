/*
  Warnings:

  - You are about to drop the column `next_seller_id` on the `estate_sellers` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[next_contact_id]` on the table `estate_sellers` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `next_contact_id` to the `estate_sellers` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "estate_sellers_next_seller_id_key";

-- AlterTable
ALTER TABLE "estate_sellers" DROP COLUMN "next_seller_id",
ADD COLUMN     "next_contact_id" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "estate_sellers_next_contact_id_key" ON "estate_sellers"("next_contact_id");
