-- AlterTable
ALTER TABLE "inspection_folders_audit" ADD COLUMN     "email_audit_id" UUID;

-- AlterTable
ALTER TABLE "listing_agreements" ALTER COLUMN "suggested_price" SET DATA TYPE DOUBLE PRECISION;

-- CreateTable
CREATE TABLE "email_audit" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "subject" TEXT,
    "from_email" VARCHAR(255),
    "template_name" VARCHAR(255),
    "context_type" VARCHAR(64),
    "context_id" VARCHAR(255),
    "global_metadata" JSONB,

    CONSTRAINT "email_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "email_audit_recipient" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "email_audit_id" UUID NOT NULL,
    "recipient_email" TEXT NOT NULL,
    "contact_id" TEXT,
    "message_id" VARCHAR(255) NOT NULL,
    "status" VARCHAR(32) NOT NULL DEFAULT 'sent',
    "sent_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "opened_at" TIMESTAMPTZ(6),
    "open_count" INTEGER NOT NULL DEFAULT 0,
    "last_event_at" TIMESTAMPTZ(6),
    "bounce_type" VARCHAR(255),
    "reject_reason" VARCHAR(255),
    "recipient_metadata" JSONB,
    "raw_last_event" JSONB,

    CONSTRAINT "email_audit_recipient_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "email_audit_context_type_context_id_idx" ON "email_audit"("context_type", "context_id");

-- CreateIndex
CREATE INDEX "email_audit_recipient_email_audit_id_idx" ON "email_audit_recipient"("email_audit_id");

-- CreateIndex
CREATE INDEX "email_audit_recipient_message_id_recipient_email_idx" ON "email_audit_recipient"("message_id", "recipient_email");

-- CreateIndex
CREATE INDEX "email_audit_recipient_email_audit_id_status_idx" ON "email_audit_recipient"("email_audit_id", "status");

-- CreateIndex
CREATE UNIQUE INDEX "email_audit_recipient_message_id_recipient_email_key" ON "email_audit_recipient"("message_id", "recipient_email");

-- CreateIndex
CREATE INDEX "offer_access_tokens_estate_id_idx" ON "offer_access_tokens"("estate_id");

-- CreateIndex
CREATE INDEX "offer_access_tokens_listing_agreement_id_idx" ON "offer_access_tokens"("listing_agreement_id");

-- AddForeignKey
ALTER TABLE "inspection_folders_audit" ADD CONSTRAINT "inspection_folders_audit_email_audit_id_fkey" FOREIGN KEY ("email_audit_id") REFERENCES "email_audit"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_audit_recipient" ADD CONSTRAINT "email_audit_recipient_email_audit_id_fkey" FOREIGN KEY ("email_audit_id") REFERENCES "email_audit"("id") ON DELETE CASCADE ON UPDATE CASCADE;
