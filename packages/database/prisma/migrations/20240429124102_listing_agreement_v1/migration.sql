-- CreateTable
CREATE TABLE "listing_agreements" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "suggested_price" DECIMAL(32,4),
    "fee_percentage" DECIMAL(32,4),
    "seller_insurance" BOOLEAN,
    "general_terms" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ(6),

    CONSTRAINT "listing_agreements_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "budget_posts" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "next_budget_post_id" BIGINT NOT NULL,
    "listing_agreement_id" UUID NOT NULL,
    "title" TEXT,
    "description" TEXT,
    "price" DECIMAL(32,4),
    "initial_price" DECIMAL(32,4),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ(6),

    CONSTRAINT "budget_posts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "listing_agreements_estate_id_key" ON "listing_agreements"("estate_id");

-- CreateIndex
CREATE UNIQUE INDEX "budget_posts_next_budget_post_id_key" ON "budget_posts"("next_budget_post_id");

-- AddForeignKey
ALTER TABLE "budget_posts" ADD CONSTRAINT "budget_posts_listing_agreement_id_fkey" FOREIGN KEY ("listing_agreement_id") REFERENCES "listing_agreements"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
