-- CreateTable
CREATE TABLE "broker_partner" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "instagram" VARCHAR(255),
    "website" VARCHAR(255),
    "description" TEXT,
    "category" TEXT,
    "images" TEXT[],
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "employee_id" TEXT NOT NULL,
    "profile_picture" TEXT,
    "hidden" BOOLEAN NOT NULL DEFAULT false,
    "sequence" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "broker_partner_pkey" PRIMARY KEY ("id")
);
